[gd_scene load_steps=5 format=2]

[ext_resource path="res://effects/treeleafalt.png" type="Texture" id=1]

[sub_resource type="Gradient" id=2]
offsets = PoolRealArray( 0, 0.0225564, 0.729323, 1 )
colors = PoolColorArray( 0, 0, 0, 0, 1, 1, 1, 1, 0.586466, 0.586466, 0.586466, 1, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=3]
gradient = SubResource( 2 )

[sub_resource type="ParticlesMaterial" id=1]
emission_shape = 2
emission_box_extents = Vector3( 200, 30, 1 )
flag_disable_z = true
direction = Vector3( 0, 1, 0 )
spread = 90.0
gravity = Vector3( 0, 13, 0 )
initial_velocity = 55.0
initial_velocity_random = 0.3
angular_velocity = 50.0
angular_velocity_random = 0.8
orbit_velocity = 0.0
orbit_velocity_random = 0.0
radial_accel = -23.0
radial_accel_random = 1.0
color_ramp = SubResource( 3 )

[node name="fallingleaves" type="Particles2D"]
use_parent_material = true
z_index = 17
amount = 2
lifetime = 12.0
explosiveness = 0.3
visibility_rect = Rect2( -150, -100, 300, 400 )
local_coords = false
process_material = SubResource( 1 )
texture = ExtResource( 1 )
