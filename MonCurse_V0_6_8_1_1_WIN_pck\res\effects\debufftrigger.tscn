[gd_scene load_steps=7 format=2]

[ext_resource path="res://font/OpenSans-ExtraBold.ttf" type="DynamicFontData" id=1]
[ext_resource path="res://Assets/ui/debufficons/2m.png" type="Texture" id=2]
[ext_resource path="res://effects/blinkmask.png" type="Texture" id=3]
[ext_resource path="res://effects/debufftrigger.gd" type="Script" id=4]

[sub_resource type="Animation" id=1]
resource_name = "bounce"
length = 1.8
tracks/0/type = "value"
tracks/0/path = NodePath("Control:rect_position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.4, 0.6, 0.8, 1, 1.2, 1.4, 1.6, 1.8 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 8, -20.7 ), Vector2( 16, -39.5 ), Vector2( 24, -54.9 ), Vector2( 32, -65.2 ), Vector2( 40, -69.8 ), Vector2( 48, -68.2 ), Vector2( 56, -60.4 ), Vector2( 64, -47.3 ), Vector2( 72, -30 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Control:rect_scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1.8 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 0.4, 0.4 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("Control:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.3, 1.8 ),
"transitions": PoolRealArray( 1, 2, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("AnimationPlayer:playback_speed")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.4, 1.8 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ 1.8, 0.8, 1.4 ]
}

[sub_resource type="DynamicFont" id=2]
size = 38
outline_size = 3
outline_color = Color( 0, 0, 0, 0.627451 )
font_data = ExtResource( 1 )

[node name="debufftrigger" type="Node2D"]
script = ExtResource( 4 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
playback_speed = 1.8
anims/bounce = SubResource( 1 )

[node name="Control" type="Control" parent="."]
margin_right = 40.003
margin_bottom = 39.9999

[node name="Sprite" type="TextureRect" parent="Control"]
self_modulate = Color( 0.392157, 0.392157, 0.392157, 1 )
margin_left = -280.0
margin_top = -80.0
margin_right = 128.0
margin_bottom = 16.0
texture = ExtResource( 3 )
expand = true
stretch_mode = 1
__meta__ = {
"_edit_lock_": true
}

[node name="Label" type="Label" parent="Control"]
margin_left = -260.0
margin_top = -64.0
grow_horizontal = 0
grow_vertical = 0
custom_fonts/font = SubResource( 2 )
text = "Hypnosis  "
align = 2
__meta__ = {
"_edit_lock_": true
}

[node name="TextureRect" type="TextureRect" parent="Control"]
margin_top = -64.0
margin_right = 64.0
grow_vertical = 0
texture = ExtResource( 2 )
__meta__ = {
"_edit_lock_": true
}

[connection signal="animation_finished" from="AnimationPlayer" to="." method="_on_AnimationPlayer_animation_finished"]
