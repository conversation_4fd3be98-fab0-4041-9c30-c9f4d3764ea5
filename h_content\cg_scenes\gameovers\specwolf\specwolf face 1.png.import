[remap]

importer="texture"
type="StreamTexture"
path.s3tc="res://.import/specwolf face 1.png-70c45749a5e00d37a346c34c7177c5a8.s3tc.stex"
path.etc2="res://.import/specwolf face 1.png-70c45749a5e00d37a346c34c7177c5a8.etc2.stex"
metadata={
"imported_formats": [ "s3tc", "etc2" ],
"vram_texture": true
}

[deps]

source_file="res://DialogueArt/CG/gameovers/specwolf/specwolf face 1.png"
dest_files=[ "res://.import/specwolf face 1.png-70c45749a5e00d37a346c34c7177c5a8.s3tc.stex", "res://.import/specwolf face 1.png-70c45749a5e00d37a346c34c7177c5a8.etc2.stex" ]

[params]

compress/mode=2
compress/lossy_quality=0.7
compress/hdr_mode=0
compress/bptc_ldr=0
compress/normal_map=0
flags/repeat=0
flags/filter=true
flags/mipmaps=false
flags/anisotropic=false
flags/srgb=2
process/fix_alpha_border=true
process/premult_alpha=false
process/HDR_as_SRGB=false
process/invert_color=false
process/normal_map_invert_y=false
stream=false
size_limit=0
detect_3d=true
svg/scale=1.0
