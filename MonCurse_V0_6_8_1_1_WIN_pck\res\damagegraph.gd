extends Control


func graph(typedamage,_directionsarray,blocks,debuffvectorarray,unusedblocks,attackname):
	if Playervariables.modarray[Playervariables.mods.TRANSLATION] == true:
		if Playervariables.transdict["attacknames"].has(attackname):
			attackname = Playervariables.transdict["attacknames"][attackname]
	$vbox/attackname.set_text(attackname)
	var totalblocks = 0
	for blockarray in blocks:
		totalblocks += blockarray[2]
	rect_rotation = -get_parent().get_parent().rotation_degrees
	if typedamage.y == 0:
		var newtile = TextureRect.new()
		$vbox/damageprocess.add_child(newtile)
		newtile.texture = load(Mainpreload.get("damageicon"+str(typedamage.x)))#load("res://Assets/ui/damageicons/"+str(typedamage.x)+".png")
		newtile.set_self_modulate(Color(0.65,0.65,0.65,0.4))
	else:
		for damagecount in range(typedamage.y):
			var newtile = TextureRect.new()
			$vbox/damageprocess.add_child(newtile)
			newtile.texture = load(Mainpreload.get("damageicon"+str(typedamage.x)))# load("res://Assets/ui/damageicons/"+str(typedamage.x)+".png")
			if damagecount < totalblocks:
				newtile.set_self_modulate(Color(0.7,0.7,0.7,0.5))
#	if directionsarray.size() == 1:
#		if directionsarray[0] == -2:
#			var newtile = TextureRect.new()
#			$vbox/damageprocess.add_child(newtile)
#			newtile.texture = load(Mainpreload.directionauto)# load("res://Assets/ui/directionauto.png")
#		elif directionsarray[0] == 5:
#			var newtile = TextureRect.new()
#			$vbox/damageprocess.add_child(newtile)
#			newtile.texture =  load(Mainpreload.directionthrough)#load("res://Assets/ui/directionthrough.png")
#	else:
#		for direction in directionsarray:
#			if direction < 4:
#				var newtile = TextureRect.new()
#				$vbox/damageprocess.add_child(newtile)
#				newtile.texture =  load(Mainpreload.get("direction"+str(direction)))#load("res://Assets/ui/direction"+str(direction)+".png")
#	if directionsarray[0] == 5:
#		pass
#	if blocks.size() == 0:
#		var newtile = TextureRect.new()
#		$vbox/damageprocess.add_child(newtile)
#		newtile.texture = load(Mainpreload.blocktilenone) #load("res://Assets/ui/blocktilenone.png")
#	else:
	for blockarray in blocks:
		for _blockcount in range(blockarray[2]):
			var newtile = TextureRect.new()
			$vbox/damageprocess.add_child(newtile)
			newtile.texture =  load(Mainpreload.get("blocktile5"))#load("res://Assets/ui/blocktile"+str(blockarray[0])+".png")
			newtile.set_self_modulate(Playervariables.typecolorarray[blockarray[1]])
			newtile.set_modulate(Color(0.7,0.7,0.7,0.5))
	if unusedblocks.size() > 0:
		for blockarray in unusedblocks:
			for _blockcount in range(blockarray[2]):
				var newtile = TextureRect.new()
				$vbox/damageprocess.add_child(newtile)
				newtile.texture = load(Mainpreload.get("blocktile"+str(blockarray[0])))#load("res://Assets/ui/blocktile"+str(blockarray[0])+".png")
				newtile.set_self_modulate(Playervariables.typecolorarray[blockarray[1]])
#		if debuffvectorarray.size() == 0:
		var newtile = TextureRect.new()
		$vbox/damageprocess.add_child(newtile)
		newtile.texture = load("res://Assets/ui/debufficons/safem.png")
	else:
		for debuffvector in debuffvectorarray:
#			debuffdamage = clamp(debuffdamage+(debuffvector.y-0.5),0,10)
			for _debuffcount in range(debuffvector.y):
				var newtile = TextureRect.new()
				$vbox/damageprocess.add_child(newtile)
				newtile.texture = load("res://Assets/ui/debufficons/"+str(debuffvector.x)+"m.png")#load("res://Assets/ui/debufficons/"+str(debuffvector.x)+"m.png")
