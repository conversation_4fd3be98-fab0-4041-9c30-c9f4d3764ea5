[gd_scene load_steps=6 format=2]

[ext_resource path="res://Assets/6464whitesq.png" type="Texture" id=1]
[ext_resource path="res://trash.gd" type="Script" id=2]

[sub_resource type="Gradient" id=1]
offsets = PoolRealArray( 0, 0.165138, 0.53211, 1 )
colors = PoolColorArray( 0, 0, 0, 0, 0.784314, 0.784314, 0.784314, 1, 0.666667, 0.666667, 0.823529, 1, 0.588235, 0.588235, 0.784314, 0 )

[sub_resource type="GradientTexture" id=2]
gradient = SubResource( 1 )

[sub_resource type="ParticlesMaterial" id=3]
flag_disable_z = true
direction = Vector3( 0, -1, 0 )
spread = 110.0
gravity = Vector3( 0, 200, 0 )
initial_velocity = 250.0
angular_velocity = 200.0
angular_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale = 0.4
color_ramp = SubResource( 2 )

[node name="trash" type="Particles2D"]
z_index = 10
emitting = false
amount = 1
lifetime = 2.0
one_shot = true
process_material = SubResource( 3 )
texture = ExtResource( 1 )
script = ExtResource( 2 )

[node name="removetimer" type="Timer" parent="."]

[connection signal="timeout" from="removetimer" to="." method="_on_removetimer_timeout"]
