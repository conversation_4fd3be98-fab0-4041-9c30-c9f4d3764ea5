[gd_scene load_steps=4 format=2]

[ext_resource path="res://Assets/endpoint.png" type="Texture" id=1]
[ext_resource path="res://font/Audiowide-Regular.ttf" type="DynamicFontData" id=2]

[sub_resource type="DynamicFont" id=1]
size = 24
font_data = ExtResource( 2 )

[node name="Node2D" type="Sprite"]
z_index = 9
texture = ExtResource( 1 )

[node name="Label" type="Label" parent="."]
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = -64.0
margin_top = -64.0
margin_right = -64.0
margin_bottom = -64.0
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 1, 1, 1, 1 )
custom_fonts/font = SubResource( 1 )
align = 1
valign = 1

[node name="HBoxContainer" type="HBoxContainer" parent="."]
anchor_left = 0.5
anchor_right = 0.5
margin_left = -64.0
margin_top = -77.0
margin_right = -64.0
margin_bottom = -67.0
grow_horizontal = 2
grow_vertical = 0
