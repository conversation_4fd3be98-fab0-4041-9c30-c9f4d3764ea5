[gd_scene load_steps=10 format=2]

[ext_resource path="res://effects/leaves.gd" type="Script" id=1]
[ext_resource path="res://effects/treeleafalt.png" type="Texture" id=2]

[sub_resource type="Gradient" id=1]
offsets = PoolRealArray( 0, 0.358696, 0.717391, 1 )
colors = PoolColorArray( 1, 1, 1, 1, 0.784314, 0.784314, 0.784314, 1, 1, 1, 1, 1, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=2]
gradient = SubResource( 1 )

[sub_resource type="Curve" id=6]
max_value = 100.0
_data = [ Vector2( 0, 1 ), 0.0, 0.0, 0, 0, Vector2( 0.356522, 7.1591 ), 0.0, 0.0, 0, 0, Vector2( 1, 100 ), 0.0, 0.0, 0, 0 ]

[sub_resource type="CurveTexture" id=7]
curve = SubResource( 6 )

[sub_resource type="Curve" id=3]
bake_resolution = 40
_data = [ Vector2( 0, 0.878409 ), 0.0, 0.0, 0, 0, Vector2( 0.243478, 1 ), 0.0, 0.0, 0, 0, Vector2( 1, 0.742045 ), 0.0, 0.0, 0, 0 ]

[sub_resource type="CurveTexture" id=4]
curve = SubResource( 3 )

[sub_resource type="ParticlesMaterial" id=5]
emission_shape = 1
emission_sphere_radius = 30.0
flag_disable_z = true
direction = Vector3( 0, 1, 0 )
gravity = Vector3( 0, 0, 0 )
initial_velocity = 60.0
initial_velocity_random = 0.6
orbit_velocity = 0.0
orbit_velocity_random = 0.0
damping_curve = SubResource( 7 )
angle = 360.0
angle_random = 1.0
scale_curve = SubResource( 4 )
color_ramp = SubResource( 2 )

[node name="leaves" type="Particles2D"]
use_parent_material = true
z_index = 30
emitting = false
amount = 4
lifetime = 2.0
one_shot = true
explosiveness = 0.9
process_material = SubResource( 5 )
texture = ExtResource( 2 )
script = ExtResource( 1 )

[node name="Timer" type="Timer" parent="."]
wait_time = 2.0
autostart = true

[connection signal="timeout" from="Timer" to="." method="_on_Timer_timeout"]
