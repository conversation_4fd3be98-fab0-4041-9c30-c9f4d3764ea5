[gd_scene load_steps=2 format=2]

[ext_resource path="res://soundrepeater.gd" type="Script" id=1]

[node name="soundrepeater" type="Node2D"]
script = ExtResource( 1 )

[node name="SFX1" type="AudioStreamPlayer2D" parent="."]

[node name="SFX2" type="AudioStreamPlayer2D" parent="."]

[node name="SFX3" type="AudioStreamPlayer2D" parent="."]

[node name="SFX4" type="AudioStreamPlayer2D" parent="."]

[node name="SFX5" type="AudioStreamPlayer2D" parent="."]

[node name="SFX6" type="AudioStreamPlayer2D" parent="."]

[node name="SFX7" type="AudioStreamPlayer2D" parent="."]

[node name="Timer" type="Timer" parent="."]
wait_time = 0.35
one_shot = true

[connection signal="timeout" from="Timer" to="." method="on_Timer_timeout"]
