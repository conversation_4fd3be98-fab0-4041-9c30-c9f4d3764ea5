[gd_resource type="Animation" format=2]

[resource]
resource_name = "attackeffect"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5, 1.5, 2 ),
"transitions": PoolRealArray( 0.7, 3, 1, 2 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0.470588 ), Color( 0, 0, 0, 0.196078 ), Color( 0, 0, 0, 0 ) ]
}
