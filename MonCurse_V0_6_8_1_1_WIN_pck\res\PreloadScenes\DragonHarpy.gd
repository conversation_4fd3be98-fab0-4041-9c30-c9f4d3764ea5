extends Node

const dragonharpysprites = preload("res://Enemy/dragonharpy/dragonharpyframes.tres") #DragonHarpy
const elitedragonharpysprites = preload("res://Enemy/dragonharpy/elitedragonharpyframes.tres") #DragonHarpy
const elitedragonharpyspritestail = preload("res://Enemy/dragonharpy/elitedragonharpyframestail.tres") #DragonHarpy
const SpriteDragonHarpy = preload("res://DialogueArt/dragonharpyart.tscn") #DragonHarpy

const HarpyEgg = preload("res://Enemy/dragonharpy/eggr.png") #DragonHarpy
const Obj4 = preload("res://obj4.tscn") #DragonHarpy

const HarpyScene = preload("res://DialogueArt/CG/harpyscene.tscn") #DragonHarpy


const SpeechDragonHarpy = preload("res://Conversations/speechbubblerectDragonHarpy.png") #DragonHarpy
const SymbolDragonHarpy = preload("res://Conversations/symbolDragonHarpy.png") #DragonHarpy
