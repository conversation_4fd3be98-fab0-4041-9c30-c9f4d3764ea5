[remap]

importer="texture"
type="StreamTexture"
path.s3tc="res://.import/specwolf rules unbirth shadow.png-e9ebfea3a21ba77b715688690727f0d4.s3tc.stex"
path.etc2="res://.import/specwolf rules unbirth shadow.png-e9ebfea3a21ba77b715688690727f0d4.etc2.stex"
metadata={
"imported_formats": [ "s3tc", "etc2" ],
"vram_texture": true
}

[deps]

source_file="res://DialogueArt/CG/gameovers/specwolf/specwolf rules unbirth shadow.png"
dest_files=[ "res://.import/specwolf rules unbirth shadow.png-e9ebfea3a21ba77b715688690727f0d4.s3tc.stex", "res://.import/specwolf rules unbirth shadow.png-e9ebfea3a21ba77b715688690727f0d4.etc2.stex" ]

[params]

compress/mode=2
compress/lossy_quality=0.7
compress/hdr_mode=0
compress/bptc_ldr=0
compress/normal_map=0
flags/repeat=0
flags/filter=true
flags/mipmaps=false
flags/anisotropic=false
flags/srgb=2
process/fix_alpha_border=true
process/premult_alpha=false
process/HDR_as_SRGB=false
process/invert_color=false
process/normal_map_invert_y=false
stream=false
size_limit=0
detect_3d=true
svg/scale=1.0
