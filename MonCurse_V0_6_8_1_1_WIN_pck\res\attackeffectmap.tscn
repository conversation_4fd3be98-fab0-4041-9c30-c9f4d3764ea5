[gd_scene load_steps=13 format=2]

[ext_resource path="res://Tilemaps/purev2.png" type="Texture" id=1]
[ext_resource path="res://attackeffect.tres" type="Animation" id=2]
[ext_resource path="res://attackeffectmap.gd" type="Script" id=3]
[ext_resource path="res://Tilemaps/forcev2.png" type="Texture" id=4]
[ext_resource path="res://Tilemaps/precisionv2.png" type="Texture" id=5]
[ext_resource path="res://Tilemaps/spiritv2.png" type="Texture" id=6]
[ext_resource path="res://Tilemaps/sweetv2.png" type="Texture" id=7]
[ext_resource path="res://Tilemaps/bitterv2.png" type="Texture" id=8]
[ext_resource path="res://Tilemaps/energyv2png.png" type="Texture" id=9]

[sub_resource type="TileSet" id=1]
0/name = "purev2.png 0"
0/texture = ExtResource( 1 )
0/tex_offset = Vector2( 0, 0 )
0/modulate = Color( 1, 1, 1, 1 )
0/region = Rect2( 0, 0, 128, 128 )
0/tile_mode = 0
0/occluder_offset = Vector2( 0, 0 )
0/navigation_offset = Vector2( 0, 0 )
0/shape_offset = Vector2( 0, 0 )
0/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
0/shape_one_way = false
0/shape_one_way_margin = 0.0
0/shapes = [  ]
0/z_index = 0
1/name = "physicalv2.png 1"
1/texture = ExtResource( 4 )
1/tex_offset = Vector2( 0, 0 )
1/modulate = Color( 1, 1, 1, 1 )
1/region = Rect2( 0, 0, 128, 128 )
1/tile_mode = 0
1/occluder_offset = Vector2( 0, 0 )
1/navigation_offset = Vector2( 0, 0 )
1/shape_offset = Vector2( 0, 0 )
1/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
1/shape_one_way = false
1/shape_one_way_margin = 0.0
1/shapes = [  ]
1/z_index = 0
2/name = "rangedv2.png 2"
2/texture = ExtResource( 5 )
2/tex_offset = Vector2( 0, 0 )
2/modulate = Color( 1, 1, 1, 1 )
2/region = Rect2( 0, 0, 128, 128 )
2/tile_mode = 0
2/occluder_offset = Vector2( 0, 0 )
2/navigation_offset = Vector2( 0, 0 )
2/shape_offset = Vector2( 0, 0 )
2/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
2/shape_one_way = false
2/shape_one_way_margin = 0.0
2/shapes = [  ]
2/z_index = 0
3/name = "spiritualv2.png 3"
3/texture = ExtResource( 6 )
3/tex_offset = Vector2( 0, 0 )
3/modulate = Color( 1, 1, 1, 1 )
3/region = Rect2( 0, 0, 128, 128 )
3/tile_mode = 0
3/occluder_offset = Vector2( 0, 0 )
3/navigation_offset = Vector2( 0, 0 )
3/shape_offset = Vector2( 0, 0 )
3/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
3/shape_one_way = false
3/shape_one_way_margin = 0.0
3/shapes = [  ]
3/z_index = 0
4/name = "sweetv2.png 4"
4/texture = ExtResource( 7 )
4/tex_offset = Vector2( 0, 0 )
4/modulate = Color( 1, 1, 1, 1 )
4/region = Rect2( 0, 0, 128, 128 )
4/tile_mode = 0
4/occluder_offset = Vector2( 0, 0 )
4/navigation_offset = Vector2( 0, 0 )
4/shape_offset = Vector2( 0, 0 )
4/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
4/shape_one_way = false
4/shape_one_way_margin = 0.0
4/shapes = [  ]
4/z_index = 0
5/name = "bitterv2.png 5"
5/texture = ExtResource( 8 )
5/tex_offset = Vector2( 0, 0 )
5/modulate = Color( 1, 1, 1, 1 )
5/region = Rect2( 0, 0, 128, 128 )
5/tile_mode = 0
5/occluder_offset = Vector2( 0, 0 )
5/navigation_offset = Vector2( 0, 0 )
5/shape_offset = Vector2( 0, 0 )
5/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
5/shape_one_way = false
5/shape_one_way_margin = 0.0
5/shapes = [  ]
5/z_index = 0
6/name = "energyv2png.png 6"
6/texture = ExtResource( 9 )
6/tex_offset = Vector2( 0, 0 )
6/modulate = Color( 1, 1, 1, 1 )
6/region = Rect2( 0, 0, 128, 128 )
6/tile_mode = 0
6/occluder_offset = Vector2( 0, 0 )
6/navigation_offset = Vector2( 0, 0 )
6/shape_offset = Vector2( 0, 0 )
6/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
6/shape_one_way = false
6/shape_one_way_margin = 0.0
6/shapes = [  ]
6/z_index = 0

[sub_resource type="Animation" id=2]
resource_name = "attackeffectmemory"
length = 4.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 4 ),
"transitions": PoolRealArray( 1, 2 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.588235 ), Color( 0, 0, 0, 0 ) ]
}

[sub_resource type="Animation" id=3]
resource_name = "attackeffectweak"
length = 1.8
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5, 1.3, 1.8 ),
"transitions": PoolRealArray( 0.7, 3, 1, 2 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.784314 ), Color( 1, 1, 1, 0.392157 ), Color( 0, 0, 0, 0.156863 ), Color( 0, 0, 0, 0 ) ]
}

[node name="attackeffectmap" type="TileMap"]
tile_set = SubResource( 1 )
cell_size = Vector2( 128, 128 )
format = 1
script = ExtResource( 3 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
anims/attackeffect = ExtResource( 2 )
anims/attackeffectmemory = SubResource( 2 )
anims/attackeffectweak = SubResource( 3 )

[connection signal="animation_finished" from="AnimationPlayer" to="." method="_on_AnimationPlayer_animation_finished"]
