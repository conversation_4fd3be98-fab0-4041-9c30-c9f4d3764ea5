[gd_scene load_steps=13 format=2]

[ext_resource path="res://Assets/torchthrowv2.png" type="Texture" id=1]
[ext_resource path="res://obj1torch.gd" type="Script" id=2]
[ext_resource path="res://Assets/torchsprites.png" type="Texture" id=3]
[ext_resource path="res://LightNode.tscn" type="PackedScene" id=4]

[sub_resource type="AtlasTexture" id=1]
atlas = ExtResource( 3 )
region = Rect2( 128, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=2]
atlas = ExtResource( 3 )
region = Rect2( 0, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=3]
atlas = ExtResource( 3 )
region = Rect2( 128, 128, 128, 128 )

[sub_resource type="SpriteFrames" id=4]
animations = [ {
"frames": [ SubResource( 1 ), SubResource( 2 ), SubResource( 3 ) ],
"loop": true,
"name": "default",
"speed": 2.0
} ]

[sub_resource type="Animation" id=8]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath(".:z_index")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=7]
resource_name = "burn"
length = 0.7
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.7 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 4, 1.5, 1.5, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:z_index")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.6 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ 0, 6, 0 ]
}

[sub_resource type="Animation" id=6]
resource_name = "cum"
length = 0.7
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.7 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 3, 3, 3, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:z_index")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.6 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ 0, 6, 0 ]
}

[sub_resource type="Animation" id=5]
resource_name = "water"
length = 0.7
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.7 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.7, 0.7, 4, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:z_index")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.6 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ 0, 6, 0 ]
}

[node name="torch" type="Sprite"]
texture = ExtResource( 1 )
offset = Vector2( 0, 30 )
script = ExtResource( 2 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
position = Vector2( 30, 6 )
frames = SubResource( 4 )
frame = 2

[node name="LightNode" parent="AnimatedSprite" instance=ExtResource( 4 )]

[node name="flash" type="AnimationPlayer" parent="."]
anims/RESET = SubResource( 8 )
anims/burn = SubResource( 7 )
anims/cum = SubResource( 6 )
anims/water = SubResource( 5 )
