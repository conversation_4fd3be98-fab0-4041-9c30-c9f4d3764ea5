[gd_scene load_steps=3 format=2]

[ext_resource path="res://Assets/selectorpathwhite.png" type="Texture" id=1]

[sub_resource type="Animation" id=2]
resource_name = "wiggle"
length = 4.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:offset")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1, 1.3, 1.5, 1.8, 2.1, 2.5, 4 ),
"transitions": PoolRealArray( 1, 1.4, 0.8, 1, 2, 0.8, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0, 0 ), Vector2( 0, -10 ), Vector2( 0, 7 ), Vector2( 0, -5 ), Vector2( 0, 3 ), Vector2( 0, 0 ), Vector2( 0, 0 ) ]
}

[node name="Node2D" type="Sprite"]
texture = ExtResource( 1 )

[node name="wiggle" type="AnimationPlayer" parent="."]
anims/wiggle = SubResource( 2 )
