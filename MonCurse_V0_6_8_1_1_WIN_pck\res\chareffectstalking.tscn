[gd_scene load_steps=2 format=2]

[sub_resource type="Animation" id=1]
resource_name = "talking2"
length = 0.4
tracks/0/type = "value"
tracks/0/path = NodePath("..:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.4 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 0.54902, 0.54902, 0.54902, 1 ), Color( 1, 1, 1, 1 ) ]
}

[node name="talking" type="AnimationPlayer"]
anims/talking2 = SubResource( 1 )
