[gd_scene load_steps=12 format=2]

[ext_resource path="res://touchoverlay.gd" type="Script" id=1]
[ext_resource path="res://Assets/ui/touchcamera.png" type="Texture" id=2]
[ext_resource path="res://Assets/ui/circlemask.png" type="BitMap" id=3]
[ext_resource path="res://Assets/ui/touchzoomin.png" type="Texture" id=4]
[ext_resource path="res://Assets/ui/touchzoomout.png" type="Texture" id=5]
[ext_resource path="res://Assets/dialogueblacksquare.png" type="Texture" id=6]
[ext_resource path="res://Assets/ui/cameraborder.png" type="Texture" id=7]

[sub_resource type="Animation" id=21]
resource_name = "flash"
length = 2.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5, 1, 1.5, 2 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.901961 ), Color( 0.313726, 0.313726, 0.313726, 0.470588 ), Color( 1, 1, 1, 0.784314 ), Color( 0.705882, 0.705882, 0.705882, 0.27451 ), Color( 1, 1, 1, 0.901961 ) ]
}

[sub_resource type="Animation" id=22]
resource_name = "flashicon"
length = 4.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:anchor_bottom")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = false
tracks/0/keys = {
"times": PoolRealArray( 0, 4 ),
"transitions": PoolRealArray( 5, 1 ),
"update": 0,
"values": [ 0.2, -0.1 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:anchor_top")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = false
tracks/1/keys = {
"times": PoolRealArray( 0, 4 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ 0.0, -0.1 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 2, 4 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0.294118 ), Color( 1, 1, 1, 1 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("toggle:self_modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=1]
resource_name = "hide"
tracks/0/type = "value"
tracks/0/path = NodePath(".:anchor_bottom")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = false
tracks/0/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 5, 1 ),
"update": 0,
"values": [ 0.2, -0.1 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:anchor_top")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = false
tracks/1/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ 0.0, -0.1 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0.294118 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("toggle:self_modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ Color( 1, 1, 1, 0.705882 ) ]
}

[sub_resource type="Animation" id=2]
resource_name = "show"
tracks/0/type = "value"
tracks/0/path = NodePath(".:anchor_bottom")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = false
tracks/0/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 5, 1 ),
"update": 0,
"values": [ 0.2, -0.1 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:anchor_top")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = false
tracks/1/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ 0.0, -0.1 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0.294118 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("toggle:self_modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ Color( 1, 1, 1, 0.705882 ) ]
}

[node name="touchoverlay" type="CanvasLayer"]
layer = 11
script = ExtResource( 1 )

[node name="cameramode" type="TextureRect" parent="."]
visible = false
self_modulate = Color( 0.882353, 0.882353, 0.882353, 0.580392 )
anchor_left = 0.03
anchor_top = 0.06
anchor_right = 0.97
anchor_bottom = 0.94
mouse_filter = 2
texture = ExtResource( 7 )
expand = true

[node name="camera" type="AnimationPlayer" parent="cameramode"]
playback_speed = 0.25
anims/flash = SubResource( 21 )

[node name="Control" type="Control" parent="."]
anchor_right = 1.0
anchor_bottom = 0.2
mouse_filter = 2

[node name="backer" type="TextureRect" parent="Control"]
visible = false
self_modulate = Color( 1, 1, 1, 0.156863 )
anchor_right = 1.0
anchor_bottom = 0.7
mouse_filter = 2
texture = ExtResource( 6 )
expand = true
flip_v = true

[node name="toggle" type="TextureButton" parent="Control"]
self_modulate = Color( 1, 1, 1, 0.705882 )
anchor_right = 0.2
anchor_bottom = 1.0
mouse_filter = 1
action_mode = 0
texture_normal = ExtResource( 2 )
texture_click_mask = ExtResource( 3 )
expand = true
stretch_mode = 5

[node name="zoomout" type="TextureButton" parent="Control"]
visible = false
self_modulate = Color( 1, 1, 1, 0.705882 )
anchor_left = 0.8
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 0
mouse_filter = 2
action_mode = 0
texture_normal = ExtResource( 5 )
texture_click_mask = ExtResource( 3 )
expand = true
stretch_mode = 5
flip_h = true

[node name="zoomin" type="TextureButton" parent="Control"]
visible = false
self_modulate = Color( 1, 1, 1, 0.705882 )
anchor_left = 0.6
anchor_right = 0.8
anchor_bottom = 1.0
grow_horizontal = 0
mouse_filter = 2
action_mode = 0
texture_normal = ExtResource( 4 )
texture_click_mask = ExtResource( 3 )
expand = true
stretch_mode = 5
flip_h = true

[node name="tempshow" type="AnimationPlayer" parent="Control"]
playback_speed = 4.0
anims/flashicon = SubResource( 22 )
anims/hide = SubResource( 1 )
anims/show = SubResource( 2 )

[node name="showtimer" type="Timer" parent="Control"]
one_shot = true

[connection signal="pressed" from="Control/toggle" to="." method="_on_toggle_pressed"]
[connection signal="pressed" from="Control/zoomout" to="." method="_on_zoomout_pressed"]
[connection signal="pressed" from="Control/zoomin" to="." method="_on_zoomin_pressed"]
[connection signal="animation_finished" from="Control/tempshow" to="." method="_on_tempshow_animation_finished"]
[connection signal="timeout" from="Control/showtimer" to="." method="_on_showtimer_timeout"]
