extends Node2D


# Declare member variables here. Examples:
# var a = 2
# var b = "text"


# Called when the node enters the scene tree for the first time.
#enum objecttype{ABILITY = 0}
func projectify(startlocv,endlocv,movedict,zoom,slot,fast):#,_type = objecttype.ABILITY):
#	var movedict = Playervariables.get("Move"+str(movenum))
	var rarity = movedict["types"][5]
	$Light2D.set_self_modulate(Playervariables.raritycolorsarray[rarity])
	$projectile.playback_speed = 1 - (0.1*rarity)
	$Sprite2/Sprite.set_texture(load("res://Assets/abilityicons/"+movedict["name"]+".png"))
#	if movedict["slot"] == Playervariables.CONSUMABLE:
#		$Sprite2.set_texture(load("res://Assets/ui/deployablev2.png"))
	var newanim# = $projectile.get_animation("pickup").duplicate()
	if fast == false:
		newanim = $projectile.get_animation("pickup").duplicate()
	else:
		newanim = $projectile.get_animation("pickuppushfront").duplicate()
	if slot == Playervariables.CURSE:# != 0:
#		newanim.track_set_key_value(1,0,0)
#		newanim.track_set_key_value(1,1,0)
#		newanim.track_set_key_value(1,2,0)
		$Sprite2.set_self_modulate(Color(0.5,0.5,1))
		zoom = zoom*0.5
	newanim.track_set_key_value(0,0,startlocv)
	newanim.track_set_key_value(0,1,startlocv-(endlocv*0.15))
	newanim.track_set_key_value(0,2,endlocv)
	newanim.track_set_key_value(5,0,newanim.track_get_key_value(5,0)*zoom)
	newanim.track_set_key_value(5,1,newanim.track_get_key_value(5,1)*zoom)
	newanim.track_set_key_value(5,2,newanim.track_get_key_value(5,2)*zoom)
	$projectile.add_animation("fly",newanim)
	$projectile.play("fly")


func _on_projectile_animation_finished(_anim_name):
	queue_free()
