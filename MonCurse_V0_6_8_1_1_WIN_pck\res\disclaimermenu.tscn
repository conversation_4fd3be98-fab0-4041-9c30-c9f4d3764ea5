[gd_scene load_steps=22 format=2]

[ext_resource path="res://button.gd" type="Script" id=1]
[ext_resource path="res://disclaimermenu.gd" type="Script" id=2]
[ext_resource path="res://Assets/blacksquare.png" type="Texture" id=3]
[ext_resource path="res://font/Verily-Serif-Mono/VerilySerifMono.otf" type="DynamicFontData" id=4]
[ext_resource path="res://font/OpenSans-ExtraBold.ttf" type="DynamicFontData" id=5]
[ext_resource path="res://Assets/endpointbluebig.png" type="Texture" id=6]
[ext_resource path="res://Assets/endpointbig.png" type="Texture" id=7]
[ext_resource path="res://TL_BUTTON.tscn" type="PackedScene" id=8]
[ext_resource path="res://Assets/endpointblueplay.png" type="Texture" id=9]
[ext_resource path="res://Assets/endpointplay.png" type="Texture" id=10]
[ext_resource path="res://languagemenu.gd" type="Script" id=11]
[ext_resource path="res://Assets/ui/settingcog.png" type="Texture" id=12]
[ext_resource path="res://TL_BUTTON.gd" type="Script" id=13]
[ext_resource path="res://font/OpenSans-SemiBoldItalic.ttf" type="DynamicFontData" id=14]
[ext_resource path="res://Assets/directionalhologram.png" type="Texture" id=15]
[ext_resource path="res://Assets/6464whitesq.png" type="Texture" id=16]

[sub_resource type="DynamicFont" id=1]
size = 17
outline_size = 3
outline_color = Color( 0.4, 0.4, 0.4, 1 )
extra_spacing_top = -3
extra_spacing_bottom = -3
font_data = ExtResource( 5 )

[sub_resource type="DynamicFont" id=2]
outline_size = 1
outline_color = Color( 0.113725, 0.121569, 0.333333, 1 )
font_data = ExtResource( 4 )

[sub_resource type="Shader" id=33]
code = "shader_type canvas_item;

uniform float speed = 0.5;
uniform sampler2D hologramTexture;

vec2 tilingAndOffset(vec2 uv, float offset) {
    return mod(uv - vec2(0.0, offset), 1);
}

void fragment() {
    float offset = float(TIME * speed / 100.0);
    vec2 tiling = tilingAndOffset(UV, offset);
    
    vec4 noise = texture(hologramTexture, tiling);
//    vec4 colorLines = linesColor * vec4(vec3(linesColorIntensity), 1.0);
    vec4 emission = 0.2 * noise;
    
    vec4 albedo = vec4(0,0,0,0);
    float alpha = dot(noise.rgb, vec3(1.0));
    vec4 hologram;
    hologram.rgb = emission.rgb + (1.0 - emission.rgb) * albedo.rgb * albedo.a;
    hologram.a = emission.a + (1.0 - emission.a) * alpha;
    hologram.a = hologram.a + (1.0 - hologram.a) * albedo.a;
    COLOR = texture(TEXTURE, UV);
    COLOR.rgb = COLOR.rgb + (1.0 - COLOR.rgb) * hologram.rgb;
    COLOR.a = min(COLOR.a, hologram.a);
}"

[sub_resource type="ShaderMaterial" id=34]
shader = SubResource( 33 )
shader_param/speed = 8.0
shader_param/hologramTexture = ExtResource( 15 )

[sub_resource type="DynamicFont" id=4]
outline_size = 2
outline_color = Color( 0.4, 0.4, 0.4, 1 )
extra_spacing_top = -3
extra_spacing_bottom = -3
font_data = ExtResource( 14 )

[node name="specialmenu" type="CanvasLayer"]
layer = 120

[node name="disclaimermenu" type="TextureRect" parent="."]
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = -5.0
margin_top = -5.0
margin_right = 5.0
margin_bottom = 5.0
mouse_filter = 0
texture = ExtResource( 3 )
expand = true
script = ExtResource( 2 )

[node name="TextureButton2" type="TextureButton" parent="disclaimermenu"]
modulate = Color( 0.784314, 1, 0.784314, 1 )
anchor_left = 0.25
anchor_top = 0.65
anchor_right = 0.4
anchor_bottom = 0.9
grow_horizontal = 2
grow_vertical = 2
texture_normal = ExtResource( 7 )
texture_hover = ExtResource( 6 )
expand = true
stretch_mode = 5
script = ExtResource( 1 )

[node name="Label3" type="Label" parent="disclaimermenu/TextureButton2"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_fonts/font = SubResource( 1 )
text = "YES, I am of 
legal age."
align = 1
valign = 1
autowrap = true

[node name="TextureButton3" type="TextureButton" parent="disclaimermenu"]
modulate = Color( 1, 0.784314, 0.784314, 1 )
anchor_left = 0.6
anchor_top = 0.65
anchor_right = 0.75
anchor_bottom = 0.9
grow_horizontal = 2
grow_vertical = 2
texture_normal = ExtResource( 7 )
texture_hover = ExtResource( 6 )
expand = true
stretch_mode = 5
script = ExtResource( 1 )

[node name="Label3" type="Label" parent="disclaimermenu/TextureButton3"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_fonts/font = SubResource( 1 )
text = "NO, get me 
out of here!"
align = 1
valign = 1
autowrap = true

[node name="Label" type="Label" parent="disclaimermenu"]
anchor_left = 0.1
anchor_top = 0.1
anchor_right = 0.9
anchor_bottom = 0.55
grow_horizontal = 2
grow_vertical = 2
custom_fonts/font = SubResource( 2 )
text = "The following game contains sexually explicit themes and content. Please ensure you are of legal age to access such content.

This game is in active development and may change over time. Characters shown are of legal age and legal consent.

This message will only show once."
align = 1
valign = 1
autowrap = true

[node name="languagemenu" type="TextureRect" parent="."]
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 0
texture = ExtResource( 3 )
expand = true
script = ExtResource( 11 )

[node name="TextureButton4" type="TextureButton" parent="languagemenu"]
modulate = Color( 0.784314, 1, 0.784314, 1 )
anchor_left = 0.12
anchor_top = 0.6
anchor_right = 0.47
anchor_bottom = 0.85
margin_left = -2.5
margin_top = 1.5
margin_right = -2.5
margin_bottom = 1.5
grow_horizontal = 2
grow_vertical = 2
texture_normal = ExtResource( 10 )
texture_hover = ExtResource( 9 )
expand = true
stretch_mode = 5

[node name="hologram" type="TextureRect" parent="languagemenu/TextureButton4"]
self_modulate = Color( 0.282353, 0.576471, 0.705882, 0.588235 )
material = SubResource( 34 )
anchor_left = 0.47
anchor_top = -0.55
anchor_right = 0.53
anchor_bottom = 0.02
grow_horizontal = 2
grow_vertical = 0
texture = ExtResource( 16 )
expand = true

[node name="TL_BUTTON" parent="languagemenu" instance=ExtResource( 8 )]
anchor_left = 0.12
anchor_top = 0.18
anchor_right = 0.47
anchor_bottom = 0.47
margin_right = 0.0
margin_bottom = 0.0

[node name="Label2" type="Label" parent="languagemenu"]
visible = false
anchor_left = 0.12
anchor_top = 0.47
anchor_right = 0.47
anchor_bottom = 0.6
grow_horizontal = 2
grow_vertical = 2
custom_fonts/font = SubResource( 2 )
text = "SELECT LANGUAGE"
align = 1
valign = 1
autowrap = true

[node name="ScrollContainer" type="ScrollContainer" parent="languagemenu"]
anchor_left = 0.6
anchor_top = 0.1
anchor_right = 0.91
anchor_bottom = 0.9
margin_right = 12.0
margin_bottom = 12.0

[node name="VBoxContainer" type="VBoxContainer" parent="languagemenu/ScrollContainer"]
margin_right = 307.0
margin_bottom = 150.0

[node name="TL_BUTTON" parent="languagemenu/ScrollContainer/VBoxContainer" instance=ExtResource( 8 )]
margin_right = 307.2
margin_bottom = 150.0

[node name="tools" type="TextureButton" parent="languagemenu"]
modulate = Color( 0.568627, 0.568627, 0.941176, 0.862745 )
anchor_left = 0.02
anchor_top = 0.02
anchor_right = 0.12
anchor_bottom = 0.15
texture_normal = ExtResource( 12 )
expand = true
stretch_mode = 5
script = ExtResource( 13 )

[node name="tooltext" type="Label" parent="languagemenu/tools"]
self_modulate = Color( 0.737255, 0.670588, 0.584314, 1 )
anchor_right = 1.0
anchor_bottom = 1.0
grow_vertical = 2
custom_fonts/font = SubResource( 4 )
text = "Translation Tools"
align = 1
valign = 1
autowrap = true

[node name="errorlog" type="RichTextLabel" parent="languagemenu"]
visible = false
anchor_left = 0.405
anchor_top = 0.02
anchor_right = 0.595
anchor_bottom = 0.64
grow_horizontal = 2
rect_clip_content = false
mouse_filter = 2
custom_colors/default_color = Color( 1, 0.843137, 0.505882, 1 )
text = "ERROR LOG
ERROR: THIS SHOULDN'T BE SHOWING THERE ARE NO ERRORS
I FORGOT TO MAKE IT INVISIBLE, I'M DUMB
- The Developer"
scroll_active = false

[node name="TextureRect" type="TextureRect" parent="languagemenu/errorlog"]
self_modulate = Color( 1, 1, 1, 0.666667 )
show_behind_parent = true
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource( 3 )
expand = true

[connection signal="pressed" from="disclaimermenu/TextureButton2" to="disclaimermenu" method="_on_TextureButton2_pressed"]
[connection signal="pressed" from="disclaimermenu/TextureButton3" to="disclaimermenu" method="_on_TextureButton3_pressed"]
[connection signal="pressed" from="languagemenu/TextureButton4" to="languagemenu" method="_on_TextureButton4_pressed"]
[connection signal="mouse_entered" from="languagemenu/tools" to="languagemenu/tools" method="_on_TL_BUTTON_mouse_entered"]
[connection signal="mouse_exited" from="languagemenu/tools" to="languagemenu/tools" method="_on_TL_BUTTON_mouse_exited"]
[connection signal="pressed" from="languagemenu/tools" to="languagemenu" method="_on_tools_pressed"]
