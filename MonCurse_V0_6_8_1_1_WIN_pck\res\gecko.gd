extends AnimatedSprite

func _ready():
	set_physics_process(false)
	add_to_group("Creatures")

var start_pos_flips = []
var possible_start_positions = []
var start_position = Vector2(200,200)
var state = NULL
enum{NULL,ENTER,EXIT}
func play_anim():
	if $AnimationPlayer.is_playing() == false:
		var newanim
		var flip_mult = 1
		if flip_h == true:
			flip_mult = -1
		if state == NULL:
			newanim = $AnimationPlayer.get_animation("round_trunk_enter").duplicate()
			state = ENTER
			$leave_timer.start(4)
		else:
			newanim = $AnimationPlayer.get_animation("round_trunk_exit").duplicate()
			state = EXIT
			$leave_timer.stop()
		$AnimationPlayer.add_animation("newround", newanim)
		for i in newanim.track_get_key_count(0):
			var value = newanim.track_get_key_value(0,i)
			value.x = value.x*flip_mult
			newanim.track_set_key_value(0,i,value+start_position)
		if flip_mult == -1:
			for i in newanim.track_get_key_count(2):
				var value = newanim.track_get_key_value(2,i)
				value = value*flip_mult
				newanim.track_set_key_value(2,i,value)
			for i in newanim.track_get_key_count(4):
				var value = newanim.track_get_key_value(4,i)
				value.x = value.x*flip_mult
				newanim.track_set_key_value(4,i,value)
		$AnimationPlayer.play("newround")
		last_pos = start_position
		set_physics_process(true)

func _input(event):
	if event.is_action_pressed("debugequals"):
		play_anim()

var borderthickness = 0
const tilesize = 128
var mapsize = Vector2(0,0)
var use_tilemap
var current_tile = Vector2(0,0)
var destination_locv = Vector2(0,0)
func roaming(mainscene,locv):
	roamer = true
	use_tilemap = mainscene.map2darray
	borderthickness = mainscene.borderthickness
	mapsize = mainscene.mapsize
	position = locv
	last_pos = position
	destination_locv = position
	rotation_degrees = randi()%360
	last_rotation = rotation_degrees
	current_tile = (locv/tilesize)-Vector2(0.5,0.5)
	fade_check()
	$leave_timer.start(5+randf()*4)

var fade_state = false
func fade_check():
	for x in range(3):
		for y in range(3):
			if use_tilemap[current_tile.y+(y-1)][current_tile.x+(x-1)] == 0:
				if fade_state == true:
					fade_state = false
					$AnimationPlayer.play("wandering_light")
				return
	if fade_state == false:
		fade_state = true
		$AnimationPlayer.play("wandering_dark")

const direction_array = PoolVector2Array([Vector2(1,0),Vector2(1,1),Vector2(0,1),Vector2(-1,1),Vector2(-1,0),Vector2(-1,-1),Vector2(0,-1),Vector2(1,-1)])
func get_new_target():
	var direction_vector = direction_array[randi()%8]
	var targetloc = current_tile + direction_vector
	if use_tilemap[targetloc.y][targetloc.x] == 0:
		return
	else:
		var direction_vector2 = direction_array[randi()%8]
		var targetloc2 = targetloc + direction_vector2
		if use_tilemap[targetloc2.y][targetloc2.x] > 0:
			targetloc = targetloc2
			if targetloc == current_tile:
				speed = 0.5+randf()
			else:
				speed = 3+randf()
		else:
			speed = 2+randf()
	if targetloc.x > mapsize.x-borderthickness or targetloc.x < borderthickness or targetloc.y > mapsize.y-borderthickness or targetloc.y < borderthickness:
		return
	current_tile = targetloc
	destination_locv = (current_tile+0.5*(Vector2(randf(),randf())+Vector2(0.5,0.5)))*tilesize
	set_physics_process(true)
	fade_check()

var speed = 3
var roamer = false
var speed_tally = 0
var last_rotation = 0
var last_pos = Vector2(0,0)
func _physics_process(_delta):
	if roamer == true:
		var direction = (destination_locv - position).normalized()
		rotation = direction.angle() + (1.57079632679)
		position += direction*speed
		if (destination_locv - position).length() < 10:
			set_physics_process(false)
#			$leave_timer.start(randf()*randf()*36)
	speed_tally += (position - last_pos).length()*10 + abs(last_rotation-rotation_degrees)*5
	last_rotation = rotation_degrees
	last_pos = position
	if speed_tally > 50:
		force_frame()


func _on_AnimationPlayer_animation_finished(_anim_name):
	set_physics_process(false)
	if state == EXIT:
		state = NULL
		new_start()

func new_start():
	if possible_start_positions.size() == 0:
		queue_free()
	else:
		var randstart = randi()%possible_start_positions.size()
		start_position = possible_start_positions[randstart]
		if randstart == 0:
			flip_h = randf() > 0.5
		else:
			flip_h = start_pos_flips[randstart]

func force_frame():#this is what causes the gecko to sometimes flip at the half-way point. Whoops! Kinda cool though, I'll keep it.
	self.frame = (self.frame+1) % 5
	flip_h = self.frame > 2
	speed_tally = 0

func _on_leave_timer_timeout():
	if roamer == false:
		if randf() > 0.7:
			play_anim()
		else:
			if randf() > 0.6:
				force_frame()
				if randf() > 0.7:
					force_frame()
			$leave_timer.start(randf()+1)
	else:
		if randf() > 0.6:
			if (destination_locv - position).length() < 75 and use_tilemap != null:
				get_new_target()
				$leave_timer.start(1+randf()*5)
			else:
				$leave_timer.start(randf()+0.05)
		else:
			if randf() > 0.35:
				force_frame()
			$leave_timer.start(randf()*9)

func disturb(locv):
	if roamer == true:
		var distance = (locv - position).length()
		if distance < 800:
			$leave_timer.stop()
			_on_leave_timer_timeout()
	else:
		var distance = (locv - start_position).length()
		if state == NULL and distance > 700 and distance < 1300:
			play_anim()
		elif distance < (4+randi()%5)*100:
			if state == ENTER and distance < 260:
				play_anim()
			else:
				_on_leave_timer_timeout()
				$leave_timer.start(randf()+0.2)
