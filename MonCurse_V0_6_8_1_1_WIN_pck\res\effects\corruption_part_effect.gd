extends Sprite

var force_gauge = false
var saverace = 0
var savegauge = 0
func corruption_effect(partref,race,partname,partnumber):
	var usetexture = null
	var multiplier = 1.0
	saverace = race
	var spec_offset = Vector2(0,0)
	match race:
		Playervariables.raceNAMAN:
			pass
		Playervariables.raceIMP:
			if partname == "body":
				multiplier *= 1.5
				usetexture = load("res://DialogueArt/rules/debuff/rulesdebuff sweat.png")
			elif partname == "wings":
				usetexture = load("res://DialogueArt/rules/rules imp wingsback.png")
			elif partname == "tail":
				usetexture = load("res://DialogueArt/rules/rules imp tail.png")
			elif partname == "leg":
				multiplier *= 0.25
				match partnumber:
					0:usetexture = load("res://DialogueArt/rules/rules imp legright.png")
					1:usetexture = load("res://DialogueArt/rules/rules imp legleft.png")
					2:usetexture = load("res://DialogueArt/rules/rules imp bootright.png")
					3:usetexture = load("res://DialogueArt/rules/rules imp bootleft.png")
			elif partname == "backhorns":
				usetexture = load("res://DialogueArt/rules/rules imp backhorns.png")
			elif partname == "face":
				usetexture = load("res://DialogueArt/rules/rules imp face.png")
#				spec_offset = Vector2(-40,0)#should have been this but it isn't??
				spec_offset = Vector2(-35,12)
		Playervariables.raceRAM:
			if partname == "horns":
				multiplier *= 0.5
				if partnumber == 0:
					usetexture = load("res://DialogueArt/rules/rules ramgirl hornright.png")
				else:
					usetexture = load("res://DialogueArt/rules/rules ramgirl hornleft.png")
			elif partname == "eyes":
				usetexture = load("res://DialogueArt/rules/debuff/rulesdebuff eyes hypno yellow.png")
			elif partname == "top":
				multiplier *= 0.5
				if partnumber == 0:
					usetexture = load("res://DialogueArt/rules/rules clothing top under ram.png")
				else:
					usetexture = load("res://DialogueArt/rules/rules clothing top ram.png")
			elif partname == "pants":
				multiplier *= 0.5
				if partnumber == 0:
					usetexture = load("res://DialogueArt/rules/rules clothing pantsright ram.png")
				else:
					usetexture = load("res://DialogueArt/rules/rules clothing pants ram.png")
		Playervariables.raceNEKO:
			if partname == "ears":
				multiplier *= 0.5
				if partnumber == 0:
					usetexture = load("res://DialogueArt/rules/rules catgirl earright.png")
				else:
					usetexture = load("res://DialogueArt/rules/rules catgirl earleft.png")
			elif partname == "tail":
				usetexture = load("res://DialogueArt/rules/rules catgirl tail.png")
			elif partname == "armright":
				force_gauge = true
				multiplier *= 0.25
				match partnumber:
					0:usetexture = load("res://DialogueArt/rules/rules catgirl armright stub.png")
					1:usetexture = load("res://DialogueArt/rules/rules catgirl armleft stub.png")
					2:usetexture = load("res://DialogueArt/rules/rules catgirl armright.png")
					3:usetexture = load("res://DialogueArt/rules/rules catgirl armleft.png")
		Playervariables.raceWOLF:
			if partname == "ears":
				multiplier *= 0.5
				if partnumber == 0:
					usetexture = load("res://DialogueArt/rules/rules werewolf earright Rose.png")
				else:
					usetexture = load("res://DialogueArt/rules/rules werewolf earleft Rose.png")
			elif partname == "tail":
				usetexture = load("res://DialogueArt/rules/rules werewolf tail.png")
			elif partname == "top":
				multiplier *= 0.5
				if partnumber == 0:
					usetexture = load("res://DialogueArt/rules/rules clothing top under wolf.png")
				else:
					usetexture = load("res://DialogueArt/rules/rules clothing top wolf large.png")
			elif partname == "leg":
				multiplier *= 0.25
				match partnumber:
					0:usetexture = load("res://DialogueArt/rules/rules werewolf legright stub.png")
					1:usetexture = load("res://DialogueArt/rules/rules werewolf legleft stub.png")
					2:usetexture = load("res://DialogueArt/rules/rules werewolf legright.png")
					3:usetexture = load("res://DialogueArt/rules/rules werewolf legleft.png")
		Playervariables.raceKITSUNE:
			if partname == "ears":
				multiplier *= 0.5
				if partnumber == 0:
					usetexture = load("res://DialogueArt/rules/rules foxgirl earright Rose.png")
				else:
					usetexture = load("res://DialogueArt/rules/rules foxgirl earleft Rose.png")
			elif partname == "tail":
				usetexture = load("res://DialogueArt/rules/rules foxgirl tail Rose.png")
			elif partname == "hair":
				force_gauge = true
				multiplier *= 0.5
				match partnumber:
					0: usetexture = load("res://DialogueArt/rules/rules default hairback fox.png")
					1: usetexture = load("res://DialogueArt/rules/rules default hairbangsright fox.png")
					2: usetexture = load("res://DialogueArt/rules/rules default hairbangsleft fox.png")
					3: usetexture = load("res://DialogueArt/rules/rules default hairfront fox.png")
		Playervariables.raceHARPY:
			if partname == "armright":
				multiplier *= 0.25
				match partnumber:
					0:
						usetexture = load("res://DialogueArt/rules/rules harpy armright.png")
						spec_offset = Vector2(0,82)
					1:
						usetexture = load("res://DialogueArt/rules/rules harpy armleft.png")
#						spec_offset = Vector2(-82,0)
					2:
						usetexture = load("res://DialogueArt/rules/rules harpy gloveright.png")
						spec_offset = Vector2(0,82)
					3:
						usetexture = load("res://DialogueArt/rules/rules harpy gloveleft.png")
						spec_offset = Vector2(-82,0)
			elif partname == "leg":
				multiplier *= 0.25
				spec_offset = Vector2(5,20)
				match partnumber:
					0:usetexture = load("res://DialogueArt/rules/rules harpy legright.png")
					1:usetexture = load("res://DialogueArt/rules/rules harpy legleft.png")
					2:usetexture = load("res://DialogueArt/rules/rules harpy bootright.png")
					3:
						usetexture = load("res://DialogueArt/rules/rules harpy bootleft.png")
						spec_offset = Vector2(82,50)
			elif partname == "tail":
				usetexture = load("res://DialogueArt/rules/rules dragonharpy tail.png")
			elif partname == "backhorns":
				usetexture = load("res://DialogueArt/rules/rules imp backhorns.png")
	if usetexture == null:
		print("Couldn't find usetexture in corruption part: "+str(race)+str(partname))
		return false
	else:
		self.position = partref.offset + usetexture.get_size()*0.5 + spec_offset
		self.texture = usetexture
		$glowsprite.texture = usetexture
		var gauge = Playervariables.racecorruptionarray[race]
		savegauge = gauge
		if force_gauge == true:
			gauge = 50
		multiplier = multiplier * (7 + gauge*0.17)
		
		var race_color = Playervariables.racecolorarray[race]
		
		$Particles2D.process_material = $Particles2D.process_material.duplicate()
		$Particles2D.process_material.color_ramp = $Particles2D.process_material.color_ramp.duplicate()
		$Particles2D.process_material.color_ramp.gradient = $Particles2D.process_material.color_ramp.gradient.duplicate()
		$Particles2D.process_material.color_ramp.gradient.set_color(1,race_color)
		var darker_color = Color(race_color.r*0.6,race_color.g*0.6,race_color.b*0.6,0.70)
		$Particles2D.process_material.color_ramp.gradient.set_color(2,darker_color)
		
		$Particles2D.amount = int(multiplier)
		$Particles2D.process_material.emission_sphere_radius = min(usetexture.get_size().x,usetexture.get_size().y)
		
		
		self.material = self.material.duplicate()
		self.material.set_shader_param("source_color1",race_color*0.25)
		self.material.set_shader_param("source_color2",race_color*0.5)
		self.material.set_shader_param("source_color3",race_color*0.75)
		
		var newanim = $animation.get_animation("corruption")#.duplicate()
		newanim.track_set_key_value(0,1,race_color)
		newanim.track_set_key_value(1,1,gauge*0.01)
		var finalcolor = race_color*0.2
		finalcolor.a = gauge*0.01
		newanim.track_set_key_value(4,1,finalcolor)
		var useanim = $animation.get_animation("corruption").duplicate()
		$animation.add_animation("corr", useanim)
		$animation.play("corr")
		return true

func force_finish(race):
	if race == saverace:
		$animation.advance(3)

func _on_Timer_timeout():
	queue_free()
