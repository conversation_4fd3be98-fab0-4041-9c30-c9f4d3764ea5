[gd_scene load_steps=9 format=2]

[ext_resource path="res://Assets/blacksquare.png" type="Texture" id=1]
[ext_resource path="res://font/OpenSans-ExtraBold.ttf" type="DynamicFontData" id=2]
[ext_resource path="res://Assets/ui/monmusuinventoryui.png" type="Texture" id=3]
[ext_resource path="res://Assets/abilityicons/Lance-II.png" type="Texture" id=4]
[ext_resource path="res://font/OpenSans-Regular.ttf" type="DynamicFontData" id=5]
[ext_resource path="res://Assets/ui/debuffnamemeter.png" type="Texture" id=6]

[sub_resource type="DynamicFont" id=1]
size = 24
font_data = ExtResource( 2 )

[sub_resource type="DynamicFont" id=2]
size = 24
font_data = ExtResource( 5 )

[node name="CanvasLayer" type="CanvasLayer"]

[node name="veil" type="TextureRect" parent="."]
self_modulate = Color( 1, 1, 1, 0.784314 )
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 0
texture = ExtResource( 1 )
expand = true
stretch_mode = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="TextureRect" type="TextureRect" parent="veil"]
self_modulate = Color( 0.392157, 0.392157, 0.392157, 1 )
anchor_left = 0.3
anchor_top = 0.2
anchor_right = 0.7
anchor_bottom = 0.8
texture = ExtResource( 3 )
expand = true
__meta__ = {
"_edit_use_anchors_": false
}

[node name="TextureRect2" type="TextureRect" parent="veil/TextureRect"]
margin_top = 41.0
margin_right = 409.0
margin_bottom = 105.0
texture = ExtResource( 6 )
stretch_mode = 4

[node name="Label" type="Label" parent="veil/TextureRect/TextureRect2"]
margin_top = 4.0
margin_right = 409.0
margin_bottom = 38.0
grow_horizontal = 2
custom_fonts/font = SubResource( 1 )
text = "You have unlocked:"
align = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="TextureRect4" type="TextureRect" parent="veil/TextureRect"]
margin_top = 109.0
margin_right = 409.0
margin_bottom = 173.0
texture = ExtResource( 6 )
stretch_mode = 4

[node name="Label2" type="Label" parent="veil/TextureRect/TextureRect4"]
grow_horizontal = 2
custom_fonts/font = SubResource( 1 )
text = "BASIC ATTACK"
align = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="TextureRect" type="TextureRect" parent="veil/TextureRect"]
margin_top = 177.0
margin_right = 409.0
margin_bottom = 301.0
texture = ExtResource( 4 )
stretch_mode = 1

[node name="TextureRect3" type="TextureRect" parent="veil/TextureRect"]
margin_top = 305.0
margin_right = 409.0
margin_bottom = 369.0
texture = ExtResource( 6 )

[node name="Label3" type="Label" parent="veil/TextureRect/TextureRect3"]
margin_top = 68.0
margin_right = 409.0
margin_bottom = 176.0
grow_horizontal = 2
custom_fonts/font = SubResource( 2 )
text = "Unlike any other attack,
 you always have access to this.
Deals 1 PHYSICAL damage."
align = 1
