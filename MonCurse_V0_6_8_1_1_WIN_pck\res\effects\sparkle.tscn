[gd_scene load_steps=58 format=2]

[ext_resource path="res://effects/indicatorv2.png" type="Texture" id=1]
[ext_resource path="res://effects/sparkle.gd" type="Script" id=2]
[ext_resource path="res://effects/defeat effect.png" type="Texture" id=3]
[ext_resource path="res://effects/defeat effect alt.png" type="Texture" id=4]
[ext_resource path="res://effects/dust.png" type="Texture" id=5]
[ext_resource path="res://effects/alerted.png" type="Texture" id=6]

[sub_resource type="AtlasTexture" id=50]
atlas = ExtResource( 6 )
region = Rect2( 0, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=51]
atlas = ExtResource( 6 )
region = Rect2( 128, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=20]
atlas = ExtResource( 1 )
region = Rect2( 0, 192, 64, 64 )

[sub_resource type="AtlasTexture" id=21]
atlas = ExtResource( 1 )
region = Rect2( 64, 192, 64, 64 )

[sub_resource type="AtlasTexture" id=22]
atlas = ExtResource( 1 )
region = Rect2( 128, 192, 64, 64 )

[sub_resource type="AtlasTexture" id=23]
atlas = ExtResource( 1 )
region = Rect2( 192, 192, 64, 64 )

[sub_resource type="AtlasTexture" id=24]
atlas = ExtResource( 1 )
region = Rect2( 256, 192, 64, 64 )

[sub_resource type="AtlasTexture" id=25]
atlas = ExtResource( 1 )
region = Rect2( 320, 192, 64, 64 )

[sub_resource type="AtlasTexture" id=26]
atlas = ExtResource( 3 )
region = Rect2( 0, 0, 256, 256 )

[sub_resource type="AtlasTexture" id=27]
atlas = ExtResource( 3 )
region = Rect2( 256, 0, 256, 256 )

[sub_resource type="AtlasTexture" id=28]
atlas = ExtResource( 3 )
region = Rect2( 512, 0, 256, 256 )

[sub_resource type="AtlasTexture" id=29]
atlas = ExtResource( 3 )
region = Rect2( 768, 0, 256, 256 )

[sub_resource type="AtlasTexture" id=30]
atlas = ExtResource( 3 )
region = Rect2( 0, 256, 256, 256 )

[sub_resource type="AtlasTexture" id=31]
atlas = ExtResource( 3 )
region = Rect2( 256, 256, 256, 256 )

[sub_resource type="AtlasTexture" id=32]
atlas = ExtResource( 3 )
region = Rect2( 512, 256, 256, 256 )

[sub_resource type="AtlasTexture" id=33]
atlas = ExtResource( 3 )
region = Rect2( 768, 256, 256, 256 )

[sub_resource type="AtlasTexture" id=34]
atlas = ExtResource( 4 )
region = Rect2( 0, 0, 256, 256 )

[sub_resource type="AtlasTexture" id=35]
atlas = ExtResource( 4 )
region = Rect2( 256, 0, 256, 256 )

[sub_resource type="AtlasTexture" id=36]
atlas = ExtResource( 4 )
region = Rect2( 512, 0, 256, 256 )

[sub_resource type="AtlasTexture" id=37]
atlas = ExtResource( 4 )
region = Rect2( 768, 0, 256, 256 )

[sub_resource type="AtlasTexture" id=38]
atlas = ExtResource( 4 )
region = Rect2( 0, 256, 256, 256 )

[sub_resource type="AtlasTexture" id=39]
atlas = ExtResource( 4 )
region = Rect2( 256, 256, 256, 256 )

[sub_resource type="AtlasTexture" id=40]
atlas = ExtResource( 4 )
region = Rect2( 512, 256, 256, 256 )

[sub_resource type="AtlasTexture" id=41]
atlas = ExtResource( 4 )
region = Rect2( 768, 256, 256, 256 )

[sub_resource type="AtlasTexture" id=42]
atlas = ExtResource( 5 )
region = Rect2( 0, 0, 256, 256 )

[sub_resource type="AtlasTexture" id=43]
atlas = ExtResource( 5 )
region = Rect2( 256, 0, 256, 256 )

[sub_resource type="AtlasTexture" id=44]
atlas = ExtResource( 5 )
region = Rect2( 512, 0, 256, 256 )

[sub_resource type="AtlasTexture" id=45]
atlas = ExtResource( 5 )
region = Rect2( 768, 0, 256, 256 )

[sub_resource type="AtlasTexture" id=46]
atlas = ExtResource( 5 )
region = Rect2( 0, 256, 256, 256 )

[sub_resource type="AtlasTexture" id=47]
atlas = ExtResource( 5 )
region = Rect2( 256, 256, 256, 256 )

[sub_resource type="AtlasTexture" id=48]
atlas = ExtResource( 5 )
region = Rect2( 512, 256, 256, 256 )

[sub_resource type="AtlasTexture" id=49]
atlas = ExtResource( 5 )
region = Rect2( 768, 256, 256, 256 )

[sub_resource type="AtlasTexture" id=14]
atlas = ExtResource( 1 )
region = Rect2( 0, 128, 64, 64 )

[sub_resource type="AtlasTexture" id=15]
atlas = ExtResource( 1 )
region = Rect2( 64, 128, 64, 64 )

[sub_resource type="AtlasTexture" id=16]
atlas = ExtResource( 1 )
region = Rect2( 128, 128, 64, 64 )

[sub_resource type="AtlasTexture" id=17]
atlas = ExtResource( 1 )
region = Rect2( 192, 128, 64, 64 )

[sub_resource type="AtlasTexture" id=18]
atlas = ExtResource( 1 )
region = Rect2( 256, 128, 64, 64 )

[sub_resource type="AtlasTexture" id=19]
atlas = ExtResource( 1 )
region = Rect2( 320, 128, 64, 64 )

[sub_resource type="AtlasTexture" id=2]
atlas = ExtResource( 1 )
region = Rect2( 0, 0, 64, 64 )

[sub_resource type="AtlasTexture" id=3]
atlas = ExtResource( 1 )
region = Rect2( 64, 0, 64, 64 )

[sub_resource type="AtlasTexture" id=4]
atlas = ExtResource( 1 )
region = Rect2( 128, 0, 64, 64 )

[sub_resource type="AtlasTexture" id=5]
atlas = ExtResource( 1 )
region = Rect2( 192, 0, 64, 64 )

[sub_resource type="AtlasTexture" id=6]
atlas = ExtResource( 1 )
region = Rect2( 256, 0, 64, 64 )

[sub_resource type="AtlasTexture" id=7]
atlas = ExtResource( 1 )
region = Rect2( 320, 0, 64, 64 )

[sub_resource type="AtlasTexture" id=8]
atlas = ExtResource( 1 )
region = Rect2( 0, 64, 64, 64 )

[sub_resource type="AtlasTexture" id=9]
atlas = ExtResource( 1 )
region = Rect2( 64, 64, 64, 64 )

[sub_resource type="AtlasTexture" id=10]
atlas = ExtResource( 1 )
region = Rect2( 128, 64, 64, 64 )

[sub_resource type="AtlasTexture" id=11]
atlas = ExtResource( 1 )
region = Rect2( 192, 64, 64, 64 )

[sub_resource type="AtlasTexture" id=12]
atlas = ExtResource( 1 )
region = Rect2( 256, 64, 64, 64 )

[sub_resource type="AtlasTexture" id=13]
atlas = ExtResource( 1 )
region = Rect2( 320, 64, 64, 64 )

[sub_resource type="SpriteFrames" id=1]
animations = [ {
"frames": [ SubResource( 50 ), null, SubResource( 51 ), SubResource( 51 ), null, null, SubResource( 51 ), SubResource( 51 ), SubResource( 51 ), null ],
"loop": false,
"name": "alerted",
"speed": 12.0
}, {
"frames": [ SubResource( 20 ), SubResource( 21 ), SubResource( 22 ), SubResource( 23 ), SubResource( 24 ), SubResource( 25 ), null ],
"loop": false,
"name": "bubble",
"speed": 8.0
}, {
"frames": [ SubResource( 26 ), SubResource( 27 ), SubResource( 28 ), SubResource( 29 ), SubResource( 30 ), SubResource( 31 ), SubResource( 32 ), SubResource( 33 ) ],
"loop": true,
"name": "defeat",
"speed": 10.0
}, {
"frames": [ SubResource( 34 ), SubResource( 35 ), SubResource( 36 ), SubResource( 37 ), SubResource( 38 ), SubResource( 39 ), SubResource( 40 ), SubResource( 41 ) ],
"loop": true,
"name": "defeatalt",
"speed": 10.0
}, {
"frames": [ SubResource( 42 ), SubResource( 43 ), SubResource( 44 ), SubResource( 45 ), SubResource( 46 ), SubResource( 47 ), SubResource( 48 ), SubResource( 49 ) ],
"loop": true,
"name": "dust",
"speed": 30.0
}, {
"frames": [ SubResource( 14 ), SubResource( 15 ), SubResource( 16 ), SubResource( 17 ), SubResource( 18 ), SubResource( 19 ), null ],
"loop": false,
"name": "hitmark",
"speed": 18.0
}, {
"frames": [ SubResource( 2 ), SubResource( 3 ), SubResource( 4 ), SubResource( 5 ), SubResource( 6 ), SubResource( 7 ), SubResource( 8 ), SubResource( 9 ), SubResource( 10 ), SubResource( 11 ), SubResource( 12 ), SubResource( 13 ) ],
"loop": false,
"name": "sparkle",
"speed": 12.0
} ]

[node name="Node2D" type="AnimatedSprite"]
scale = Vector2( 2, 2 )
z_index = 15
frames = SubResource( 1 )
animation = "alerted"
script = ExtResource( 2 )

[connection signal="animation_finished" from="." to="." method="_on_Node2D_animation_finished"]
