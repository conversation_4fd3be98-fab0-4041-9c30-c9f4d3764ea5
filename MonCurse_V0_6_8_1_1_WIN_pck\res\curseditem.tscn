[gd_scene load_steps=25 format=2]

[ext_resource path="res://Assets/pickups/curseditems.png" type="Texture" id=1]
[ext_resource path="res://Assets/ui/debufficons/horns5.png" type="Texture" id=2]
[ext_resource path="res://Assets/enemyselectorhologram.png" type="Texture" id=3]
[ext_resource path="res://Assets/materials/hologramshader.shader" type="Shader" id=4]

[sub_resource type="AtlasTexture" id=1]
flags = 4
atlas = ExtResource( 1 )
region = Rect2( 256, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=3]
flags = 4
atlas = ExtResource( 1 )
region = Rect2( 0, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=4]
flags = 4
atlas = ExtResource( 1 )
region = Rect2( 384, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=2]
flags = 4
atlas = ExtResource( 1 )
region = Rect2( 128, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=19]
flags = 4
atlas = ExtResource( 1 )
region = Rect2( 896, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=11]
flags = 4
atlas = ExtResource( 1 )
region = Rect2( 512, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=15]
flags = 4
atlas = ExtResource( 1 )
region = Rect2( 768, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=12]
flags = 4
atlas = ExtResource( 1 )
region = Rect2( 640, 0, 128, 128 )

[sub_resource type="SpriteFrames" id=5]
animations = [ {
"frames": [ SubResource( 1 ) ],
"loop": true,
"name": "bell",
"speed": 5.0
}, {
"frames": [ SubResource( 3 ) ],
"loop": true,
"name": "claw",
"speed": 5.0
}, {
"frames": [  ],
"loop": true,
"name": "empty",
"speed": 5.0
}, {
"frames": [ SubResource( 4 ) ],
"loop": true,
"name": "paw",
"speed": 5.0
}, {
"frames": [ SubResource( 2 ) ],
"loop": true,
"name": "pelt",
"speed": 5.0
}, {
"frames": [ SubResource( 19 ) ],
"loop": true,
"name": "pixiedust",
"speed": 5.0
}, {
"frames": [ SubResource( 11 ) ],
"loop": true,
"name": "ring",
"speed": 5.0
}, {
"frames": [ SubResource( 15 ) ],
"loop": true,
"name": "ropegag",
"speed": 5.0
}, {
"frames": [ SubResource( 12 ) ],
"loop": true,
"name": "symbol",
"speed": 5.0
} ]

[sub_resource type="AtlasTexture" id=6]
flags = 4
atlas = ExtResource( 1 )
region = Rect2( 0, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=9]
flags = 4
atlas = ExtResource( 1 )
region = Rect2( 512, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=13]
flags = 4
atlas = ExtResource( 1 )
region = Rect2( 768, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=7]
flags = 4
atlas = ExtResource( 1 )
region = Rect2( 128, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=10]
flags = 4
atlas = ExtResource( 1 )
region = Rect2( 384, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=14]
flags = 4
atlas = ExtResource( 1 )
region = Rect2( 640, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=20]
flags = 4
atlas = ExtResource( 1 )
region = Rect2( 896, 128, 128, 128 )

[sub_resource type="SpriteFrames" id=8]
animations = [ {
"frames": [ SubResource( 6 ) ],
"loop": false,
"name": "active",
"speed": 5.0
}, {
"frames": [ SubResource( 9 ), SubResource( 13 ) ],
"loop": false,
"name": "closed",
"speed": 5.0
}, {
"frames": [  ],
"loop": false,
"name": "empty",
"speed": 5.0
}, {
"frames": [ SubResource( 7 ) ],
"loop": false,
"name": "inactive",
"speed": 5.0
}, {
"frames": [ SubResource( 10 ), SubResource( 14 ) ],
"loop": false,
"name": "open",
"speed": 5.0
}, {
"frames": [ SubResource( 20 ) ],
"loop": true,
"name": "skill",
"speed": 5.0
} ]

[sub_resource type="ShaderMaterial" id=16]
shader = ExtResource( 4 )
shader_param/speed = 5.0
shader_param/hologramTexture = ExtResource( 3 )

[sub_resource type="Animation" id=17]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath(".:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ false ]
}

[sub_resource type="Animation" id=18]
resource_name = "reqplay"
length = 2.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1, 2 ),
"transitions": PoolRealArray( 0.5, 2, 1 ),
"update": 0,
"values": [ Vector2( 0, -70 ), Vector2( 0, -100 ), Vector2( 0, -70 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}

[node name="curseditem" type="AnimatedSprite"]
frames = SubResource( 5 )
animation = "empty"

[node name="stand" type="AnimatedSprite" parent="."]
show_behind_parent = true
frames = SubResource( 8 )
animation = "inactive"
offset = Vector2( 0, 22 )

[node name="requirement" type="Sprite" parent="stand"]
visible = false
material = SubResource( 16 )
position = Vector2( 0, -70 )
texture = ExtResource( 2 )

[node name="reqplayer" type="AnimationPlayer" parent="stand/requirement"]
playback_speed = 0.4
anims/RESET = SubResource( 17 )
anims/reqplay = SubResource( 18 )
