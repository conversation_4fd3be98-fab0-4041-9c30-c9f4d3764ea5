extends AnimatedSprite

onready var mainscene = get_parent().get_parent()
var friendly_node = null

const attackable = true
var isenemy = true

var original_monsternum = -1#the monsternum of a converted enemy before it was converted
var converted = 0#false#might use to ban or allow certain quip lines. true when spawn_converted_enemy such as elite harpy's egg
enum{NOTCONVERTED=0,CONVERTED=1,PLAYERCONVERTED=2}

var dummy = false #spawns differently
var maxhealthvalue = 2
var maxhealth = 2
var health = 2
const tilesize = 128
var basedamage = 1
var enemyswimspeed = 2
var enemybasespeed = 3
var enemyspeed = 3 #how much the enemy moves normally
var enemyfallspeed = 4 #how much the enemy falls
var basefallspeed = 4 #how much the enemy falls, but the default value to set it to
var enemybasejumpspeed = 2
var enemyjumpspeed = 2
var enemyrank = 0

enum{SPADE=0,DIAMOND=1,HEART=2,CLUB=3}

var held_object = null

var attack_obstructions = false

var use_extra = false
var extra_effect = null

enum Multiplier{NORMAL,VULNERABLE,RESISTANT,IMMUNE}
var DamageMultiplier = [0,0,0,0,0,0,0] #PURE, FORCE, PRECISION, SPIRIT, SWEET, BITTER, ENERGY

var MinionOwner = null
var MinionOwnerWeakRef = null
var TargetNotPlayer = false
var CurrentTarget = null
#var CurrentTargetWeakRef = null
enum Target{PLAYER,NEAREST,ONLYPLAYER,ONLYENEMY,MOSTLYENEMY}
#INFO: PLAYER enemy should only target player until the enemy is annoyed at another enemy.
# NEAREST enemey will target anything that's close
#ONLYPLAYER enemy will never turn on other enemies
#ONLYENEMY enemy will only target other enemies
func player_favor(strong=false):#,passify = false):
	if TargetType != Target.ONLYENEMY and TargetType != Target.ONLYPLAYER:
		friendly(true)
#		if strong == true:
#		mainscene.maxhealthincrement -= maxhealth
#		if mainscene.rewardgiven == false and mainscene.maxhealthdefeated >= clamp(mainscene.tiernums[Playervariables.tier],1,mainscene.maxhealthincrement):
#			mainscene.rewardgiven = true
#			get_node("/root/Master").call("offer_rewards")
		if strong == true:
			TargetType = Target.ONLYENEMY
			if monsternum == RAMGIRL:
				if mainscene.mapgenlocation == Playervariables.Mission4:
					Playervariables.questcondition = clamp(Playervariables.questcondition-1,0,99)
					if Playervariables.questcondition <= 0:
						get_node("/root/Master").call("update_quest",0,false)
						get_node("/root/Master").call("update_quest",1,false)
						get_node("/root/Master").call("update_quest",2,true)
		else:
			TargetType = Target.MOSTLYENEMY
		if TargetNotPlayer == false:
			var _loc = get_target_loc(true)
#			if TargetNotPlayer == false:
#				make_passive()
#		if TargetNotPlayer == false and passify == true:
#			make_passive()
#			get_target_loc(true)
#			TargetNotPlayer = true
#			targetloc = get_target_loc(true)
#			targetlocv = get_target_locv()
#		else:
#			make_passive()
var TargetType = Target.PLAYER
func untarget(ID):
	if CurrentTarget == ID:
		CurrentTarget = null
		targetloc = get_target_loc(true)
		targetlocv = get_target_locv()
func get_target_loc(retarget = false,forcetarget = null) -> Vector2:
#	var testfindenemy = mainscene.enemy_targeting(loc,[monsternum],allyarray,25)
	if retarget == true or (TargetNotPlayer == true and CurrentTarget == null):# or (TargetNotPlayer == true and (CurrentTargetWeakRef == null or CurrentTargetWeakRef.get_ref() == null or ("health" in CurrentTarget and CurrentTarget.get("health") < 1))):
		var attacker_is_minion = false
		if forcetarget != null and TargetType != Target.ONLYPLAYER:
			attacker_is_minion = ("MinionOwner" in forcetarget and forcetarget.MinionOwner != null)
			if (forcetarget == get_parent().get_parent() or (attacker_is_minion == true and forcetarget.MinionOwner == get_parent().get_parent())):
				forcetarget = null
#				if friendly_node == null:#lockpassive == false:
				if TargetType == Target.MOSTLYENEMY:
					passive = false
					TargetType = Target.NEAREST
					friendly(false)
				elif TargetType != Target.ONLYENEMY:
					TargetType = Target.PLAYER
					passive = false
					friendly(false)
			else:
#				if friendly_node == null:#if lockpassive == false:
				passive = false
				if TargetType == Target.PLAYER:
					TargetType = Target.NEAREST
		match TargetType:
			Target.PLAYER,Target.ONLYPLAYER:
				TargetNotPlayer = false
				CurrentTarget = null
			Target.NEAREST:
#				var findenemy = mainscene.find_nearest_enemy(loc,60,-1,[monsternum])
				var findenemy
				if retarget == true and forcetarget != null:
					if attacker_is_minion == true:
						if forcetarget.MinionOwnerWeakRef != null and forcetarget.MinionOwnerWeakRef.get_ref() != null:
							findenemy = forcetarget.MinionOwner
						elif "isenemy" in forcetarget:
							findenemy = forcetarget
						else:
							findenemy = null
					else:
						findenemy = forcetarget
				else:
					findenemy = mainscene.enemy_targeting(loc,[monsternum],allyarray,25)
				if findenemy != null and (forcetarget != null or mainscene.get("playerloc").length() > findenemy.get("loc").length()):
					passive = false
					TargetNotPlayer = true
					CurrentTarget = findenemy
#					CurrentTargetWeakRef = weakref(findenemy)
				else:
					TargetNotPlayer = false
#					CurrentTargetWeakRef = null
					CurrentTarget = null
			Target.ONLYENEMY,Target.MOSTLYENEMY:
#				var findenemy = mainscene.find_nearest_enemy(loc,60,-1,[monsternum])
				var findenemy
				if retarget == true and forcetarget != null:
					if attacker_is_minion == true:
						if forcetarget.MinionOwnerWeakRef != null and forcetarget.MinionOwnerWeakRef.get_ref() != null:
							findenemy = forcetarget.MinionOwner
						elif "isenemy" in forcetarget:
							findenemy = forcetarget
						else:
							findenemy = null
					else:
						findenemy = forcetarget
				else:
					findenemy = mainscene.enemy_targeting(loc,[monsternum],[],25)
				if findenemy != null and (("CurrentTarget" in findenemy) == false or (findenemy.CurrentTarget == null or (("TargetType" in findenemy) == true and findenemy.TargetType != Target.ONLYENEMY and findenemy.TargetType != Target.MOSTLYENEMY))):#and "loc" in findenemy: #"loc" in find enemy is a failsafe, because this might've been a problem...
					TargetNotPlayer = true
					CurrentTarget = findenemy
#					lockpassive = false
					passive = false
#					CurrentTargetWeakRef = weakref(findenemy)
				else:
					TargetNotPlayer = false
					passive = true
#					lockpassive = true
#					CurrentTargetWeakRef = null
					CurrentTarget = null
					friendly(true)
	if TargetNotPlayer == false:
		if friendly_node != null:
			passive = true
		return mainscene.get("playerloc")
	else:
		if CurrentTarget != null:
			if ("loc" in CurrentTarget) == false:
				print("Tried to target something with no LOC!!")
				CurrentTarget = null
				passive = true
				return loc
			return CurrentTarget.get("loc")
		else:
			passive = true
			return loc
func get_target_locv():
	if TargetNotPlayer == false:
		return mainscene.get("playerlocv")
	else:
		if CurrentTarget == null:
			return loc
		else:
			return CurrentTarget.get("locv")

var quipalts = 0
var quip = null #stores a conversation dictionary
var quipnum = "-1" #stores quip startpoint

#SPRITE FRAMES: 
#0 =  PREP
#1 = FLINCH
#2 = IDLE
#3 = JUMP
#4 = ATTACK
#5 = RUN

#var Hitmarker = preload("res://hitmarker.tscn")
var attack2trigger = Vector2(0,0) #the relative loc that an enemy will trigger attack1 in

#var droptable = []

var hadhypno = false
var hypno_id = null
var hypno_id_weakref = null

var boundarymin = Vector2(0,0) #min loc th enemy can move to
var boundarymax = Vector2(99,99) #max loc the enemy can move to
var boundarysize = Vector2(99,99)
var storedboundarymin = Vector2(0,0)
var storedboundarymax = Vector2(99,99)
var roampoint = -1 #astar ID of the roampoint
var attackshift1 = 0 #how much the enemy accounts for enemy movement on attack2
var allyarray = []
#var friendarray = [] #doesn't count as this race but won't take damage from this race
var enemyname = "Enemy"
var monsternum
enum{CATGIRL=0,FOXGIRL=1,WEREWOLF=2,RAMGIRL=3,GHOSTFOX=4,DRAGONHARPY=5,IMP=6}
var shrinenum = -1
var enemyfrustration = 0 #some enemies do certain things if they fall several times in a row
var exhaustjump = false #If true, the enemy can only jump occasionally.
var exhaustedjumpcount = 0 #countdown until enemy can jump again
#var playerfriendly = false
#var lockpassive = false #can't un-passive
var passive = false #different to playerfriendly, makes enemies not bother doing anything much
var lewd = true #enables certain 'lewd' attacks, similar to passive
var enemynegativedebuffarray = []
var enemydebuffarray = []
var flinchimmunity = false
var GEHENNA = false

var can_reclothe = false
var shredded = false

var enemycurrenteffectnode = null #stuff like windstep
#var queuewindstep = false

var mapsize

var uniqueenemyID

var homepoint = Vector2(20,20)

var quipcountdown = 0

enum {MAJOR=0,MINION=1,BOSS=2}
var EnemyCategory = MAJOR
var ignoreobstruction = false
enum {JUMPING,FLYING,GHOSTLY}
var MovementType = JUMPING
var ruthlessness = 0 #how likely the enemy is to try and jump the player when they're vulnerable
var permatracking = false
var perfecttracking = false

signal continue_moving
func _ready():
# warning-ignore:return_value_discarded
# warning-ignore:return_value_discarded
	connect("continue_moving",self,"placeholder")
	set_physics_process(false)
func placeholder():
	pass

func limitroaming(onoff):
	if onoff ==  true and limitroam == false:
		limitroam = true
		if monsternum in [WEREWOLF,CATGIRL]:
			var border = mainscene.get("borderthickness")
			boundarymin = Vector2(clamp(homepoint.x-7,border,99999),clamp(homepoint.y-6,border,99999))
			boundarymax = Vector2(clamp(homepoint.x+7,0,mapsize.x-border),clamp(homepoint.y+6,0,mapsize.y-border))
			boundarysize = boundarymax-boundarymin
			storedboundarymin = boundarymin
			storedboundarymax = boundarymax
	elif limitroam == true:
		limitroam = false
		if monsternum in [WEREWOLF,CATGIRL]:
			var border = mainscene.get("borderthickness")
			boundarymin = Vector2(clamp(homepoint.x-14,border,99999),clamp(homepoint.y-12,border,99999))
			boundarymax = Vector2(clamp(homepoint.x+14,0,mapsize.x-border),clamp(homepoint.y+12,0,mapsize.y-border))
			boundarysize = boundarymax-boundarymin
			storedboundarymin = boundarymin
			storedboundarymax = boundarymax

var displayname = "Enemy"
func spawnpreview(mapshow = false):
	var randframe = randi()%4
	if Playervariables.monster_display_names.has(monsternum):
		if monsternum == IMP:
			displayname = Playervariables.monster_display_names[monsternum][enemyrank]
		elif enemyrank == 1:
			if Playervariables.monster_display_names[monsternum].size() > 1:
				displayname = Playervariables.monster_display_names[monsternum][2]
			else:
				displayname = Playervariables.monster_display_names[monsternum][0]
				print("ERROR: Enemy rank is 1 but monster_display_names has no case for it")
		elif enemyrank > 0:
			displayname = Playervariables.monster_display_names[monsternum][1]
		else:
			displayname = Playervariables.monster_display_names[monsternum][0]
	match monsternum:
		CATGIRL:
			enemyname = "CatKnight"
#			if enemyrank == 0:
#				displayname = "Cat-Knight"
#			else:
#				displayname = "Tiger-Knight"
			set_sprite_frames(load(Mainpreload.catknightsprites))
			$tail.set_sprite_frames(load(Mainpreload.catknightspritestail))
		FOXGIRL: #shika
			if randframe == 1:
				randframe = 4
			enemyname = "FoxGirl"
#			if enemyrank == 0:
#				displayname = "Fox-Girl"
#			else:
			if enemyrank > 0:
				futa = true
#				displayname = "Shika"
			if futa == false or Playervariables.prefdict[Playervariables.pref.FUTAENEMY] == false or Playervariables.consent == false:
				set_sprite_frames(load(Mainpreload.foxgirlsprites))
			else:
				set_sprite_frames(load(Mainpreload.foxgirlspritesdick))
			$tail.set_sprite_frames(load(Mainpreload.foxgirlspritestail))
		WEREWOLF: #pitch
			enemyname = "WereWolf"
#			if enemyrank == 0:
#				displayname = "WereWolf"
#			else:
#				displayname = "Pitch"
			set_sprite_frames(load(Mainpreload.pitchsprites))
			$tail.set_sprite_frames(load(Mainpreload.pitchspritestail))
		RAMGIRL: #ramgirl
			enemyname = "RamGirl"
#			if enemyrank == 0:
#				displayname = "Ram-Girl"
#			else:
			if enemyrank > 0:
				futa = true
#				displayname = "Satyr"
			if enemyrank == 1:
				set_sprite_frames(load(Mainpreload.ramgirlspritesspecial))
			else:
				set_sprite_frames(load(Mainpreload.ramgirlsprites))
			if randframe == 1:
				randframe = 6
			$tail.visible = false
		GHOSTFOX:
			enemyname = "GhostFox"
#			displayname = "Spirit"
			randframe = 0
			emotionlock = true
			set_sprite_frames(load(Mainpreload.ghostfoxsprites))
			z_index = 8
			$tail.visible = false
		DRAGONHARPY:
			enemyname = "DragonHarpy"
			randframe = 0
			if enemyrank <= 0:
				set_sprite_frames(load(Mainpreload.dragonharpysprites))
				$tail.visible = false
#				displayname = "Harpy"
			else:
#				displayname = "Drake"
				set_sprite_frames(load(Mainpreload.elitedragonharpysprites))
				$tail.set_sprite_frames(load(Mainpreload.elitedragonharpyspritestail))
		IMP:
			if randframe == 1:
				randframe = 4
			self.offset.x = 10
			enemyname = "ImpGirl"
			match enemyrank:
				SPADE:set_sprite_frames(load("res://Enemy/imp/imp_spade_clothes.tres"))
				DIAMOND:set_sprite_frames(load("res://Enemy/imp/imp_diamond_clothes.tres"))
				HEART:set_sprite_frames(load("res://Enemy/imp/imp_heart_clothes.tres"))
				CLUB:
					futa = true
					if Playervariables.prefdict[Playervariables.pref.FUTAENEMY] == false or Playervariables.consent == false:
						set_sprite_frames(load("res://Enemy/imp/imp_club_clothes_female.tres"))
					else:
						set_sprite_frames(load("res://Enemy/imp/imp_club_clothes.tres"))
			$tail.visible = false
	self.frame = randframe
	$tail.frame = randframe
	if dummy == true:
		self.use_parent_material = false
		self.material.set_shader_param("hue_shift",0.8)
		self.material.set_shader_param("sat_mul",0.5)
		self.material.set_shader_param("val_mul",0.05)
	elif enemyrank > 0:
		var HSV = Playervariables.RANKhsv[monsternum]
		if enemyrank == 1 and monsternum  == RAMGIRL:
			HSV = Playervariables.specialramhsv
			shredded = true
		if monsternum == IMP:
			HSV = Playervariables.IMPRANKhsv[enemyrank]
		self.use_parent_material = false
		self.material.set_shader_param("hue_shift",HSV.x)
		self.material.set_shader_param("sat_mul",HSV.y)
		self.material.set_shader_param("val_mul",HSV.z)
	if mapshow == true:
		modulate = Playervariables.racecolorarray[Playervariables.enemyracearray[monsternum]]#Color(0.15,0.15,0.2,0.8)
		modulate.a = 0.7
var worldquip = false
func spawnready(newpos,monsterID,guardpoint=Vector2(0,0),GEHENNAyesno=false,rank=0,AssignMinionOwner=null,appearlocv=null): #the spawnready is like ready for enemies, which is called correctly after spawn.
	var _doesnotmatter = connect("advancemovequeue",self,"placeholder")
	set_physics_process(true)
	if (mainscene.map_ready) == true:
		update_map()
	mapstar = mainscene.get("mapstar")
	skystar = mainscene.get("skystar") #steals the skystar grid from parent
	astar = mainscene.get("astar")
	add_to_group("LoadMapArrays") #delays the loading process until after the maps are made
	if mainscene.fixedzone == false or Playervariables.nextlocation == Playervariables.WolfCombat:
		worldquip = true
		match monsterID:
			FOXGIRL:quip = "worldfoxgirlquips"
			RAMGIRL: quip = "worldramgirlquips"
			CATGIRL: quip = "worldcatknightquips"
			WEREWOLF:
				quip = "worldwerewolfquips"
				if Playervariables.nextlocation == Playervariables.WolfCombat:
					assignquip(3*100,null,"normal",false,15)
					no_unquip_on_damage = true
			DRAGONHARPY:
				quip = "worldharpyquips"
				if converted > 0:
					match original_monsternum:
						FOXGIRL:assignquip(310,null,"normal",false,5,true)
						RAMGIRL:assignquip(320,null,"normal",false,5,true)
						WEREWOLF:assignquip(330,null,"normal",false,5,true)
						IMP:assignquip(340,null,"normal",false,5,true)
						CATGIRL,_:
							if converted == 1:
								assignquip(3*100,null,"normal",false,5,true)
							elif converted == 2:
								assignquip(4*100,null,"normal",false,5,true)
			_: worldquip = false
	if GEHENNAyesno == true:
		GEHENNAyesno = false #debug anti-GEHENNA
	uniqueenemyID = mainscene.enemyincrement
	mainscene.enemyincrement += 1
	if Playervariables.debugmodeon:
		mainscene.create_enemy_point(uniqueenemyID)
		if Playervariables.debugenemytracking == true:
			var numsp = Label.new()
			add_child(numsp)
			numsp.set_text(str(uniqueenemyID))
			numsp.rect_scale = Vector2(2,2)
	enemyrank = rank
	homepoint = guardpoint
	monsternum = monsterID
	if monsternum == IMP:
		enemyrank = enemyrank % 4
		var test_array = mainscene.imp_array
		if test_array[enemyrank] == true:
			for _i in range(4): #it's four so it loops back around to the start
				enemyrank = (enemyrank+1) % 4
				if test_array[enemyrank] == false:
					test_array[enemyrank] = true
					break
		else:
			test_array[enemyrank] = true
		Playervariables.spawnedarray[monsterID] +=1
	else:
		if mainscene.fixedzone == false:
			if rank == 0:
				if Playervariables.spawnedarray[monsterID] == 0 and worldquip == true:
					if Playervariables.curseditemdict[Playervariables.ROPEGAG] == true:
						assignquip(39*100,null,"normal",false,30)
					else:
						#quip != null and quip.length() > 5 and quip.substr(0,5) == "world":
						if monsterID == RAMGIRL:
							match Playervariables.corruptiondict.get("tail"):
								Playervariables.raceKITSUNE:assignquip(500,null,"normal",false,30)
								Playervariables.raceNEKO:assignquip(600,null,"normal",false,30)
								_:assignquip(0,null,"normal",false,30)
							if Playervariables.corruptiondict.get("horns") == Playervariables.raceRAM:
								assignquip(700,null,"normal",false,30)
						else:
							assignquip(0,null,"normal",false,30) #greeting
				Playervariables.spawnedarray[monsterID] +=1
			elif rank == 2:#does not include rank = 1
				if Playervariables.elitespawnedarray[monsterID] == 0 and worldquip == true:
					if Playervariables.curseditemdict[Playervariables.ROPEGAG] == true:
						assignquip(39*100,null,"normal",false,30)
					else:
		#				#if quip != null and quip.length() > 5 and quip.substr(0,5) == "world":
						assignquip(100,null,"normal",false,30) #greeting elite
				Playervariables.elitespawnedarray[monsterID] +=1
	var border = mainscene.get("borderthickness")
	$aura/AnimationPlayer.set_current_animation("menacing")
	enemydebuffarray = Playervariables.debuffblankarray.duplicate()
	enemynegativedebuffarray = Playervariables.debuffblanknegativearray.duplicate()
#	for n in Playervariables.debuffidentifyarray.size():
#		enemydebuffarray.append(0)
#	for n in Playervariables.debuffidentifynegativearray.size():
#		 enemynegativedebuffarray.append(0)
	$tail/tail.play("tailwag")
	$tail/tail.advance(randf()*3.99)
	if mainscene.fixedzone == true and mainscene.map_ready == false:
		mapsize = mainscene.get("mapsize") + Vector2(border,border)
	else:
		mapsize = mainscene.get("mapsize")
#	$AnimationPlayer2.stop()
	$AnimationPlayer2.play("fadeout")
#	if GEHENNAyesno == true:
#		GEHENNA = true
#		var newshader = self.material.shader.duplicate()
#		self.material.shader = newshader
	match monsterID:
		CATGIRL: #testcatgirl
			boundarymin = Vector2(border,border)
			boundarymax = Vector2(mapsize.x-border,mapsize.y-border)
			boundarysize = boundarymax-boundarymin
			dict_readiedattack = Playervariables.Move100
			attackshift1 = 1
			add_to_group("Catgirls")
			if enemyrank > 1:
				health = 2
				accuracy = 0.8
			else:
				health = 1
				accuracy = 0.6
			dict_instantattack = Playervariables.Move101
#			droptable = [Vector2(100,0.1),Vector2(101,0.2)] #10% POUNCE, 10% SCRATCH
			enemyspeed = 4
			enemybasespeed = 4
			attack2trigger = Vector2(1,1)
			allyarray = [CATGIRL,RAMGIRL]#append(3)
#			friendarray.append(5)
			hunter = true
			trackmodifier = 0.1
			if Playervariables.curseditemdict[Playervariables.PAWS] == true:
				make_passive()
				lewd = true
		FOXGIRL: #shika
			boundarymin = Vector2(clamp(guardpoint.x-4,border,99999),clamp(guardpoint.y-4,border,99999))
			boundarymax = Vector2(clamp(guardpoint.x+4,0,mapsize.x-border),clamp(guardpoint.y+3,0,mapsize.y-border))
			boundarysize = boundarymax-boundarymin
			dict_readiedattack = Playervariables.Move105
			attackshift1 = 0
			attack2trigger = Vector2(1,1)
#			droptable = [Vector2(105,0.1),Vector2(109,0.2)]  #10% resonance , 10% spike seal
			dict_instantattack = Playervariables.Move109
			dict_special = Playervariables.Move113
			allyarray = [FOXGIRL]
			health = 2
			trackmodifier = 0.25
			trackconsistency = 0.65
			accuracy = 0.8
			quipalts = randi()%2
		GHOSTFOX: #spirit
			dict_readiedattack = null
			attackshift1 = 0
			attack2trigger = Vector2(1,1)
#			droptable = [Vector2(105,0.1),Vector2(109,0.2)]  #10% resonance , 10% spike seal
			dict_instantattack = null
			enemyfallspeed = 0
			basefallspeed = 0
			enemybasejumpspeed = 2
			enemyspeed = 1
			enemybasespeed = 1
			allyarray = [GHOSTFOX]
			health = 4
			trackmodifier = 0.05
			trackconsistency = 0.9
			accuracy = 0.85
			EnemyCategory = MINION
			MovementType = GHOSTLY
			DamageMultiplier[Playervariables.FORCE] = Multiplier.IMMUNE
			DamageMultiplier[Playervariables.PRECISION] = Multiplier.IMMUNE
			DamageMultiplier[Playervariables.SWEET] = Multiplier.VULNERABLE
			DamageMultiplier[Playervariables.SPIRIT] = Multiplier.VULNERABLE
			DamageMultiplier[Playervariables.ENERGY] = Multiplier.VULNERABLE
			if mainscene.mapgenlocation == Playervariables.Shrine:
				passive = true
				guardpoint = Vector2(floor(newpos.x/tilesize),floor(newpos.y/tilesize))
				homepoint = guardpoint
				boundarymin = guardpoint - Vector2(5,5)
				boundarymax = guardpoint + Vector2(5,2)
			else:
				permatracking = true
				perfecttracking = true
				tracking = true
				boundarymin = Vector2(border,border)
				boundarymax = Vector2(mapsize.x-border,mapsize.y-border)
			boundarysize = boundarymax-boundarymin
		IMP: #various
			boundarymin = Vector2(border,border)
			boundarymax = Vector2(mapsize.x-border,mapsize.y-border)
			boundarysize = boundarymax-boundarymin
			dict_readiedattack = null
			attackshift1 = 0
			attack2trigger = Vector2(1,1)
			dict_instantattack = null
			dict_special = Playervariables.Move115
			match enemyrank:
				SPADE:
					health = 3
				DIAMOND:
					dict_superattack = Playervariables.Move117
					health = 4
				HEART:
					dict_superattack = Playervariables.Move116
					health = 4
				CLUB:
					dict_superattack = Playervariables.Move115
					health = 5
			enemyfallspeed = 0
			basefallspeed = 0
			enemybasejumpspeed = 2
			enemyspeed = 1
			enemybasespeed = 1
			allyarray = [IMP]
			trackmodifier = 0.25
			trackconsistency = 0.65
			accuracy = 1.2#yes, they know what they're doing.
			MovementType = FLYING
		WEREWOLF: #pitch
			if pitch_tar_check() == true:
				extra_effect = load(Mainpreload.SpritePitchTar).instance()
				add_child(extra_effect)
				use_extra = true
				extra_effect.frame = self.frame
				extra_effect.offset.y = -102
#			scale = Vector2(0.8,0.8)
#			$tail.scale = Vector2(0.8,0.8)
#			set_modulate(Color(0.8,0.9,0.7))
			$AnimationPlayer2.play("fadeoutpitch")
			offset.y = -102 #make 128 if full size
			$tail.offset.y = -102
			$quip.position.y += -30
			boundarymin = Vector2(border,border)
			boundarymax = Vector2(mapsize.x-border,mapsize.y-border)
			boundarysize = boundarymax-boundarymin
			dict_readiedattack = Playervariables.Move106
			attackshift1 = 1
			health = 5
			ruthlessness = 0.5
			attack_obstructions = true
			dict_instantattack = Playervariables.Move110
			dict_superattack = Playervariables.Move112
			dict_special = Playervariables.Move107
			dict_extramove = Playervariables.Move111
#			droptable = [Vector2(110,0.15),Vector2(106,0.3),Vector2(112,0.45)] #15% RUIN, 15% WARPATH, 15% SHOCKWAVE
			enemybasejumpspeed = 3
			enemyspeed = 4
			enemybasespeed = 4
			$aura/AnimationPlayer.set_current_animation("menacingpitch")
			$aura.scale = Vector2(1.8,1.8)
			$aura.offset.y = -51
#			$extraeffect.set_animation("alert")
#			$extraeffect.scale = Vector2(1.25,1.25)
			attack2trigger = Vector2(2,2)
			allyarray = [WEREWOLF]
			trackmodifier = 0.03
			trackconsistency = 0.75 
			hunter = true
			accuracy = 0.85
			if enemyrank > 0:
				DamageMultiplier[Playervariables.SWEET] = Multiplier.RESISTANT
#				DamageMultiplier[Playervariables.ENERGY] = Multiplier.RESISTANT
		RAMGIRL: #ramgirl
			if (mainscene.mapgenlocation in Playervariables.safezones) == true:#== Playervariables.Shrine:
				boundarymin = guardpoint-Vector2(1,1)
				boundarymax = guardpoint+Vector2(1,1)
			else:
				boundarymin = Vector2(clamp(guardpoint.x-7-Playervariables.escalationdict.get("Map Width"),border,99999),clamp(guardpoint.y-7-Playervariables.escalationdict.get("Map Depth"),border,99999))
				boundarymax = Vector2(clamp(guardpoint.x+7+Playervariables.escalationdict.get("Map Width"),0,mapsize.x-border),clamp(guardpoint.y+7+Playervariables.escalationdict.get("Map Depth"),0,mapsize.y-border))
			boundarysize = boundarymax-boundarymin
			dict_readiedattack = Playervariables.Move102
			attackshift1 = 0
			enemyspeed = 2
			enemybasespeed = 2
			if enemyrank > 1:
				health = 3
			else:
				health = 2
			exhaustjump = true
			if enemyrank <= 0:
				dict_instantattack = Playervariables.Move103
				attack2trigger = Vector2(2,1)
#				droptable = [Vector2(102,0.1),Vector2(103,0.25)] #10% HEAVENLY CHORD, 25% SHEEP SONGw
			else:
				dict_instantattack = Playervariables.Move108
				attack2trigger = Vector2(3,2)
#				droptable = [Vector2(108,0.15),Vector2(102,0.3)] #15% HYPNOSIS, 15% HEAVENLY CHORD
			dict_special = Playervariables.Move108
			if enemyrank == 1:
				worldquip = false
				assignquip((randi()%2)*100,"specialramgirlquips","normal",false,30)
				allyarray = []
				for monnum in range(Playervariables.monsternumarray.size()):
					allyarray.append(monnum)
				passive = true
				friendly(true,true)
				
				var new_butterfly = load(Mainpreload.Butterfly).instance()
				add_child(new_butterfly)
				new_butterfly.set_sentry_butterfly(self,[Vector2(-0.6,-0.8)],2)
				new_butterfly.percher = false
				new_butterfly.add_locv = true
				var new_butterfly2 = load(Mainpreload.Butterfly).instance()
				add_child(new_butterfly2)
				new_butterfly2.set_sentry_butterfly(self,[Vector2(-0.4,-1.1)],2)
				new_butterfly2.percher = true
				new_butterfly2.add_locv = true
				var new_butterfly3 = load(Mainpreload.Butterfly).instance()
				mainscene.get_node("features").add_child(new_butterfly3)
				new_butterfly3.set_sentry_butterfly(mainscene,[mainscene.playerloc-Vector2(0,1)].duplicate(),2)
				new_butterfly3.percher = false
			else:
				allyarray = [RAMGIRL,CATGIRL]#allyarray.append(5)
#			friendarray.append(3)
			trackmodifier = 0.25
			trackconsistency = 0.4
		DRAGONHARPY: #harpy
			boundarymin = Vector2(clamp(guardpoint.x-7-Playervariables.escalationdict.get("Map Width"),border,99999),clamp(guardpoint.y-7-Playervariables.escalationdict.get("Map Depth"),border,99999))
			boundarymax = Vector2(clamp(guardpoint.x+7+Playervariables.escalationdict.get("Map Width"),0,mapsize.x-border),clamp(guardpoint.y+7+Playervariables.escalationdict.get("Map Depth"),0,mapsize.y-border))
			boundarysize = boundarymax-boundarymin
			dict_readiedattack = Playervariables.Move112
			dict_superattack = Playervariables.Move114
			attackshift1 = 0
			enemyspeed = 1
			enemybasespeed = 1
			enemyjumpspeed = 2
			enemybasejumpspeed = 2
			MovementType = FLYING
			enemyfallspeed = 2
			basefallspeed = 2
			if enemyrank > 1:
				health = 3
				DamageMultiplier[Playervariables.ENERGY] = Multiplier.RESISTANT
				dict_instantattack = Playervariables.Move119
				dict_special = Playervariables.Move118
			else:
				health = 2
				dict_instantattack = Playervariables.Move101
			attack2trigger = Vector2(2,1)
			allyarray = [DRAGONHARPY]
			trackmodifier = 0.15
			trackconsistency = 0.4
			held_object = null
#			DamageMultiplier[Playervariables.PRECISION] = Multiplier.VULNERABLE
#	set_animation("default")
#	$tail.set_animation("default")
	if GEHENNA == true:
		health += 2
#		self.material.set_shader(load("res://Assets/materials/hairhueshifter.shader"))
	spawnpreview()
	storedboundarymin = boundarymin
	storedboundarymax = boundarymax
#	map2darray = mainscene.get("map2darray")
#	mapsize = mainscene.get("mapsize")
	add_to_group("Turnends")
#	add_to_group("Attackable")
	add_to_group("Attackers") #this is used for GET_READY
	add_to_group("Quippers")
	locv = newpos
	loc = Vector2(floor(locv.x/tilesize),floor(locv.y/tilesize))
	enemymovepoint = newpos
#	mainscene.call("astardisable",loc,locv,self)
	facing = 1
#	for raceid in allyarray:
#		if Playervariables.races.find(raceid) != -1:
#			playerfriendly = true
#	if tracking == false:
#		$aura.visible = false
#	else:
#		$aura.set_self_modulate(Color(0.7,1,0,0.3))
	$aura.visible = false
	$aura/AnimationPlayer.stop()
	maxhealth = health
	maxhealthvalue = maxhealth
	if monsternum == FOXGIRL and enemyrank > 0:
		maxhealthvalue += 1
	elif monsternum == WEREWOLF and mainscene.mapgenlocation == Playervariables.WolfCombat:
		health += 2
		handle_health()
	if AssignMinionOwner != null:
		if "locv" in AssignMinionOwner:
			appearlocv = AssignMinionOwner.locv
			allyarray = AssignMinionOwner.allyarray
			MinionOwner = AssignMinionOwner
			MinionOwnerWeakRef = weakref(MinionOwner)
			shrinenum = MinionOwner.shrinenum
			TargetNotPlayer = MinionOwner.TargetNotPlayer
			TargetType = MinionOwner.TargetType
			CurrentTarget = MinionOwner.CurrentTarget
		elif "playerlocv" in AssignMinionOwner:
			appearlocv = AssignMinionOwner.playerlocv
			TargetType = Target.MOSTLYENEMY
			friendly(true)
			TargetNotPlayer = true
			allyarray = [GHOSTFOX]
			MinionOwner = get_parent().get_parent()
			MinionOwnerWeakRef = weakref(MinionOwner)
		else:
			MinionOwner = null
			print("Tried to assign a minion owner that doesn't make sense.")
	if appearlocv != null:
		position = appearlocv
		enemymovepoint = appearlocv
		_move_enemy([mapstar.get_closest_point(newpos,true)],1,[-1,0])
	else:
		position = newpos
	if EnemyCategory != MINION:
		mainscene.maxhealthincrement += maxhealthvalue
	else:
		maxhealthvalue = 0
		_process_shadow()

# Called when the node enters the scene tree for the first time.
var dict_readiedattack #READIED attack #should be pounce right now
var dict_instantattack #QUICK attack #should be scratch right now
var dict_superattack #EXTRA attack

var dict_special #these are usually more unusual abilities
var dict_extramove

var specialmovecooldown = 0 #juggernaut, foxfire etc
var specialmovecooldown2 = 0 #in shika's case, windstep.
var ready = false #readied a move based on player location
var targetloc = Vector2(10,10) #avoids being playerloc to show errors when porting over. Tracks where the target is.
var targetlocv = Vector2(1280,1280)
var relativeloc #targetloc - currentloc absolute
var targetlock #if this is above 0, the enemy will attack
var enemyactive = true #false while death animation plays
var enemyground #returns the value of the block below to see if it's on the ground astar or not.
var loc #= Vector2(45,45) #grid spawning position of the test enemy.
var locv #loc but vector version
var disableloc = Vector2(1,1) #previous location for use in astar enable/disable
var disablepoint = -1 #just the locv version for convenience

var attacked = true #if the enemy has taken an attack or move this turn
var route #astar routing
var facing #the way the enemy is going right now, 1 is right -1 is left
var targetprevloc #watches to see if the player moves left or right
var targetshift #where the target moved
var tracking = false
#var sleepy = false #if the catgirl seems to be kinda locked in place, it'll just play this animation.
var trackmodifier = 0.1 #how likely an enemy is to chase the player. LOWER = more likely.
var trackconsistency = 0.65 #how likely an enemy is to change roampoint to player location while tracking. HIGHER means more likely. 0-1
var accuracy = 0.5 #the HIGHER accuracy is, the more likely the enemy is to correctly calculate playerloc.
#var enemymap = []

var map2dpatharray# = mainscene.map2dpatharray
var map2darray# = mainscene.map2darray
var watermap

var incubation = 0
var scissors = 0
var comment_num = 0#avoid repeating the same quips over and over
var quickshot = false #you used a quick attack! Wanna follow up with some bullshit?
func calculate_attack():
	enemyground = map2dpatharray[loc.y+1][loc.x]
	if passive == true or enemyactive == false:
		return
	if monsternum == DRAGONHARPY and Playervariables.mobile_ads == true:
		if TargetNotPlayer == false and mainscene.capture_state != EGGCAPTURE and enrage == true and (mainscene.get("playerloc") -loc).length() < 1.8 and (mainscene.get("playerloc") -loc).length() > 0.2:
			mainscene.call("attacking",[mainscene.get("playerloc")],Vector2(1,0),self,[Vector2(-2,-2)],allyarray,-1,"Grab")
			attacked = true
			if mainscene.get("playerloc") == loc:
				if map2dpatharray[loc.y+1][loc.x] == 0:
					mainscene.add_buff(mainscene.buffs.WINDSTEP)
				Playervariables.playerstunstate = Playervariables.SCISSORLOCKED
				if enemyrank > 0:
					specialmovecooldown = clamp(specialmovecooldown-1,0,100)
				else:
					make_angry(false)
			return
		if enemyrank >= 2 and (Playervariables.prefdict[Playervariables.pref.UNBIRTH] == true or Playervariables.prefdict[Playervariables.pref.PSUEDOPREGNANCY] == true):
			if held_object == null and capture_state == NOCAPTURE:
				if TargetNotPlayer == false and mainscene.playerdebuffarray[Playervariables.DROWSY-1] > 0:
					for y in range(3):
						if mainscene.playerloc == loc+Vector2(0,y+3):
							var useenemy = mainscene
#							print("DRAGON DROP SCENARIO (enemy version but on player):")
#							print(useenemy.playerdebuffarray[Playervariables.DROWSY-1] > 0)
#							print(held_object)
#							print("If this is not true something is clearly weird:"+str(useenemy.capture_state == NOCAPTURE))
							if useenemy.playerdebuffarray[Playervariables.DROWSY-1] > 0 and useenemy.capture_state == NOCAPTURE:
								var valid = true
								for y2 in range(3+y):
									if map2darray[loc.y+y2][loc.x] != 0:
										valid = false
#										print("Enemy -on-player dragon drop scenario: obstrutced")
										break
								for allynum in Playervariables.races:
									if allyarray.find(allynum) > -1:
										valid = false
#										print("breaking because player had allynum??? what?")
										break
								if valid == true:
									var tempstar = mainscene.get("skystarenemy")
									var finalloc = loc + Vector2(0,(3+y))
									var swoop_array = tempstar.get_id_path(tempstar.get_closest_point(locv,true),tempstar.get_closest_point((finalloc + Vector2(0.5,0.5))*tilesize,true))
#									print("Swooparraysize:"+str(swoop_array))
									if swoop_array.size() > 2:
#										print("COMMENCE on-player dd")
										loc = finalloc
										locv = (loc+Vector2(0.5,0.5))*tilesize
										hold_object(true,false,useenemy)
										swoop_array.remove(0)
										attacked = true
										_move_enemy(swoop_array,1,[0,2])
										var newannounce = load(Mainpreload.AnnounceAttack).instance()
										add_child(newannounce)
										newannounce.announce("!!!",true,0)
				elif TargetType != Target.ONLYPLAYER:
					for y in range(3):
						if mainscene.enemy2darray[loc.y+3+y][loc.x] != null:
							var useenemy = mainscene.enemy2darray[loc.y+3+y][loc.x]
#							if "isenemy" in useenemy:
#								print("DRAGON DROP SCENARIO (enemy version infighting):")
#								print(useenemy.enemydebuffarray[Playervariables.DROWSY-1] > 0)
#								print(useenemy.allyarray)
#								print(held_object)
#								print(useenemy.monsternum != DRAGONHARPY)
#								print(useenemy.enemyactive)
							if "isenemy" in useenemy and useenemy.monsternum != DRAGONHARPY and useenemy.enemydebuffarray[Playervariables.DROWSY-1] > 0 and useenemy.capture_state == NOCAPTURE and useenemy.enemyactive == true:
								var valid = true
								for y2 in range(3+y):
									if map2darray[loc.y+y2][loc.x] != 0:
										valid = false
#										print("Enemy infighting dragon drop scenario: obstrutced")
										break
								for allynum in useenemy.allyarray:
									if allyarray.find(allynum) > -1:
										valid = false
										break
								if valid == true:
									var tempstar = mainscene.get("skystarenemy")
									var finalloc = loc + Vector2(0,(3+y))
									var swoop_array = tempstar.get_id_path(tempstar.get_closest_point(locv,true),tempstar.get_closest_point((finalloc + Vector2(0.5,0.5))*tilesize,true))
									if swoop_array.size() > 2:
#										print("COMMENCE INFIGHTING DD")
										loc = finalloc
										locv = (loc+Vector2(0.5,0.5))*tilesize
										hold_object(true,false,useenemy)
										swoop_array.remove(0)
										attacked = true
										_move_enemy(swoop_array,1,[0,2])
										var newannounce = load(Mainpreload.AnnounceAttack).instance()
										add_child(newannounce)
										newannounce.announce("!!!",true,0)
	if monsternum == FOXGIRL:
		if TargetNotPlayer == false and revenge == true and (mainscene.get("playerloc") -loc).length() < 1.8 and (mainscene.get("playerloc") -loc).length() > 0.2:
			mainscene.call("attacking",[mainscene.get("playerloc")],Vector2(1,0),self,[Vector2(-2,-2)],allyarray,-1,"Grab")
			attacked = true
			return
#		else:
#			print("Revenge is: "+str(revenge)+" and distance is: " +str((mainscene.get("playerloc") -loc).length()))
	if randf() > accuracy:
		targetloc = get_target_loc() + Vector2(randi()%3-1,randi()%3-1) #targetloc is now generally slightly wrong.
		targetlocv = get_target_locv() + Vector2(randi()%3-1,randi()%3-1)*tilesize
	else:
		targetloc = get_target_loc()
		targetlocv = get_target_locv()
#	map2darray = mainscene.get("map2darray")
	locv = (loc+Vector2(0.5,0.5))*tilesize
#	if donotenable == false:
#		mainscene.call("astarenable",loc,locv,self)
	relativeloc = Vector2(abs((targetloc - loc).x),abs((targetloc - loc).y))
	targetlock = 0 #why is there targetloc and targetlock... you bitch
	
	var relativedistance = relativeloc.length()
	
	if monsternum == IMP and relativedistance <= 5:#(relativedistance <= 5 or (TargetNotPlayer == false and relativedistance <= 5.5)):
#:		var imp_eight = locate_nearest_knight_point(targetloc,loc,true)
#		if imp_eight > -1
		if quickshot == false:
			attackdict = dict_special
		elif dict_superattack != null:
#			if enemyrank < 3:
			if ready == false:
				specialmovecooldown = 1
				return
			else:
				attackdict = dict_superattack
		else:
			return
		attack_basic()
		if enemyrank <= 0:
			attacked = true
	if relativedistance >= 9 and attack_obstructions == true:
		var map2dobstructionarray = mainscene.map2dobstructionarray
		for x in int((attack2trigger.x*2) +1):
			for y in int((attack2trigger.y*2) +1):
				if map2dobstructionarray[(loc.y+y) - attack2trigger.y][(loc.x+x) - attack2trigger.x] != -1:
					var temprelativeloc = Vector2(abs(x - attack2trigger.x),abs(y - attack2trigger.y))
					var temprelativedistance = temprelativeloc.length()
					if temprelativedistance < relativedistance and dict_instantattack != null and temprelativeloc in dict_instantattack.get("1a"):
						relativeloc = temprelativeloc
						relativedistance = temprelativedistance
	if enemydebuffarray[3] <= 0:
		match monsternum:
			CATGIRL:pass
#				if randf() > 0.85:
#					attempt_random_quip(relativedistance)
			FOXGIRL:
				if ready == true and specialmovecooldown == 0 and shrinenum > -1 and relativedistance > 2 and (mainscene.shrinelocations[shrinenum]-loc).length() < 12:
					mainscene.fox_lightning(mainscene.shrinelocationslocv[shrinenum],locv)
					specialmovecooldown = 4
					attackdict = dict_special
					attack_basic()
				else:
					attempt_random_quip(relativedistance)
#					print_debug("FOXFIRE DOES NOT WORK RIGHT NOW")
#					var startpos = locv + Vector2(((randi()%5)-1.5)*tilesize,((randi()%4)-1.5)*tilesize)
#					if (startpos-locv).length() <= 130:
#						startpos = locv + Vector2(((randi()%5)-1.5)*tilesize,((randi()%4)-1.5)*tilesize)
#					mainscene.foxfire(1,startpos,self,enemyrank)
#					attacked = true
#				else:
#					print_debug("FOXFIRE DOES NOT WORK RIGHT NOW")
#					mainscene.foxfire(0,locv,self) #1 means spawn, 0 means move, -1 means despawn
			WEREWOLF: #pitch's special abilities. this first one is her 'sense' ability
				var cumscore = mainscene.playerdebuffarray[Playervariables.CUM-1]
				if (mainscene.get_node("cummap").get_cellv(mainscene.playerloc) in [7,8,9,10,11]) == true:
					cumscore += 5
				if Playervariables.playerbustsize > 3:
					cumscore += 1
				if relativedistance > 12 and specialmovecooldown == 0 and enemyground == 1: #sense
					specialmovecooldown = 5
					attackdict = dict_special
					attack_basic()
				elif cumscore >= 7 and Playervariables.playerbustsize >= 3 and TargetNotPlayer == false and passive == false and final_hypnosis_check() == true:
					var truedistance = (mainscene.playerloc - loc).length()
					if truedistance < 2.9 and truedistance > 1.9:
						var tempstar = mainscene.get("skystarenemy")
						jump = tempstar.get_id_path(tempstar.get_closest_point(locv,true),tempstar.get_closest_point(mainscene.playerlocv,true))
						var prospectivelocv = skystar.get_point_position(jump[1])
						var prospectiveloc = (prospectivelocv/tilesize) - Vector2(0.5,0.5)
						if jump.size() >= 2 and (map2dpatharray[loc.y+1][loc.x] > 0 or map2dpatharray[prospectiveloc.y+1][prospectiveloc.x] > 0):
							locv = prospectivelocv
							loc = prospectiveloc
							_move_enemy([jump[1]],0,[-1,0])
							attacked = true
					truedistance = (mainscene.playerloc - loc).length()
					if truedistance < 1.9:
						attacked = true
						emotion(1)
						if Playervariables.playeroutfit[0] > 0 and Playervariables.playeroutfit[0] != Playervariables.raceWOLF:
							mainscene.pitch_attack_effect(mainscene.playerlocv+Vector2(0,64))
							if failedtactic == false:
								mainscene.get_node("CanvasLayer2/Dialogue/ConversationV1").rulestalker.disrobe()
								mainscene.emotion(10,0)
								failedtactic = true
								mainscene.register_event_via_main("She's pulling at my clothes!",[],true)
							else:
								mainscene.call("attacking",[mainscene.playerloc],Vector2(1,0),self,[Vector2(Playervariables.MARKED,4)],allyarray,4,"Disrobe")
						else:
							Playervariables.playerstunstate = Playervariables.PITCHLOCKED
					else:
						if comment_num < 2:
							assignquip(6*100,null,"normal",false,2)
							comment_num += 1
				elif cumscore >= 7 and Playervariables.playerbustsize < 3:
					if comment_num < 2:
						assignquip(6*100,null,"normal",false,2)
						comment_num += 1
				elif ready == false and specialmovecooldown < 3 and relativedistance >= 2 and relativedistance <= 5 and map2darray[loc.y][loc.x+sign(targetloc.x-loc.x)] == 0 and (max(relativeloc.x,relativeloc.y) - min(relativeloc.x,relativeloc.y)) > 1:
#					if Playervariables.curseditemdict[Playervariables.WOLFPELT] == true:
#						if randf() > 0.5:
#							specialmovecooldown = 5
#							attackdict = dict_superattack #shockwave
#							attack_basic()
#					else:
					specialmovecooldown = 5
					attackdict = dict_superattack #shockwave
					attack_basic()
	#			else:
	#				print("relativedistance (2<=relativedistance<=5) is: "+str(relativedistance)+"  and then specialmovecooldown (<3) is:  "+str(specialmovecooldown)+"and then map check is: "+str(map2darray[loc.y][loc.x+sign(targetloc.x-loc.x)])+"AND THEN relativeloc check is:  "+str((max(relativeloc.x,relativeloc.y) - min(relativeloc.x,relativeloc.y))))
			RAMGIRL: #ram-girl's special abilities. She only uses it on nearby players who she is tracking.
				attempt_random_quip(relativedistance)
#				if enemyrank > 0 and relativeloc.length() < 3.3 and relativeloc.y < 2 and tracking == true:
#					attackdict = dict_special
#					attack_basic()
#					tracking = false #she loses tracking each time she uses it
#					if ready == false:
#						$aura.visible = false
			DRAGONHARPY:
				attempt_random_quip(relativedistance)
				if incubation > 0:
					incubation -= 1
					if incubation <= 0:
						hold_object(false)
				elif held_object != null and relativeloc.x < 3 and relativeloc.y < 13:
					if final_hypnosis_check() == true:
						if enemydebuffarray[Playervariables.CUM-1] <= 0:
							emotion(1)
							hold_object(false)
							emotionlock = true
						else:
							debuff_bounce(Playervariables.CUM)
				elif passive == false and (TargetNotPlayer == true or friendly_node == null):
					if relativedistance < 12 and relativedistance > 3 and specialmovecooldown == 0:
						if enemydebuffarray[Playervariables.HEAT-1] <= 0:
							emotion(5)
							hold_object(true)
							if enemyrank < 2:
								specialmovecooldown = 4
							else:
								specialmovecooldown = 3
							emotionlock = true
						else:
							debuff_bounce(Playervariables.HEAT)
					elif enemyrank > 0 and ready == false and relativedistance > 1.8 and abs(relativeloc.y-abs(relativeloc.x)) <= 1:
						attackdict = dict_special
						attack_basic()
		if specialtargetlock == true:
			targetlock = 0
			attackdict = dict_extramove
			attack_basic()
#			flinchimmunity = true
#			$extraeffect.visible = true
	elif monsternum == RAMGIRL:
		if mainscene.get_node("cummap").get_cellv(loc) > -1: #she doesn't even like to stand on milk!
#		if watermap[loc.y][loc.x] == 3 and (mainscene.get_node("cummap").get_cellv(loc) in [0,1,2]) == true:
			newroampoint()
		return
	elif monsternum != CATGIRL:
		debuff_bounce(Playervariables.CUM)
	if relativedistance < 12:# and playerfriendly == false:
		if attacked == false:
			if monsternum == RAMGIRL:
				emotionlock = false
			if monsternum == FOXGIRL and mainscene.playerdebuffarray[Playervariables.POSSESSION-1] > 0 and shrinenum > -1:
				enable_tracking()
				roampoint = astar.get_closest_point(targetlocv+Vector2(randi()%5-2,randi()%3-1)*tilesize)
				if Playervariables.debugmodeon == true:
					mainscene.enemy_point(uniqueenemyID,roampoint)
			elif randf() > trackmodifier*relativedistance: #so max length is 17, min is 0
				roaming_tracking(targetlocv)
			else:
				disable_tracking()
#				tracking = false
#				if ready == false:
#					$aura.visible = false
			if $quip.visible == true and passive == false:
				quipcountdown += -1
				if quipcountdown == 0:
					disablequip()
			if attacked == false:
				if ready == true:
					targetlock = 1
					if monsternum == DRAGONHARPY and relativeloc.y > abs(relativeloc.x):
						attackdict = dict_superattack
					else:
						attackdict = dict_readiedattack
				elif quickshot == false:
#					if monsternum != WEREWOLF or (health < maxhealth) or Playervariables.curseditemdict[Playervariables.WOLFPELT] == false:
					if enemynegativedebuffarray[0] == 0:
						attackdict = dict_instantattack
						if relativeloc.y <= attack2trigger.y and relativeloc.x <= attack2trigger.x:
							targetlock = 2
					else:
						debuff_bounce(Playervariables.FLINCH)
				match targetlock:
					0:
						pass
					1:
						targetshift = int(targetloc.x - targetprevloc.x)
#						var beforemove = movequeue
						var before_shift_locv = locv
						for n in attackshift1:
							if abs(targetshift) > n and map2darray[loc.y][loc.x+sign(targetshift)] == 0:
								loc.x += sign(targetshift)
								locv.x += sign(targetshift)*tilesize
							else: 
								break
						if locv != before_shift_locv:
							var minijump = [skystar.get_closest_point(locv,true)]
							_move_enemy(minijump,1,[-1,0])
						attack_basic()
#						if beforemove < movequeue and $enemymoving.get_time_left() != 0:
#							pass
#						else:
#							var minijump = [skystar.get_closest_point(locv,true)]
#							_move_enemy(minijump,1,[-1,0])
#							print_debug("WolfCat-girl failed to shift.")
					2:
						attack_basic()
	else:
		disable_tracking()
#		tracking = false
#		if ready == false:
#			$aura.visible = false
	enemyground = map2dpatharray[loc.y+1][loc.x]

func roaming_tracking(uselocv):
	enable_tracking()
	if perfecttracking == true:
		roampoint = mapstar.get_closest_point(uselocv)
	elif MovementType == FLYING:
		var below_point = astar.get_point_position(astar.get_closest_point(uselocv,true)).y
		var above_point = skystar.get_point_position(skystar.get_closest_point(uselocv,true)).y
		var limited_flight = min(0,((below_point-above_point)/tilesize) - 3)
		if enrage == true:
			limited_flight += 2
		if monsternum == DRAGONHARPY and held_object != null:
			roampoint = skystar.get_closest_point(uselocv+Vector2(randi()%5-2,limited_flight)*tilesize)
		else:
			roampoint = skystar.get_closest_point(uselocv+Vector2(randi()%5-2,(randi()%3)+limited_flight)*tilesize)
	else:
		roampoint = astar.get_closest_point(uselocv+Vector2(randi()%5-2,randi()%3-1)*tilesize)
	if Playervariables.debugmodeon == true:
		mainscene.enemy_point(uniqueenemyID,roampoint)

var knockback_skip_move = false
var enrage = false#if you hit a harpy's egg, she'll enter closer quarters?
var failedtactic = false
var limitroam = false #avoid falling down and shit at all costs
var aggressiveness = 0.2 #how likely an enemy is to roampoint near to the player
var hunter = false #if the enemy uses the aggressiveness stat or not
#var donotenable = false
var GEHENNAfirst = false
func _end_turn():
	bounces = 0
	if (GEHENNA == false or GEHENNAfirst == true) and mainscene.stunned == false:
		for child in $CanvasLayer3/raisezlevel/notice.get_children():
			child.queue_free()
		recordattackarray = []
		recordattacktype = []
		recordmovearray = [locv]
		recordmovearray = [locv]
		for child in $CanvasLayer3/raisezlevel/attackgraphs.get_children():
			child.queue_free()
	if fadeout == true:
		pass
	else:
		if GEHENNA == false:
			$turnwait.start(randf()*(0.3+Playervariables.gameslowness))
		else:
			if GEHENNAfirst == true:
				$turnwait.start(randf()*(0.1+(Playervariables.gameslowness/3)))
			else:
				$turnwait.start((randf()*(0.1+(Playervariables.gameslowness/3))) +0.1)
		yield($turnwait,"timeout")
#	enemymap = mainscene.get("enemy2darray")
	if enemyblockarray.size() > 0:
		incrementenemyblock()
#	elif block != null:
#		block.queue_free()
#		block = null
	if watermap[loc.y][loc.x] == 2:
		enemyspeed = enemyswimspeed
		enemyjumpspeed = enemyswimspeed
	elif exhaustedjumpcount > 0:
		exhaustedjumpcount += -1
		enemyjumpspeed = 0
	else:
		enemyspeed = enemybasespeed
		if monsternum == DRAGONHARPY and (self.frame == 3 or self.frame == 5):
			enemyjumpspeed = enemybasejumpspeed+1
		else:
			enemyjumpspeed = enemybasejumpspeed
	if ready_kiss == true:
		if kiss_id_weakref == null or kiss_id_weakref.get_ref() == null or kiss_id.enemyactive == false:#in theory, can attack faded out enemies?
			ready_kiss = false
	if enemyactive == true and fadeout == false and ready_kiss == false:
		if monsternum == CATGIRL and Playervariables.playerstunstate in [Playervariables.CATLOCKED,Playervariables.PUSSYLOCKED]:
			$enemyhearts.visible = true
			$enemyhearts.emitting = true
			enemynegativedebuffarray[0] += 1#cat-girls don't interfere with each other in this case
		var targetdiff = (loc - mainscene.get("playerloc")) #note, this is positive if player is behind/above enemy
		if Playervariables.lewdness > 0 and capture_state != EGGCAPTURE:
			var distancefromplayer = max(abs(targetdiff.x),abs(targetdiff.y))
			if randf() < (Playervariables.lewdness - 0.5*abs(distancefromplayer-1)):
				if mainscene.playerdebuffarray[Playervariables.CUM-1] > 0:
					mainscene.register_event_via_main("VAR1's charming body can't distract while covered in goo!",[Playervariables.tempname],true)
				else:
					if flinchimmunity == true:#monsternum == WEREWOLF and flinchimmunity == true:
						mainscene.register_event_via_main("VAR1 resists the flinch effect.",[displayname])
						flinchimmunity = false
	#					$extraeffect.visible = false
						enemynegativedebuffarray[0] = 0
					elif monsternum != IMP:
						$enemyhearts.visible = true
						$enemyhearts.emitting = true
						var usedescriptorarray = []
						if Playervariables.playerbustsize > 2:
							usedescriptorarray.append(1)
							usedescriptorarray.append(1)
						if Playervariables.playeroutfit[0] != 1:
							usedescriptorarray.append(2)
							usedescriptorarray.append(2)
						for i in Playervariables.sealsarray:
							if i > 0:
								usedescriptorarray.append(3)
						if usedescriptorarray.size() == 0:
							usedescriptorarray.append(1)
						usedescriptorarray.shuffle()
						mainscene.register_event_via_main("VAR1 was distracted by VAR2's VAR3.",[displayname,Playervariables.tempname,Playervariables.get("lewddescriptor"+str(usedescriptorarray[0]))],true)
						enemynegativedebuffarray[0] += 1
#		if enemydebuffarray[1] >= 4 and hypno_id_weakref != null and hypno_id_weakref.get_ref() != null:
#			print("Hypnotime. previous roampoint:"+str(roampoint))
#			debuff_bounce(Playervariables.HYPNOSIS)
#			if "playerlocv" in hypno_id:
#				roampoint = skystar.get_closest_point(hypno_id.playerlocv,true)
#			elif "locv" in hypno_id:
#				roampoint = skystar.get_closest_point(hypno_id.locv,true)
#			else:
#				print("Error. something without a locv hypnotized a character?")
#			print("roampoint now:"+str(roampoint))
#			$AnimationPlayer2.play("hypno")
		if roampoint == -1 or dummy == true:
			newroampoint()
		elif monsternum == CATGIRL and enemyfrustration >= 5:
			enemyjumpspeed += 3
			enemyfrustration = 0
		quickshot = false
		if capture_state == EGGCAPTURE:
			if capture_id_weakref != null and capture_id_weakref.get_ref() != null:
				var eggtypedamage = Vector2(Playervariables.FORCE,0)
				match monsternum:
					WEREWOLF:
						eggtypedamage.y = 2
					CATGIRL:
						eggtypedamage.y = 1
					FOXGIRL:
						if enemyrank > 0:
							eggtypedamage = Vector2(Playervariables.SPIRIT,2)
						else:
							eggtypedamage = Vector2(Playervariables.SPIRIT,1)
					RAMGIRL:
						if enemyrank > 0:
							eggtypedamage.y = 1
				if eggtypedamage.y > 0:
					capture_id.taking_damage([loc],eggtypedamage,self,[],[],-2,"Struggle")
				enemyground = map2dpatharray[loc.y+1][loc.x]
#				enemyfall()
			else:
				free_capture()
			attacked = true
			enemyground = map2dpatharray[loc.y+1][loc.x]
		elif enemydebuffarray[7] > 3:
			debuff_bounce(Playervariables.IMPACT)
			enemydebuffarray[7] += -2
			attacked = true
		else:
			attacked = false
			if monsternum == CATGIRL and lewd == true:
				if mainscene.get("playerloc").x == loc.x:
#					var locydistance = (loc.y - mainscene.playerloc.y)
					if mainscene.get("playerloc").y == loc.y:
						if GEHENNA == false or GEHENNAfirst == true:
							if final_hypnosis_check() == true:
								if final_milk_check(0.3) == true:
									if Playervariables.playerstunstate == Playervariables.NOSTUN:
										Playervariables.playerstunstate = Playervariables.BUSTLOCKED
								elif TargetNotPlayer == false and passive == false:
									if Playervariables.playeroutfit[1] > 0:#Playervariables.playerstatedict["Shredded"] == 0: #nothing even uses shredded on/off...
										if passive == false:
											mainscene.call("attacking",[loc],Vector2(1,1),self,[Vector2(Playervariables.MARKED,2)],allyarray,4,"Shredder")
#										else:
#											mainscene.call("attacking",[loc],Vector2(1,0),self,[Vector2(Playervariables.MARKED,2)],allyarray,4,"Disrobe")
									else:#if passive == false:
										if Playervariables.playerbustsize > 2:
											Playervariables.playerstunstate = Playervariables.BUSTLOCKED
										elif Playervariables.consent == true:
											mainscene.call("attacking",[loc],Vector2(5,1),self,[Vector2(Playervariables.MARKED,2)],allyarray,-2,"Grope")
										else:
											mainscene.call("attacking",[loc],Vector2(5,1),self,[Vector2(Playervariables.MARKED,2)],allyarray,-2,"Close Quarters")
								attacked = true
					elif targetdiff.y > 0 and failedtactic == false and ((accuracy+0.6) - 0.3*targetdiff.y) > randf() and targetdiff.y <= Playervariables.playerfallspeedmax:# and (enemyrank == 0 or mainscene.climb_check(mainscene.playerloc) < mainscene.YESCLIMB):
						if enemyrank > 0 and (mainscene.climb_check(mainscene.playerloc) >= mainscene.YESCLIMB or (targetdiff.y >= 1.5 and map2dpatharray[loc.y-1][loc.x] > 0) or (targetdiff.y >= 2.5 and map2dpatharray[loc.y-2][loc.x] > 0) or (targetdiff.y >= 3.5 and map2dpatharray[loc.y-3][loc.x] > 0)):
							calculate_attack()
						else:
							revenge = true
							attacked = true
							emotion(1)
					else:
						calculate_attack()
				else:
					calculate_attack()
			else:
				calculate_attack()
			if monsternum == FOXGIRL:
				if revenge == true and (mainscene.get("playerloc") - loc).length() > 1.1:
					revenge = false
				if enemycurrenteffectnode != null:
					enemycurrenteffectnode.queue_free()
					enemycurrenteffectnode = null
				if ((shrinenum > -1 and loc.y > boundarymax.y) or enemyfrustration >= 4) and specialmovecooldown2 != 0:
#					queuewindstep = false
					enemyfallspeed = 0
					enemycurrenteffectnode = load("res://effects/windstep.tscn").instance()
					add_child(enemycurrenteffectnode)
					enemycurrenteffectnode.emitting = true
					enemyfrustration = clamp(enemyfrustration-2,0,10)
					if specialmovecooldown2 == -1:
						specialmovecooldown2 = 1
#						mainscene.call("movesoundeffect",locv,Playervariables.Move11["name"])
					else:
						specialmovecooldown2 = 0
				else:
					enemyfallspeed = basefallspeed
					specialmovecooldown2 = -1
		if quickshot == true and attacked == false:
			calculate_attack()
			quickshot = false
			if monsternum == IMP:
				attacked = true
		if enemydebuffarray[1] >= 5:
			if hypno_id_weakref != null and hypno_id_weakref.get_ref() != null:
				var tempstar = mainscene.get("skystarenemy")
				debuff_bounce(Playervariables.HYPNOSIS)
				if "playerlocv" in hypno_id:
					roampoint = tempstar.get_closest_point(hypno_id.playerlocv,true)
				elif "locv" in hypno_id:
					roampoint = tempstar.get_closest_point(hypno_id.locv,true)
				else:
					print("Error. something without a locv hypnotized a character?")
				jump = tempstar.get_id_path(tempstar.get_closest_point(locv,true),roampoint)
				$AnimationPlayer2.play("hypno")
				attacked = true
				if jump.size() > 0:
					var prompt = true
					if jump.size() > 5:
						jump.resize(5)
						prompt = false
					locv = mapstar.get_point_position(jump[-1])
					loc = Vector2(floor(locv.x/tilesize),floor(locv.y/tilesize))
					jump.remove(0)
					_move_enemy(jump,0,[-1,0])
					if prompt == true and "ready_kiss" in hypno_id:
						hypno_id.ready_kiss = true
						hypno_id.kiss_id = self
						hypno_id.kiss_id_weakref = weakref(self)
			else:
				hypno_id_weakref = null
				hypno_id = null
		if knockback_skip_move == false:
			if attacked == false:# and capture_state == NOCAPTURE:
				enemyground = map2dpatharray[loc.y+1][loc.x]
				if limitroam == false and (mainscene.map2dobstructionarray[loc.y][loc.x] != -1 or (monsternum == CATGIRL and randf() > 0.85)):
					skystar = mainscene.get("skystarenemy")
					astar = mainscene.get("astarenemy")
					ignoreobstruction = true
				else:
					skystar = mainscene.get("skystar")
					astar = mainscene.get("astar")
					ignoreobstruction = false
				if monsternum == WEREWOLF and Playervariables.curseditemdict[Playervariables.WOLFPELT] == true and tracking == true and randf() < ruthlessness: #mainscene.playerdebuffarray[targetdebuff] > 5
					if randf() > accuracy:
						targetloc = get_target_loc() + Vector2(randi()%3-1,randi()%3-1) #targetloc is now generally slightly wrong... or more accurate now?
						targetlocv = get_target_locv() + Vector2(randi()%3-1,randi()%3-1)*tilesize
					else:
						targetloc = get_target_loc()
						targetlocv = get_target_locv()
					roampoint = astar.get_closest_point(targetlocv)
					if Playervariables.debugmodeon == true:
						mainscene.enemy_point(uniqueenemyID,roampoint)
				elif tracking == true and randf() < trackconsistency:
					roaming_tracking(targetlocv)
				elif mapstar.get_point_position(roampoint) == locv:
					if Playervariables.debugmodeon == true:
						mainscene.enemy_point(uniqueenemyID,roampoint)
					roampoint = -1
					if tracking == true:
						newroampoint()
					else:
						emotion(2)
				elif monsternum == IMP:
					if enemyfrustration >= 13 or (enemyfrustration >= 6 and (mapstar.get_point_position(roampoint) - locv).length() <= 240):
						enemyfrustration = 0
						newroampoint()
				if roampoint != -1:
					if MovementType == GHOSTLY:
						var ghostjump = []
						ghostjump = mapstar.get_id_path(mapstar.get_closest_point(locv,true),roampoint)
						var ghostv = Vector2(-1,-1)
						if ghostjump.size()-1 > enemyjumpspeed:
							ghostjump.resize(enemyjumpspeed)#+enemyground)
						var ghostcost = ghostjump.size()-1
						if ghostcost > 0:
							if skystar.has_point(ghostjump[-1]) == true and skystar.is_point_disabled(ghostjump[-1]) == true:
								ghostjump.resize(ghostcost)
								ghostcost -= 1
							if ghostcost > 0:
	#							enemymovepoint = locv
								ghostv = mapstar.get_point_position(ghostjump[ghostcost])
								loc = Vector2(floor(ghostv.x/tilesize),floor(ghostv.y/tilesize))
								locv = (loc+Vector2(0.5,0.5))*tilesize
								ghostjump.remove(0)
								_move_enemy(ghostjump,1,[-1,0])
					elif monsternum == IMP:
						var roampointv = mapstar.get_point_position(roampoint)
						var temp_target = (roampointv - Vector2(64,64)) / tilesize
						var target_point = locate_nearest_knight_point(temp_target)
						if target_point > -1:
							var impjump = [mapstar.get_closest_point(locv,true),target_point]
							locv = mapstar.get_point_position(target_point)
							loc = Vector2(floor(locv.x/tilesize),floor(locv.y/tilesize))
							_move_enemy(impjump,1,[0,2])
					else:
						var reenablepoint = -1
						if astar.has_point(roampoint) and astar.is_point_disabled(roampoint) == true:
							reenablepoint = roampoint
							astar.set_point_disabled(roampoint, false)
							skystar.set_point_disabled(roampoint, false)
						jump = skystar.get_id_path(skystar.get_closest_point(locv,true),roampoint)
						var enemyeffectivespeed = enemyspeed#-enemydebuffarray[0] -1 + clamp(enemyground,0,1)
						var enemyeffectivejumpspeed = enemyjumpspeed#-enemydebuffarray[0]
						if enemydebuffarray[0] > 0:
							debuff_bounce(Playervariables.DROWSY)
							enemyeffectivespeed = int(enemyspeed/2)
							enemyeffectivejumpspeed = int(enemyjumpspeed/2)
						if jump.size() == 0:
							route = []
							var tempskystar = mainscene.get("skystarenemy")
							var tempjump = tempskystar.get_id_path(tempskystar.get_closest_point(locv,true),roampoint)
							var points_valid = 0
							for i in tempjump.size():
								if skystar.is_point_disabled(tempjump[i]) == true:
									if i > 0:
										break
								else:
									points_valid = i
							if points_valid > 0:
								roampoint = tempjump[points_valid]
								jump = skystar.get_id_path(skystar.get_closest_point(locv,true),roampoint)
						var roampointv = mapstar.get_point_position(roampoint)
						if MovementType != FLYING and (astar.get_point_position(astar.get_closest_point(locv,true)) - locv).length() < 1.8*tilesize:
							if (roampointv-locv).length() > (enemyeffectivespeed+3)*tilesize and jump.size() > (enemyeffectivespeed):#+enemyground):
								var smallroute = jump
								smallroute.resize(enemyeffectivespeed)#+enemyground)
								if smallroute != null and smallroute.size() > 0:
									var templocvgoal = skystar.get_point_position(smallroute[smallroute.size()-1])
									var temproampoint = astar.get_closest_point(templocvgoal)
									var startpoint = astar.get_closest_point(locv+(templocvgoal-locv).normalized()*63,true)
									if (astar.get_point_position(startpoint) - locv).length() > 190:
										route = []
	#									print("modular enemy tried to start its move 2 away.")
									else:
										route = astar.get_id_path(startpoint,temproampoint)
									#a slight bias is given towards the route direction in case enemyground == 0 so the enemy hops onto the right astar.
								else:
									route = []
									print("modularenemy routing error...")
							else:
	#							var new_closest_point = astar.get_closest_point(locv)
	#							if (astar.get_point_position(new_closest_point) - locv).length() < 1.8*tilesize:
								if astar.has_point(roampoint) == false:
									roampoint = astar.get_closest_point(mapstar.get_point_position(roampoint))
	#								route = astar.get_id_path(new_closest_point,roampoint)
								route = astar.get_id_path(astar.get_closest_point(locv,true),roampoint)
	#							else:
	#								route = []
						else: #if the closest astar is further than 1 space away, don't even bother
							route = []
						#if jump can ever be faster than move speed, make it so it ignores all this shit.
						if enemyeffectivespeed <= 0:
							route = []
						elif route.size()*enemyeffectivejumpspeed > jump.size()*enemyeffectivespeed:
							route = jump
							if route.size()-1 > enemyeffectivespeed:
									route.resize(enemyeffectivespeed)#+enemyground)
							for ID in route.size():
								if astar.has_point(route[ID]) == false:
									route.resize(ID)
									break
						elif route.size()-1 > enemyeffectivespeed:
							route.resize(enemyeffectivespeed)#+enemyground)
						var routecost = route.size()-1
						if enemyeffectivejumpspeed <= 0:
							jump = []
						elif jump.size()-1 > enemyeffectivejumpspeed:
							jump.resize(enemyeffectivejumpspeed+1)
						jumpcost = jump.size()-1
						enemyground = map2dpatharray[loc.y+1][loc.x]
						if jumpcost > 0:
							jumpv = skystar.get_point_position(jump[jumpcost])
						else:
							jumpv = locv
						if enemyfallspeed > 2:
							if enemyground == 0:
								jumpcost = -1
							elif limitroam == true or (targetloc - loc).length() > 40*aggressiveness and jump.size() > 0:
								var jumploc = (jumpv/tilesize)-Vector2(0.5,0.5)
								var goodjump = false
								var modifier = 0.5
								if hunter == true:
									modifier = 1.0
									match monsternum:
										CATGIRL:
											if passive == true:
												modifier = 0.5
											elif randf() > 0.8:
												modifier = 1.5
										WEREWOLF:
											if randf() > 0.7:
												modifier = 3.0
											else:
												modifier = 1.5
									var yscore = targetloc.y - loc.y
									if yscore >= (enemyfallspeed+enemyjumpspeed)-1 and abs(targetloc.x-loc.x) <= yscore*0.2:
										modifier = -1
								if modifier > 0:
									for i in range(int(ceil(float(enemyjumpspeed)*modifier))):
										if map2dpatharray[jumploc.y+1+i][jumploc.x] == 1:
											goodjump = true
											break
									if goodjump == false:
										jumpcost = -1
		#				jumpv = locv
						var routev = locv
		#				if jumpcost > 0:
		#					jumpv = skystar.get_point_position(jump[jumpcost])
		#				print("Cat. "+str(routecost)+" <routecost - jumpcost> "+str(jumpcost)+"and length,"+str((roampointv - routev).length())+"..andground"+str(enemyground))
						if routecost > 0:
							routev = astar.get_point_position(route[routecost])
						if (((roampointv - routev).length()) <= ((roampointv - jumpv).length()) or jumpcost <= 0) and routecost > 0:
	#						enemymovepoint = locv
							loc = Vector2(floor(routev.x/tilesize),floor(routev.y/tilesize))
							locv = (loc+Vector2(0.5,0.5))*tilesize
							route.remove(0)
							_move_enemy(route,0,[-1,0])
						elif enemyground == 0 and (enemyfallspeed > 2 or enemynegativedebuffarray[0] > 0):# and currentlymoving == false:
							enemyfall()
						elif jumpcost > 0:
	#						enemymovepoint = locv
							loc = Vector2(floor(jumpv.x/tilesize),floor(jumpv.y/tilesize))
							locv = (loc+Vector2(0.5,0.5))*tilesize
							jump.remove(0)
							_move_enemy(jump,1,[-1,0])
							if exhaustjump == true:
								exhaustedjumpcount = 2
						else:
							if Playervariables.debugmodeon == true:
								mainscene.enemy_point(uniqueenemyID,roampoint)
							roampoint = -1
							emotion(2)
						if randf() > 0.97:
							if Playervariables.debugmodeon == true:
								mainscene.enemy_point(uniqueenemyID,roampoint)
							roampoint = -1
						if hunter == true:
							if abs(loc.y - targetloc.y) > enemyjumpspeed:
								if tracking == true:
									enemyfrustration += 1
							elif enemyfrustration > 0:
								enemyfrustration -= 2
						elif enemyfrustration > 0:
							enemyfrustration += -1
						if reenablepoint >= int(0):
							astar.set_point_disabled(reenablepoint, true)
							skystar.set_point_disabled(reenablepoint, true)
			elif enemyground == 0:# and currentlymoving == false:
				enemyfall()
	#	print(((loc+Vector2(0.5,0.5))*tilesize) - locv)   #good tester
		disable_ready()
#		ready = false
#		$ready.emitting = false
#		$ready.visible = false
#		if tracking == false:
#			$aura.visible = false
#		else:
#			$aura.set_self_modulate(Color(0.7,1,0,0.3))
#		$aura/AnimationPlayer.stop()
		specialtargetlock = false
		targetprevloc = Vector2(0,0)
		enemyjumpspeed = enemybasejumpspeed
#		if enemydebuffarray[5] > 0:
#			mainscene.clearfog(loc,clamp(enemydebuffarray[5],1,5),-1)
#			debuff_bounce(Playervariables.MARKED)
#			RAMGIRL: 
#				if position == enemymovepoint:
#					incrementenemydebuffs()
#		if enemynegativedebuffarray[0] > 0:
#			enemynegativedebuffarray[0] += -1
#			if enemynegativedebuffarray[0] == 0:
#				$AnimationFlinch.stop()
#				rotation_degrees = 0
#			if monsternum == WEREWOLF:
#				flinchimmunity = true
#				$extraeffect.visible = true
#				rotation = 0 #in case the enemy was rotated from flinch
		if monsternum == GHOSTFOX and TargetNotPlayer == false and loc == mainscene.get("playerloc") and passive == false and final_hypnosis_check() == true and capture_state == NOCAPTURE and knockbacks.size() == 0 and enemynegativedebuffarray[0] <= 0:
			Playervariables.possessionrank = enemyrank > 0
			if health > 4:
				health = 4
			mainscene.call("attacking",[loc],Vector2(Playervariables.SPIRIT,ceil(health/2)),self,[Vector2(Playervariables.POSSESSION,health)],allyarray,4,"Kitsune Possession")
			$AnimationPlayer2.playback_speed = 0.4
			health -= 4
			mainscene.get_node("sfx/foxobj").volume_db = -11+(health*2)
			mainscene.get_node("sfx/foxobj").play()
			handle_health(true)
		elif GEHENNA == true and GEHENNAfirst == true:
			GEHENNAfirst = false
			_end_turn()
		else:
#			if monsternum == WEREWOLF and Playervariables.curseditemdict[Playervariables.WOLFPELT] == true and loc == mainscene.get("playerloc") and Playervariables.playerstunstate == Playervariables.NOSTUN:
#				Playervariables.playerstunstate = Playervariables.MILKLOCKED
#				mainscene.caught()
#		#		fadeout = true
			if monsternum == FOXGIRL:
				if perfecttracking == true:
					if Playervariables.sealsarray[0] + Playervariables.sealsarray[1] + Playervariables.sealsarray[2] >= 3:
						pass
					else:
						perfectly_track(false)
						perfecttracking = false
#						trackmodifier = 0.25
#						trackconsistency = 0.65
#						accuracy = 0.8
				if TargetNotPlayer == false and (revenge == true or perfecttracking == true) and monsternum == FOXGIRL and loc == mainscene.get("playerloc") and Playervariables.playerstunstate == Playervariables.NOSTUN:
					if perfecttracking == true:
						Playervariables.playerstunstate = Playervariables.FOXLOCKED
						mainscene.caught()
						perfectly_track(false)
#						perfecttracking = false
#						trackmodifier = 0.25
#						trackconsistency = 0.65
#						accuracy = 0.8
						shred(false)
					elif shredded == false:# and final_hypnosis_check() == true:
						Playervariables.playerstunstate = Playervariables.ARMLOCKED
						mainscene.caught()
#					else:
#						debuff_bounce(Playervariables.HYPNOSIS)
				elif shrinenum > -1 and loc.x > boundarymin.x and loc.x < boundarymax.x and loc.y > boundarymin.y and loc.y < boundarymax.y:
					if enemyrank > 1 and (targetloc-loc).length() < 12:
						if (TargetNotPlayer == false and mainscene.playerdebuffarray[Playervariables.POSSESSION-1] > 0) or (TargetNotPlayer == true and "enemydebuffarray" in CurrentTarget and CurrentTarget.enemydebuffarray[Playervariables.POSSESSION-1] > 0):
							pass
						else:
							enemyblockarray.append([5,0,1])
							mainscene.fox_lightning(mainscene.shrinelocationslocv[shrinenum],locv)
					updateenemyblock()
			elif monsternum == DRAGONHARPY and perfecttracking == true:
				if held_object == null and TargetNotPlayer == false and (loc - mainscene.get("playerloc")).length() < 1.8 and Playervariables.playerstunstate == Playervariables.NOSTUN and mainscene.capture_state == EGGCAPTURE:
					Playervariables.playerstunstate = Playervariables.HARPYLOCKED
					mainscene.caught()
					perfectly_track(false)
				else:
					perfectly_track(false)
	handlepointcalc()

#"1a":[Vector2(-1,-2)],"2a":[Vector2(1,-2)],"3a":[Vector2(2,-1)],"4a":[Vector2(2,1)],"5a":[Vector2(1,2)],"6a":[Vector2(-1,2)],"7a":[Vector2(-2,1)],"8a":[Vector2(-2,-1)],
const knight_array = [Vector2(-1,-2),Vector2(1,-2),Vector2(2,-1),Vector2(2,1),Vector2(1,2),Vector2(-1,2),Vector2(-2,1),Vector2(-2,-1)]
#const elephant_array = [Vector2(2,2),Vector2(-2,2),Vector2(2,-2),Vector2(-2,-2)]
#const compass_array = [Vector2(0,2),Vector2(-2,0),Vector2(0,-2),Vector2(2,0)]
enum {KNIGHTMOVE,ELEPHANTMOVE,COMPASSMOVE}
func locate_nearest_knight_point(intendedloc,return_point=true,include_disabled=false,movebias = KNIGHTMOVE) -> int:
#	var border = mainscene.get("borderthickness")
#	if loc.x < 2 or loc.y < 2 or loc.x > border+border+(mapsize.x-3) or loc.y > border+border+(mapsize.y-3):
#		print(mapsize)
#		print(loc)
#		print("CRITICAL ERROR: Knight mover attempted to move outside map")
#		return -1
	var distance = intendedloc - loc#loc can be either loc or locv
	var length = distance.length()
	var vector_num = -1
	#STEP ONE: Try and find a vector that lands on the target.
	if length < 2.5 and length > 2 and knight_array.find(intendedloc - loc) > -1:
		vector_num = knight_array.find(intendedloc - loc)
	elif length < 5:
		#STEP TWO: Try and find a vector that can land on target next turn.
		for vector in knight_array:
			var t_attack_vector = (intendedloc+vector) - loc
			if knight_array.find(t_attack_vector) > -1:
				vector_num = knight_array.find(t_attack_vector)
				break
	#STEP THREE: try and find vector that moves in the right direction.
	if vector_num < 0:
		var knight_move
		if distance.x == 0:
			distance.x = 1 + (-2*(randi()%2))
		if distance.y == 0:
			distance.y = 1 + (-2*(randi()%2))
		if abs(distance.x) > abs(distance.y):
			knight_move = Vector2(2*sign(distance.x),1*sign(distance.y))
		else:
			knight_move = Vector2(1*sign(distance.x),2*sign(distance.y))
		vector_num = knight_array.find(knight_move)
#	if vector_num < 0:
#		print("What the fuck. your knight array is not working.")
#		print(vector_num)
#		print(knight_move)
#		return -1
	if vector_num < 0:
		return -1
	var not_ideal_point = -1
	var not_ideal_shift = -1
	if tracking == false:
		enemyfrustration -= 1
	var current_target_loc = get_target_loc()
	#STEP FOUR: Check that the desired vector doesn't violate any other rules.
	var first_acceptable_move = -1
	var first_acceptable_point
	for i in range(8):
		var shift = (vector_num+(int(0.5 + i*0.5) * int(pow(-1,i+1)))) % 8
#		print("I is:"+str(i)+"..."+"Now, -1^i is:"+str(int(pow(-1,i+1)))+"..."+"now, shift is:"+str(shift)+"...")
		var intended_move = loc+knight_array[shift]
		if tracking == false:
			enemyfrustration += 1
		if map2darray[intended_move.y][intended_move.x] == 0:
			var absolute_x = abs(intendedloc.x - intended_move.x)
			var absolute_y = abs(intendedloc.y - intended_move.y)
			var is_compass_move = (absolute_x == 0 or absolute_y == 0)
			var is_elephant_move = (absolute_x ==  absolute_y)
			if absolute_x + absolute_y == 0:
				is_compass_move = false
				is_elephant_move = false
			if enemyrank < 2 or is_elephant_move == false:
				if enemyrank >= 2 or is_compass_move == false:#(intendedloc - intended_move).length() != 1.0:
					var get_point = mapstar.get_closest_point((intended_move+Vector2(0.5,0.5))*tilesize,true)
					if skystar.has_point(get_point) == true:
						if ((movebias == COMPASSMOVE and is_compass_move == true) or (movebias == ELEPHANTMOVE and is_elephant_move == true) or include_disabled == true or (skystar.is_point_disabled(get_point) == false and movebias == KNIGHTMOVE) or current_target_loc == intended_move):
			#				print("Debug: found point, but continuing to end.")
	#						if skystar.is_point_disabled(get_point) == true and include_disabled == false:
	#							print("Also, this was a move onto a disabled point due to it being an intended target.")
							if return_point == true:
								return get_point
							else:
								return int(shift)
						elif first_acceptable_move == -1 or knight_array.find(current_target_loc-intended_move) > -1:
							first_acceptable_point = mapstar.get_closest_point((intended_move+Vector2(0.5,0.5))*tilesize,true)
							first_acceptable_move = shift
				else:
					not_ideal_point = mapstar.get_closest_point((intended_move+Vector2(0.5,0.5))*tilesize,true)
					not_ideal_shift = shift
			else:
				not_ideal_point = mapstar.get_closest_point((intended_move+Vector2(0.5,0.5))*tilesize,true)
				not_ideal_shift = shift
	var risk_range = 1.9
	if Playervariables.attackmaxrange > 1:
		risk_range = sqrt(Playervariables.attackmaxrange*2)
	if (loc-intendedloc).length() <= risk_range:#If the imp is too close to a player, make a move even if it isn't a great one.
		if return_point == true:
			return not_ideal_point
		else:
			return int(not_ideal_shift)
	elif movebias != KNIGHTMOVE:
		if return_point == true:
			return first_acceptable_point
		else:
			return first_acceptable_move
	var point = -1
	return point
#	for i in range(8):
#		var attempt_point = loc + Vector2(1,2)

enum {REGULARDEATH,SWEETDEATH}
func consider_dying(deathtype=REGULARDEATH,allowheal = false): #right now doesn't drop items??
	enemyactive = false
	for enemy in mainscene.get_node("enemies").get_children():
		enemy.untarget(self)
	fadeout = false
	$healthouter.visible = false
	if mainscene.currentenemylock == self:
		mainscene.get_node("CanvasLayer2/Dialogue/ConversationV1").unscroll(false,true)
		mainscene.currentenemyscrollloc = Vector2(-5,-5)
	$enemymoving.stop()
	handlepointcalc()
	match monsternum:
#		FOXGIRL:
#			print(allowheal)
#			if allowheal == true and mainscene.mapgenlocation != Playervariables.Mission4:
#				Playervariables.questcondition = clamp(Playervariables.questcondition-1,0,99)
#				if Playervariables.questcondition <= 0:
#					print(Playervariables.questcondition)
#					mainscene.reset_hotsprings(false)
#			if shrinenum > -1:
#									mainscene.call("encounterresult","defeated",1)
#				print_debug("FOXFIRE DOES NOT WORK RIGHT NOW")
#										mainscene.foxfire(-1,locv,self) #note: if you can de-shrine a fox, make this happen WHEN you de-shrine it, not here..
		WEREWOLF:
#			if GEHENNA == true:
#				mainscene.dropitemondefeat(-7,locv)
			if mainscene.mapgenlocation == Playervariables.WolfCombat:
				mainscene.spawn_exit(loc)
				Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] = max(Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL],Playervariables.tutorialstages.ROUTEA11)
				Playervariables.saveprogress()
			if use_extra == true:
				use_extra = false
				extra_effect.visible = false
			if mainscene.hypnosisorigin == self:
				mainscene.hypnosisorigin = null
		RAMGIRL:
			if mainscene.mapgenlocation == Playervariables.Mission4 and TargetType != Target.ONLYENEMY:
				Playervariables.questcondition = clamp(Playervariables.questcondition-1,0,99)
				if Playervariables.questcondition <= 0:
					get_node("/root/Master").call("update_quest",0,false)
					get_node("/root/Master").call("update_quest",1,false)
					get_node("/root/Master").call("update_quest",2,true)
#										print("Quest condition reduced defeating ramgirls?" + str(Playervariables.questcondition))
			if mainscene.hypnosisorigin == self:
				mainscene.hypnosisorigin = null
	if mainscene.lastdrawID == self:
		mainscene.lastdrawID = null
	$shadow.visible = false
#							hideunhidequip(false,monsternum,true)
	$quip.visible = false
	$AnimationFlinch.stop()
	if held_object != null:
		hold_object(false,true)
	var defeatsize = 1
	match monsternum:
		RAMGIRL,FOXGIRL:
			defeatsize = 1.2
		WEREWOLF:
			defeatsize = 1.6
	if EnemyCategory != MINION and TargetType != Target.ONLYENEMY and maxhealthvalue > 0:
		if allowheal == false:
			mainscene.maxhealthincrement -= maxhealthvalue
		else:
			mainscene.create_sparkle(locv,3,false,defeatsize,Color(0.3,1,0.6,0.7))
			var healreturn = float(maxhealthvalue)#float(maxhealth)/2
			mainscene.defeat_heal(locv,healreturn)
			mainscene.enemiesdefeated += 1
			mainscene.maxhealthdefeated += maxhealthvalue
			maxhealthvalue = 0
		if mainscene.rewardgiven == false and mainscene.maxhealthdefeated >= clamp(mainscene.tiernums[Playervariables.tier],1,mainscene.maxhealthincrement):
			if drop_item_not_escalate == true:
				mainscene.dropitemondefeat(locv)
			else:
				mainscene.rewardgiven = true
				get_node("/root/Master").call("offer_rewards")
	if deathtype == REGULARDEATH or EnemyCategory == MINION:
		$AnimationPlayer2.advance(9)
		$AnimationPlayer2.play("death")
	elif deathtype == SWEETDEATH:
		remove_from_group("Turnends")
		remove_from_group("Attackers")
		remove_from_group("Quippers")
		$CanvasLayer3/raisezlevel.visible = false
	#	$rank.visible = false
		if monsternum == CATGIRL:
			remove_from_group("Catgirls")
		disablequip()
		if Playervariables.debugmodeon == true:
			mainscene.enemy_point(uniqueenemyID,-1)
		$AnimationPlayer2.advance(9)
		$AnimationPlayer2.playback_speed = 0.25
		emotion(2)
		self.z_index -= 5 #arbitrary number
		if monsternum != WEREWOLF:
			$AnimationPlayer2.play("fadeout")
		else:
			$AnimationPlayer2.play("fadeoutpitch")

var avoidinc = 0
func newroampoint():
	if permatracking == true and roampoint != -1:
		return
	var intendroampoint = null
	if dummy == true:
		if map2darray[loc.y][loc.x+1] > 0:
			intendroampoint = loc - Vector2(2,0)
		else:
			intendroampoint = loc + Vector2(2,0)
	elif limitroam == true:
		var pursuitcheck = (targetloc-loc).length() < (randf()*aggressiveness) *25
		if pursuitcheck == true and (targetloc-loc).length() < (randf()*aggressiveness)*25:
			limitroaming(false)
		if passive == false and hunter == true and pursuitcheck == true:
			intendroampoint = Vector2((randi()%int(clamp(boundarysize.x,1,9999)))+boundarymin.x,(randi()%int(clamp(boundarysize.y,1,9999)))+boundarymin.y)
			intendroampoint = Vector2(floor(intendroampoint.x+targetloc.x/2),floor(intendroampoint.y+targetloc.y/2))
		else:
			intendroampoint = Vector2((randi()%int(clamp(boundarysize.x,1,9999)))+boundarymin.x,(randi()%int(clamp(boundarysize.y,1,9999)))+boundarymin.y)
			var intendroamx = (randi()%int(clamp(boundarysize.x,1,9999)))+boundarymin.x
			if (intendroamx - loc.x) > enemyspeed*2:
				intendroamx = loc.x + ((intendroamx-loc.x)/2)
			var intendroamy = null
			if pursuitcheck == false:
				intendroamy = loc.y + (randi()%3) -1
			else:
				intendroamy = (randi()%int(clamp(boundarysize.y,1,9999)))+boundarymin.y
				if (intendroamy - loc.y) > clamp(enemyjumpspeed-1,1,999):
					var ratio = clamp(enemyjumpspeed-1,1,999) / clamp((intendroamy - loc.y),0.01,999)
					intendroamy = loc.y + ((intendroamy-loc.y)*ratio)
			intendroampoint = Vector2(intendroamx,intendroamy)
	else:
		var randx = randi()%int(clamp(boundarysize.x,1,9999))
		var randy = randi()%int(clamp(boundarysize.y,1,9999))
		intendroampoint = Vector2(randx+boundarymin.x,randy+boundarymin.y)
#		intendroampoint = Vector2((randi()%int(clamp(boundarysize.x,1,9999)))+boundarymin.x,(randi()%int(clamp(boundarysize.y,1,9999)))+boundarymin.y)
		if hunter == true:
			if randf() < aggressiveness:
				intendroampoint = Vector2(floor(intendroampoint.x+targetloc.x/2),floor(intendroampoint.y+targetloc.y/2))
	if monsternum == RAMGIRL: #keep that ram-girl from jumping all over the place?
		intendroampoint += Vector2(1,3)
	elif monsternum == FOXGIRL and enemyfallspeed < 2:
		intendroampoint -= Vector2(0,4)
	elif monsternum == DRAGONHARPY:
		if TargetNotPlayer == false and passive == true:
			intendroampoint -= Vector2(0,4+randi()%2)
	if avoidpoint.x > 0 and (intendroampoint-avoidpoint).length() < 1.1:
		avoidinc += 1
		if avoidinc < 3:
			newroampoint() #recursive
		else:
			avoidpoint = Vector2(-9,-9)
			newroampoint()
	else:
		if Playervariables.debugmodeon == true:
			mainscene.enemy_point(uniqueenemyID,roampoint)
		if MovementType != JUMPING:
			roampoint = skystar.get_closest_point((intendroampoint*tilesize) + Vector2(0.5,0.5))
		else:
			roampoint = astar.get_closest_point((intendroampoint*tilesize) + Vector2(0.5,0.5),true)
		avoidinc = 0
	#			roampointv = astar.get_point_position(roampoint)
		if tracking == false:
			enemyfrustration = 0
	if Playervariables.debugmodeon == true:
		mainscene.enemy_point(uniqueenemyID,roampoint)

func enemyfall():
#	skystar = mainscene.get("skystar")
	var fallamount = enemyfallspeed
	if capture_state == EGGCAPTURE:
		if capture_id_weakref != null and capture_id_weakref.get_ref() != null:
			fallamount = 3
		else:
			return
	if fallamount > 0:
		var drag_player = false
		if Playervariables.playerstunstate == Playervariables.SCISSORLOCKED and monsternum == DRAGONHARPY and mainscene.get("playerloc") == loc:
			drag_player = true
		var oldpoint = mapstar.get_closest_point(locv,true)
		enemyground = map2dpatharray[loc.y+1][loc.x]
		for _n in fallamount:
			if enemyground == 0:
				loc += Vector2(0,1)
				locv += Vector2(0,tilesize)
				enemyground = map2dpatharray[loc.y+1][loc.x]
			else:
				break
		var newpoint = mapstar.get_closest_point(locv,true)
		if oldpoint != newpoint:
			if MovementType == JUMPING:
				_move_enemy([oldpoint,newpoint],1,[-3,2]) #-3 indicates 'do something after movement and don't flip
			else:
				_move_enemy([oldpoint,newpoint],1,[-4,2]) #-4 truly TRULY indicates "do nothing"
			if drag_player == true:
				mainscene.enemy_drags_player(newpoint,self)
			if randf() > 0.8:
				if Playervariables.debugmodeon == true:
					mainscene.enemy_point(uniqueenemyID,roampoint)
				roampoint = -1
			else:
				enemyfrustration += 3


func final_milk_check(distance):
	if Playervariables.playerbustsize < 3:
		return false
	var milkvalue = mainscene.playerdebuffarray[Playervariables.MILK-1]
	var aimloc = mainscene.get("playerloc")
	if milkvalue >= 4 and (aimloc-loc).length() <= distance and (map2darray[aimloc.y][aimloc.x] == 0 or aimloc == loc):#0.3:
		debuff_bounce(Playervariables.MILK)
		return true
	else:
		return false
func final_hypnosis_check():
	if enemydebuffarray[1] <= 0:
		return true
	else:
		debuff_bounce(Playervariables.HYPNOSIS)
		return false

func handlepointcalc():
	if capture_state == EGGCAPTURE:
		if capture_id_weakref != null and capture_id_weakref.get_ref() != null:
			if capture_id.visible == false:
				return
		else:
			free_capture()
			print("Slipped outside of egg during handle poitn calc?")
	var ban_player_space = false
	if TargetNotPlayer == false:
		ban_player_space = mainscene.force_occupy_space
	if enemyactive == false and fadeout == false:
		mainscene.call("astarenabledisable",loc,-1,disableloc,disablepoint,self)
	elif disableloc != loc:
		var i = 0
		var failed = true
		while failed == true:
			if i >= 4:
				_fade_out()
				break
			failed = _evacuate_space(ban_player_space)
			i += 1

func _evacuate_space(ban_player_space):
	var closestpoint
	var failed = false
	var starused = skystar
	if MovementType == GHOSTLY and map2darray[loc.y][loc.x] == 1:
		starused = mapstar
	closestpoint = starused.get_closest_point(locv,true)
	if mainscene.enemy2darray[loc.y][loc.x] == null and (ban_player_space == false or loc != mainscene.playerloc):
		failed = mainscene.call("astarenabledisable",loc,closestpoint,disableloc,disablepoint,self)
	elif mainscene.enemy2darray[loc.y][loc.x] != self:
		if ignoreobstruction == true:
			skystar = mainscene.get("skystar")
		var newpoint
		newpoint = starused.get_closest_point(locv)
		if newpoint == closestpoint:
			starused.set_point_disabled(closestpoint,true)
			newpoint = starused.get_closest_point(locv)
			starused.set_point_disabled(closestpoint,false)
		var distance = (mapstar.get_point_position(newpoint) - locv).length()
		if distance > 185 or distance < 20 or newpoint == closestpoint:
			if Playervariables.debugmodeon == true:
				print("couldn't reach point or found error in space evacuation, returning")
			_fade_out() #can never fail, so no trigger
		else:
			locv = mapstar.get_point_position(newpoint)
			loc = (locv/tilesize) - Vector2(0.5,0.5)
			_move_enemy([closestpoint,newpoint],1,[-1,0])
			failed = mainscene.call("astarenabledisable",loc,newpoint,disableloc,disablepoint,self)
		if ignoreobstruction == true:
			skystar = mainscene.get("skystarenemy")
	else:
		print("Debug, found a case where the enemy tries to dodge from its own tile! Told it not to do that.")
	return failed

#fadeout=true, enemyactive=true: this should never happen...?
#fadeout=false, enemyactive=true: normal state.
#fadeout=true, enemyactive=false: enemy is temporarily hiding.
#fadeout=false, enemyactive=false: enemy is defeated.
func _fade_out():
	if fadeout == false and enemyactive == true:
		fadeout = true
		enemyactive = false
		mainscene.call("astarenabledisable",loc,-1,disableloc,disablepoint,self) #can never fail, so no trigger
		$AnimationPlayer2.stop(true)
		if monsternum != WEREWOLF:
			$AnimationPlayer2.play("fadeout")
		else:
			$AnimationPlayer2.play("fadeoutpitch")

var ready_kiss = false
var kiss_id = null
var kiss_id_weakref = null
var fadeout = true
var specialtargetlock = false
var readybreak = true #you can't ready twice in a row, idiot!
func _get_ready(): #note right now enemy can ready mid-air
#	if enemydebuffarray[1] > 0:
#		enemydebuffarray[1] -= 1
	if emotionlock == true and monsternum == DRAGONHARPY:
		emotionlock = false
	if fadeout == true:
		fadeout = false
		enemyactive = true
		update_aura()
#		if tracking == false:
#			$aura.visible = false
#		else:
#			$aura.set_self_modulate(Color(0.7,1,0,0.3))
#		$aura/AnimationPlayer.stop()
		$AnimationPlayer2.stop()
		if monsternum != WEREWOLF:
			$AnimationPlayer2.play_backwards("fadeout")
		else:
			$AnimationPlayer2.play_backwards("fadeoutpitch")
		handlepointcalc()
		ready_kiss = false
#		$rank.visible = true
	elif monsternum == GHOSTFOX:
		if mainscene.mapgenlocation == Playervariables.Shrine:
			pass
		elif TargetNotPlayer == true and passive == false and final_hypnosis_check() == true and knockbacks.size() == 0 and enemynegativedebuffarray[0] <= 0:
			targetloc = get_target_loc()
			if (targetloc-loc).length() < 1.9 and capture_state == NOCAPTURE:
				loc = targetloc
				locv = (loc+Vector2(0.5,0.5))*tilesize
				if health > 4:
					health = 4
				_move_enemy([mapstar.get_closest_point(locv,true)],1,[0,4])
				mainscene.call("attacking",[loc],Vector2(Playervariables.SPIRIT,ceil(health/2)),self,[Vector2(Playervariables.POSSESSION,health)],allyarray,4,"Kitsune Possession")
				$AnimationPlayer2.playback_speed = 0.4
				health -= 4
				mainscene.get_node("sfx/foxobj").volume_db = -11+(health*2)
				mainscene.get_node("sfx/foxobj").play()
				handle_health(true)
				_move_enemy([mapstar.get_closest_point(locv,true)],1,[-1,0])
				if enemyactive == true:
					consider_dying(REGULARDEATH)
			else:
				health -= 1
				handle_health(true)
		else:
			health -= 1
			handle_health(true)
	elif ready_kiss == true:
		ready_kiss = false
		if enemyactive == true and kiss_id_weakref != null and kiss_id_weakref.get_ref() != null and kiss_id.enemyactive == true:#in theory, can attack faded out enemies?
#			var before_health = kiss_id.health
			if monsternum == WEREWOLF:
				kiss_id.call("taking_damage",[kiss_id.loc],Vector2(Playervariables.SWEET,1),self,[],[WEREWOLF],-2,"Breastfeeding")
			elif monsternum == RAMGIRL:
				kiss_id.call("taking_damage",[kiss_id.loc],Vector2(Playervariables.SWEET,1),self,[],[RAMGIRL],-2,"Kiss")
				if kiss_id_weakref != null and kiss_id_weakref.get_ref() != null:
					if kiss_id.enemyactive == false and ("MinionOwner" in kiss_id and kiss_id.MinionOwner == null):# or kiss_id.health <= 0 and before_health > 0:
						mainscene.heal_enemy(self,1)
				else:
					print("ERROR with infighting kiss, enemy is un-id-ing itself as it dies?")
			if "enemydebuffarray" in kiss_id:
				kiss_id.enemydebuffarray[Playervariables.HYPNOSIS-1] = clamp(kiss_id.enemydebuffarray[Playervariables.HYPNOSIS-1] - 1,0,999)
			mainscene.get_node("sfx/abilitysfx/Draining Kiss").position = locv
			mainscene.get_node("sfx/abilitysfx/Draining Kiss").play()
#	elif monsternum == DRAGONHARPY:
#		enemyfallspeed = 2
	elif revenge == true and monsternum == CATGIRL and enemyactive == true and lewd == true and capture_state == NOCAPTURE:
		if mainscene.get("playerloc") == loc:
			if final_milk_check(0.3) == true:
				if Playervariables.playerstunstate == Playervariables.NOSTUN:
					Playervariables.playerstunstate = Playervariables.BUSTLOCKED
			elif TargetNotPlayer == false and (passive == false or Playervariables.curseditemdict[Playervariables.PAWS] == true):# and enemydebuffarray[Playervariables.HYPNOSIS-1] <= 0:
				if Playervariables.playeroutfit[1] > 0:#Playervariables.playerstatedict["Shredded"] == 0: #nothing even uses shredded on/off...
					if passive == false and final_hypnosis_check() == true:#enemydebuffarray[Playervariables.HYPNOSIS-1] <= 0:
						mainscene.call("attacking",[loc],Vector2(1,1),self,[Vector2(Playervariables.MARKED,2)],allyarray,4,"Shredder")
					else:
#						if enemydebuffarray[Playervariables.HYPNOSIS-1] > 0:
#							debuff_bounce(Playervariables.HYPNOSIS)
						mainscene.call("attacking",[loc],Vector2(1,0),self,[Vector2(Playervariables.MARKED,2)],allyarray,4,"Disrobe")
				elif final_hypnosis_check() == true:#enemydebuffarray[Playervariables.HYPNOSIS-1] <= 0:
					Playervariables.playerstunstate = Playervariables.PUSSYLOCKED
					mainscene.caught()
					if randf() > 0.3:
						failedtactic = true #not actually failed, just doesn't wanna do it again, rarely will do it multiple times
#			else:
#				debuff_bounce(Playervariables.HYPNOSIS)
			attacked = true
		else:
			if randf() > 0.6:
				failedtactic = true #cat-girl will not continue to do the same shit over and over
	elif monsternum == WEREWOLF and shredded == true:
		if (mainscene.get_node("cummap").get_cellv(loc) in [7,8,9,10,11]) == true or (mainscene.get_node("destructiblefeatures").get_cellv(loc) in [16,17]) == true:
			can_reclothe = true
			unshred()
#	handlepointcalc()
	if GEHENNA == true:
		GEHENNAfirst = true
	relativeloc = Vector2(abs((targetloc - loc).x),abs((targetloc - loc).y))
	if readybreak == true or enemydebuffarray[6] > 0:
		readybreak = false
		if enemydebuffarray[6] > 0:
			debuff_bounce(Playervariables.HEAT)
	elif enemyactive == true and fadeout == false and passive == false and capture_state == NOCAPTURE and (TargetNotPlayer == true or friendly_node == null):
		if mainscene.get_node("destructiblefeatures").get_cellv(loc) == 15:
			if monsternum == WEREWOLF:
				health += 1
				embraces += 1
				handle_health()
				assignquip(22*100,null,"normal",false,6)
			else:
				mainscene.attacking([loc],Vector2(Playervariables.SWEET,1),get_parent().get_parent(),[Vector2(9,2)],Playervariables.races,-2,Playervariables.tempname.strip_escapes()+"'s Urn")
				mainscene.get_node("destructiblefeatures").set_cellv(loc,0)
		if dict_readiedattack != null:
	#		var temptargetlock = 0
			if monsternum == DRAGONHARPY and held_object != null:
				pass
			elif monsternum == WEREWOLF and enemyfrustration >= 5 and specialmovecooldown < 3: #pitch's second special
				enemyfrustration = 0
				specialtargetlock = true
				specialmovecooldown = 5
			elif relativeloc.x + relativeloc.y < 10:
				if monsternum == DRAGONHARPY and relativeloc.y > abs(relativeloc.x):
					attackdict = dict_superattack
				else:
					attackdict = dict_readiedattack
				var directionaleight = 1
				var attackdictattackarray = attackdict.get(str(directionaleight)+"a")
				if attackdictattackarray.find(relativeloc) != -1:
	#				temptargetlock = 1
	#			for n in attackdictattackarray.size():
	#				if relativeloc == attackdictattackarray[n]:
	#					temptargetlock = 1
	#					break
	#			if temptargetlock == 1:
	#				skystar = mainscene.get("skystar")
					xdirection = sign(targetloc.x - loc.x)
					if xdirection != 0:
	#					jumpcost = 0
	#					jump = []
						crash = false
						var attackdictmovearray = attackdict.get(str(directionaleight)+"m") #only works for 2-dir attacks?
						for n in min(attackdictmovearray.size(),attackdict.get("types")[0]):
							nextmove = attackdictmovearray[n]
							nextmove.x = nextmove.x*xdirection
							nextmovediff = loc + nextmove
	#						nextmovediffv = (nextmovediff+Vector2(0.5,0.5))*tilesize
							if map2darray[nextmovediff.y][nextmovediff.x] != 0:# and crash == false:
	#							jumpcost += 1
	#							jump.append(skystar.get_closest_point(nextmovediffv,true))
	#						else:
								crash = true
								break
						if crash == false:
							flinchimmunity = false
							enable_ready()
							facing = xdirection
							targetprevloc = targetloc
				elif monsternum == FOXGIRL and specialmovecooldown == 0 and (tracking == true or relativeloc.x + relativeloc.y < 6):
					enable_ready()
					facing = xdirection
					targetprevloc = targetloc
			if ready == true:
				emotion(0)
				readybreak = true
	if monsternum == IMP:
		if specialmovecooldown > 0:
			enable_ready()
	specialmovecooldown = clamp(specialmovecooldown-1,0,100)
	if GEHENNA == true:
		specialmovecooldown = clamp(specialmovecooldown-1,0,100)
	if enrage == true and specialmovecooldown == 0:
		make_angry(false)
	if monsternum != FOXGIRL and enemyfallspeed != basefallspeed:
		if enemycurrenteffectnode != null:
			enemycurrenteffectnode.queue_free()
			enemycurrenteffectnode = null
		enemyfallspeed = basefallspeed
	if monsternum == FOXGIRL and passive == false and TargetNotPlayer == false and relativeloc.length() < 1.9 and mainscene.playerdebuffarray[Playervariables.POSSESSION-1] > 0 and ready == false and randf() > 0.6:#(randf() > 0.75 or (final_hypnosis_check() == false and hadhypno == true)):
		revenge = true
		assignquip(6*100,null,"normal",false,2)
	else:
		revenge = false
	incrementenemydebuffs()
	knockback_skip_move = false

func update_aura():
	if ready == true:
		enable_ready()
	elif tracking == true:
		enable_tracking()
func enable_ready():
	if dummy == false:
		ready = true
		$ready.emitting = true
		$ready.visible = true
		$aura.visible = true
		$aura/AnimationPlayer.play($aura/AnimationPlayer.get_assigned_animation())
func enable_tracking():
	if dummy == false:
		if tracking == false:
			tracking = true
			new_sparkle_enemy(6,false)
		if ready == false:
			$aura.visible = true
			$aura.set_self_modulate(Color(0.7,1,0,0.3))
func disable_tracking():
	if permatracking == false:
		tracking = false
		if ready == false:
			$aura.visible = false
func disable_ready():
	ready = false
	$ready.emitting = false
	$ready.visible = false
	$aura/AnimationPlayer.stop()
	if tracking == false:
		$aura.visible = false
	else:
		$aura.set_self_modulate(Color(0.7,1,0,0.3))

var temp_scene_ready_value = false
var attackcount #how many of the attacks to use based on if crashed or not
var xdirection #which way to the player?
var jumpcost #enemy jumpcost ver see main
var jump #enemy jump ver see main
#var ghostjump #enemy jump but it goes through walls now
var crash #ends movement early sometimes
var attackdict #chooses which attack to use.
var nextmove #see main
var nextmovediff #just the loc version
var nextmovediffv #loc version but vector now
var mapstar #for ghosts I guess
var skystar# = mainscene.get("skystar") #steals the skystar grid from parent
var astar# = mainscene.get("astar")#steals the astar grid from parent
var jumpv # I guess it's like jump but v
#var attacktargetarray # for multihit
#var specialbuff = false
var debuffnextattack = []
#var attemptseal = false
var targetshit = 0
func attack_basic():
#	skystar = mainscene.get("skystar")
	if attackdict == null or capture_state == EGGCAPTURE:
		return
	if monsternum == DRAGONHARPY:
		if held_object != null:
			return
		elif emotionlock == true:
			emotionlock = false
		if (TargetNotPlayer == false and mainscene.capture_state == EGGCAPTURE) or (CurrentTarget != null and "capture_state" in CurrentTarget and CurrentTarget.capture_state == EGGCAPTURE):
			return
#	if enemydebuffarray[1] > 0:
	if attackdict.get("types")[2] > 0:
		if final_hypnosis_check() == false:
	#		debuff_bounce(Playervariables.HYPNOSIS)
#			if monsternum == WEREWOLF:
#				incrementenemydebuffs()
			return
	if attackdict.get("types")[3] == 2 or attackdict.get("types")[3] == 5:
		if ready == true:
			xdirection = facing
		else:
			xdirection = sign(targetloc.x - loc.x)
		if xdirection == 0:
			xdirection = ((randi()%2) *2) -1
		if monsternum == RAMGIRL and attackdict == dict_special:
			if abs(xdirection - sign(get_target_loc().x - loc.x)) > 1: #only triggers if directions are OPPOSITE.
				return #this prevents ram-girl from firing her attacks backwards
	else:
		xdirection = 1
	jumpcost = 0
	jump = []
	crash = false
	if monsternum != IMP:# or quickshot == true:
		if ready == true and monsternum == WEREWOLF:
			enemyblockarray.append([5,0,2])
			updateenemyblock()
		disable_ready()
#	ready = false
#	$ready.emitting = false
#	$ready.visible = false
	if monsternum == CATGIRL:
#		var milkvalue = mainscene.playerdebuffarray[Playervariables.MILK-1]
#		if milkvalue >= 4:
		if final_milk_check(1.85) == true:
			revenge = false
			loc = mainscene.get("playerloc")
			locv = (loc+Vector2(0.5,0.5))*tilesize
			_move_enemy([mapstar.get_closest_point(locv,true)],1,[0,4])
			if Playervariables.playerstunstate == Playervariables.NOSTUN:
				Playervariables.playerstunstate = Playervariables.BUSTLOCKED
			attacked = true
			return
	elif monsternum == WEREWOLF:
		permatracking = false
	var directionaleight = 1
	match attackdict.get("types")[3]:
		5: #if it's a 4-direction DIAGONAL ability (i.e. juggernaut)
			if loc.y - targetloc.y < 0:
				directionaleight = 2
			if loc.x - targetloc.x > 0:
				xdirection = -1
			else:
				xdirection = 1
		4: #4-direction horizontal/vertical abilitiy
			if relativeloc.x > relativeloc.y:
				if loc.x - targetloc.x > 0:
					directionaleight = 3 #aiming left
			elif loc.y - targetloc.y > 0:
				directionaleight = 4 #aiming up!
			else:
				directionaleight = 2 #aiming down
		8: #eight directions. WARNING, bypasses CALCULATE ATTACK, not good for ordinary readied attacks
			directionaleight = rad2deg(loc.angle_to_point(targetloc)-0.392699)#-pi/8
			if sign(directionaleight) == -1:
				directionaleight += 360
			directionaleight = int(ceil(directionaleight/45))
			xdirection = 1
		9:
			if monsternum == IMP:
				if ready == true and enemyrank == DIAMOND:
					directionaleight = locate_nearest_knight_point(targetloc,false,false,ELEPHANTMOVE) + 1
				elif ready == true and enemyrank == HEART:
					directionaleight = locate_nearest_knight_point(targetloc,false,false,COMPASSMOVE) + 1
				else:
					directionaleight = locate_nearest_knight_point(targetloc,false) + 1
				if directionaleight <= 0:
#					quickshot = true
					return
			else:
				directionaleight = rad2deg(loc.angle_to_point(targetloc)-0.523598)#-pi/6
				if sign(directionaleight) == -1:
					directionaleight += 360
				directionaleight = int(ceil(directionaleight/45))
			xdirection = 1
	var attackdictmovearray = attackdict.get(str(directionaleight)+"m").duplicate()
	if attackdict.get("special")[3] == 1: #juggernaut
		attackdictmovearray.resize(int(clamp(abs(ceil((targetloc.y-loc.y)*0.35)),0,attackdictmovearray.size())))
		var border = mainscene.get("borderthickness")
		for i in attackdictmovearray.size():
			attackdictmovearray[attackdictmovearray.size()-1].x *= xdirection
		for i in attackdictmovearray.size():
			var lastmove = attackdictmovearray[attackdictmovearray.size()-1]+loc
			if lastmove.y > border and lastmove.y < mapsize.y+border and lastmove.x > border and lastmove.x < mapsize.x+border and map2darray[lastmove.y][lastmove.x] == 0:
				break
			else:
				attackdictmovearray.remove(attackdictmovearray.size()-1)
				crash = true
		for vector in attackdictmovearray:
			jumpcost += 1
			jump.append(skystar.get_closest_point((vector+Vector2(0.5,0.5))*tilesize+locv,true))
	else:
		for n in attackdictmovearray.size():
			nextmove = attackdictmovearray[n]
			nextmove.x = nextmove.x*xdirection
			nextmovediff = loc + nextmove
			nextmovediffv = (nextmovediff+Vector2(0.5,0.5))*tilesize
			if map2darray[nextmovediff.y][nextmovediff.x] == 0:# and crash == false:
				jumpcost += 1
				jump.append(skystar.get_closest_point(nextmovediffv,true))
			else:
				crash = true
				break
	if jumpcost >= attackdict.get("types")[0]:
		if jumpcost < 1:
			jumpv = locv
		else:
			jumpv = skystar.get_point_position(jump[jumpcost-1])
		var namestring = attackdict.get("name")
		if Playervariables.modarray[Playervariables.mods.TRANSLATION] == true:
			if Playervariables.transdict["attacknames"].has(namestring):
				namestring = Playervariables.transdict["attacknames"][namestring]
		var attacktargetarray = []
		var attackdictattackarray = attackdict.get(str(directionaleight)+"a").duplicate()
		if crash == true and attackdict.get("types")[0] != -1 and attackdict.get("types")[1] < attackdictattackarray.size():
			attackdictattackarray.resize(attackdict.get("types")[1])
		for n in attackdictattackarray.size():
			nextmove = attackdictattackarray[n]
			nextmove.x = nextmove.x*xdirection
			nextmovediff = loc + nextmove
			attacktargetarray.append(nextmovediff)
		if attacktargetarray.size() > 0 and attackdict.get("special")[0].find(Playervariables.DEPLOYABLE) != -1:
			match attackdict.get("special")[1]:
				3:mainscene.foxfire(1,(nextmovediff+Vector2(0.5,0.5))*tilesize,self,enemyrank)
		if attackdict.get("special")[0].find(Playervariables.ECHO) != -1 and enemyrank > 0: #only elite fox-girl gets double
			#BIG WARNING: This is incapable of hitting the same square twice, unlike player's moves.
			#THEREFORE if you ever give other enemies ECHO, rework this.
			locv = jumpv #watch out! This is very important that I made the enemy move BEFORE attacking.
			loc = Vector2(floor(locv.x/tilesize),floor(locv.y/tilesize))
			match attackdict.get("types")[3]:
				8,9:
					directionaleight = (int(directionaleight) % 8) +1
				5:
					directionaleight = (int(directionaleight) % 2) +1
				4:
					directionaleight = (int(directionaleight) % 4) +1
				2:
					xdirection = xdirection *-1
			attackdictattackarray = attackdict.get(str(directionaleight)+"a").duplicate()
			if crash == true and attackdict.get("types")[0] != -1 and attackdict.get("types")[1] < attackdictattackarray.size():
				attackdictattackarray.resize(attackdict.get("types")[1])
			for n in attackdictattackarray.size():
				nextmove = attackdictattackarray[n]
				nextmove.x = nextmove.x*xdirection
				nextmovediff = loc + nextmove
				attacktargetarray.append(nextmovediff)
			if attackdict.get("special")[0].find(Playervariables.DEPLOYABLE) != -1:
				match attackdict.get("special")[1]:
					3:mainscene.foxfire(1,(nextmovediff+Vector2(0.5,0.5))*tilesize,self,enemyrank)
			var newannounce = load(Mainpreload.AnnounceAttack).instance()
			add_child(newannounce)
			if quickshot == true:
				newannounce.announce(namestring,true,0.85)
			else:
				newannounce.announce(namestring,true,0.35)
		var finaldebuff = []
		if attackdict.has("debuff"):
			finaldebuff += attackdict.get("debuff")
		if enemyrank > 0:
			if attackdict.has("debuffe1"):
				finaldebuff += attackdict.get("debuffe1")
			if enemyrank > 1:
				if attackdict.has("debuffe2"):
					finaldebuff += attackdict.get("debuffe2")
			#if monsternum == FOXGIRL and specialbuff == true and attackdictattackarray.size() > 0: #if shika has her special buff, she adds a knockback 1 effect
		if attacktargetarray.size() > 0 and debuffnextattack.size() > 0:
			finaldebuff += debuffnextattack#append(Vector2(-2,2))
			debuffnextattack = []
#				specialbuff = false
			$tail.set_modulate(Color(1,1,1))
#			if monsternum == FOXGIRL:
#				attemptseal = true
		match attackdict.get("special")[2]:
			2: #sense ability. note it also gives the exact target loc.
				targetloc = get_target_loc()
				targetlocv = get_target_locv()
				if Playervariables.debugmodeon == true:
					mainscene.enemy_point(uniqueenemyID,roampoint)
				enable_tracking()
				enemyfrustration += 3
				roampoint = astar.get_closest_point(targetlocv)
			3: #enemy special buff. For shika, this gives her KNOCKBACK 1 on her next attack.
				if attackdict.has("debuffnextattack"):
					debuffnextattack += attackdict["debuffnextattack"]#append(Vector2(Playervariables.KNOCKBACK,2))
#				specialbuff = true
					$tail.set_modulate(Color(0.2,0.2,0.2))
#			get_tree().call_group("Attackable","_taking_damage",attacktargetarray,Vector2(attackdict.get("types")[4],attackdict.get("types")[2]),self,attackdict.get("special")[2],allyarray)
#			script below shows you that you may want to limit moves to within map boundaries..?
#		d	if jumpv.x > 0 and jumpv.y > 0 and jumpv.y < (mapsize.y*tilesize - 128) and jumpv.x < (mapsize.x*tilesize - 128):
		var before_loc = loc
		locv = jumpv #watch out! This is very important that I made the enemy move BEFORE attacking.
		loc = Vector2(floor(locv.x/tilesize),floor(locv.y/tilesize))
		if monsternum == CATGIRL:
			if final_milk_check(1.85) == true:
				revenge = false
				attacktargetarray = []
				loc = mainscene.get("playerloc")#get_target_loc()
				locv = (loc+Vector2(0.5,0.5))*tilesize
				jumpv = locv
				jumpcost += 1
				jump.append(skystar.get_closest_point(jumpv,true))
				if Playervariables.playerstunstate == Playervariables.NOSTUN:
					Playervariables.playerstunstate = Playervariables.BUSTLOCKED
		targetshit = 0
		var playerhealthbefore = Playervariables.playerresistance
		if enemyrank >= 2 and monsternum == DRAGONHARPY and attackdict == dict_superattack and (Playervariables.prefdict[Playervariables.pref.UNBIRTH] == true or Playervariables.prefdict[Playervariables.pref.PSUEDOPREGNANCY] == true):
			if held_object == null:
				var useenemy
				var minimumdistance = 1.9
				for target in attacktargetarray:
					if mainscene.playerloc == target:
#						print("Found playerloc.")
						if (target-loc).length() <= minimumdistance and mainscene.capture_state == NOCAPTURE:
#							print("considered player.")
							minimumdistance = (target-loc).length()
							useenemy = mainscene
#						else:
#							print("Denied because:" +str((target-loc).length())+"..."+str(mainscene.capture_state))
					elif mainscene.enemy2darray[target.y][target.x] != null:
						var enemycase = mainscene.enemy2darray[target.y][target.x]
						if (target-loc).length() < minimumdistance and "isenemy" in enemycase and enemycase.capture_state == NOCAPTURE:
							var valid = true
							for allynum in enemycase.allyarray:
								if allyarray.find(allynum) > -1:
									valid = false
									break
							if valid == true:
								minimumdistance = (target-loc).length()
								useenemy = enemycase
#				print("PLUNGETEST:"+str(minimumdistance))
				if useenemy != null:
					hold_object(true,false,useenemy)
		mainscene.call("attacking",attacktargetarray,Vector2(attackdict.get("types")[4],attackdict.get("types")[2]),self,finaldebuff,allyarray,-1,attackdict.get("name"))
		if monsternum != IMP or quickshot == true:
			var newannounce = load(Mainpreload.AnnounceAttack).instance()
			add_child(newannounce)
			if quickshot == true and monsternum != IMP:
				newannounce.announce(namestring,true,0.5)
			else:
				newannounce.announce(namestring,true,0)
		if monsternum == IMP:
			if loc == mainscene.playerloc and TargetNotPlayer == false and friendly_node == null:
				if enemyrank != CLUB or quickshot == true or ready == false:
					if temp_scene_ready_value == false:
						temp_scene_ready_value = true
					else:
						temp_scene_ready_value = false
						Playervariables.playerstunstate = Playervariables.IMPLOCKED
		recordattacktype.append(Vector2(attacktargetarray.size(),attackdict.get("types")[4]))
		recordattackarray += attacktargetarray
		if attackdict.get("special")[4] > 0:
			mainscene.call("movesoundeffect",attackdict.get("special")[4],locv,attackdict.get("name"))
		if attackdict.get("special")[0].find(5) != -1:
			incrementenemydebuffs()
			incrementenemydebuffs()
		if attackdict.get("special")[2] == Playervariables.WINDSTEP:
			enemyfallspeed = 0
			enemycurrenteffectnode = load("res://effects/windstep.tscn").instance()
			add_child(enemycurrenteffectnode)
			enemycurrenteffectnode.emitting = true
			enemyfrustration = clamp(enemyfrustration-2,0,10)
#		if attackdict.get("types")[3]:
#			var tempjump = jump.duplicate()
#			for i in tempjump.size():
#				tempjump[i] = ((mapstar.get_point_position(tempjump[i])-locv) / tilesize)
#			print(tempjump)
		if attackdict.get("special")[0].find(6) != -1: #these aren't elif statements because we'll update them later for multiple types
			var blockstat = attackdict.get("block")
			for blockarray in blockstat:
				var dupearray = blockarray.duplicate()
#				if dupearray[0] == 4:
#					if attackdict.get("types")[3] == 2:
#						dupearray[0] = int((xdirection-2)*-1)
#					elif attackdict.get("types")[3] == 4:
#						dupearray[0] = int(directionaleight) % 4
				dupearray[0] = 5
				enemyblockarray.append(dupearray)
			updateenemyblock()
		if attackdictattackarray.size() > 0:
			if position.x - targetlocv.x > 0:
				flip_h = true
				$tail.flip_h = true
			else:
				flip_h = false
				$tail.flip_h = false
			if use_extra == true and extra_effect != null:
				extra_effect.flip_h = self.flip_h
			if monsternum == IMP:
				var targetlength = (loc - targetloc).length()
				if self.frame == 1 and (before_loc - targetloc).length()+0.5 < targetlength:
					_move_enemy(jump,1,[-4,1])
				elif targetlength > 1.9:
#					if self.frame == 1 and #self.frame == 1 and quickshot == false and enemyrank > 0:
#						_move_enemy(jump,1,[-4,1])
#					else:
					_move_enemy(jump,1,[0,3])
				else:
					_move_enemy(jump,1,[0,4])
			else:
				_move_enemy(jump,1,[-3,4])
		else:
			if attackdictmovearray.size() > 0:
				_move_enemy(jump,1,[0,3])
			else:
				_move_enemy(jump,1,[-2,1])
		facing = xdirection
		match monsternum:
			WEREWOLF:#incrementenemydebuffs()
				if map2darray[loc.y+1][loc.x] > 0 and pitch_tar_check() == true:
					if mainscene.get_node("destructiblefeatures").get_cellv(loc) > -1:
						if mainscene.get_node("destructiblefeatures").get_cellv(loc) == 1:
							mainscene.get_node("destructiblefeatures").set_cellv(loc,0)
					elif (mainscene.get_node("cummap").get_cellv(loc) in [7,8,9,10,11]) == true:
						mainscene.get_node("cummap").set_cellv(loc,-1)
						mainscene.get_node("destructiblefeatures").set_cellv(loc,16+randi()%2)
					else:
						mainscene.get_node("cummap").set_cellv(loc,7+randi()%5)
				if attackdict.get("name") == Playervariables.Move107["name"]:
					assignquip(21*100,null,"normal",false,6)
				elif attackdict.get("name") == Playervariables.Move111["name"]:
					assignquip(20*100,null,"normal",false,6)
			DRAGONHARPY:
				if attackdict == dict_instantattack:
					enemyfallspeed = 0
			RAMGIRL:
				if attackdict == dict_special:
					emotion(6)
					emotionlock = true
			FOXGIRL:
#				if attemptseal == true:
#					attemptseal = false
#					if targetshit > 0:
				if playerhealthbefore > Playervariables.playerresistance:
#					if mainscene.playerdebuffarray[Playervariables.POSSESSION-1] > 0:
#						mainscene.get_node("CanvasLayer2/Dialogue").updatedebufftiles()
					var randarray = [0,1,2]
					randarray.shuffle()
					for i in range(3):
						if Playervariables.sealsarray[randarray[i]] <= 0:
							mainscene.get_node("CanvasLayer2/Dialogue/ConversationV1").get("rulestalker").apply_seal(randarray[i])
							if Playervariables.sealsarray[randarray[i]] > 0:
								assignquip((20+randarray[i])*100,null,"normal",false,3)
								break
#					if Playervariables.sealsarray[Playervariables.SEAL] == 0:
#						mainscene.get_node("CanvasLayer2/Dialogue/ConversationV1").get("rulestalker").apply_seal(Playervariables.SEAL)
#						if Playervariables.sealsarray[Playervariables.SEAL] > 0:
#							assignquip(21*100)
#					elif Playervariables.sealsarray[Playervariables.SLUT] == 0:
#						mainscene.get_node("CanvasLayer2/Dialogue/ConversationV1").get("rulestalker").apply_seal(Playervariables.SLUT)
#						if Playervariables.sealsarray[Playervariables.SLUT] > 0:
#							assignquip(20*100)
#					elif Playervariables.sealsarray[Playervariables.OBEY] == 0:
#						mainscene.get_node("CanvasLayer2/Dialogue/ConversationV1").get("rulestalker").apply_seal(Playervariables.OBEY)
#						if Playervariables.sealsarray[Playervariables.OBEY] > 0:
#							assignquip(22*100)
#					else:
#						mainscene.get_node("CanvasLayer2/Dialogue/ConversationV1").get("rulestalker").apply_seal(randi()%3)
					if perfecttracking == false and Playervariables.sealsarray[0] + Playervariables.sealsarray[1] + Playervariables.sealsarray[2] >= 3:
#						var debuffs = [Vector2(Playervariables.DROWSY,2),Vector2(Playervariables.FLINCH,1)]#,Vector2(Playervariables.MARKED,4)]
#						mainscene.solve_debuff(debuffs[0])
#						mainscene.solve_debuff(debuffs[1])
#						mainscene.solve_debuff(debuffs[2])
#						mainscene.get_node("CanvasLayer2/Dialogue").generatedebufftoken(locv,debuffs)
#						specialmovecooldown2 = -1
#						enemyfrustration = 8
#						mainscene.reduce_debuff(Playervariables.POSSESSION-1,40,locv)
						mainscene.emotion(10,0)
#						enable_tracking()
#						trackconsistency = 1
#						accuracy = 1
#						trackmodifier = 0.03
#						perfecttracking = true
#						attacked = true
#						readybreak = true
						perfectly_track(true)
						mainscene.fox_lightning(locv,mainscene.playerlocv)
						assignquip(23*100,null,"normal",false,3)
#						mainscene.debug_toggle_clothes(true,false)
#						mainscene.debug_toggle_clothes(false,true)
					quipcountdown = 6
		if attackdict.get("special")[0].find(3) == -1 and monsternum != IMP:
			attacked = true
		else:
			quickshot = true
#	roampoint = astar.get_closest_point(targetlocv)

func perfectly_track(onoff):
	if onoff == true and TargetNotPlayer == false:
		perfecttracking = true
		attacked = true
		readybreak = true
		trackmodifier = 0.03
		accuracy = 1
		trackconsistency = 1
		enemyfrustration = 8
		specialmovecooldown2 = -1
		enable_tracking()
	else:
		perfecttracking = false
		if monsternum == FOXGIRL:
			trackmodifier = 0.25
			trackconsistency = 0.65
			accuracy = 0.8
		elif monsternum == DRAGONHARPY:
			trackmodifier = 0.15
			trackconsistency = 0.4
			accuracy = 0.5
			enemyfrustration = 0
	

var animspeed = 0.01 #how fast character should move from A to B
var enemymovepoint = Vector2(0,0) #the waypoints to animate along
#var currentlymoving = false
var revenge = false
var movequeue = 0
signal advancemovequeue
var recordmovearray = []
var recordmovetype = 0 #0 means run, 1 means jump... -1 means warp??
var recordattackarray = []
var recordattacktype = [] #means damage type
func _on_enemymoving_timeout():
	emit_signal("continue_moving")
func _move_enemy(routearray,jumping,emotion):
	$CanvasLayer3/raisezlevel.visible = false
	if capture_state == EGGCAPTURE:
		if capture_id_weakref != null and capture_id_weakref.get_ref() != null:
			capture_id.assign_loc(loc)
		else:
			free_capture()
#	var queueposition = movequeue
	movequeue += 1
	if monsternum == IMP:
		animspeed = (0.5+Playervariables.gameslowness)/(clamp(movequeue+routearray.size(),3,999))*1.5
	else:
		animspeed = (0.5+Playervariables.gameslowness)/(clamp(movequeue+routearray.size(),2,999))
	if movequeue > 1 and quickshot == false:
		$enemymoving.start(0.02)
	for i in movequeue-1:
		yield(self,"advancemovequeue")
#		yield(get_tree(),"idle_frame")
	if enemyactive == false and fadeout == false:
		reset_queue()
		return
	set_physics_process(true)
	if routearray.size() > 0:
		if held_object != null and held_object_type == HELDENEMY and held_object.CaptureIsPlayer == true:
			mainscene.enemy_drags_player(routearray[-1],self,true)
		if emotionlock == true and monsternum == RAMGIRL:
			emotionlock = false
		if emotion[0] > -3:
			if position.x - locv.x > 0:
				flip_h = true
				$tail.flip_h = true
			else:
				flip_h = false
				$tail.flip_h = false
			if use_extra == true and extra_effect != null:
				extra_effect.flip_h = self.flip_h
		if jumping != 1:
			$shadow.set_self_modulate(Color(1,1,1,0.6))
			recordmovetype = 0
			emotion(5)
			for i in range (routearray.size()):
				if i == emotion[0]:
					emotion(emotion[1])
				enemymovepoint = mapstar.get_point_position(routearray[i])
				recordmovearray.append(enemymovepoint)
#				if queue == queueposition:
				$enemymoving.start(animspeed)
				yield(self,"continue_moving")
				set_physics_process(true)
#			if queue != queueposition:
#				return
		else:
			$shadow.set_self_modulate(Color(1,1,1,0))
			recordmovetype = 1
			if emotion[0] != -4:
				emotion(3)
			for i in range (routearray.size()):
				if i == emotion[0]:
					emotion(emotion[1])
				enemymovepoint = mapstar.get_point_position(routearray[i])
				recordmovearray.append(enemymovepoint)
#				if queue == queueposition:
				$enemymoving.start(animspeed)
				yield(self,"continue_moving")
				set_physics_process(true)
	else:
		if emotion[0] != -1:
			emotion(emotion[1])
		$enemymoving.start(animspeed)
		yield(self,"continue_moving")
	if ready == true:
		emotion(0)
	elif emotion[0] != -4 and emotion[0] < -1:
		emotion(emotion[1])
#	currentlymoving = false
	movequeue -= 1
	if enemyactive == false and fadeout == false:
		reset_queue()
		return
	else:
		emit_signal("advancemovequeue")
	if movequeue <= 0:
		reset_queue()
	_process_shadow(jumping)

func reset_queue():
	movequeue = 0
	$CanvasLayer3/raisezlevel.visible = true
	$CanvasLayer3/raisezlevel.position = locv
	enemymovepoint = locv

func _process_shadow(jumping = 1):
	if jumping == 1:
		if map2dpatharray[loc.y+1][loc.x] > 0:
			$shadow.set_self_modulate(Color(1,1,1,0.6))
			$shadow.position.y =  78
		elif map2dpatharray[loc.y+2][loc.x] > 0:
			$shadow.set_self_modulate(Color(1,1,1,0.4))
			$shadow.position.y =  78+tilesize
		elif map2dpatharray[loc.y+3][loc.x] > 0:
			$shadow.set_self_modulate(Color(1,1,1,0.2))
			$shadow.position.y =  78+tilesize*2
	else:
		$shadow.set_self_modulate(Color(1,1,1,0.6))
		$shadow.position.y =  78

var failsafe = false
func _physics_process(_delta):
	position = lerp(position,enemymovepoint,((0.51+Playervariables.gameslowness)-animspeed)/(2+Playervariables.gameslowness*4.2))
	if (position-enemymovepoint).length() < 2:
		if health > 0:
			if movequeue == 0:
				if (position-locv).length() < 2:
					enemymovepoint = locv
					position = locv
					set_physics_process(false)
					failsafe = false
				else:
					enemymovepoint = locv
					print("ENEMY FAILSAFE: movepoint was not locv")
			else:
				if $enemymoving.get_time_left() == 0:
					if Playervariables.debugmodeon == true:
						print("ENEMY FAILSAFE: queue stuck.")
					if failsafe == true:
						if Playervariables.debugmodeon == true:
							print("ENEMY FAILSAFE: proceeding to un-stuck.")
						emit_signal("continue_moving")
						emit_signal("advancemovequeue")
					else:
						failsafe = true
		else:
			set_physics_process(false)

const drop_item_not_escalate = false
var reaquire_friendly = null
func friendly(truefalse,super=false):
	if truefalse == true:
		if friendly_node == null:
			if maxhealthvalue > 0 and (mainscene.mapgenlocation in Playervariables.safezones) == false:#!= Playervariables.Shrine:
				mainscene.maxhealthincrement -= maxhealthvalue
				if mainscene.rewardgiven == false and mainscene.maxhealthdefeated >= clamp(mainscene.tiernums[Playervariables.tier],1,mainscene.maxhealthincrement):
					if drop_item_not_escalate == true:
						mainscene.dropitemondefeat(locv)
					else:
						mainscene.rewardgiven = true
						get_node("/root/Master").call("offer_rewards")
				maxhealthvalue = 0
			if reaquire_friendly != null:
				friendly_node = reaquire_friendly
			else:
				friendly_node = load(Mainpreload.Friendly).instance()
				add_child(friendly_node)
		else:
			friendly_node.modulate = Color(1,1,1)
		if super == true:
			friendly_node.get_child(0).visible = true
			friendly_node.get_child(1).visible = true
			allyarray.append(-3)
	elif (mainscene.mapgenlocation in Playervariables.safezones) == false:#mainscene.mapgenlocation != Playervariables.Shrine:
		if friendly_node != null:
			friendly_node.modulate = Color(0.3,0.05,0.05)
#			friendly_node.queue_free()
			friendly_node = null

func make_passive(disablequip = false):
	if mainscene.mapgenlocation in [Playervariables.Tutorial1s1,Playervariables.Tutorial1s2]:#Playervariables.curseditemdict[Playervariables.PAWS] == false:
		lewd = false
#	elif monsternum == FOXGIRL and mainscene.mapgenlocation == Playervariables.Shrine:
	elif monsternum != RAMGIRL or mainscene.mapgenlocation != Playervariables.Shrine:
		friendly(true)
	passive = true
	disable_ready()
	disable_tracking()
#	tracking = false
#	$aura.visible = false
	if disablequip == true:
		disablequip()
	limitroaming(true)
var avoidpoint = Vector2(-9,-9)
var specquip = 0 #if 1, enables tutorialmission1catknightquips on ally hurt
func teamavoid(point): #should not enable LEWD.r
	if specquip < 2:
		disablequip()
	if passive == true:
		if specquip == 1 and uniqueenemyID != 2:
			assignquip(200,"tutorialmission1catknightquips")
			$quip.play("normal")
	avoidpoint = point
	if limitroam == true and randf() < 0.08:
		limitroaming(false)
	if friendly_node == null:#if lockpassive == false and friendly_node == null:
		passive = false

var paw_resistance = true
var knockbacks = []
var boundarychanged = false
var warninggiven = false
func taking_damage(targetarray,typedamage,ID,debuff,friendnums,hardcodeddirection,attackname):
	if enemyactive == true:# and playerfriendly == false:
#		var raceimmunity = false
		if capture_state != NOCAPTURE:
			if capture_id_weakref != null and capture_id_weakref.get_ref() != null:
				if capture_id != ID and capture_id.visible == true:
					if capture_id.has_method("taking_damage"):
						capture_id.taking_damage(targetarray,typedamage,ID,debuff,friendnums,hardcodeddirection,attackname)
					return
			else:
				free_capture()
				print("Free capture due to: no egg at taking_damage")
		for num in friendnums:
			if allyarray.find(num) != -1:# or friendarray.find(race) != -1:
				if num == -3 and "playerloc" in ID:
					var attackerloc = ID.playerloc
					var use_knockback = 0
					if typedamage.y > 0:
						use_knockback = typedamage.y
					for debuffvector in debuff:
						if debuffvector.x == Playervariables.KNOCKBACK:
							use_knockback += debuffvector.y
							break
					if use_knockback > 0:
						if loc == attackerloc:
							var diff = Vector2((randi()%3)-1,(randi()%3)-1)
							var proposedloc = attackerloc-diff
							var inc = 0
							while (map2darray[proposedloc.y][proposedloc.x] > 0 or proposedloc == attackerloc) and inc < 15:
								inc += 1
								diff = Vector2((randi()%3)-1,(randi()%3)-1)
								proposedloc = attackerloc-diff
							attackerloc = attackerloc+diff
						if loc != attackerloc:
							knockback_skip_move = true
							mainscene.call("astarenabledisable",loc,-1,disableloc,disablepoint,self)
							knockbacks.append(Vector3(attackerloc.x,attackerloc.y,use_knockback))
							add_to_group("delayed_knockback")
				return
#				raceimmunity = true
#		if raceimmunity == false:
		var finaldamage = typedamage.y
		var multiplier = int(DamageMultiplier[int(typedamage.x)])
		match multiplier:
			Multiplier.NORMAL:pass
			Multiplier.RESISTANT:finaldamage = ceil((typedamage.y*0.71) -1)
			Multiplier.IMMUNE:finaldamage = -1
			Multiplier.VULNERABLE:finaldamage = floor((finaldamage+1)*1.4)
		var attackerloc = ID.get("loc")
		if attackerloc == null:
			attackerloc = ID.get("playerprevloc") #so that if the player lances into an enemy, it knocks them back properly
#		elif TargetType == Target.PLAYER and finaldamage > -1:
#			TargetType = Target.NEAREST
		if finaldamage > 0:
			if (mainscene.mapgenlocation in [Playervariables.Shrine,Playervariables.RamHome]) == true:
				finaldamage = 0
				match monsternum:
					FOXGIRL:
						for debuffeffectnum in range(debuff.size()):
							if int(debuff[debuffeffectnum].x) != Playervariables.MARKED:
								debuff[debuffeffectnum].y = 0
					RAMGIRL:
						for debuffeffectnum in range(debuff.size()):
							if (int(debuff[debuffeffectnum].x) in [Playervariables.HYPNOSIS,Playervariables.MARKED]) == false:
								debuff[debuffeffectnum].y = 0
								if mainscene.mapgenlocation == Playervariables.Shrine:
									if warninggiven == false:
										warninggiven = true
										mainscene.register_event_via_main("A different debuff might work.",[],true)
#			elif TargetType == Target.MOSTLYENEMY and ID == get_parent().get_parent() and lockpassive == false:
#				$friendly.visible = false
#				TargetType = Target.NEAREST
#				passive = false
#			if passive == false:
#				if "loc" in ID:
#					if TargetType != Target.ONLYPLAYER:
#						TargetNotPlayer = true
#						CurrentTarget = ID
#						CurrentTargetWeakRef = weakref(CurrentTarget)
#				else:
#					if TargetType != Target.ONLYENEMY:
#						TargetNotPlayer = false
#						CurrentTarget = null
#						CurrentTargetWeakRef = null
		if typedamage.y > 0 and typedamage.x != Playervariables.SWEET:
			targetloc = get_target_loc(true,ID)
#		else:
#			for debuffeffect in debuff:
#				if (int(debuffeffect.x) in [Playervariables.HYPNOSIS,Playervariables.MILK,Playervariables.DROWSY,Playervariables.POSSESSION]) == false:
#					targetloc = get_target_loc(true,ID)
#					break
		if attackerloc != null:
			var soundmade = 1
			if enemyblockarray.size() > 0 and finaldamage > -1:
#					soundmade = 3
				finaldamage = blockcheck(attackerloc,Vector2(typedamage.x,finaldamage),hardcodeddirection)
				if finaldamage == typedamage.y:
					pass
				elif finaldamage > 0:#or finaldamage.y == typedamage.y:# (typedamage.y == 0 and finaldamage.y > -1):
					soundmade = 2
				else:
					soundmade = 3
					mainscene.get_node("sfx/enemyblock").position = locv
					mainscene.get_node("sfx/enemyblock").play()
			if typedamage.x == Playervariables.SWEET:
				soundmade = 4
#				elif monsternum == FOXGIRL and typedamage.y > 0 and finaldamage <= 0 and ID == get_parent().get_parent() and shredded == false:
			elif monsternum == FOXGIRL and finaldamage <= 0 and ID == get_parent().get_parent() and shredded == false and attackname.to_lower().find("torch") == -1:
				revenge = true
			if finaldamage >= 0:
				for _i in range(finaldamage):
					mainscene.create_sparkle(locv,1,true)
				health -= finaldamage
				if finaldamage > 0 and fadeout == false and enemyactive == true:
					if finaldamage >= 2:
						if finaldamage >= 3:
							$AnimationPlayer2.play("hugehurt")
							if finaldamage >= 4:
								$AnimationPlayer2.play("megahurt")
						else:
							$AnimationPlayer2.play("bighurt")
					else:
						$AnimationPlayer2.play("hurt")
				var usecolor = Playervariables.typecolorarray[typedamage.x]
				usecolor.a = clamp(typedamage.y*0.1,0,1)
				mainscene.create_sparkle(locv,3,false,2.5,usecolor)
				handle_health()
				if "locv" in ID:
					mainscene.generate_hit_effect(ID.locv,locv,typedamage)
				elif friendnums.find(-3) > -1:
					mainscene.generate_hit_effect(mainscene.get("playerlocv"),locv,typedamage)
				else:
					mainscene.generate_hit_effect(locv,locv,typedamage)
				if typedamage.x != Playervariables.SWEET and finaldamage > 0 and monsternum == CATGIRL:# and TargetType != Target.ONLYENEMY:
					get_tree().call_group("Catgirls","teamavoid",loc)
					if attackname == Playervariables.swipe_attack_name and paw_resistance == true and Playervariables.wolf_variants == false:
						paw_resistance = false
						catgirl_reaction_attack()
						if health < 2:
							finaldamage = -1
							mainscene.register_event_via_main("Cat-girl dodges the clumsy paws!",[],true)
				elif monsternum == CATGIRL and (attackname == Playervariables.soft_attack_name and ((enemyrank == 0 and GEHENNA == false) or health > 0)): #GEHENNA cat doesn't do it when they die because that's awkward for gehennacall
					catgirl_reaction_attack(true)
					finaldamage = -1
				elif limitroam == true:
					limitroaming(false)
				mainscene.update_enemy_health(finaldamage,self)
#				if passive == true and lockpassive == false:
#					for debuffeffect in debuff:
#						if (int(debuffeffect.x) in [Playervariables.HYPNOSIS,Playervariables.MILK,Playervariables.DROWSY,Playervariables.POSSESSION]) == false:
#							targetloc = get_target_loc(true,ID)
				for debuffeffect in debuff:
					for _i in range(abs(debuffeffect.y)):
						mainscene.create_sparkle(locv,2,true)
					if sign(debuffeffect.x) == 1:
						if debuffeffect.x == Playervariables.HEAT and debuffeffect.y > 0:
							if ready == true:
								crash = false
								disable_ready()
#								ready = false
								$ready.emitting = false
								$ready.visible = false
								debuff_bounce(Playervariables.HEAT)
						elif debuffeffect.x == Playervariables.HYPNOSIS and debuffeffect.y > 0:#enemydebuffarray[1] > 0:
							hypno_id = ID
							hypno_id_weakref = weakref(ID)
							if current_emotion != HYPNO:
								emotion_particles(HYPNO)
							if hadhypno == false and ID == get_parent().get_parent():#newhypno == null:
								hadhypno = true
#								if ID == get_parent().get_parent():
#									hadhypno = true
								if monsternum == RAMGIRL:
									if mainscene.mapgenlocation == Playervariables.Shrine:
										friendly(true)
										if Playervariables.questcondition2 > 0:
											assignquip(500,"quipshrineram","exclamation",true)
										else:
											assignquip(400,"quipshrineram","exclamation",true)
											Playervariables.questcondition2 += 1
									elif mainscene.mapgenlocation == Playervariables.RamHome:
										if Playervariables.curseditemdict[Playervariables.ROPEGAG] == false:
											if Playervariables.get_corruption("horns") != Playervariables.raceRAM:
												assignquip((3+uniqueenemyID)*100,"ramhomequips","question",true)
											else:
												assignquip((6+uniqueenemyID)*100,"ramhomequips","exclamation",true)
												Playervariables.gameoverrank = enemyrank == 2
												if attackname == "Heavenly Chord":
													get_parent().get_parent().queue_convo("ropegagconvo",0)
												else:
													get_parent().get_parent().queue_convo("ropegagconvo",1)
									else:
										if enemyrank == 0:
											if Playervariables.corruptiondict.has("ears") and Playervariables.corruptiondict.has("tail"):
												assignquip(300)
											else:
												assignquip(200)
										else:
											assignquip(400)
								elif worldquip == true and monsternum in [FOXGIRL,DRAGONHARPY,WEREWOLF,CATGIRL,RAMGIRL] and randf() < 0.33:# and mainscene.mapgenlocation != Playervariables.Shrine:
									assignquip(2*100,null,"normal",false,3)
						elif debuffeffect.x == Playervariables.MARKED and debuffeffect.y > 0:
							shred(true)
						enemydebuffarray[debuffeffect.x-1] += debuffeffect.y
					elif sign(debuffeffect.x) == -1:
						if debuffeffect.x == -2: #knockback
							if loc == attackerloc:
								var diff = Vector2((randi()%3)-1,(randi()%3)-1)
								var proposedloc = attackerloc-diff
								var inc = 0
								while (map2darray[proposedloc.y][proposedloc.x] > 0 or proposedloc == attackerloc) and inc < 15:
									inc += 1
									diff = Vector2((randi()%3)-1,(randi()%3)-1)
									proposedloc = attackerloc-diff
								attackerloc = attackerloc+diff
							if loc != attackerloc:
								knockback_skip_move = true
#									yield(get_tree(),"idle_frame")
								mainscene.call("astarenabledisable",loc,-1,disableloc,disablepoint,self)
								knockbacks.append(Vector3(attackerloc.x,attackerloc.y,debuffeffect.y))
								add_to_group("delayed_knockback")
#										fadeoutswitch(false,false)
						else:
							enemynegativedebuffarray[(debuffeffect.x*-1)-1] += debuffeffect.y
							if debuffeffect.x == -1 and debuffeffect.y > 0:
#									if monsternum != FOXGIRL: #maybe remove this later? Allows push to trigger scene
								revenge = false
								if flinchimmunity == true:#monsternum == WEREWOLF and flinchimmunity == true:
									mainscene.register_event_via_main("VAR1 resists the flinch effect.",[displayname],true)
									flinchimmunity = false
#									$extraeffect.visible = false
									enemynegativedebuffarray[0] = 0
#									else:
#										$AnimationFlinch.play("flinch")
				if held_object != null and (finaldamage > 0 or enemynegativedebuffarray[0] > 0) and held_object_type == HELDEGG:
					if finaldamage == 0 and enemynegativedebuffarray[0] > 0:
						debuff_bounce(Playervariables.FLINCH)
					hold_object(false,true)
				if enemydebuffarray[4] > 0 and boundarychanged == false and "shrinenum" in ID and ID.shrinenum > -1:
					debuff_bounce(Playervariables.POSSESSION)
					boundarymin = mainscene.shrinelocations[ID.shrinenum]-Vector2(4,4)
					boundarymax = mainscene.shrinelocations[ID.shrinenum]+Vector2(4,4)
					boundarysize = boundarymax-boundarymin
					boundarychanged = true
				if finaldamage == 0 and typedamage.y == 0:
					mainscene.targetshit += 1
				elif finaldamage > 0:
					var attackeffect = load(Mainpreload.Hitmarker).instance()
					mainscene.get_node("attackeffects").add_child(attackeffect)
					attackeffect.definehit(-1,finaldamage,debuff,false,multiplier)
					attackeffect.position = locv
					revenge = false
					if no_unquip_on_damage == false:# or "isenemy" in ID:
						disablequip()
#					else:
#						no_unquip_on_damage = false
					mainscene.targetshit += 1
					match soundmade:
						1:
							mainscene.get_node("sfx/enemydamagetaken").position = locv
							mainscene.get_node("sfx/enemydamagetaken").play()
						2:
							mainscene.get_node("sfx/enemyblockfailed").position = locv
							mainscene.get_node("sfx/enemyblockfailed").play()
#							3:
#								mainscene.get_node("sfx/enemyblock").position = locv
#								mainscene.get_node("sfx/enemyblock").play()
						4:
							mainscene.get_node("sfx/enemysweetdamage").position = locv
							mainscene.get_node("sfx/enemysweetdamage").play()
					if monsternum == CATGIRL: #catgirls get a jumpspeed buff when hit
						enemybasejumpspeed = 3
#							set_self_modulate(Color(1,0.9,0.8))
					if sign(targetloc.x - loc.x) != 0 and ready == false:
						facing = sign(targetloc.x - loc.x)
					if monsternum == RAMGIRL:
						emotionlock = false
					emotion(1)
					var allowheal = false
					if ID == get_parent().get_parent() or ("MinionOwner" in ID and ID.MinionOwner == get_parent().get_parent()):
						allowheal = true
					if health < 1:
						if typedamage.x == Playervariables.SWEET or capture_state == EGGCAPTURE:
							consider_dying(SWEETDEATH,allowheal)
						else:
							consider_dying(REGULARDEATH,allowheal)
					else:
						$AnimationFlinch.play("flinch")
				elif debuff.size() == 0 and finaldamage < typedamage.y:
					var newhit = load(Mainpreload.Hitmarker).instance()
					mainscene.get_node("attackeffects").add_child(newhit)
					newhit.position = locv
					newhit.blocked_effect(typedamage.y,0,multiplier)
			else:
				var newhit = load(Mainpreload.Hitmarker).instance()
				mainscene.get_node("attackeffects").add_child(newhit)
				newhit.position = locv
				newhit.blocked_effect(typedamage.y,0,multiplier)

func make_angry(onoff=true):
	if onoff == true:
		enrage = true
		emotion_particles(FRUSTRATION)
		specialmovecooldown = 5
	else:
		enrage = false
		emotion_particles(-1)

var no_unquip_on_damage = false
func shred(forced = true):
	if shredded == false:
		can_reclothe = !forced
		shredded = true
		mainscene.update_enemy_clothes(self,false)
		if monsternum == FOXGIRL:
			if forced == true:
				if mainscene.mapgenlocation == Playervariables.Shrine:
					assignquip(400,"quipshrinefox","exclamation",true)
				else:
					emotion_particles(FRUSTRATION)
					if TargetNotPlayer == true:
						assignquip(2500)
					else:
						assignquip(2400)
#				mainscene.destroy_shrine(shrinenum)
				mainscene.get_node("sfx/bigrip").position = locv
				mainscene.get_node("sfx/bigrip").play()
				debuff_bounce(Playervariables.MARKED)
		elif monsternum == RAMGIRL:
			if mainscene.mapgenlocation == Playervariables.RamHome:
				assignquip(900,"ramhomequips","exclamation",true)
			elif mainscene.mapgenlocation == Playervariables.Shrine:
				assignquip(600,"quipshrineram")
			elif enemyrank >= 2:
				assignquip(2500)
			else:
				assignquip(2400)
			if forced == true:
				mainscene.get_node("sfx/bigrip").position = locv
				mainscene.get_node("sfx/bigrip").play()
				debuff_bounce(Playervariables.MARKED)
				enemyjumpspeed = 1
				exhaustjump = false
		elif monsternum == WEREWOLF:
			if forced == true:
				if TargetNotPlayer == true:
					assignquip(2600)
				elif mainscene.mapgenlocation == Playervariables.WolfCombat or (Playervariables.escalationdict.has("Map Water") and Playervariables.escalationdict["Map Water"] > 0):
					assignquip(2500)
				else:
					assignquip(2400)
				debuff_bounce(Playervariables.MARKED)
			attack_obstructions = false
			dict_instantattack = Playervariables.Move200
			if use_extra == true:
				use_extra = false
				extra_effect.visible = false
		elif monsternum == CATGIRL:
			if forced == true:
				if false == true:
					if enemyrank >= 2:
						assignquip(2500)
					else:
						assignquip(2400)
		elif monsternum == DRAGONHARPY:
			enemybasejumpspeed = 1
			if enemyrank >= 2:
				assignquip(2500)
			else:
				assignquip(2400)
			if forced == true:
				mainscene.get_node("sfx/bigrip").position = locv
				mainscene.get_node("sfx/bigrip").play()
				debuff_bounce(Playervariables.MARKED)
		if $quip.visible == true:
			no_unquip_on_damage = true
		if Playervariables.consent == true:
			match monsternum:
				RAMGIRL:
					if enemyrank != 1:
						set_sprite_frames(load(Mainpreload.ramgirlspritesunclothed))
				FOXGIRL:
					if futa == false or Playervariables.prefdict[Playervariables.pref.FUTAENEMY] == false:
						set_sprite_frames(load(Mainpreload.foxgirlspritesnocloth))
					else:
						set_sprite_frames(load(Mainpreload.foxgirlspritesnoclothdick))
				WEREWOLF:
					set_sprite_frames(load(Mainpreload.pitchspritesnotar))
				DRAGONHARPY:
					if enemyrank >= 2:
						set_sprite_frames(load(Mainpreload.elitedragonharpyspritesstripped))
					else:
						set_sprite_frames(load(Mainpreload.dragonharpyspritesstripped))
				IMP:
					match enemyrank:
						SPADE:set_sprite_frames(load("res://Enemy/imp/imp_spade_bare.tres"))
						DIAMOND:set_sprite_frames(load("res://Enemy/imp/imp_diamond_bare.tres"))
						HEART:set_sprite_frames(load("res://Enemy/imp/imp_heart_bare.tres"))
						CLUB:
							if Playervariables.prefdict[Playervariables.pref.FUTAENEMY] == false:
								set_sprite_frames(load("res://Enemy/imp/imp_club_bare_female.tres"))
							else:
								set_sprite_frames(load("res://Enemy/imp/imp_club_bare.tres"))

func catgirl_reaction_attack(talk=false):
	if (mainscene.get("playerloc") -loc).length() < 1.8 and (mainscene.get("playerloc") -loc).length() > 0.2 and capture_state == NOCAPTURE:
		mainscene.call("attacking",[mainscene.get("playerloc")],Vector2(1,0),self,[Vector2(-2,-2)],allyarray,-1,"Grab")
		if mainscene.get("playerloc") == loc:
			mainscene.emotion(10,0)
			if talk == true:
				var randnum = randf()
				if Playervariables.consent == true:
					mainscene.register_event_via_main("Attack fumbled, the cat-girl's messing with me!",[],true)
				else:
					if randnum > 0.6:
						mainscene.register_event_via_main("My attack fumbled. She's smothering me?!",[],true)
					elif randnum > 0.3:
						mainscene.register_event_via_main("My attack fumbled. She's teasing me!",[],true)
					else:
						mainscene.register_event_via_main("My attack fumbled...",[],true)
			Playervariables.playerstunstate = Playervariables.CATLOCKED
			mainscene.caught()

var futa = false
func unshred():
	if shredded == true and can_reclothe == true:
		if monsternum == FOXGIRL:# and shrinenum > -1:
			emotion_particles(-1)
			if futa == false or Playervariables.prefdict[Playervariables.pref.FUTAENEMY] == false or Playervariables.consent == false:
				set_sprite_frames(load(Mainpreload.foxgirlsprites))
			else:
				set_sprite_frames(load(Mainpreload.foxgirlspritesdick))
		elif monsternum == WEREWOLF:
			attack_obstructions = true
			set_sprite_frames(load(Mainpreload.pitchsprites))
			dict_instantattack = Playervariables.Move110
			debuff_bounce(Playervariables.CUM)
			if use_extra == false and extra_effect != null:
				use_extra = true
				extra_effect.visible = true
		elif monsternum == RAMGIRL:
			exhaustjump = true
			enemyjumpspeed = 2
			set_sprite_frames(load(Mainpreload.ramgirlsprites))
		elif monsternum == IMP:
			match enemyrank:
				SPADE:set_sprite_frames(load("res://Enemy/imp/imp_spade_clothes.tres"))
				DIAMOND:set_sprite_frames(load("res://Enemy/imp/imp_diamond_clothes.tres"))
				HEART:set_sprite_frames(load("res://Enemy/imp/imp_heart_clothes.tres"))
				CLUB:
					if Playervariables.prefdict[Playervariables.pref.FUTAENEMY] == false or Playervariables.consent == false:
						set_sprite_frames(load("res://Enemy/imp/imp_club_clothes_female.tres"))
					else:
						set_sprite_frames(load("res://Enemy/imp/imp_club_clothes.tres"))
		elif monsternum == DRAGONHARPY:
			enemybasejumpspeed = 2
			if enemyrank >= 2:
				set_sprite_frames(load(Mainpreload.elitedragonharpysprites))
			else:
				set_sprite_frames(load(Mainpreload.dragonharpysprites))
		shredded = false
		mainscene.update_enemy_clothes(self,true)

func blockcheck(_attackerloc,typedamage,_direction):
#	var _directionshitarray = [0]
#	if direction == -1:
#		if attackerloc == loc:
#			directionshitarray = [5]
#		else:
#			direction = int(stepify(((attackerloc - loc).angle())/3.14,0.25)*4) #0 = right, -1 is up-right, +1 is bottom-right
#			match direction:
#				-1: directionshitarray = [0,1,5]
#				-2: directionshitarray = [0,5]
#				-3: directionshitarray = [0,3,5]
#				-4,4: directionshitarray = [3,5]
#				3: directionshitarray = [2,3,5]
#				2: directionshitarray = [2,5]
#				1: directionshitarray = [1,2,5]
#				0: directionshitarray = [1,5]
#	elif direction == -2:
#		return typedamage.y #block failed
#	elif direction == 4:
#		directionshitarray = [0,1,2,3,5]
#	else:
#		directionshitarray = [direction,5]
#	var beforedamage = typedamage.y
	for blockvector in enemyblockarray:
		if blockvector[2] > 0 and (blockvector[1] == typedamage.x or blockvector[1] == 0):
#			if directionshitarray.find(blockvector[0]) != -1:
			if blockvector[2] > typedamage.y:
				blockvector[2] -= typedamage.y
				typedamage.y = 0
				updateenemyblock()
				return -1
			else:
				typedamage.y -= blockvector[2]
				blockvector[2] = 0
			mainscene.create_sparkle(locv,0,true)
#			for _i in range(typedamage.y):
#				if blockvector[2] <= 0:
#					break
##				beforedamage += 1
#				typedamage.y -= 1# -blockvector[2]
#				blockvector[2] -= 1#-beforedamage
#				mainscene.create_sparkle(locv,0,true)
#			if typedamage.y < 0:
#				mainscene.get_node("sfx/damagetakenblock").play()
#				updateenemyblock()
#				return -1 #block success
	updateenemyblock()
	return typedamage.y #block failed

var enemyblockarray = []
#var Block = preload("res://block.tscn")
#var block = null
#func updateenemyblock():
#	if block != null:
#		block.queue_free()
#		block = null
#	if enemyblockarray.size() > 0:
#		block = Block.instance()
#		add_child(block)
#		block.updateblock(enemyblockarray)
const Newblock = preload("res://newblock.tscn")
func updateenemyblock():
	for blockimage in $blocks.get_children():
		blockimage.queue_free()
	if enemyblockarray.size() > 0:
		$blockanim.play("block")
		for blockinfo in enemyblockarray:
			var newblock = Newblock.instance()
			$blocks.add_child(newblock)
			newblock.get_node("Label").set_text(str(blockinfo[2]))
			newblock.set_modulate(Playervariables.typecolorarray[blockinfo[1]])
	else:
		$blockanim.stop()


func incrementenemyblock():
	var blockcount = enemyblockarray.size()
	for vectornum in range(blockcount):
		var currentvector = blockcount - 1 - vectornum
		enemyblockarray[currentvector][2] += -1
		if enemyblockarray[currentvector][2] <= 0:
			enemyblockarray.remove(currentvector)
	updateenemyblock()

var emotionlock = false
func emotion(pose): #0 = READY, 1 = DAMAGED, 2 = IDLE, 3 = JUMP, 4 = ATTACK, 5 = RUN, 6 = ATTACK2
	if emotionlock == false:
		if monsternum == DRAGONHARPY:
			if pose == 3 and self.frame == 3:
				return
			elif pose == 3 and self.frame != 5:
				pose = 5
			elif pose == 5 and self.frame == 5:
				pose = 3
			if held_object != null:
				if self.flip_h == true:
					held_object.rotation_degrees = -90
				else:
					held_object.rotation_degrees = 90
		elif monsternum == IMP and pose == 0:
			return
		self.frame = pose
		$tail.frame = pose
		if use_extra == true:
			if extra_effect != null:
				extra_effect.frame = pose
#		if monsternum == WEREWOLF:
#			$extraeffect.frame = pose
	

func check_boundary(location):
	if location.x < boundarymin.x or location.y < boundarymin.y or location.x > boundarymax.x or location.y > boundarymax.y:
		return false #outside the boundary
	else:
		return true #inside the boundary

func _on_AnimationPlayer2_animation_finished(anim_name):
	if anim_name == "death":
		if enemyactive == false:
			if Playervariables.debugmodeon == true:
				mainscene.enemy_point(uniqueenemyID,-1)
			while $enemymoving.get_time_left() > 0:
				emit_signal("continue_moving")
				yield(get_tree(),"idle_frame")
			if mainscene.playergrabbed == true:#if an enemy dies while a scene is happening, don't queue free in case it breaks the player_grabbed script. this shouldn't be a problem but it's a JUST-IN-CASE back-up.
				for _i in range(360):
					yield(get_tree(),"idle_frame")
			for _i in range(15):
				yield(get_tree(),"idle_frame")
			disconnect("continue_moving",self,"placeholder")
			disconnect("advancemovequeue",self,"placeholder")
			queue_free()
		else:
			print("Why are you trying to queue free an active enemy??")

func incrementenemydebuffs():
#	if enemydebuffarray[2] > 0:
#		enemydebuffarray[2] += -1
#		mainscene.create_sparkle(locv,0,true)
#		debuff_bounce(Playervariables.SWEATY)
#	else:
	for debuffnum in enemydebuffarray.size():
		if enemydebuffarray[debuffnum] > 0:
			enemydebuffarray[debuffnum] += -1
			mainscene.create_sparkle(locv,0,true)
	if enemynegativedebuffarray[0] > 0:
		enemynegativedebuffarray[0] += -1
		if enemynegativedebuffarray[0] == 0:
			$AnimationFlinch.stop()
			rotation_degrees = 0
		if monsternum == WEREWOLF:
			flinchimmunity = true
#			$extraeffect.visible = true
			rotation = 0 #in case the enemy was rotated from flinch
	check_debuff_effects()

func check_debuff_effects():
	if boundarychanged == true and enemydebuffarray[4] <= 0:
		boundarymin = storedboundarymin
		boundarymax = storedboundarymax
		boundarysize = boundarymax-boundarymin
		boundarychanged = false
	if current_emotion == HYPNO:
		emotion_particles(-1)

#func quipused():
#	if get("quip") != null and quip.length() > 10 and quip.substr(0,11) == "quipmeeting":
#		Playervariables.conversationsrecorddict[enemyname][Playervariables.indexMEETING] += 1

var lock_quip = false
var alwaysquip = false
func assignquip(num,quipname = null,type = "normal",forceshow=false,newcountdown=8,lock = false):
	if lock == true:
		lock_quip = true#bypasses existing lock_quip
	elif lock_quip == true:
		return
	quipcountdown = newcountdown
	alwaysquip = forceshow
	if quipname != null:
		quip = quipname
	if quip == null:
		print("Nullquip, returning. Quipnum was:"+str(num)+"... and countdown was:"+str(newcountdown))
		return
	if monsternum == IMP:
		num += int(enemyrank*30)
	elif quipalts > 0 and num <= 3000 and worldquip == true:#mainscene.mapgenlocation != Playervariables.Shrine: #TEMPORARY FIX, Make a better way of telling if alt quips exist or not in the future
		num += 50
	if num < 10:
		quipnum = "00" + str(num)
	elif num < 100:
		quipnum = "0" + str(num)
	else:
		quipnum = str(num)
	if Playervariables.usedquipdict.has(monsternum) == false:
		Playervariables.usedquipdict[monsternum] = {}
	if Playervariables.usedquipdict[monsternum].has(quip):
		currentusedquiparray = Playervariables.usedquipdict[monsternum][quip]
	else:
		if type != "conversation" and Playervariables.process_speechdict("quip/"+quip).has(quipnum) == false:
#			print("ENEMY ATTEMPTED INVALID QUIP: "+quipnum+" in speechdict: "+quip)
			alwaysquip = false
			lock_quip = false
			disablequip()
			if OS.is_debug_build() == true:
				print("Failed to process quip speechdict: "+str(quipname))
			return
#		else:
#			print("ENEMY valid QUIP: "+quipnum+" in speechdict: "+quip)
		Playervariables.usedquipdict[monsternum][quip] = []
		currentusedquiparray = Playervariables.usedquipdict[monsternum][quip]
	if type.length() > 0:
		enablequip(type)
func disablequip():#_disable = true):
	if $quip.visible == true:
		$quip.visible = false
		lock_quip = false
#		if get("quip") != null:
#			if disable == true:
#				if quip.length() > 10 and quip.substr(0,11) == "quipmeeting":
#					donotquiparray.append(Playervariables.indexMEETING)
var currentusedquiparray = []
func enablequip(quiptype): #may not work for convo
	if Playervariables.consent == false or quip == null:
		$quip.visible = false
		return
	$quip.play(quiptype)
	$quip.visible = true
	unhovered()
	if currentusedquiparray.find(int(quipnum)) == -1 or alwaysquip == true:
		if enemyactive == true:#$AnimationPlayer.is_playing() == false:
			$quip.set_self_modulate(Color(1,1,1,0.8))
		quipused = false
	else:
		if enemyactive == true:#$AnimationPlayer.is_playing() == false:
			$quip.set_self_modulate(Color(0.2,0.2,0.2,0.50))
		quipused = true
	#		match quipindex:
	#			Playervariables.indexMEETING:
	#				quip = "quipmeeting"+enemyname
func hideunhidequip(onoff,mnum,override = false, qnum = ""):
	if (mnum == monsternum or override == true) and get("quip") != null and (qnum.length() == 0 or qnum == quipnum):
		if onoff == true:
			if quipused == false or alwaysquip == true:
				$quip.set_self_modulate(Color(1,1,1,0.8))
			else:
				$quip.set_self_modulate(Color(0.2,0.2,0.2,0.50))
		else:
			if override == true:
				$quip.set_self_modulate(Color(0.3,0.3,0.3,0))
			else:
				$quip.set_self_modulate(Color(0.2,0.2,0.2,0.50))
				if $quip.get_animation() != "converse" and currentusedquiparray.find(int(quipnum)) == -1 and alwaysquip == false:
					currentusedquiparray.append(int(quipnum))
				unhovered()
#				else:
#					unhovered()
				quipused = true
var quipused = false

func hovered():
	if $quip.visible == true and quipused == false and $quip.get_animation() != "converse":
		$quipanim.play("quipanim2")
func unhovered():
	$quip.offset.y = 0
	$quipanim.stop()

var bounces = 0
func debuff_bounce(debuffid):
	mainscene.debuff_bounce_main(debuffid,self.position,bounces)
	bounces += 1

func new_sparkle_enemy(type,rand = true,size = 1,color=null):
	var newsparkle = load(Mainpreload.SparkleEffect).instance()
	newsparkle.scale = Vector2(size,size)
	add_child(newsparkle)
	if rand == true:
		newsparkle.position = Vector2((randf()-0.5)*90,(randf()-0.5)*90)
	match int(type):
		0:newsparkle.play("sparkle")
		1:newsparkle.play("hitmark")
		2:newsparkle.play("bubble")
		3:newsparkle.play("defeat")
		4:newsparkle.play("defeatalt")
		5:newsparkle.play("dust")
		6:
			newsparkle.play("alerted")
			newsparkle.offset = Vector2(25,-100)
			if monsternum == WEREWOLF:
				newsparkle.offset += Vector2(10,-60)
		_:
			newsparkle.play("sparkle")
			print("Main: No valid sparkle type:"+str(type))
	if color != null:
		newsparkle.set_modulate(color)

func handle_health(deathcheck = false,damageifdeath=Playervariables.PURE):
	if health != maxhealth:
		if maxhealth > 0:
			var healthfraction = float(health)/float(maxhealth)
			if healthfraction <= 0:
				$healthouter.visible = false
			else:
				var biasfraction = clamp((health-0.35)/maxhealth,0,2)
				if health == maxhealth:
					biasfraction = 1
#				if healthfraction < 1:
#					biasfraction = (healthfraction+(healthfraction*healthfraction)) / 2
				$healthouter.visible = true
				$healthouter/healthinner.scale.x = biasfraction
				if healthfraction < 1:
					if monsternum == GHOSTFOX:
						var halffraction = (1+healthfraction)*0.5
						set_self_modulate(Color(1,halffraction,halffraction,halffraction))
					else:
						set_self_modulate(Color(1,0.95,0.88))
					$healthouter.modulate = Color(1-biasfraction,0+biasfraction,0.2)
				else:
					set_self_modulate(Color(1,1,1))
					$healthouter.modulate = Color(0,1,0.5)
	else:
		$healthouter.visible = false
		set_self_modulate(Color(1,1,1))
	if deathcheck == true and health < 1:
		if damageifdeath == Playervariables.SWEET or capture_state == EGGCAPTURE:
			consider_dying(SWEETDEATH)
		else:
			consider_dying(REGULARDEATH)

func update_map():
	map2dpatharray = mainscene.map2dpatharray
	map2darray = mainscene.map2darray
	watermap = mainscene.watermap

func attempt_random_quip(relativedistance):
	if worldquip == false:
		pass
	elif monsternum == DRAGONHARPY and held_object != null and held_object_type == HELDENEMY:
		if randf() > 0.75:
			if held_object.CaptureIsPlayer == true:
				assignquip(1700,null,"normal",false,5)
			else:
				assignquip(1800,null,"normal",false,4)
	elif mainscene.capture_state == EGGCAPTURE:
		if monsternum == DRAGONHARPY and randf() > 0.9:
			assignquip(1600,null,"normal",false,4)
	elif relativedistance < 11 and relativedistance > 2 and $quip.visible == false and randf() > 0.9 and Playervariables.consent == true:
		var randnum = randi()%30 + 50
		var doquip = false
		match randnum:
			50,63:doquip = true
#				mainscene.proximity_apply_quip(loc,Playervariables.RAMGIRL,randnum,-1,12)
			65:
				if Playervariables.corruptiondict.has("hair") and Playervariables.corruptiondict["hair"] == Playervariables.raceKITSUNE:
					doquip = true
#					mainscene.proximity_apply_quip(loc,Playervariables.RAMGIRL,randnum,-1,12)
			66,67:
				if Playervariables.curseditemdict[Playervariables.ROPEGAG] == true:
					randnum = 78
					doquip = true
				elif Playervariables.corruptiondict.has("armright") == true and Playervariables.corruptiondict["armright"] == Playervariables.raceHARPY:
					doquip = true
			68:
				if Playervariables.corruptiondict.has("ears") and Playervariables.corruptiondict["ears"] == Playervariables.raceWOLF:
					doquip = true
			69:
				if Playervariables.corruptiondict.has("tail") and Playervariables.corruptiondict["tail"] == Playervariables.raceWOLF:
					doquip = true
			70:
				if Playervariables.corruptiondict.has("leg") and Playervariables.corruptiondict["leg"] == Playervariables.raceWOLF:
					doquip = true
			71:
				if Playervariables.playeroutfit[0] == Playervariables.raceWOLF:
					doquip = true
			72:
				if (Playervariables.get_corruption("backhorns") in [Playervariables.raceIMP,Playervariables.raceHARPY]) == true:
					doquip = true
			73:
				if Playervariables.get_corruption("tail") == Playervariables.raceIMP:
					doquip = true
			74:
				if Playervariables.get_corruption("body") == Playervariables.raceIMP:
					doquip = true
			75:
				if Playervariables.get_corruption("wings") == Playervariables.raceIMP:
					doquip = true
			76:
				if Playervariables.get_corruption("face") == Playervariables.raceIMP:
					doquip = true
			77:
				if Playervariables.get_corruption("leg") == Playervariables.raceIMP:
					doquip = true
			78:
				if Playervariables.curseditemdict[Playervariables.ROPEGAG] == true:
					doquip = true
			79:
				if Playervariables.get_corruption("tail") == Playervariables.raceHARPY:
					doquip = true
			64:
				if enemyrank == 2:
					doquip = true
#					mainscene.proximity_apply_quip(loc,Playervariables.RAMGIRL,randnum,2,12)
			51:
				if Playervariables.tempcorruptiondict.has("beads"):
					doquip = true
#					mainscene.proximity_apply_quip(loc,Playervariables.RAMGIRL,randnum,-1,12)
			52:
				if Playervariables.playeroutfit[1] == Playervariables.raceRAM and Playervariables.corruptiondict.has("leg") == false:
#				if Playervariables.corruptiondict.has("pants") and Playervariables.corruptiondict["pants"] == Playervariables.raceRAM:
					doquip = true
#					mainscene.proximity_apply_quip(loc,Playervariables.RAMGIRL,randnum,-1,12)
			53:
				if Playervariables.playeroutfit[0] == Playervariables.raceRAM:
#				if Playervariables.corruptiondict.has("top") and Playervariables.corruptiondict["top"] == Playervariables.raceRAM:
					doquip = true
#					mainscene.proximity_apply_quip(loc,Playervariables.RAMGIRL,randnum,-1,12)
			54,57:
				if randnum == 57 and monsternum == DRAGONHARPY and Playervariables.CurrentClass == Playervariables.raceHARPY:
					doquip = true
				elif randnum == 57 and monsternum == FOXGIRL and Playervariables.CurrentClass == Playervariables.raceKITSUNE:
					doquip = true
				elif Playervariables.corruptiondict.has("horns") and Playervariables.corruptiondict["horns"] == Playervariables.raceRAM:
					doquip = true
#					mainscene.proximity_apply_quip(loc,Playervariables.RAMGIRL,randnum,-1,12)
			55:
				if Playervariables.playerbustsize > 2:
					doquip = true
#					mainscene.proximity_apply_quip(loc,Playervariables.RAMGIRL,randnum,-1,12)
			56:
				if Playervariables.playerbustsize > 3:
					doquip = true
#					mainscene.proximity_apply_quip(loc,Playervariables.RAMGIRL,randnum,-1,12)
			58:
				if Playervariables.curseditemdict[Playervariables.ROPEGAG] == true:
					randnum = 78
					doquip = true
				elif Playervariables.corruptiondict.has("armright") == true and Playervariables.corruptiondict["armright"] == Playervariables.raceNEKO:
					doquip = true
#					mainscene.proximity_apply_quip(loc,Playervariables.RAMGIRL,randnum,-1,12)
			59:
				if Playervariables.corruptiondict.has("tail") and Playervariables.corruptiondict["tail"] == Playervariables.raceNEKO:
					doquip = true
#					mainscene.proximity_apply_quip(loc,Playervariables.RAMGIRL,randnum,-1,12)
			60:
				if Playervariables.corruptiondict.has("tail") and Playervariables.corruptiondict["tail"] == Playervariables.raceKITSUNE:
					doquip = true
#					mainscene.proximity_apply_quip(loc,Playervariables.RAMGIRL,randnum,-1,12)
			61:
				if Playervariables.playerresistance < 10:
					doquip = true
#					mainscene.proximity_apply_quip(loc,Playervariables.RAMGIRL,randnum,-1,12)
			62:
				if Playervariables.playerresistance < 5:
					doquip = true
#					mainscene.proximity_apply_quip(loc,Playervariables.RAMGIRL,randnum,-1,12)
		if doquip == true:
			assignquip(randnum*100,null,"normal",false,12)


enum{HELDEGG,HELDENEMY}
var held_object_type = HELDEGG
func hold_object(onoff,damage=false,autocapture=null):
#	if onoff == false and held_object_type == HELDENEMY:
#		print("incubation:"+str(incubation))
	if monsternum == DRAGONHARPY:
		if onoff == true:
			if held_object == null:
				held_object = load(Mainpreload.Obj4).instance()
				held_object.mainnode = get_parent().get_parent()
				add_child(held_object)
				held_object.position = Vector2(0,50)
				if monsternum == DRAGONHARPY:
					trackmodifier = 0.07
					trackconsistency = 0.65
				held_object.MinionOwner = self
				held_object.MinionOwnerWeakRef = weakref(self)
				if enemyrank >= 2:
					held_object.Elite = true
			if autocapture != null:
				held_object.visible = false
				held_object.capture_process(autocapture)
				held_object_type = HELDENEMY
				if "disableloc" in autocapture:
					mainscene.call("astarenabledisable",autocapture.loc,-1,autocapture.disableloc,autocapture.disablepoint,autocapture)
					incubation = 2+randi()%4
				else:
					incubation = 4+randi()%5
			else:
				held_object_type = HELDEGG
		else:
			if held_object != null:
				held_object.visible = true
				self.remove_child(held_object)
				mainscene.get_node("objects").add_child(held_object)
				held_object.visible = true
				if randf() > accuracy:
					targetloc = get_target_loc() + Vector2(randi()%3-1,0)
				var bias = stepify((targetloc.x - loc.x)*0.5,1)
				if xdirection == 1:
					bias = clamp(bias,0,1)
				elif xdirection == -1:
					bias = clamp(bias,-1,0)
				held_object.assign_loc(loc,true)
				if held_object_type == HELDENEMY:
					held_object.set_enemy_to_loc()
					held_object.add_to_group("Attackers")
				else:
					held_object.drop()
					held_object.direction = clamp(bias,-1,1)
					held_object.rotation_degrees = 180 - (45*clamp(bias,-1,1))
				held_object.z_index = 10
				if damage == true:
					held_object.destroysize = -1
					held_object.destroy_egg()
				held_object = null
	else:
		print("Who's trying to hold this???")


#
#func player_climb_check() -> bool:
#	var mainnode = get_parent().get_parent()
#	if mainnode.playerfallspeed == 0 or (mainnode.backgroundmap[mainnode.playerloc.y][mainnode.playerloc.x] > 1 and mainnode.playernegativedebuffarray[Playervariables.FLINCH-1] <= 0):
#		return true
#	else:
#		return false

func knockback():
	if self.is_in_group("delayed_knockback"):
		remove_from_group("delayed_knockback")
		for i in range(knockbacks.size()):
			var attackerloc = Vector2(knockbacks[i].x,knockbacks[i].y)
			var debuffeffect = knockbacks[i].z
			var knockback = Vector2(1,0).rotated((loc - attackerloc).angle())
			knockback = Vector2(int(stepify(knockback.x,1)),int(stepify(knockback.y,1)))
			var knockbackcount = 0
			var knockdir = 1
			var enemymovedarray = [mapstar.get_closest_point(locv,true)]
			if debuffeffect < 0:
	#			debuffeffect = -debuffeffect
				knockdir = -1
			for _i in range(abs(debuffeffect)):
				if map2darray[loc.y+knockback.y*knockdir][loc.x+knockback.x*knockdir] == 0 or MovementType == GHOSTLY:
					loc += knockback*knockdir
					locv += knockback*tilesize*knockdir
					knockbackcount += 1
					enemymovedarray.append(mapstar.get_closest_point(locv,true))
				if loc == attackerloc:
					break
			if knockbackcount > 0:
				if Playervariables.playerstunstate != Playervariables.NOSTUN:
					if mainscene.get("playerloc") == loc:
						if mainscene.selfscene == false or Playervariables.playerstunstate != Playervariables.BUSTLOCKED:
							Playervariables.playerstunstate = Playervariables.NOSTUN
							mainscene.selfscene = false
					elif Playervariables.playerstunstate == Playervariables.BUSTLOCKED and monsternum == CATGIRL and (mainscene.get("playerloc") -loc).length() < 1.8 and mainscene.selfscene == false:
						Playervariables.playerstunstate = Playervariables.NOSTUN
				if MovementType == GHOSTLY:
					var finalpoint = mapstar.get_closest_point(locv,true)
					locv = mapstar.get_point_position(finalpoint)
					enemymovedarray[-1] = finalpoint
				else:
					var finalpoint = skystar.get_closest_point(locv,true)
					locv = skystar.get_point_position(finalpoint)
				loc = (locv/tilesize) - Vector2(0.5,0.5)
		#										locv = (loc+Vector2(0.5,0.5))*tilesize
				debuff_bounce(Playervariables.KNOCKBACK)
				if enemymovedarray.size() > 0:
					enemymovedarray.remove(0)
				_move_enemy(enemymovedarray,1,[0,1])
		handlepointcalc()
		knockbacks = []

enum{NOCAPTURE,RAMCAPTURE,EGGCAPTURE}
var capture_state = NOCAPTURE
var capture_id = null
var capture_id_weakref = null
func free_capture():
	self.visible = true
	capture_state = NOCAPTURE
	capture_id = null
	capture_id_weakref = null
func capture(ID,capturetype):
	match capturetype:
		NOCAPTURE:return
		RAMCAPTURE:return
		EGGCAPTURE:pass
	self.visible = false
	capture_id = ID
	capture_id_weakref = weakref(ID)
	capture_state = capturetype
	disablequip()


func assign_enemy_loc_and_move(locarray,locjump,emotion):
	if locarray.size() > 0:
		loc = locarray[-1]
		locv = (loc+Vector2(0.5,0.5))*tilesize
		var positions_array = []
		for location in locarray:
			positions_array.append(mapstar.get_closest_point(tilesize*(Vector2(0.5,0.5)+location),true))
		_move_enemy(positions_array,locjump,emotion)
		handlepointcalc()


func pitch_tar_check()->bool:
	if shredded == false and (mainscene.mapgenlocation == Playervariables.WolfCombat or (Playervariables.escalationdict.has("Map Water") and Playervariables.escalationdict["Map Water"] > 0)):
		return true
	else:
		return false

var emotion_effect = null
var current_emotion = -1
enum{FRUSTRATION=0,HYPNO=1}
func emotion_particles(reference):
	if reference == current_emotion or (current_emotion == HYPNO and enemydebuffarray[Playervariables.HYPNOSIS-1] > 0):
		return
	else:
		current_emotion = reference
		if emotion_effect != null:
			emotion_effect.queue_free()
			emotion_effect = null
	match reference:
		-1:pass
		FRUSTRATION:
			emotion_effect = load(Mainpreload.FrustrationEffect).instance()
			add_child(emotion_effect)
			emotion_effect.position.y -= 30
			emotion_effect.emitting = true
		HYPNO:
			emotion_effect = load(Mainpreload.DebuffHypno).instance()
			add_child(emotion_effect)
			emotion_effect.emitting = true
			emotion_effect.get_child(0).emitting = true

var embraces = 0
func embrace():
	if friendly_node == null:
		embraces += 1
		Playervariables.spec_ram_var += 0.01
		if embraces >= health and (embraces >= maxhealth*0.5):
			mainscene.get_node("sfx/enemysweetdamage").position = locv
			mainscene.get_node("sfx/enemysweetdamage").play()
			player_favor(true)
			make_passive()
			mainscene.create_sparkle(locv,4,false,4,Color(1,0.6,1,0.15))
			Playervariables.spec_ram_var += 0.01
			if worldquip == true and Playervariables.curseditemdict[Playervariables.ROPEGAG] == false:
				assignquip(2800,null,"normal",false,8)
			if monsternum == WEREWOLF and mainscene.mapgenlocation == Playervariables.WolfCombat:
				mainscene.spawn_exit(loc)
				Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] = max(Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL],Playervariables.tutorialstages.ROUTEA11)
				Playervariables.saveprogress()
		else:
			mainscene.get_node("sfx/hitsweet").play()
		if Playervariables.spec_ram_var > 1.0:
			Playervariables.spec_ram_var = 1.0
	else:
		passive = true
		if health == 1 and maxhealth > 1:
			mainscene.heal_enemy(self,1)


#enum{NOSTUN = 0,KISSLOCKED,MILKLOCKED,ARMLOCKED,CATLOCKED,PUSSYLOCKED,RAMLOCKED,FOXLOCKED,BUSTLOCKED,HARPYLOCKED,PITCHLOCKED,BUFFERC}
func request_scene(mas_case = false):
	if mas_case == true and (monsternum in [CATGIRL,RAMGIRL,FOXGIRL]) == false:# [WEREWOLF,DRAGONHARPY,GHOSTFOX,IMP]:
			return false
	var tempstar = mainscene.get("skystarenemy")
	roampoint = tempstar.get_closest_point(mainscene.playerlocv,true)
	jump = tempstar.get_id_path(tempstar.get_closest_point(locv,true),roampoint)
	if jump.size() > 0:
		if mas_case == true and jump.size() > 3:
			jump.resize(3)
		locv = mapstar.get_point_position(jump[-1])
		loc = Vector2(floor(locv.x/tilesize),floor(locv.y/tilesize))
		jump.remove(0)
		_move_enemy(jump,0,[-1,0])
		animspeed = (0.5+Playervariables.gameslowness)/(clamp(movequeue+jump.size(),2,999)) *1.8#NEVER make it more than 2 or the enemy will go in reverse!!!
	if mas_case == true and loc != mainscene.playerloc:
		return false
	match monsternum:
		FOXGIRL:
			if mas_case == true or enemyrank > 0:
				Playervariables.playerstunstate = Playervariables.FOXLOCKED
			else:
				Playervariables.playerstunstate = Playervariables.ARMLOCKED
		RAMGIRL:
			if enemyrank == 1 or (mas_case == true and shredded == true):
				return false
			elif shredded == true:
				Playervariables.playerstunstate = Playervariables.KISSLOCKED
			elif mas_case == true or (enemyrank > 0 and Playervariables.GalleryArrayHeadCG[Playervariables.RAMLOCKED] >= 1):
				Playervariables.playerstunstate = Playervariables.RAMLOCKED
			else:
				Playervariables.playerstunstate = Playervariables.KISSLOCKED
		WEREWOLF:
			if use_extra == true and extra_effect.visible == true and Playervariables.playerbustsize >= 3:
				Playervariables.playerstunstate = Playervariables.PITCHLOCKED
			else:
				Playervariables.playerstunstate = Playervariables.MILKLOCKED
		CATGIRL:
			if mas_case == true:
				Playervariables.playerstunstate = Playervariables.PUSSYLOCKED
			elif Playervariables.playerbustsize >= 4:
				Playervariables.playerstunstate = Playervariables.BUSTLOCKED
			elif enemyrank > 0 and Playervariables.GalleryArrayHeadCG[Playervariables.CATLOCKED] >= 1:
				Playervariables.playerstunstate = Playervariables.CATLOCKED
			else:
				Playervariables.playerstunstate = Playervariables.PUSSYLOCKED
		DRAGONHARPY:
			if mainscene.capture_state != EGGCAPTURE:
				var call_object = null
				if held_object == null:
					hold_object(true)
				if held_object != null and held_object_type != HELDENEMY:
					call_object = held_object
					hold_object(false)
					call_object.capture_process(mainscene)
			Playervariables.playerstunstate = Playervariables.HARPYLOCKED
		GHOSTFOX:
			Playervariables.possessionrank = enemyrank > 0
			if health > 4:
				health = 4
			mainscene.call("attacking",[mainscene.playerloc],Vector2(Playervariables.SPIRIT,ceil(health/2)),self,[Vector2(Playervariables.POSSESSION,health)],allyarray,4,"Kitsune Possession")
			$AnimationPlayer2.playback_speed = 0.4
			health -= 4
			mainscene.get_node("sfx/foxobj").volume_db = -11+(health*2)
			mainscene.get_node("sfx/foxobj").play()
			handle_health(true)
			return false
		_:
			if mas_case == false:
				Playervariables.playerstunstate = Playervariables.NOSTUN
			return false
	get_parent().get_parent().force_occupy_space = false
	knockback_skip_move = true
	return true

var ssfx = false
