[gd_scene load_steps=4 format=2]

[ext_resource path="res://Assets/ui/deployabledouble.png" type="Texture" id=1]
[ext_resource path="res://reseteffect.gd" type="Script" id=2]

[sub_resource type="Animation" id=1]
resource_name = "reload"
length = 1.2
tracks/0/type = "value"
tracks/0/path = NodePath(".:rect_rotation")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5, 0.7, 0.8, 1 ),
"transitions": PoolRealArray( 1, 0.5, 1, 1, 1 ),
"update": 0,
"values": [ 55.0, -4.0, 3.0, 0.0, 0.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:rect_position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.5, 0.7, 0.9, 1.2 ),
"transitions": PoolRealArray( 1, 1, 1.5, 2, 1 ),
"update": 0,
"values": [ Vector2( -250, -50 ), Vector2( 30, -100 ), Vector2( 0, -70 ), Vector2( 0, -90 ), Vector2( 0, 450 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:self_modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1.2 ),
"transitions": PoolRealArray( 2, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}

[node name="offset" type="Control"]
script = ExtResource( 2 )

[node name="reloadtab" type="TextureRect" parent="."]
self_modulate = Color( 1, 1, 1, 0.555556 )
margin_top = -77.0711
margin_right = 202.0
margin_bottom = 157.93
mouse_filter = 2
texture = ExtResource( 1 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="reloadtab"]
autoplay = "reload"
anims/reload = SubResource( 1 )

[connection signal="animation_finished" from="reloadtab/AnimationPlayer" to="." method="_on_AnimationPlayer_animation_finished"]
