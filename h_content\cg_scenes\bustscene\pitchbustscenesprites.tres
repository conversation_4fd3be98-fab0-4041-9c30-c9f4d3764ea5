[gd_resource type="SpriteFrames" load_steps=15 format=2]

[ext_resource path="res://DialogueArt/CG/bustscene/pitch1.png" type="Texture" id=1]
[ext_resource path="res://DialogueArt/CG/bustscene/pitch5.png" type="Texture" id=2]
[ext_resource path="res://DialogueArt/CG/bustscene/pitch2.png" type="Texture" id=3]
[ext_resource path="res://DialogueArt/CG/bustscene/pitchb1.png" type="Texture" id=4]
[ext_resource path="res://DialogueArt/CG/bustscene/pitchb5.png" type="Texture" id=5]
[ext_resource path="res://DialogueArt/CG/bustscene/pitchb4.png" type="Texture" id=6]
[ext_resource path="res://DialogueArt/CG/bustscene/pitch6.png" type="Texture" id=7]
[ext_resource path="res://DialogueArt/CG/bustscene/pitchb3.png" type="Texture" id=8]
[ext_resource path="res://DialogueArt/CG/bustscene/pitch3.png" type="Texture" id=9]
[ext_resource path="res://DialogueArt/CG/bustscene/pitch4.png" type="Texture" id=10]
[ext_resource path="res://DialogueArt/CG/bustscene/pitch7.png" type="Texture" id=11]
[ext_resource path="res://DialogueArt/CG/bustscene/pitchb2.png" type="Texture" id=12]
[ext_resource path="res://DialogueArt/CG/bustscene/pitchb7.png" type="Texture" id=13]
[ext_resource path="res://DialogueArt/CG/bustscene/pitchb6.png" type="Texture" id=14]

[resource]
animations = [ {
"frames": [ ExtResource( 4 ), ExtResource( 12 ), ExtResource( 14 ), ExtResource( 13 ), ExtResource( 12 ), ExtResource( 8 ), ExtResource( 6 ), ExtResource( 5 ), ExtResource( 14 ), ExtResource( 13 ) ],
"loop": true,
"name": "suck",
"speed": 7.0
}, {
"frames": [ ExtResource( 1 ), ExtResource( 3 ), ExtResource( 9 ), ExtResource( 4 ), ExtResource( 13 ), ExtResource( 12 ), ExtResource( 13 ), ExtResource( 12 ), ExtResource( 4 ), ExtResource( 10 ), ExtResource( 2 ), ExtResource( 7 ), ExtResource( 11 ) ],
"loop": true,
"name": "transition",
"speed": 5.0
}, {
"frames": [ ExtResource( 4 ), ExtResource( 12 ), ExtResource( 13 ), ExtResource( 2 ), ExtResource( 10 ), ExtResource( 9 ) ],
"loop": true,
"name": "transition2",
"speed": 7.0
}, {
"frames": [ ExtResource( 1 ), ExtResource( 3 ), ExtResource( 9 ), ExtResource( 10 ), ExtResource( 2 ), ExtResource( 7 ), ExtResource( 11 ) ],
"loop": true,
"name": "wave",
"speed": 7.0
} ]
