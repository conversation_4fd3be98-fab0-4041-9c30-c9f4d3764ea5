[gd_scene load_steps=3 format=2]

[ext_resource path="res://Assets/testlightinvert.png" type="Texture" id=1]

[sub_resource type="Animation" id=1]
resource_name = "collect"
length = 1.4
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.4, 1.4 ),
"transitions": PoolRealArray( 1, 1, 2, 1 ),
"update": 0,
"values": [ Vector2( 7, 20 ), Vector2( 100, -30 ), Vector2( 128, 0 ), Vector2( 128, -200 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.4, 1.4 ),
"transitions": PoolRealArray( 0.5, 0.5, 1 ),
"update": 0,
"values": [ -210.0, 40.0, 0.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.2, 0.8, 1.4 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}

[node name="drop" type="Sprite"]
modulate = Color( 1, 1, 1, 0 )
position = Vector2( 128, -200 )
scale = Vector2( 0.5, 0.5 )

[node name="player" type="AnimationPlayer" parent="."]
anims/collect = SubResource( 1 )

[node name="back" type="Sprite" parent="."]
self_modulate = Color( 0.509804, 0.705882, 0.509804, 0.294118 )
texture = ExtResource( 1 )
