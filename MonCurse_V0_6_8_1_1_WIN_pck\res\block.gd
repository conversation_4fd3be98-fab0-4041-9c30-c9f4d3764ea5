extends Node2D


func updateblock(blockarray):
		get_node("blockanim").play("blockanim")
		var assigncheck = [0,0,0,0]
		for blockstat in blockarray:
			if blockstat[0] != 5:
				if blockstat[1] == 0:
					var nodetarget = get_node(str(blockstat[0])).get_node("ex/pure")
					nodetarget.value += blockstat[2]
					nodetarget.visible = true
				else:
					var nodetarget = get_node(str(blockstat[0])).get_node(str(blockstat[1]))
					nodetarget.value += blockstat[2]
					if nodetarget.is_visible() == false:
						nodetarget.visible = true
						if assigncheck[blockstat[0]] > 1:
							if assigncheck[blockstat[0]] == 4:
								get_node(str(blockstat[0])).get_node("dummy").raise()
							nodetarget.raise()
						assigncheck[blockstat[0]] += 1
			else:
				if blockstat[1] == 0:
					for direction in range(4):
						var nodetarget = get_node(str(direction)).get_node("ex/pure")
						nodetarget.value += blockstat[2]
						nodetarget.visible = true
				else:
					for direction in range(4):
						var nodetarget = get_node(str(direction)).get_node(str(blockstat[1]))
						nodetarget.value += blockstat[2]
						if nodetarget.is_visible() == false:
							nodetarget.visible = true
							if assigncheck[direction] > 2:
								nodetarget.raise()
								if assigncheck[direction] == 4:
									get_node(str(direction)).get_node("dummy").raise()
							assigncheck[direction] += 1
