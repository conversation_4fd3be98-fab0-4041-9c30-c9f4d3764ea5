extends TextureButton


# Declare member variables here. Examples:
# var a = 2
# var b = "text"

var infobutton = false
# Called when the node enters the scene tree for the first time.
func _ready():
	if get_parent().get_name() == "VBoxContainer":
		infobutton = true
	# warning-ignore:return_value_discarded
	connect("mouse_entered",self,"button_hovered")
	# warning-ignore:return_value_discarded
	connect("mouse_exited",self,"button_not_hovered")

func button_hovered():
	set_modulate(Color(1.2,1.05,1.1))
	if infobutton == true:
		$info/expand.play("info")
func button_not_hovered():
	set_modulate(Color(1,1,1))
	if infobutton == true:
		$info/expand.play_backwards("info")
