[gd_scene load_steps=33 format=2]

[ext_resource path="res://Enemy/voice/voicesprite.png" type="Texture" id=1]
[ext_resource path="res://Enemy/voice/wand.png" type="Texture" id=2]
[ext_resource path="res://Enemy/voice/wandhead.png" type="Texture" id=3]
[ext_resource path="res://modularnpc.gd" type="Script" id=4]
[ext_resource path="res://Enemy/dropshadow.png" type="Texture" id=5]
[ext_resource path="res://Enemy/voice/wandheadshadow.png" type="Texture" id=6]
[ext_resource path="res://Assets/ui/quipbubble.png" type="Texture" id=7]
[ext_resource path="res://Enemy/qades/qadesshopsprite.png" type="Texture" id=8]
[ext_resource path="res://Enemy/villagers/spritesvillagers.png" type="Texture" id=9]
[ext_resource path="res://Enemy/voice/voicespritecat.png" type="Texture" id=10]

[sub_resource type="AtlasTexture" id=2]
atlas = ExtResource( 8 )
region = Rect2( 0, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=17]
atlas = ExtResource( 9 )
region = Rect2( 600, 0, 200, 300 )

[sub_resource type="AtlasTexture" id=15]
atlas = ExtResource( 9 )
region = Rect2( 200, 0, 200, 300 )

[sub_resource type="AtlasTexture" id=16]
atlas = ExtResource( 9 )
region = Rect2( 400, 0, 200, 300 )

[sub_resource type="AtlasTexture" id=14]
atlas = ExtResource( 9 )
region = Rect2( 0, 0, 200, 300 )

[sub_resource type="AtlasTexture" id=1]
atlas = ExtResource( 1 )
region = Rect2( 0, 0, 300, 300 )

[sub_resource type="AtlasTexture" id=22]
atlas = ExtResource( 10 )
region = Rect2( 0, 0, 300, 300 )

[sub_resource type="SpriteFrames" id=3]
animations = [ {
"frames": [ SubResource( 2 ) ],
"loop": false,
"name": "Qades",
"speed": 5.0
}, {
"frames": [ SubResource( 17 ) ],
"loop": true,
"name": "VillagerBag",
"speed": 5.0
}, {
"frames": [ SubResource( 15 ), SubResource( 16 ) ],
"loop": false,
"name": "VillagerOnee",
"speed": 5.0
}, {
"frames": [ SubResource( 14 ) ],
"loop": false,
"name": "VillagerShort",
"speed": 5.0
}, {
"frames": [ SubResource( 1 ) ],
"loop": false,
"name": "Voice",
"speed": 5.0
}, {
"frames": [ SubResource( 22 ) ],
"loop": true,
"name": "VoiceCat",
"speed": 5.0
} ]

[sub_resource type="Animation" id=18]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath("Character:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ) ]
}

[sub_resource type="Animation" id=4]
resource_name = "hopup"
length = 1.4
tracks/0/type = "value"
tracks/0/path = NodePath("Extra:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.8, 1 ),
"transitions": PoolRealArray( 0.5, 2, 1 ),
"update": 0,
"values": [ Vector2( -150, 159 ), Vector2( -115, 59 ), Vector2( -126, 89 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Extra:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("Character:offset")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.4, 1.1, 1.4 ),
"transitions": PoolRealArray( 1, 2, 2, 1 ),
"update": 0,
"values": [ Vector2( 0, 200 ), Vector2( 0, 200 ), Vector2( 0, -35 ), Vector2( 0, 0 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("Character:modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.4, 1.4 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("Character:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 0.4, 1.4 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ false, true, true ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("Character:z_index")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0, 1, 1.4 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ -4, 0, 0 ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("Extra/ExtraOver/wandshadow:modulate")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0, 0.8, 1, 1.4 ),
"transitions": PoolRealArray( 1, 2, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/7/type = "value"
tracks/7/path = NodePath("Character/dropshadow:modulate")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"times": PoolRealArray( 0, 1.1, 1.4 ),
"transitions": PoolRealArray( 1, 2, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=19]
resource_name = "hurt"
length = 0.7
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.7 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 2, 1.35, 1.35, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Character:offset")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.3, 0.5, 0.7 ),
"transitions": PoolRealArray( 0.5, 0.6, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -10, 0 ), Vector2( 5, 0 ), Vector2( -3, 0 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=20]
resource_name = "hypno"
length = 0.7
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.7 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 2.7, 1.5, 2.7, 1 ), Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=5]
resource_name = "neutral"
length = 0.2
tracks/0/type = "value"
tracks/0/path = NodePath("Character:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 1, 1 ) ]
}

[sub_resource type="Animation" id=6]
resource_name = "squish"
length = 1.3
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("Character:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.6, 1.3 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0.95, 1.05 ), Vector2( 1.05, 0.95 ), Vector2( 0.95, 1.05 ) ]
}

[sub_resource type="AtlasTexture" id=81]
atlas = ExtResource( 7 )
region = Rect2( 0, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=82]
atlas = ExtResource( 7 )
region = Rect2( 128, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=83]
atlas = ExtResource( 7 )
region = Rect2( 0, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=84]
atlas = ExtResource( 7 )
region = Rect2( 128, 128, 128, 128 )

[sub_resource type="SpriteFrames" id=11]
animations = [ {
"frames": [ SubResource( 81 ) ],
"loop": true,
"name": "conversation",
"speed": 5.0
}, {
"frames": [ SubResource( 82 ) ],
"loop": true,
"name": "exclamation",
"speed": 5.0
}, {
"frames": [ null ],
"loop": true,
"name": "none",
"speed": 5.0
}, {
"frames": [ SubResource( 83 ) ],
"loop": true,
"name": "normal",
"speed": 5.0
}, {
"frames": [ SubResource( 84 ) ],
"loop": true,
"name": "question",
"speed": 5.0
} ]

[sub_resource type="Animation" id=85]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath("quip:offset")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("quip:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0.65, 0.65 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("quip:rotation_degrees")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}

[sub_resource type="Animation" id=12]
resource_name = "quipanim"
length = 1.2
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("quip:rotation_degrees")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3, 0.7, 1.2 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, -8.0, 3.0, 0.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("quip:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.3, 0.6, 0.8, 1.2 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0.65, 0.65 ), Vector2( 1, 0.7 ), Vector2( 0.8, 0.9 ), Vector2( 1, 0.7 ), Vector2( 0.65, 0.65 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("quip:offset")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.7, 1.2 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -6, 7 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=13]
resource_name = "quipanim2"
length = 3.5
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("quip:offset")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.3, 3.5 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0, -30 ), Vector2( 0, 0 ), Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("quip:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1, 2.5, 3.5 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, -14.0, 14.0, 0.0 ]
}

[node name="NPC" type="Node2D"]
z_index = 4
script = ExtResource( 4 )

[node name="Extra" type="Sprite" parent="."]
position = Vector2( -126, 89 )
texture = ExtResource( 2 )

[node name="ExtraOver" type="Sprite" parent="Extra"]
z_index = 13
texture = ExtResource( 3 )

[node name="wandshadow" type="Sprite" parent="Extra/ExtraOver"]
z_index = -1
texture = ExtResource( 6 )
offset = Vector2( 0, 35 )

[node name="Character" type="AnimatedSprite" parent="."]
frames = SubResource( 3 )
animation = "Voice"

[node name="dropshadow" type="Sprite" parent="Character"]
self_modulate = Color( 1, 1, 1, 0.705882 )
scale = Vector2( 2, 1 )
z_index = -1
texture = ExtResource( 5 )
offset = Vector2( 0, 100 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
anims/RESET = SubResource( 18 )
anims/hopup = SubResource( 4 )
anims/hurt = SubResource( 19 )
anims/hypno = SubResource( 20 )
anims/neutral = SubResource( 5 )
anims/squish = SubResource( 6 )

[node name="quip" type="AnimatedSprite" parent="."]
visible = false
self_modulate = Color( 1, 1, 1, 0.784314 )
position = Vector2( 40, -114 )
scale = Vector2( 0.65, 0.65 )
z_index = 15
frames = SubResource( 11 )
animation = "normal"

[node name="quipanim" type="AnimationPlayer" parent="."]
playback_speed = 0.85
anims/RESET = SubResource( 85 )
anims/quipanim = SubResource( 12 )
anims/quipanim2 = SubResource( 13 )

[connection signal="animation_finished" from="AnimationPlayer" to="." method="_on_AnimationPlayer_animation_finished"]
