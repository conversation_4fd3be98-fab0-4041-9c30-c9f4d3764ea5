extends Node

const catknightsprites = preload("res://Enemy/cat/catknightframes.tres")
const catknightspritestail = preload("res://Enemy/cat/catknightframestail.tres")
const SpriteCatKnight = preload("res://DialogueArt/catknightart.tscn")
const SpeechCatKnight = preload("res://Conversations/speechbubblerectCatKnight.png")
const SymbolCatKnight = preload("res://Conversations/symbolCatKnight.png")
#const CatBadEnd1 = preload("res://DialogueArt/CG/gameovers/catbadend1rerig.tscn") #CatKnight
const CatScene = preload("res://DialogueArt/CG/catscene.tscn") #CatKnight

#const tfsprite1 = preload("res://DialogueArt/rules/rules catgirl armleft.png")
#const tfsprite2 = preload("res://DialogueArt/rules/rules catgirl armright.png")
#const tfsprite3 = preload("res://DialogueArt/rules/rules catgirl earleft.png")
#const tfsprite4 = preload("res://DialogueArt/rules/rules catgirl earright.png")
#const tfsprite5 = preload("res://DialogueArt/rules/rules catgirl tail.png")
#const tfsprite6 = preload("res://DialogueArt/rules/rules catgirl tail beads.png")
#const tfsprite7 = preload("res://DialogueArt/rules/rules catgirl tail beadsalt.png")

#const headsprite1 = preload("res://DialogueArt/CG/headscene/Rules Cat Ear Left.png") #the editor seems to have bugged out and doesn't believe this exists.
#const headsprite2 = preload("res://DialogueArt/CG/headscene/Rules Cat Ear Right.png")
