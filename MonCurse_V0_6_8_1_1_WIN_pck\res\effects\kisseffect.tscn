[gd_scene load_steps=12 format=2]

[ext_resource path="res://kisseffect.gd" type="Script" id=1]
[ext_resource path="res://effects/yellowkiss.png" type="Texture" id=2]

[sub_resource type="Shader" id=8]
code = "shader_type canvas_item;

uniform float hue_shift : hint_range(0.0,1.0) = 0.0;
uniform float sat_mul : hint_range(0.0,10.0) = 1.0;
uniform float val_mul : hint_range(0.0,10.0) = 1.0;

vec3 rgb2hsv(vec3 c) {
vec4 K = vec4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);
vec4 p = mix(vec4(c.bg, K.wz), vec4(c.gb, K.xy), step(c.b, c.g));
vec4 q = mix(vec4(p.xyw, c.r), vec4(c.r, p.yzx), step(p.x, c.r));
float d = q.x - min(q.w, q.y);
float e = 1.0e-10;
return vec3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), d / (q.x + e), q.x);
}

vec3 hsv2rgb(vec3 c) {
vec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
vec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);
return c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);
}

void fragment() {
vec4 texture_color = texture(TEXTURE, UV);
//vec4 texture_color = texture(SCREEN_TEXTURE, SCREEN_UV);
vec3 color_hsv = rgb2hsv(texture_color.rgb);
color_hsv.x = mod((color_hsv.x + hue_shift), 1.0);
color_hsv.y = min((color_hsv.y * sat_mul), 1.01);
color_hsv.z = min((color_hsv.z * val_mul), 1.01);
vec3 color_rgb = hsv2rgb(color_hsv);
COLOR.rgba = vec4(color_rgb.rgb,texture_color.a);

}

"

[sub_resource type="ShaderMaterial" id=9]
shader = SubResource( 8 )
shader_param/hue_shift = 0.85
shader_param/sat_mul = 0.75
shader_param/val_mul = 1.25

[sub_resource type="Gradient" id=1]
offsets = PoolRealArray( 0, 0.688073, 1 )
colors = PoolColorArray( 1, 1, 1, 1, 1, 1, 1, 0.901961, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=2]
gradient = SubResource( 1 )

[sub_resource type="Curve" id=3]
min_value = -1.0
_data = [ Vector2( 0, -0.815909 ), 0.0, 0.0, 0, 0, Vector2( 0.159091, -1 ), 0.0, 0.0, 0, 0 ]

[sub_resource type="CurveTexture" id=4]
curve = SubResource( 3 )

[sub_resource type="Curve" id=5]
_data = [ Vector2( 0, 0.561364 ), 0.0, 0.0, 0, 0, Vector2( 0.030303, 1 ), 0.0, 0.0, 0, 0, Vector2( 0.106061, 0.915909 ), 0.0, 0.0, 0, 0 ]

[sub_resource type="CurveTexture" id=6]
curve = SubResource( 5 )

[sub_resource type="ParticlesMaterial" id=7]
emission_shape = 2
emission_box_extents = Vector3( 110, 160, 1 )
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
angle = -75.0
angle_random = 1.0
scale_curve = SubResource( 6 )
color_ramp = SubResource( 2 )
hue_variation_curve = SubResource( 4 )

[node name="kisseffect" type="Particles2D"]
material = SubResource( 9 )
emitting = false
amount = 1
lifetime = 20.0
one_shot = true
process_material = SubResource( 7 )
texture = ExtResource( 2 )
script = ExtResource( 1 )

[node name="Timer" type="Timer" parent="."]

[connection signal="timeout" from="Timer" to="." method="_on_Timer_timeout"]
