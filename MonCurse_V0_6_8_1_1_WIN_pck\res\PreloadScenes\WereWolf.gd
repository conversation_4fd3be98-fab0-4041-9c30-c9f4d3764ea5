extends Node

const SpeechWereWolf = preload("res://Conversations/speechbubblerectWereWolf.png") #WereWolf
const SymbolWereWolf = preload("res://Conversations/symbolWereWolf.png") #WereWolf
#const SpriteWereWolf = preload("res://DialogueArt/pitchart.tscn") #WereWolf
const SpritePitchTar = preload("res://Enemy/pitch/pitchtar.tscn") #WereWolf
const SpriteWereWolf = preload("res://DialogueArt/werewolfart.tscn") #WereWolf
const pitchspritesnotar = preload("res://Enemy/pitch/pitchframesnotar.tres") #WereWolf
const pitchsprites = preload("res://Enemy/pitch/pitchframes.tres") #WereWolf
const pitchspritestail = preload("res://Enemy/pitch/pitchframestail.tres") #WereWolf
const pitchattack = preload("res://Enemy/pitch/pitchattack.tscn") #WereWolf

const wolfgirlcg1 = preload("res://DialogueArt/CG/headscene/Wolfgirl Back.png")
const wolfgirlcg2 = preload("res://DialogueArt/CG/headscene/Wolfgirl BehindEffect.png")
const wolfgirlcg3 = preload("res://DialogueArt/CG/headscene/Wolfgirl Front.png")
