extends TextureButton

var tl_tools_name = "Translation\nTools"
var spec_font_size = 1.0
var prime = false
var has_error = false
var errors = []

func _on_TL_BUTTON_pressed():
	if prime == false and get_parent().get_parent().get_parent().has_method("prepare_translation"):
		get_parent().get_parent().get_parent().prepare_translation(self)
		get_node("/root/Master/SFX/ui").play()


func _on_TL_BUTTON_mouse_entered():
	if prime == false:
		set_modulate(Color(1,1,1,1))


func _on_TL_BUTTON_mouse_exited():
	if prime == false:
		set_modulate(Color(0.627451, 0.627451, 0.941176, 0.627451))
