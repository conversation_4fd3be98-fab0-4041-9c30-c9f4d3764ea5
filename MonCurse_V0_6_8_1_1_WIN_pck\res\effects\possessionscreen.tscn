[gd_scene load_steps=3 format=2]

[ext_resource path="res://effects/possession screen.png" type="Texture" id=1]

[sub_resource type="Animation" id=1]
resource_name = "screeneffect"
length = 6.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1.5, 3, 4.5, 6 ),
"transitions": PoolRealArray( 1.2, 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.333333 ), Color( 1, 1, 1, 0.156863 ), Color( 1, 1, 1, 0.294118 ), Color( 1, 1, 1, 0.156863 ), Color( 1, 1, 1, 0.333333 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:anchor_left")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 3, 6 ),
"transitions": PoolRealArray( 1.2, 0.8, 1 ),
"update": 0,
"values": [ 0.0, -0.25, 0.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:anchor_right")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 3, 6 ),
"transitions": PoolRealArray( 1.2, 0.8, 1 ),
"update": 0,
"values": [ 1.0, 1.25, 1.0 ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:anchor_top")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 3, 6 ),
"transitions": PoolRealArray( 1.2, 0.8, 1 ),
"update": 0,
"values": [ 0.0, -0.25, 0.0 ]
}
tracks/4/type = "value"
tracks/4/path = NodePath(".:anchor_bottom")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 3, 6 ),
"transitions": PoolRealArray( 1.2, 0.8, 1 ),
"update": 0,
"values": [ 1.0, 1.25, 1.0 ]
}

[node name="TextureRect" type="TextureRect"]
modulate = Color( 1, 1, 1, 0.287708 )
anchor_left = -0.243012
anchor_top = -0.243012
anchor_right = 1.24301
anchor_bottom = 1.24301
texture = ExtResource( 1 )
expand = true

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
autoplay = "screeneffect"
anims/screeneffect = SubResource( 1 )
