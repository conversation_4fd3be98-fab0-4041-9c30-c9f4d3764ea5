[gd_scene load_steps=5 format=2]

[ext_resource path="res://effects/heartparticles.png" type="Texture" id=1]

[sub_resource type="Gradient" id=611]
offsets = PoolRealArray( 0, 0.209459, 0.641892, 1 )
colors = PoolColorArray( 1, 1, 1, 0, 0.784314, 0.784314, 0.784314, 0.784314, 1, 1, 1, 0.588235, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=612]
gradient = SubResource( 611 )

[sub_resource type="ParticlesMaterial" id=610]
emission_shape = 2
emission_box_extents = Vector3( 30, 10, 1 )
flag_disable_z = true
direction = Vector3( 0, -1, 0 )
spread = 180.0
gravity = Vector3( 0, -98, 0 )
initial_velocity = 130.0
angular_velocity = 20.0
angular_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
damping = 50.0
scale = 0.5
scale_random = 1.0
color_ramp = SubResource( 612 )

[node name="enemyhearts" type="Particles2D"]
visible = false
position = Vector2( 0, -20 )
z_index = 3
emitting = false
amount = 4
lifetime = 2.0
one_shot = true
speed_scale = 0.7
explosiveness = 0.7
randomness = 0.5
process_material = SubResource( 610 )
texture = ExtResource( 1 )
