extends Node

const SpriteImpGirl = preload("res://DialogueArt/impgirlart.tscn")

const impsprites_spade_clothes = preload("res://Enemy/imp/imp_spade_clothes.tres") #Imp
const impsprites_spade_bare = preload("res://Enemy/imp/imp_spade_bare.tres") #Imp
const impsprites_diamond_clothes = preload("res://Enemy/imp/imp_diamond_clothes.tres") #Imp
const impsprites_diamond_bare = preload("res://Enemy/imp/imp_diamond_bare.tres") #Imp
const impsprites_heart_clothes = preload("res://Enemy/imp/imp_heart_clothes.tres") #Imp
const impsprites_heart_bare = preload("res://Enemy/imp/imp_heart_bare.tres") #Imp
const impsprites_club_clothes = preload("res://Enemy/imp/imp_club_clothes.tres") #Imp
const impsprites_club_bare = preload("res://Enemy/imp/imp_club_bare.tres") #Imp
const impsprites_club_clothes_female = preload("res://Enemy/imp/imp_club_clothes_female.tres") #Imp
const impsprites_club_bare_female = preload("res://Enemy/imp/imp_club_bare_female.tres") #Imp

const SymbolImpGirl = preload("res://Conversations/symbolImpGirl.png") #ImpGirl
const SpeechImpGirl = preload("res://Conversations/speechbubblerectImpGirl.png") #ImpGirl

const KnightShifter = preload("res://zapsplat/knightshifter.tscn") #ImpGirl

const ImpScene = "res://DialogueArt/CG/impscene.tscn" #Imp
