[gd_resource type="Animation" format=2]

[resource]
length = 10.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("positionnode/head:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 2.2, 6.6, 8.7, 10 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -3, -6 ), Vector2( -3, 7 ), Vector2( 7, 4 ), Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("positionnode/head:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 2.2, 6.6, 10 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.5, -2.0, 0.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("positionnode/head/hairfront:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 3.1, 10 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 6, -4 ), Vector2( 0, 0 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("positionnode/head/hairfront:rotation_degrees")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 3.1, 10 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 1.0, 0.0 ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("positionnode/head/hairbangsleft:position")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 2.6, 6.4, 10 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0, 5 ), Vector2( 0, 0 ), Vector2( 0, 0 ) ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("positionnode/head/hairbangsleft:rotation_degrees")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0, 2.6, 6.4, 10 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, -1.0, 0.3, 0.0 ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("positionnode/head/hairbangsright:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0, 1.7, 3.3, 6.4, 10 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -9, 14 ), Vector2( -9, 14 ), Vector2( -5, 7 ), Vector2( 0, 0 ) ]
}
tracks/7/type = "value"
tracks/7/path = NodePath("positionnode/head/hairbangsright:rotation_degrees")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"times": PoolRealArray( 0, 2.6, 6.4, 10 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, -1.0, 0.3, 0.0 ]
}
tracks/8/type = "value"
tracks/8/path = NodePath("positionnode/body/legleft:position")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/keys = {
"times": PoolRealArray( 0, 2.8, 5.5, 8.4, 10 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 4, -6 ), Vector2( 8, 2 ), Vector2( 5, 14 ), Vector2( 0, 0 ) ]
}
tracks/9/type = "value"
tracks/9/path = NodePath("positionnode/body/legleft:rotation_degrees")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/keys = {
"times": PoolRealArray( 0, 3.5, 10 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 1.0, 0.0 ]
}
tracks/10/type = "value"
tracks/10/path = NodePath("positionnode/body/armleft:position")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/keys = {
"times": PoolRealArray( 0, 2.2, 3.7, 7.5, 10 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 10, -7 ), Vector2( 22, -15 ), Vector2( 16, -8 ), Vector2( 0, 0 ) ]
}
tracks/11/type = "value"
tracks/11/path = NodePath("positionnode/body/armleft:rotation_degrees")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/keys = {
"times": PoolRealArray( 0, 3.7, 10 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 3.0, 0.0 ]
}
tracks/12/type = "value"
tracks/12/path = NodePath("positionnode/body/armright:position")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/keys = {
"times": PoolRealArray( 0, 1.7, 4.8, 10 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 17, -19 ), Vector2( -5, 10 ), Vector2( 0, 0 ) ]
}
tracks/13/type = "value"
tracks/13/path = NodePath("positionnode/body/armright:rotation_degrees")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/keys = {
"times": PoolRealArray( 0, 1.7, 4.8, 10 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 2.0, -2.0, 0.0 ]
}
tracks/14/type = "value"
tracks/14/path = NodePath("positionnode/body/legright:position")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/keys = {
"times": PoolRealArray( 0, 2.3, 3.8, 5.3, 10 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -30, 28 ), Vector2( -34, 28 ), Vector2( -30, 28 ), Vector2( 0, 0 ) ]
}
tracks/15/type = "value"
tracks/15/path = NodePath("positionnode/body/legright:rotation_degrees")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/keys = {
"times": PoolRealArray( 0, 3.8, 6.6, 10 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, -3.0, -2.0, 0.0 ]
}
tracks/16/type = "value"
tracks/16/path = NodePath("positionnode/body/abdomen:position")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/keys = {
"times": PoolRealArray( 0, 2.3, 5.3, 8.4, 10 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -5, -5 ), Vector2( 5, -4 ), Vector2( 5, 5 ), Vector2( 0, 0 ) ]
}
tracks/17/type = "value"
tracks/17/path = NodePath("positionnode/body/abdomen:rotation_degrees")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/keys = {
"times": PoolRealArray( 0, 10 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0 ]
}
tracks/18/type = "value"
tracks/18/path = NodePath("positionnode/body/chest:position")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/keys = {
"times": PoolRealArray( 0, 2.3, 5.3, 8.4, 10 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -5, -5 ), Vector2( 5, -4 ), Vector2( 5, 5 ), Vector2( 0, 0 ) ]
}
tracks/19/type = "value"
tracks/19/path = NodePath("positionnode/body/chest:rotation_degrees")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/keys = {
"times": PoolRealArray( 0, 10 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0 ]
}
tracks/20/type = "value"
tracks/20/path = NodePath("positionnode/head/eyes:frame")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/imported = false
tracks/20/enabled = false
tracks/20/keys = {
"times": PoolRealArray( 2.8, 2.9, 3.1, 7.2, 9.6 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 1,
"values": [ 0, 2, 0, 1, 0 ]
}
tracks/21/type = "value"
tracks/21/path = NodePath("positionnode/body:position")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/imported = false
tracks/21/enabled = true
tracks/21/keys = {
"times": PoolRealArray( 0, 2.2, 6.6, 8.7, 10 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -5, 5 ), Vector2( 0, -8 ), Vector2( 5, -5 ), Vector2( 0, 0 ) ]
}
tracks/22/type = "value"
tracks/22/path = NodePath("positionnode/body:rotation_degrees")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/imported = false
tracks/22/enabled = true
tracks/22/keys = {
"times": PoolRealArray( 0, 10 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0 ]
}
tracks/23/type = "value"
tracks/23/path = NodePath("positionnode/body/debuffmark:rotation_degrees")
tracks/23/interp = 1
tracks/23/loop_wrap = true
tracks/23/imported = false
tracks/23/enabled = true
tracks/23/keys = {
"times": PoolRealArray( 0, 2, 5, 7.6, 10 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 5.0, -5.0, 5.0, 0.0 ]
}
tracks/24/type = "value"
tracks/24/path = NodePath("positionnode/body/debuffmark:position")
tracks/24/interp = 1
tracks/24/loop_wrap = true
tracks/24/imported = false
tracks/24/enabled = true
tracks/24/keys = {
"times": PoolRealArray( 0, 2, 5, 7.6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0, -50 ), Vector2( 20, 0 ), Vector2( 20, -30 ) ]
}
