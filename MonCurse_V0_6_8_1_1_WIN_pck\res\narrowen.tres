[gd_resource type="Animation" format=2]

[resource]
resource_name = "widen"
length = 0.3
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3 ),
"transitions": PoolRealArray( 0.5, 0.5 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.627451 ), Color( 1, 1, 1, 1 ) ]
}
