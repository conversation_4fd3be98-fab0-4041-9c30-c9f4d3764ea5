[gd_resource type="Animation" format=2]

[resource]
resource_name = "widen"
length = 0.5
tracks/0/type = "value"
tracks/0/path = NodePath(".:anchor_left")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 0.5 ),
"update": 0,
"values": [ 1.0, 0.3 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:anchor_right")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 0.5 ),
"update": 0,
"values": [ 1.0, 0.7 ]
}
