extends Node

const PossessionScreen = preload("res://effects/possessionscreen.tscn") #FoxGirl

const SpeechFoxGirl = preload("res://Conversations/speechbubblerectFoxGirl.png") #FoxGirl
const SymbolFoxGirl = preload("res://Conversations/symbolFoxGirl.png") #FoxGirl
const SpriteFoxGirl = preload("res://DialogueArt/shikaart.tscn") #FoxGirl
const foxgirlsprites = preload("res://Enemy/shika/shikaframes.tres") #FoxGirl
const foxgirlspritesnocloth = preload("res://Enemy/shika/shikaframesnocloth.tres") #FoxGirl
const foxgirlspritesdick = preload("res://Enemy/shika/shikaframesdick.tres") #FoxGirl
const foxgirlspritesnoclothdick = preload("res://Enemy/shika/shikaframesnoclothdick.tres") #FoxGirl
const foxgirlspritestail = preload("res://Enemy/shika/shikaframestail.tres") #FoxGirl
const ghostfoxsprites = preload("res://Enemy/shika/ghostfoxframes.tres") #FoxGirl

const mountainfox = preload("res://music/mountain fox.ogg") #foxgirl
const otherfox = preload("res://music/other fox.ogg") #foxgirl

#const FoxBadEnd1 = preload("res://DialogueArt/CG/gameovers/foxbadend.tscn") #FoxGirl

#const tfsprite1 = preload("res://DialogueArt/rules/rules foxgirl earleft Rose.png")
#const tfsprite2 = preload("res://DialogueArt/rules/rules foxgirl earright Rose.png")
#const tfsprite3 = preload("res://DialogueArt/rules/rules foxgirl tail Rose.png")

#const headtffoxgirl1 = preload("res://DialogueArt/CG/headscene/Rules Fox Ear Left Rose.png")
#const headtffoxgirl2 = preload("res://DialogueArt/CG/headscene/Rules Fox Ear Right Rose.png")

const headsprite1 = preload("res://DialogueArt/CG/headscene/Foxgirl Armpit Back.png")
const headsprite2 = preload("res://DialogueArt/CG/headscene/Foxgirl Armpit Front.png")
const headsprite3 = preload("res://DialogueArt/CG/headscene/Foxgirl Armpit FrontEffect.png")
const headsprite4 = preload("res://DialogueArt/CG/headscene/Foxgirl Armpit Initial.png")
const headsprite5 = preload("res://DialogueArt/CG/headscene/Foxgirl Rules Sampler2D.png")

const FoxScene = preload("res://DialogueArt/CG/foxscene.tscn") #FoxGirl

const SpriteGhostFox = preload("res://DialogueArt/ghostfoxart.tscn") #FoxGirl
