[gd_scene load_steps=4 format=2]

[ext_resource path="res://font/Audiowide-Regular.ttf" type="DynamicFontData" id=1]
[ext_resource path="res://damagegraph.gd" type="Script" id=10]

[sub_resource type="DynamicFont" id=1]
outline_size = 2
outline_color = Color( 0, 0, 0, 0.647059 )
font_data = ExtResource( 1 )

[node name="damagegraph" type="Control"]
grow_horizontal = 2
grow_vertical = 0
script = ExtResource( 10 )

[node name="vbox" type="VBoxContainer" parent="."]
anchor_left = 0.5
anchor_right = 0.5
margin_left = -80.0
margin_right = 80.0
margin_bottom = 89.0
grow_horizontal = 2
grow_vertical = 0
alignment = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="attackname" type="Label" parent="vbox"]
margin_top = 32.0
margin_right = 160.0
margin_bottom = 53.0
custom_colors/font_color = Color( 0.631373, 0.541176, 0.470588, 1 )
custom_fonts/font = SubResource( 1 )
text = "AttackNameError"
align = 1

[node name="damageprocess" type="HBoxContainer" parent="vbox"]
margin_top = 57.0
margin_right = 160.0
margin_bottom = 57.0
grow_horizontal = 2
grow_vertical = 0
custom_constants/separation = 0
alignment = 1
__meta__ = {
"_edit_use_anchors_": false
}
