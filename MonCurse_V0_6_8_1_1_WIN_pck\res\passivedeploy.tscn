[gd_scene load_steps=6 format=2]

[ext_resource path="res://pickupsprite.tscn" type="PackedScene" id=1]
[ext_resource path="res://Assets/ui/passivedeployabledouble.png" type="Texture" id=2]
[ext_resource path="res://font/OpenSans-SemiBold.ttf" type="DynamicFontData" id=3]
[ext_resource path="res://passivedeploy.gd" type="Script" id=4]

[sub_resource type="DynamicFont" id=1]
size = 28
outline_size = 2
outline_color = Color( 0, 0, 0, 0.705882 )
font_data = ExtResource( 3 )

[node name="passivedeploy" type="TextureRect"]
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource( 2 )
script = ExtResource( 4 )

[node name="pickupsprite" parent="." instance=ExtResource( 1 )]
position = Vector2( 101, 117.5 )

[node name="barrier" parent="pickupsprite" index="1"]
visible = false

[node name="TextureRect" type="TextureRect" parent="."]
margin_right = 40.0
margin_bottom = 40.0

[node name="TextureButton2" type="Button" parent="."]
self_modulate = Color( 1, 1, 1, 0 )
anchor_right = 1.0
anchor_bottom = 1.0

[node name="TextureButton" type="Button" parent="."]
visible = false
self_modulate = Color( 1, 1, 1, 0.705882 )
anchor_right = 1.0
anchor_bottom = 1.0

[node name="Label" type="Label" parent="TextureButton"]
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = 5.0
margin_top = 5.0
margin_right = -5.0
margin_bottom = -5.0
grow_horizontal = 2
grow_vertical = 2
custom_fonts/font = SubResource( 1 )
align = 1
valign = 1
autowrap = true

[connection signal="pressed" from="TextureButton2" to="." method="_on_TextureButton2_pressed"]
[connection signal="pressed" from="TextureButton" to="." method="_on_TextureButton_pressed"]

[editable path="pickupsprite"]
