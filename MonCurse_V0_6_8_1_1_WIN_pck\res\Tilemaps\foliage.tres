[gd_resource type="TileSet" load_steps=4 format=2]

[ext_resource path="res://Background/foliagetiles.png" type="Texture" id=1]
[ext_resource path="res://Background/moss.png" type="Texture" id=2]
[ext_resource path="res://Background/villageprops.png" type="Texture" id=3]

[resource]
0/name = "foliagetiles.png 0"
0/texture = ExtResource( 1 )
0/tex_offset = Vector2( 0, 0 )
0/modulate = Color( 1, 1, 1, 1 )
0/region = Rect2( 0, 0, 128, 128 )
0/tile_mode = 0
0/occluder_offset = Vector2( 0, 0 )
0/navigation_offset = Vector2( 0, 0 )
0/shape_offset = Vector2( 0, 0 )
0/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
0/shape_one_way = false
0/shape_one_way_margin = 0.0
0/shapes = [  ]
0/z_index = 0
1/name = "foliagetiles.png 1"
1/texture = ExtResource( 1 )
1/tex_offset = Vector2( 0, 0 )
1/modulate = Color( 1, 1, 1, 1 )
1/region = Rect2( 127, -1, 128, 128 )
1/tile_mode = 0
1/occluder_offset = Vector2( 0, 0 )
1/navigation_offset = Vector2( 0, 0 )
1/shape_offset = Vector2( 0, 0 )
1/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
1/shape_one_way = false
1/shape_one_way_margin = 0.0
1/shapes = [  ]
1/z_index = 0
2/name = "foliagetiles.png 2"
2/texture = ExtResource( 1 )
2/tex_offset = Vector2( 0, 0 )
2/modulate = Color( 1, 1, 1, 1 )
2/region = Rect2( 255, -1, 128, 128 )
2/tile_mode = 0
2/occluder_offset = Vector2( 0, 0 )
2/navigation_offset = Vector2( 0, 0 )
2/shape_offset = Vector2( 0, 0 )
2/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
2/shape_one_way = false
2/shape_one_way_margin = 0.0
2/shapes = [  ]
2/z_index = 0
3/name = "foliagetiles.png 3"
3/texture = ExtResource( 1 )
3/tex_offset = Vector2( 0, 0 )
3/modulate = Color( 1, 1, 1, 1 )
3/region = Rect2( 383, -1, 128, 128 )
3/tile_mode = 0
3/occluder_offset = Vector2( 0, 0 )
3/navigation_offset = Vector2( 0, 0 )
3/shape_offset = Vector2( 0, 0 )
3/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
3/shape_one_way = false
3/shape_one_way_margin = 0.0
3/shapes = [  ]
3/z_index = 0
4/name = "foliagetiles.png 4"
4/texture = ExtResource( 1 )
4/tex_offset = Vector2( 0, 0 )
4/modulate = Color( 1, 1, 1, 1 )
4/region = Rect2( 511, -1, 128, 128 )
4/tile_mode = 0
4/occluder_offset = Vector2( 0, 0 )
4/navigation_offset = Vector2( 0, 0 )
4/shape_offset = Vector2( 0, 0 )
4/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
4/shape_one_way = false
4/shape_one_way_margin = 0.0
4/shapes = [  ]
4/z_index = 0
5/name = "foliagetiles.png 5"
5/texture = ExtResource( 1 )
5/tex_offset = Vector2( 0, 0 )
5/modulate = Color( 1, 1, 1, 1 )
5/region = Rect2( 0, 128, 128, 128 )
5/tile_mode = 0
5/occluder_offset = Vector2( 0, 0 )
5/navigation_offset = Vector2( 0, 0 )
5/shape_offset = Vector2( 0, 0 )
5/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
5/shape_one_way = false
5/shape_one_way_margin = 0.0
5/shapes = [  ]
5/z_index = 0
6/name = "foliagetiles.png 6"
6/texture = ExtResource( 1 )
6/tex_offset = Vector2( 0, 0 )
6/modulate = Color( 1, 1, 1, 1 )
6/region = Rect2( 128, 128, 128, 128 )
6/tile_mode = 0
6/occluder_offset = Vector2( 0, 0 )
6/navigation_offset = Vector2( 0, 0 )
6/shape_offset = Vector2( 0, 0 )
6/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
6/shape_one_way = false
6/shape_one_way_margin = 0.0
6/shapes = [  ]
6/z_index = 0
7/name = "foliagetiles.png 7"
7/texture = ExtResource( 1 )
7/tex_offset = Vector2( 0, 0 )
7/modulate = Color( 1, 1, 1, 1 )
7/region = Rect2( 256, 128, 128, 128 )
7/tile_mode = 0
7/occluder_offset = Vector2( 0, 0 )
7/navigation_offset = Vector2( 0, 0 )
7/shape_offset = Vector2( 0, 0 )
7/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
7/shape_one_way = false
7/shape_one_way_margin = 0.0
7/shapes = [  ]
7/z_index = 0
8/name = "foliagetiles.png 8"
8/texture = ExtResource( 1 )
8/tex_offset = Vector2( 0, 0 )
8/modulate = Color( 1, 1, 1, 1 )
8/region = Rect2( 384, 128, 128, 128 )
8/tile_mode = 0
8/occluder_offset = Vector2( 0, 0 )
8/navigation_offset = Vector2( 0, 0 )
8/shape_offset = Vector2( 0, 0 )
8/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
8/shape_one_way = false
8/shape_one_way_margin = 0.0
8/shapes = [  ]
8/z_index = 0
9/name = "foliagetiles.png 9"
9/texture = ExtResource( 1 )
9/tex_offset = Vector2( 0, 0 )
9/modulate = Color( 1, 1, 1, 1 )
9/region = Rect2( 512, 128, 128, 128 )
9/tile_mode = 0
9/occluder_offset = Vector2( 0, 0 )
9/navigation_offset = Vector2( 0, 0 )
9/shape_offset = Vector2( 0, 0 )
9/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
9/shape_one_way = false
9/shape_one_way_margin = 0.0
9/shapes = [  ]
9/z_index = 0
10/name = "foliagetiles.png 10"
10/texture = ExtResource( 1 )
10/tex_offset = Vector2( 0, 0 )
10/modulate = Color( 1, 1, 1, 1 )
10/region = Rect2( 768, 128, 128, 128 )
10/tile_mode = 0
10/occluder_offset = Vector2( 0, 0 )
10/navigation_offset = Vector2( 0, 0 )
10/shape_offset = Vector2( 0, 0 )
10/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
10/shape_one_way = false
10/shape_one_way_margin = 0.0
10/shapes = [  ]
10/z_index = 0
11/name = "foliagetiles.png 11"
11/texture = ExtResource( 1 )
11/tex_offset = Vector2( 0, 0 )
11/modulate = Color( 1, 1, 1, 1 )
11/region = Rect2( 256, 256, 128, 128 )
11/tile_mode = 0
11/occluder_offset = Vector2( 0, 0 )
11/navigation_offset = Vector2( 0, 0 )
11/shape_offset = Vector2( 0, 0 )
11/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
11/shape_one_way = false
11/shape_one_way_margin = 0.0
11/shapes = [  ]
11/z_index = 0
12/name = "foliagetiles.png 12"
12/texture = ExtResource( 1 )
12/tex_offset = Vector2( 0, 0 )
12/modulate = Color( 1, 1, 1, 1 )
12/region = Rect2( 384, 256, 128, 128 )
12/tile_mode = 0
12/occluder_offset = Vector2( 0, 0 )
12/navigation_offset = Vector2( 0, 0 )
12/shape_offset = Vector2( 0, 0 )
12/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
12/shape_one_way = false
12/shape_one_way_margin = 0.0
12/shapes = [  ]
12/z_index = 0
13/name = "foliagetiles.png 13"
13/texture = ExtResource( 1 )
13/tex_offset = Vector2( 0, 0 )
13/modulate = Color( 1, 1, 1, 1 )
13/region = Rect2( 640, 128, 128, 128 )
13/tile_mode = 0
13/occluder_offset = Vector2( 0, 0 )
13/navigation_offset = Vector2( 0, 0 )
13/shape_offset = Vector2( 0, 0 )
13/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
13/shape_one_way = false
13/shape_one_way_margin = 0.0
13/shapes = [  ]
13/z_index = 0
14/name = "foliagetiles.png 14"
14/texture = ExtResource( 1 )
14/tex_offset = Vector2( 0, 0 )
14/modulate = Color( 1, 1, 1, 1 )
14/region = Rect2( 896, 256, 128, 128 )
14/tile_mode = 0
14/occluder_offset = Vector2( 0, 0 )
14/navigation_offset = Vector2( 0, 0 )
14/shape_offset = Vector2( 0, 0 )
14/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
14/shape_one_way = false
14/shape_one_way_margin = 0.0
14/shapes = [  ]
14/z_index = 0
30/name = "villageprops.png 30"
30/texture = ExtResource( 3 )
30/tex_offset = Vector2( 64, -18 )
30/modulate = Color( 1, 1, 1, 1 )
30/region = Rect2( 256, 256, 128, 128 )
30/tile_mode = 0
30/occluder_offset = Vector2( 0, 0 )
30/navigation_offset = Vector2( 0, 0 )
30/shape_offset = Vector2( 0, 0 )
30/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
30/shape_one_way = false
30/shape_one_way_margin = 0.0
30/shapes = [  ]
30/z_index = 1
31/name = "foliagetiles.png 31"
31/texture = ExtResource( 1 )
31/tex_offset = Vector2( 0, 0 )
31/modulate = Color( 1, 1, 1, 1 )
31/region = Rect2( 0, 256, 128, 128 )
31/tile_mode = 0
31/occluder_offset = Vector2( 0, 0 )
31/navigation_offset = Vector2( 0, 0 )
31/shape_offset = Vector2( 0, 0 )
31/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
31/shape_one_way = false
31/shape_one_way_margin = 0.0
31/shapes = [  ]
31/z_index = 0
32/name = "foliagetiles.png 32"
32/texture = ExtResource( 1 )
32/tex_offset = Vector2( 0, 0 )
32/modulate = Color( 1, 1, 1, 1 )
32/region = Rect2( 128, 256, 128, 128 )
32/tile_mode = 0
32/occluder_offset = Vector2( 0, 0 )
32/navigation_offset = Vector2( 0, 0 )
32/shape_offset = Vector2( 0, 0 )
32/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
32/shape_one_way = false
32/shape_one_way_margin = 0.0
32/shapes = [  ]
32/z_index = 0
33/name = "foliagetiles.png 33"
33/texture = ExtResource( 1 )
33/tex_offset = Vector2( 0, 0 )
33/modulate = Color( 1, 1, 1, 1 )
33/region = Rect2( 640, 256, 128, 128 )
33/tile_mode = 0
33/occluder_offset = Vector2( 0, 0 )
33/navigation_offset = Vector2( 0, 0 )
33/shape_offset = Vector2( 0, 0 )
33/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
33/shape_one_way = false
33/shape_one_way_margin = 0.0
33/shapes = [  ]
33/z_index = 0
34/name = "foliagetiles.png 34"
34/texture = ExtResource( 1 )
34/tex_offset = Vector2( 0, 0 )
34/modulate = Color( 1, 1, 1, 1 )
34/region = Rect2( 768, 256, 128, 128 )
34/tile_mode = 0
34/occluder_offset = Vector2( 0, 0 )
34/navigation_offset = Vector2( 0, 0 )
34/shape_offset = Vector2( 0, 0 )
34/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
34/shape_one_way = false
34/shape_one_way_margin = 0.0
34/shapes = [  ]
34/z_index = 0
35/name = "foliagetiles.png 35"
35/texture = ExtResource( 1 )
35/tex_offset = Vector2( 0, 0 )
35/modulate = Color( 1, 1, 1, 1 )
35/region = Rect2( 0, 384, 128, 128 )
35/tile_mode = 0
35/occluder_offset = Vector2( 0, 0 )
35/navigation_offset = Vector2( 0, 0 )
35/shape_offset = Vector2( 0, 0 )
35/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
35/shape_one_way = false
35/shape_one_way_margin = 0.0
35/shapes = [  ]
35/z_index = 0
36/name = "foliagetiles.png 36"
36/texture = ExtResource( 1 )
36/tex_offset = Vector2( 0, 0 )
36/modulate = Color( 1, 1, 1, 1 )
36/region = Rect2( 896, 384, 128, 128 )
36/tile_mode = 0
36/occluder_offset = Vector2( 0, 0 )
36/navigation_offset = Vector2( 0, 0 )
36/shape_offset = Vector2( 0, 0 )
36/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
36/shape_one_way = false
36/shape_one_way_margin = 0.0
36/shapes = [  ]
36/z_index = 0
37/name = "foliagetiles.png 37"
37/texture = ExtResource( 1 )
37/tex_offset = Vector2( 0, 0 )
37/modulate = Color( 1, 1, 1, 1 )
37/region = Rect2( 768, 384, 128, 128 )
37/tile_mode = 0
37/occluder_offset = Vector2( 0, 0 )
37/navigation_offset = Vector2( 0, 0 )
37/shape_offset = Vector2( 0, 0 )
37/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
37/shape_one_way = false
37/shape_one_way_margin = 0.0
37/shapes = [  ]
37/z_index = 0
38/name = "foliagetiles.png 38"
38/texture = ExtResource( 1 )
38/tex_offset = Vector2( 0, 0 )
38/modulate = Color( 1, 1, 1, 1 )
38/region = Rect2( 640, 384, 128, 128 )
38/tile_mode = 0
38/occluder_offset = Vector2( 0, 0 )
38/navigation_offset = Vector2( 0, 0 )
38/shape_offset = Vector2( 0, 0 )
38/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
38/shape_one_way = false
38/shape_one_way_margin = 0.0
38/shapes = [  ]
38/z_index = 0
39/name = "foliagetiles.png 39"
39/texture = ExtResource( 1 )
39/tex_offset = Vector2( 0, 0 )
39/modulate = Color( 1, 1, 1, 1 )
39/region = Rect2( 128, 384, 128, 128 )
39/tile_mode = 0
39/occluder_offset = Vector2( 0, 0 )
39/navigation_offset = Vector2( 0, 0 )
39/shape_offset = Vector2( 0, 0 )
39/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
39/shape_one_way = false
39/shape_one_way_margin = 0.0
39/shapes = [  ]
39/z_index = 0
40/name = "foliagetiles.png 40"
40/texture = ExtResource( 1 )
40/tex_offset = Vector2( 0, 0 )
40/modulate = Color( 1, 1, 1, 1 )
40/region = Rect2( 256, 384, 128, 128 )
40/tile_mode = 0
40/occluder_offset = Vector2( 0, 0 )
40/navigation_offset = Vector2( 0, 0 )
40/shape_offset = Vector2( 0, 0 )
40/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
40/shape_one_way = false
40/shape_one_way_margin = 0.0
40/shapes = [  ]
40/z_index = 0
41/name = "foliagetiles.png 41"
41/texture = ExtResource( 1 )
41/tex_offset = Vector2( 0, 0 )
41/modulate = Color( 1, 1, 1, 1 )
41/region = Rect2( 384, 384, 128, 128 )
41/tile_mode = 0
41/occluder_offset = Vector2( 0, 0 )
41/navigation_offset = Vector2( 0, 0 )
41/shape_offset = Vector2( 0, 0 )
41/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
41/shape_one_way = false
41/shape_one_way_margin = 0.0
41/shapes = [  ]
41/z_index = 0
42/name = "foliagetiles.png 42"
42/texture = ExtResource( 1 )
42/tex_offset = Vector2( 0, 0 )
42/modulate = Color( 1, 1, 1, 1 )
42/region = Rect2( 512, 384, 128, 128 )
42/tile_mode = 0
42/occluder_offset = Vector2( 0, 0 )
42/navigation_offset = Vector2( 0, 0 )
42/shape_offset = Vector2( 0, 0 )
42/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
42/shape_one_way = false
42/shape_one_way_margin = 0.0
42/shapes = [  ]
42/z_index = 0
43/name = "foliagetiles.png 43"
43/texture = ExtResource( 1 )
43/tex_offset = Vector2( 0, 0 )
43/modulate = Color( 1, 1, 1, 1 )
43/region = Rect2( 512, 512, 128, 128 )
43/tile_mode = 0
43/occluder_offset = Vector2( 0, 0 )
43/navigation_offset = Vector2( 0, 0 )
43/shape_offset = Vector2( 0, 0 )
43/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
43/shape_one_way = false
43/shape_one_way_margin = 0.0
43/shapes = [  ]
43/z_index = 0
44/name = "foliagetiles.png 44"
44/texture = ExtResource( 1 )
44/tex_offset = Vector2( 0, 0 )
44/modulate = Color( 1, 1, 1, 1 )
44/region = Rect2( 640, 512, 128, 128 )
44/tile_mode = 0
44/occluder_offset = Vector2( 0, 0 )
44/navigation_offset = Vector2( 0, 0 )
44/shape_offset = Vector2( 0, 0 )
44/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
44/shape_one_way = false
44/shape_one_way_margin = 0.0
44/shapes = [  ]
44/z_index = 0
45/name = "foliagetiles.png 45"
45/texture = ExtResource( 1 )
45/tex_offset = Vector2( 0, 0 )
45/modulate = Color( 1, 1, 1, 1 )
45/region = Rect2( 768, 512, 128, 128 )
45/tile_mode = 0
45/occluder_offset = Vector2( 0, 0 )
45/navigation_offset = Vector2( 0, 0 )
45/shape_offset = Vector2( 0, 0 )
45/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
45/shape_one_way = false
45/shape_one_way_margin = 0.0
45/shapes = [  ]
45/z_index = 0
46/name = "foliagetiles.png 46"
46/texture = ExtResource( 1 )
46/tex_offset = Vector2( 0, 0 )
46/modulate = Color( 1, 1, 1, 1 )
46/region = Rect2( 896, 512, 128, 128 )
46/tile_mode = 0
46/occluder_offset = Vector2( 0, 0 )
46/navigation_offset = Vector2( 0, 0 )
46/shape_offset = Vector2( 0, 0 )
46/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
46/shape_one_way = false
46/shape_one_way_margin = 0.0
46/shapes = [  ]
46/z_index = 0
47/name = "foliagetiles.png 47"
47/texture = ExtResource( 1 )
47/tex_offset = Vector2( 0, 0 )
47/modulate = Color( 1, 1, 1, 1 )
47/region = Rect2( 768, 640, 128, 128 )
47/tile_mode = 0
47/occluder_offset = Vector2( 0, 0 )
47/navigation_offset = Vector2( 0, 0 )
47/shape_offset = Vector2( 0, 0 )
47/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
47/shape_one_way = false
47/shape_one_way_margin = 0.0
47/shapes = [  ]
47/z_index = 0
48/name = "foliagetiles.png 48"
48/texture = ExtResource( 1 )
48/tex_offset = Vector2( 0, 0 )
48/modulate = Color( 1, 1, 1, 1 )
48/region = Rect2( 896, 640, 128, 128 )
48/tile_mode = 0
48/occluder_offset = Vector2( 0, 0 )
48/navigation_offset = Vector2( 0, 0 )
48/shape_offset = Vector2( 0, 0 )
48/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
48/shape_one_way = false
48/shape_one_way_margin = 0.0
48/shapes = [  ]
48/z_index = 0
49/name = "foliagetiles.png 49"
49/texture = ExtResource( 1 )
49/tex_offset = Vector2( 0, 31 )
49/modulate = Color( 1, 1, 1, 1 )
49/region = Rect2( 0, 512, 96, 64 )
49/tile_mode = 0
49/occluder_offset = Vector2( 0, 0 )
49/navigation_offset = Vector2( 0, 0 )
49/shape_offset = Vector2( 0, 0 )
49/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
49/shape_one_way = false
49/shape_one_way_margin = 0.0
49/shapes = [  ]
49/z_index = 0
50/name = "foliagetiles.png 50"
50/texture = ExtResource( 1 )
50/tex_offset = Vector2( 0, -90 )
50/modulate = Color( 1, 1, 1, 1 )
50/region = Rect2( 0, 576, 224, 192 )
50/tile_mode = 0
50/occluder_offset = Vector2( 0, 0 )
50/navigation_offset = Vector2( 0, 0 )
50/shape_offset = Vector2( 0, 0 )
50/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
50/shape_one_way = false
50/shape_one_way_margin = 0.0
50/shapes = [  ]
50/z_index = 0
51/name = "foliagetiles.png 51"
51/texture = ExtResource( 1 )
51/tex_offset = Vector2( 0, -27 )
51/modulate = Color( 1, 1, 1, 1 )
51/region = Rect2( 640, 640, 128, 128 )
51/tile_mode = 0
51/occluder_offset = Vector2( 0, 0 )
51/navigation_offset = Vector2( 0, 0 )
51/shape_offset = Vector2( 0, 0 )
51/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
51/shape_one_way = false
51/shape_one_way_margin = 0.0
51/shapes = [  ]
51/z_index = 0
52/name = "foliagetiles.png 52"
52/texture = ExtResource( 1 )
52/tex_offset = Vector2( 0, 3 )
52/modulate = Color( 1, 1, 1, 1 )
52/region = Rect2( 448, 512, 64, 96 )
52/tile_mode = 0
52/occluder_offset = Vector2( 0, 0 )
52/navigation_offset = Vector2( 0, 0 )
52/shape_offset = Vector2( 0, 0 )
52/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
52/shape_one_way = false
52/shape_one_way_margin = 0.0
52/shapes = [  ]
52/z_index = 0
53/name = "foliagetiles.png 53"
53/texture = ExtResource( 1 )
53/tex_offset = Vector2( 50, 36 )
53/modulate = Color( 1, 1, 1, 1 )
53/region = Rect2( 96, 512, 64, 64 )
53/tile_mode = 0
53/occluder_offset = Vector2( 0, 0 )
53/navigation_offset = Vector2( 0, 0 )
53/shape_offset = Vector2( 0, 0 )
53/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
53/shape_one_way = false
53/shape_one_way_margin = 0.0
53/shapes = [  ]
53/z_index = 0
54/name = "foliagetiles.png 54"
54/texture = ExtResource( 1 )
54/tex_offset = Vector2( 60, 30 )
54/modulate = Color( 1, 1, 1, 1 )
54/region = Rect2( 160, 512, 32, 64 )
54/tile_mode = 0
54/occluder_offset = Vector2( 0, 0 )
54/navigation_offset = Vector2( 0, 0 )
54/shape_offset = Vector2( 0, 0 )
54/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
54/shape_one_way = false
54/shape_one_way_margin = 0.0
54/shapes = [  ]
54/z_index = 0
55/name = "foliagetiles.png 55"
55/texture = ExtResource( 1 )
55/tex_offset = Vector2( 40, 47 )
55/modulate = Color( 1, 1, 1, 1 )
55/region = Rect2( 320, 512, 64, 64 )
55/tile_mode = 0
55/occluder_offset = Vector2( 0, 0 )
55/navigation_offset = Vector2( 0, 0 )
55/shape_offset = Vector2( 0, 0 )
55/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
55/shape_one_way = false
55/shape_one_way_margin = 0.0
55/shapes = [  ]
55/z_index = 0
56/name = "foliagetiles.png 56"
56/texture = ExtResource( 1 )
56/tex_offset = Vector2( 40, 0 )
56/modulate = Color( 1, 1, 1, 1 )
56/region = Rect2( 384, 512, 32, 96 )
56/tile_mode = 0
56/occluder_offset = Vector2( 0, 0 )
56/navigation_offset = Vector2( 0, 0 )
56/shape_offset = Vector2( 0, 0 )
56/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
56/shape_one_way = false
56/shape_one_way_margin = 0.0
56/shapes = [  ]
56/z_index = 0
57/name = "foliagetiles.png 57"
57/texture = ExtResource( 1 )
57/tex_offset = Vector2( 64, 31 )
57/modulate = Color( 1, 1, 1, 1 )
57/region = Rect2( 416, 512, 32, 64 )
57/tile_mode = 0
57/occluder_offset = Vector2( 0, 0 )
57/navigation_offset = Vector2( 0, 0 )
57/shape_offset = Vector2( 0, 0 )
57/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
57/shape_one_way = false
57/shape_one_way_margin = 0.0
57/shapes = [  ]
57/z_index = 0
58/name = "foliagetiles.png 58"
58/texture = ExtResource( 1 )
58/tex_offset = Vector2( 49, 70 )
58/modulate = Color( 1, 1, 1, 1 )
58/region = Rect2( 416, 576, 32, 32 )
58/tile_mode = 0
58/occluder_offset = Vector2( 0, 0 )
58/navigation_offset = Vector2( 0, 0 )
58/shape_offset = Vector2( 0, 0 )
58/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
58/shape_one_way = false
58/shape_one_way_margin = 0.0
58/shapes = [  ]
58/z_index = 0
59/name = "foliagetiles.png 59"
59/texture = ExtResource( 1 )
59/tex_offset = Vector2( 0, 73 )
59/modulate = Color( 1, 1, 1, 1 )
59/region = Rect2( 384, 608, 128, 32 )
59/tile_mode = 0
59/occluder_offset = Vector2( 0, 0 )
59/navigation_offset = Vector2( 0, 0 )
59/shape_offset = Vector2( 0, 0 )
59/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
59/shape_one_way = false
59/shape_one_way_margin = 0.0
59/shapes = [  ]
59/z_index = 0
60/name = "foliagetiles.png 60"
60/texture = ExtResource( 1 )
60/tex_offset = Vector2( 0, 16 )
60/modulate = Color( 1, 1, 1, 1 )
60/region = Rect2( 192, 512, 128, 64 )
60/tile_mode = 0
60/occluder_offset = Vector2( 0, 0 )
60/navigation_offset = Vector2( 0, 0 )
60/shape_offset = Vector2( 0, 0 )
60/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
60/shape_one_way = false
60/shape_one_way_margin = 0.0
60/shapes = [  ]
60/z_index = 10
61/name = "foliagetiles.png 61"
61/texture = ExtResource( 1 )
61/tex_offset = Vector2( 64, 0 )
61/modulate = Color( 1, 1, 1, 1 )
61/region = Rect2( 224, 576, 64, 192 )
61/tile_mode = 0
61/occluder_offset = Vector2( 0, 0 )
61/navigation_offset = Vector2( 0, 0 )
61/shape_offset = Vector2( 0, 0 )
61/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
61/shape_one_way = false
61/shape_one_way_margin = 0.0
61/shapes = [  ]
61/z_index = 10
62/name = "foliagetiles.png 62"
62/texture = ExtResource( 1 )
62/tex_offset = Vector2( 0, 0 )
62/modulate = Color( 1, 1, 1, 1 )
62/region = Rect2( 288, 576, 96, 192 )
62/tile_mode = 0
62/occluder_offset = Vector2( 0, 0 )
62/navigation_offset = Vector2( 0, 0 )
62/shape_offset = Vector2( 0, 0 )
62/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
62/shape_one_way = false
62/shape_one_way_margin = 0.0
62/shapes = [  ]
62/z_index = 10
63/name = "foliagetiles.png 63"
63/texture = ExtResource( 1 )
63/tex_offset = Vector2( 0, 40 )
63/modulate = Color( 1, 1, 1, 1 )
63/region = Rect2( 384, 640, 160, 32 )
63/tile_mode = 0
63/occluder_offset = Vector2( 0, 0 )
63/navigation_offset = Vector2( 0, 0 )
63/shape_offset = Vector2( 0, 0 )
63/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
63/shape_one_way = false
63/shape_one_way_margin = 0.0
63/shapes = [  ]
63/z_index = 10
64/name = "foliagetiles.png 64"
64/texture = ExtResource( 1 )
64/tex_offset = Vector2( 0, 0 )
64/modulate = Color( 1, 1, 1, 1 )
64/region = Rect2( 384, 672, 160, 96 )
64/tile_mode = 0
64/occluder_offset = Vector2( 0, 0 )
64/navigation_offset = Vector2( 0, 0 )
64/shape_offset = Vector2( 0, 0 )
64/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
64/shape_one_way = false
64/shape_one_way_margin = 0.0
64/shapes = [  ]
64/z_index = 10
65/name = "foliagetiles.png 65"
65/texture = ExtResource( 1 )
65/tex_offset = Vector2( 0, 0 )
65/modulate = Color( 1, 1, 1, 1 )
65/region = Rect2( 544, 640, 64, 128 )
65/tile_mode = 0
65/occluder_offset = Vector2( 0, 0 )
65/navigation_offset = Vector2( 0, 0 )
65/shape_offset = Vector2( 0, 0 )
65/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
65/shape_one_way = false
65/shape_one_way_margin = 0.0
65/shapes = [  ]
65/z_index = 10
66/name = "foliagetiles.png 66"
66/texture = ExtResource( 1 )
66/tex_offset = Vector2( 26, 0 )
66/modulate = Color( 1, 1, 1, 1 )
66/region = Rect2( 608, 640, 32, 128 )
66/tile_mode = 0
66/occluder_offset = Vector2( 0, 0 )
66/navigation_offset = Vector2( 0, 0 )
66/shape_offset = Vector2( 0, 0 )
66/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
66/shape_one_way = false
66/shape_one_way_margin = 0.0
66/shapes = [  ]
66/z_index = 10
67/name = "moss.png 67"
67/texture = ExtResource( 2 )
67/tex_offset = Vector2( 0, 0 )
67/modulate = Color( 1, 1, 1, 1 )
67/region = Rect2( 0, 0, 96, 96 )
67/tile_mode = 0
67/occluder_offset = Vector2( 0, 0 )
67/navigation_offset = Vector2( 0, 0 )
67/shape_offset = Vector2( 0, 0 )
67/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
67/shape_one_way = false
67/shape_one_way_margin = 0.0
67/shapes = [  ]
67/z_index = 10
68/name = "moss.png 68"
68/texture = ExtResource( 2 )
68/tex_offset = Vector2( 0, 0 )
68/modulate = Color( 1, 1, 1, 1 )
68/region = Rect2( 96, 0, 96, 96 )
68/tile_mode = 0
68/occluder_offset = Vector2( 0, 0 )
68/navigation_offset = Vector2( 0, 0 )
68/shape_offset = Vector2( 0, 0 )
68/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
68/shape_one_way = false
68/shape_one_way_margin = 0.0
68/shapes = [  ]
68/z_index = 10
69/name = "moss.png 69"
69/texture = ExtResource( 2 )
69/tex_offset = Vector2( 0, 0 )
69/modulate = Color( 1, 1, 1, 1 )
69/region = Rect2( 192, 0, 96, 96 )
69/tile_mode = 0
69/occluder_offset = Vector2( 0, 0 )
69/navigation_offset = Vector2( 0, 0 )
69/shape_offset = Vector2( 0, 0 )
69/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
69/shape_one_way = false
69/shape_one_way_margin = 0.0
69/shapes = [  ]
69/z_index = 10
70/name = "moss.png 70"
70/texture = ExtResource( 2 )
70/tex_offset = Vector2( 0, 0 )
70/modulate = Color( 1, 1, 1, 1 )
70/region = Rect2( 0, 96, 96, 96 )
70/tile_mode = 0
70/occluder_offset = Vector2( 0, 0 )
70/navigation_offset = Vector2( 0, 0 )
70/shape_offset = Vector2( 0, 0 )
70/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
70/shape_one_way = false
70/shape_one_way_margin = 0.0
70/shapes = [  ]
70/z_index = 10
71/name = "moss.png 71"
71/texture = ExtResource( 2 )
71/tex_offset = Vector2( 0, 0 )
71/modulate = Color( 1, 1, 1, 1 )
71/region = Rect2( 96, 96, 96, 96 )
71/tile_mode = 0
71/occluder_offset = Vector2( 0, 0 )
71/navigation_offset = Vector2( 0, 0 )
71/shape_offset = Vector2( 0, 0 )
71/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
71/shape_one_way = false
71/shape_one_way_margin = 0.0
71/shapes = [  ]
71/z_index = 10
72/name = "moss.png 72"
72/texture = ExtResource( 2 )
72/tex_offset = Vector2( 0, 0 )
72/modulate = Color( 1, 1, 1, 1 )
72/region = Rect2( 192, 96, 96, 96 )
72/tile_mode = 0
72/occluder_offset = Vector2( 0, 0 )
72/navigation_offset = Vector2( 0, 0 )
72/shape_offset = Vector2( 0, 0 )
72/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
72/shape_one_way = false
72/shape_one_way_margin = 0.0
72/shapes = [  ]
72/z_index = 10
73/name = "moss.png 73"
73/texture = ExtResource( 2 )
73/tex_offset = Vector2( 0, 0 )
73/modulate = Color( 1, 1, 1, 1 )
73/region = Rect2( 0, 192, 96, 96 )
73/tile_mode = 0
73/occluder_offset = Vector2( 0, 0 )
73/navigation_offset = Vector2( 0, 0 )
73/shape_offset = Vector2( 0, 0 )
73/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
73/shape_one_way = false
73/shape_one_way_margin = 0.0
73/shapes = [  ]
73/z_index = 10
74/name = "moss.png 74"
74/texture = ExtResource( 2 )
74/tex_offset = Vector2( 0, 0 )
74/modulate = Color( 1, 1, 1, 1 )
74/region = Rect2( 96, 192, 96, 96 )
74/tile_mode = 0
74/occluder_offset = Vector2( 0, 0 )
74/navigation_offset = Vector2( 0, 0 )
74/shape_offset = Vector2( 0, 0 )
74/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
74/shape_one_way = false
74/shape_one_way_margin = 0.0
74/shapes = [  ]
74/z_index = 10
75/name = "moss.png 75"
75/texture = ExtResource( 2 )
75/tex_offset = Vector2( 0, 0 )
75/modulate = Color( 1, 1, 1, 1 )
75/region = Rect2( 192, 192, 96, 96 )
75/tile_mode = 0
75/occluder_offset = Vector2( 0, 0 )
75/navigation_offset = Vector2( 0, 0 )
75/shape_offset = Vector2( 0, 0 )
75/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
75/shape_one_way = false
75/shape_one_way_margin = 0.0
75/shapes = [  ]
75/z_index = 10
