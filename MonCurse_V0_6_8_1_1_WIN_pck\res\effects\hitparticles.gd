extends Particles2D



func hit_effect(quantity,direction,type):
	if direction == Vector2(0,0):
		process_material.direction = Vector3(0,1,0)
	else:
		process_material.direction = Vector3(direction.x,direction.y,0)
	amount = quantity*5
	texture = load("res://effects/particle"+str(type)+".png")
	self_modulate = Playervariables.typecolorarray[type]
	if type in [Playervariables.SWEET,Playervariables.BITTER]:
		process_material.initial_velocity = 200
		process_material.gravity = Vector3(0,200,0)
	self.emitting = true
#	$Timer.start(lifetime+0.1)

func _on_Timer_timeout():
	queue_free()
