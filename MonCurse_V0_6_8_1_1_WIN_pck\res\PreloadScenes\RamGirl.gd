extends Node

const SpeechRamGirl = preload("res://Conversations/speechbubblerectRamGirl.png") #RamGirl
const SymbolRamGirl = preload("res://Conversations/symbolRamGirl.png") #RamGirl
const SpriteRamGirl = preload("res://DialogueArt/ramgirlart.tscn") #RamGirl
const ramgirlsprites = preload("res://Enemy/ram/ramgirlframes.tres") #RamGirl

var ramgirlspritesunclothed = preload("res://Enemy/ram/ramgirlframes unclothed.tres") #RamGirl
var ramgirlspritesspecial = preload("res://Enemy/ram/ramgirlframes special.tres") #RamGirl

#const tfsprite1 = preload("res://DialogueArt/rules/rules ramgirl hornleft.png") #not in mainpreload?
#const tfsprite2 = preload("res://DialogueArt/rules/rules ramgirl hornright.png") #not in mainpreload?

#const RamBadEnd1 = preload("res://DialogueArt/CG/gameovers/rambadend.gd") #RamGirl

#const headtframgirl1 = preload("res://DialogueArt/CG/headscene/Rules Ram Horn Left.png")
#const headtframgirl2 = preload("res://DialogueArt/CG/headscene/Rules Ram Horn Right.png")
#const headtframgirl3 = preload("res://DialogueArt/CG/headscene/Rules Body Lips Angry.png")
#const headtframgirl4 = preload("res://DialogueArt/CG/headscene/Rules Body Lips Normal.png")

const headsprite1 = preload("res://DialogueArt/CG/headscene/Ramgirl Kiss Before.png")
const headsprite2 = preload("res://DialogueArt/CG/headscene/Ramgirl Kiss.png")

const HypnosisScreen = preload("res://effects/hypnoscreen.tscn") #Ramgirl

const RamScene = preload("res://DialogueArt/CG/ramscene.tscn")
