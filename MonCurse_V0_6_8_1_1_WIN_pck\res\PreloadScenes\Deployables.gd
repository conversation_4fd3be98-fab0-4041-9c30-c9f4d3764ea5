extends Node

#This one is different. It simply preloads everything in the deployables folders.

const SkillPreview = preload("res://skillpreview.tscn")

func _ready():
	var filesmain = list_files_in_directory("res://Assets/abilityicons/")
	var filesicons = list_files_in_directory("res://Assets/ui/deployableicons/")
	var files = filesmain+filesicons
	for file in files:
		$ResourcePreloader.add_resource("RP",load(file))

func list_files_in_directory(path):
	var files = []
	var dir = Directory.new()
	dir.open(path)
	dir.list_dir_begin()

	while true:
		var file = dir.get_next()
		if file == "":
			break
		elif not file.begins_with(".") and file.ends_with(".png"):
			files.append(path+file)

	dir.list_dir_end()

	return files
