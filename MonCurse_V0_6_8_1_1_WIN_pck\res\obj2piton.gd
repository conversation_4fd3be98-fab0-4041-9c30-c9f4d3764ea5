extends AnimatedSprite

var echo = false

func _ready():
	yield(get_tree(),"idle_frame")
	var existingarray = get_parent().get_parent().call("piton",position,echo)
	yield(get_tree(),"idle_frame")
	if existingarray[0] == false:
		self.set_frame(1)
		$break.play("pitonbreak")
	else:
		get_parent().get_parent().pitonarray.append(self)
		match existingarray[1]:
			1: 
				self.set_frame(2)
				offset.x += 60
			0: pass
			-1: 
				self.set_frame(2)
				flip_h = true
				offset.x += -60
			2:
				self.set_frame(2)
				rotation_degrees = -90
				offset.x += 70

func _on_break_animation_finished(_pitonbreak):
	queue_free()
