[gd_scene load_steps=4 format=2]

[ext_resource path="res://Assets/ui/backdrop.png" type="Texture" id=1]
[ext_resource path="res://font/Audiowide-Regular.ttf" type="DynamicFontData" id=2]

[sub_resource type="DynamicFont" id=1]
size = 12
outline_size = 1
outline_color = Color( 0, 0, 0, 1 )
font_data = ExtResource( 2 )

[node name="back" type="Sprite"]
texture = ExtResource( 1 )

[node name="Label" type="Label" parent="."]
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
margin_left = -50.0
margin_top = -50.0
margin_right = -14.0
margin_bottom = -14.0
grow_horizontal = 2
grow_vertical = 2
rect_min_size = Vector2( 44, 36 )
custom_fonts/font = SubResource( 1 )
custom_colors/font_color = Color( 0.745098, 0.313726, 0.313726, 1 )
text = "Slot"
align = 1
valign = 1
autowrap = true
__meta__ = {
"_edit_use_anchors_": false
}
