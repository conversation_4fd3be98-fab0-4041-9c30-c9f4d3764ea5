extends Node

const SpeechRules = preload("res://Conversations/speechbubblerectRules.png") #Rules
const SymbolRules = preload("res://Conversations/symbolRules.png") #Rules
const SpriteRules = preload("res://DialogueArt/rulesart.tscn") #Rules ; NOTE dialogue and colorinput currently has this by default

const headcase1 = preload("res://DialogueArt/CG/headscene/Rules Body Angry.png")
const headcase2 = preload("res://DialogueArt/CG/headscene/Rules Body.png")
const headcase3 = preload("res://DialogueArt/CG/headscene/Rules Hair Back Rose.png")
const headcase4 = preload("res://DialogueArt/CG/headscene/Rules Hair Rose.png")
const headcase5 = preload("res://DialogueArt/CG/headscene/Rules Hair Back Rose Fox.png")
const headcase6 = preload("res://DialogueArt/CG/headscene/Rules Hair Rose Fox.png")

#const BustScene = preload("res://DialogueArt/CG/bustscene.tscn") #Rules
#const SelfScene = preload("res://DialogueArt/CG/selfscene.tscn") #Rules
