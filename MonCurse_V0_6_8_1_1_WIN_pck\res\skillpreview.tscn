[gd_scene load_steps=3 format=2]

[ext_resource path="res://skillpreview.gd" type="Script" id=1]
[ext_resource path="res://effects/particle0.png" type="Texture" id=2]

[node name="skillpreview" type="Node2D"]
scale = Vector2( 0.5, 0.5 )
z_index = 3
script = ExtResource( 1 )

[node name="home" type="Sprite" parent="."]
texture = ExtResource( 2 )

[node name="switch" type="Timer" parent="."]
wait_time = 0.3

[node name="selectors" type="Node2D" parent="."]

[connection signal="timeout" from="switch" to="." method="_on_switch_timeout"]
