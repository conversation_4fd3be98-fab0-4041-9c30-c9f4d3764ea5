[gd_scene load_steps=7 format=2]

[ext_resource path="res://Assets/endpoint.png" type="Texture" id=1]
[ext_resource path="res://Assets/blacksquare.png" type="Texture" id=2]
[ext_resource path="res://Assets/endpointblue.png" type="Texture" id=3]
[ext_resource path="res://font/Audiowide-Regular.ttf" type="DynamicFontData" id=4]

[sub_resource type="DynamicFont" id=8]
size = 20
font_data = ExtResource( 4 )

[sub_resource type="DynamicFont" id=9]
size = 24
font_data = ExtResource( 4 )

[node name="veil" type="TextureRect"]
visible = false
modulate = Color( 0.894118, 0.788235, 0.960784, 1 )
self_modulate = Color( 1, 1, 1, 0.862745 )
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 0
texture = ExtResource( 2 )
expand = true
__meta__ = {
"_edit_use_anchors_": false
}

[node name="veilbutton1" type="TextureButton" parent="."]
visible = false
modulate = Color( 0.784314, 1, 0.784314, 1 )
anchor_left = 0.3
anchor_top = 0.6
anchor_right = 0.45
anchor_bottom = 0.8
texture_normal = ExtResource( 1 )
texture_hover = ExtResource( 3 )
expand = true
stretch_mode = 5
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label" type="Label" parent="veilbutton1"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_fonts/font = SubResource( 8 )
text = "Yes"
align = 1
valign = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="veilbutton2" type="TextureButton" parent="."]
visible = false
modulate = Color( 1, 0.784314, 0.784314, 1 )
anchor_left = 0.55
anchor_top = 0.6
anchor_right = 0.7
anchor_bottom = 0.8
texture_normal = ExtResource( 1 )
texture_hover = ExtResource( 3 )
expand = true
stretch_mode = 5
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label" type="Label" parent="veilbutton2"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_fonts/font = SubResource( 8 )
text = "Cancel"
align = 1
valign = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label" type="Label" parent="."]
anchor_left = 0.3
anchor_top = 0.3
anchor_right = 0.7
anchor_bottom = 0.6
grow_horizontal = 2
grow_vertical = 0
custom_fonts/font = SubResource( 9 )
text = "This save file has no data on it yet. Would you like to start a new adventure?"
align = 1
valign = 1
autowrap = true
