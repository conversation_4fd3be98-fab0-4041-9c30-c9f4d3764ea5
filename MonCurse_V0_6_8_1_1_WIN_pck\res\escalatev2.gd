extends Control


# Declare member variables here. Examples:
# var a = 2
# var b = "text"
#const tilesize = 128
#const tilesizexy.x = 128
#const tilesizexy.y = 64
const tilesizexy = Vector2(127,63)

# Called when the node enters the scene tree for the first time.
#enum{WOODS,DEEPFOREST,ROAD,MOUNTAINS,HILLS,LAKE,SHALLOWS,VILLAGE,GOAL,DARKTILE,LIGHTTILE,TREELEFT,TREEUP,TREERIGHT,TREEDOWN,DIAGPATH}

var debug = false
var debugwalk = false

func _ready():
	var _c = get_tree().root.connect("size_changed", self, "_on_viewport_size_changed")
	if Playervariables.modarray[Playervariables.mods.TRANSLATION] == true:
		$backbuttoncanvas/backbutton/Label3.set_text(Playervariables.shortareastringdict["Go Back"])
		if Playervariables.default_font_dict["font_debuff_description"] != null:
			$backbuttoncanvas/questbacker/Label.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_debuff_description"].font_data
			$RewardPicker/backing/board/villagebutton/duplicatelabel.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_debuff_description"].font_data
			$backbuttoncanvas/villagebutton/Label.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_debuff_description"].font_data
		if Playervariables.default_font_dict["font_attack_announce"] != null:
			$backbuttoncanvas/rewardbutton/numleft.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_announce"].font_data
		if Playervariables.default_font_dict["font_attack_record"] != null:
			$RewardPicker/backing/board/Label.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_record"].font_data
#	$backbuttoncanvas/rewardbutton.visible = true#false
	$backbuttoncanvas/rewardbutton/numleft.set_text("0")
	$backbuttoncanvas/rewardbutton.disabled = true
	$backbuttoncanvas/rewardbutton.set_modulate(Color(0.3,0.3,0.3))
	if get_parent().get_name() != "map":
		debug = true
	if debug == true:
		print("DEBUG: AUTOGENERATE MAP")
#		method = 2
		generate_region_map("ALongWalk")
		Playervariables.debugmodeon = true
	else:
		self.set_process_input(false)
	_on_viewport_size_changed()

func cure_map_sprite():
	$isometric/rulessprite.debugcureplayersprite()
	map_emotion()

var add_more = false
onready var masternode = get_parent().get_parent()
func enabledisable(truefalse,norequestmoveready = false):
	if debug == false:
		backhovered = false
		$backbuttoncanvas.visible = truefalse
		$foglayer.visible = truefalse
		self.visible = truefalse
		self.set_process_input(truefalse)
		if masternode.mainscene_check() == true:
			masternode.currentscene.set_physics_process(!truefalse)
			masternode.currentscene.set_process_input(!truefalse)
			masternode.currentscene.get_node("CanvasLayer2/Dialogue").set_process_input(!truefalse)
#			masternode.currentscene.set_process(!truefalse)
			masternode.currentscene.refreshselector = !truefalse
			if norequestmoveready == false:
				masternode.currentscene.request_move_ready(!truefalse)
			Input.set_custom_mouse_cursor(load(Mainpreload.cursorpoint))
		if truefalse == true:
			if missionid == 4:
				questinsertnumarray = [Playervariables.questcondition]
			elif missionid == 11:
				questinsertnumarray = [Playervariables.rope_quest]
#				print("Quest:"+str(Playervariables.questcondition))
			quest_animation()
			$isometric/rulessprite.updateplayersprite()
			for value in keysheld.size():
				keysheld[value] = false
			map_emotion()
			if add_more == true:
				$backbuttoncanvas/more_added/plusone.play("plusone")
				add_more = false
#		if truefalse == true:
			$appear.play("map")
			$clickdelay.start(0.4)
			yield($clickdelay,"timeout") #watch out! If you put something after this like input delay, it can fuck if the player immediately backs out
			if questinsertnumarray.size() > 0:
				set_quest_label()
#				print("Updating quest...")
			if playerloc != preparedplayerloc:
				prepare_player_move((playerloc+Vector2(1,2))*tilesizexy)
		else:
			$backbuttoncanvas/villagebutton/confirm.stop()
			_on_confirm_timeout()
			rightclickheld = false
			masternode.exit_map_checks()

func _set_details():
	var detailstring = ""
	match $isometric/tier.get_cellv(preparedplayerloc):
		-1: detailstring += "Easy"
		0: detailstring += "Fair"
		1: detailstring += "Hard"
		2: detailstring += "Awful"
		3: detailstring += "Hell"
	extrainfolines = 2
	match $isometric/icons.get_cellv(preparedplayerloc):
		CHEST:
			detailstring += "\nOdd Chest"
			extrainfolines += 1
	match tiletypearray[preparedplayerloc.y][preparedplayerloc.x]:
		ICON:
			match $isometric/icons.get_cellv(playerloc):
				VILLAGE:detailstring += "\nVillage"
				MAPSPRINGS:detailstring += "\nHotsprings"
				FOXSHRINE:detailstring += "\nShrine"
				_: extrainfolines -= 1
		MOUNTAIN:detailstring += "\nMountains"
		RIVER:detailstring += "\nRiver"
		HILL:detailstring += "\nHills"
		FOREST:detailstring += "\nForest"
		GRASSLAND:detailstring += "\nGrassland"
		DESERT:detailstring += "\nDesert Underground"
		_: extrainfolines -= 1
	$backbuttoncanvas/detailsbacker/Label.set_text(detailstring)
	_set_detail_board()

var extrainfolines = 0
func _set_detail_board():
	$backbuttoncanvas/detailsbacker.anchor_bottom = 0.07 + (questidarray.size()+extraquestlines)*0.05
	if $backbuttoncanvas/detailsbacker.rect_size.y <= 128:
		$backbuttoncanvas/detailsbacker.texture = smallboard
		$backbuttoncanvas/detailsbacker.patch_margin_top = 40
		$backbuttoncanvas/detailsbacker.patch_margin_bottom = 40
	else:
		$backbuttoncanvas/detailsbacker.texture = bigboard
		$backbuttoncanvas/detailsbacker.patch_margin_top = 64
		$backbuttoncanvas/detailsbacker.patch_margin_bottom = 64

var extraquestlines = 0
func _set_board():
#	if questidarray.size() > 2:
	$backbuttoncanvas/questbacker.anchor_bottom = 0.07 + (questidarray.size()+extraquestlines)*0.05
	if $backbuttoncanvas/questbacker.rect_size.y <= 128:
		$backbuttoncanvas/questbacker.texture = smallboard
		$backbuttoncanvas/questbacker.patch_margin_top = 40
		$backbuttoncanvas/questbacker.patch_margin_bottom = 40
	else:
		$backbuttoncanvas/questbacker.texture = bigboard
		$backbuttoncanvas/questbacker.patch_margin_top = 64
		$backbuttoncanvas/questbacker.patch_margin_bottom = 64
	$backbuttoncanvas/questbacker.rect_pivot_offset = $backbuttoncanvas/questbacker.rect_size/2

func mission_name(string):
	$backbuttoncanvas/questbacker/Label2.set_text(string)
	if self.visible == true:
		quest_animation()


onready var musicnode = get_node("/root/Master/LevelMusicLoader")

enum{NONE,QUESTCHANGE,QUESTCOMPLETE}
var storedanimation = NONE
func quest_animation():
	match storedanimation:
		NONE:
			pass
		QUESTCHANGE:
			$backbuttoncanvas/questbacker/questchange.play()
			$backbuttoncanvas/questbacker/quested.play("questchange")
			musicnode.conversation_music("lull")
		QUESTCOMPLETE:
			$backbuttoncanvas/questbacker/questcomplete.play()
			$backbuttoncanvas/questbacker/quested.play("questcomplete")
			musicnode.conversation_music("lull")
	storedanimation = NONE

var secrettilearray = []

enum{QUEST_FIND_SHRINE,QUEST_DEFEAT_RAMGIRLS,QUEST_TALK_TO_FOX,QUEST_RETURN_HOME,QUEST_ROUTEA11,QUEST_ROPEBONDAGE}
var missionid = 0 #mission4 is mission4, 11 is route A11
var questidarray = []
var questinsertspecarray = []
var questinsertnumarray = []
#enum quests{GETSHRINE,GETHYPNO,USEHYPNO,RETURN}
func set_quest(uniquenumber,onoff,sequence=true):
	if questidarray.find(uniquenumber) != -1:
		if onoff == false:
			questidarray.erase(uniquenumber)
			set_quest_label()
	elif onoff == true and questidarray.find(uniquenumber) == -1:
		var validquest = true
		if sequence == true:
			for num in questidarray:
				if num > uniquenumber:
					validquest = false #cannot get quests that are not part of the sequence, use sequence = false to add extra missions
					break
		if validquest == true:
			questidarray.append(uniquenumber)
			set_quest_label()
			if uniquenumber < Playervariables.queststringarray.size()-1:
				if storedanimation != QUESTCOMPLETE:
					storedanimation = QUESTCHANGE
					if missionid == 4 and uniquenumber == 2:
						var highlightloc = find_icon_loc(FOXSHRINE)
						if highlightloc != null:
							for i in range (5):
								for i2 in range (5):
									$isometric/tilehighlight.set_cellv(highlightloc+Vector2(i-2,i2-2),-1)
							$isometric/tilehighlight.set_cellv(highlightloc,$isometric.get_cellv(highlightloc))
			else:
				storedanimation = QUESTCOMPLETE
			if uniquenumber == 6:
				questinsertnumarray = [Playervariables.rope_quest]
			if missionid == 4 and uniquenumber == 3:
				hide_springs_if_true(false)
				var highlightloc = find_icon_loc(FOXSHRINE)
				if highlightloc != null:
					$isometric/tilehighlight.set_cellv(highlightloc,-1)
				highlightloc = find_icon_loc(VILLAGE)
				if highlightloc != null:
					$isometric/tilehighlight.set_cellv(highlightloc,$isometric.get_cellv(highlightloc))
#	if onoff == true:
#		$backbuttoncanvas/questbacker/Label.set_text(Playervariables.queststringarray[uniquenumber])
#		match uniquenumber:
#			quests.GETSHRINE:
#				$backbuttoncanvas/questbacker/Label.set_text("Explore Shrine")
#			quests.GETHYPNO:
#				$backbuttoncanvas/questbacker/Label.set_text("Defeat Ram-Girls")
#			quests.USEHYPNO:
#				$backbuttoncanvas/questbacker/Label.set_text("Go To Shrine")
	_set_board()
	if self.visible == true:
		quest_animation()


func hide_springs_if_true(hide_springs = false):
	if hide_springs == false:
		for secret in secrettilearray:
			$isometric/icons.set_cell(secret.x,secret.y,secret.z)
			if (secret.z in [CHEST,ROAD]) == false:
				tiletypearray[secret.y][secret.x] = ICON
	else:
#		if missionid == 11:
#			Playervariables.questcondition = 9
		for secret in secrettilearray:
			$isometric/icons.set_cell(secret.x,secret.y,HIDDENSPRINGS)
			if (secret.z in [CHEST,ROAD]) == false:
				tiletypearray[secret.y][secret.x] = HIDDENSPRINGS


func set_quest_label():
	extraquestlines = 1
	var queststring = ""# = ""#\n"
	for num in questidarray:
		if questinsertspecarray.find(num) > -1:
			queststring += Playervariables.queststringarray[num]+"\n("+str(questinsertnumarray[questinsertspecarray.find(num)])+")\n"
			extraquestlines += 1
			if missionid == 4:
				queststring += Playervariables.queststringarray[5]#"OR\n??? Rams"
				extraquestlines += 2
				var highlightloc = find_icon_loc(FOXSHRINE)
				if highlightloc != null:
					$isometric/tilehighlight.set_cellv(highlightloc,-1)
					for i in range (5):
						for i2 in range (5):
							if tiletypearray[highlightloc.y+(i2-2)][highlightloc.x+(i-2)] == MOUNTAIN:
								pass
							else:
								$isometric/tilehighlight.set_cellv(highlightloc+Vector2(i-2,i2-2),$isometric.get_cellv(highlightloc+Vector2(i-2,i2-2)))
					$isometric/tilehighlight.set_cellv(highlightloc,-1)
		else:
			queststring += Playervariables.queststringarray[num]+"\n"
	if extraquestlines <= 2:
		queststring = "\n" + queststring
	$backbuttoncanvas/questbacker/Label.set_text(queststring)

const zoomcapmin = Vector2(0.5,0.5)
const zoomcapmax = Vector2(2,2)
func generate_new_map(newmap = false,mapcase = 0):
	var backdoor = false
	if mapcase == 12:
		mapcase = 11
		backdoor = true
	if newmap == true:
		$isometric.scale = Vector2(clamp(1/Playervariables.lastzoom.x,zoomcapmin.x,zoomcapmax.x),clamp(1/Playervariables.lastzoom.y,zoomcapmin.y,zoomcapmax.y))
		$foglayer/fogofwar.scale = $isometric.scale
#	if newmap == true:
		if mapcase == 4:
			Playervariables.questcondition = 9
			generate_region_map("ALongWalk")
		elif mapcase == 11:
			generate_region_map("RouteA11")
			Playervariables.questcondition = 10
			Playervariables.maxresistance = 30
			Playervariables.playerresistance = 30
		elif mapcase == 17:
			generate_region_map("Glassed Desert")
			Playervariables.maxresistance = 30
			Playervariables.playerresistance = 30
		else:
			generate_region_map()
		if Playervariables.rope_quest > -1:
			set_quest(6,true,false)
		if backdoor == true:
			for x in range(20):
				for y in range(20):
					if $isometric/icons.get_cell(x,y) == 5:
						preparedplayerloc = Vector2(x,y)
						neutralize_tile()
						move_map_position((get_viewport().size * 0.5)-($isometric/rulessprite.position * $isometric.scale))
						return
		#generate_map()

enum{CATGIRL=0,FOXGIRL=1,WEREWOLF=2,RAMGIRL=3,GHOSTFOX=4,DRAGONHARPY=5,IMP=6}
const enemytier1 = [CATGIRL,FOXGIRL,RAMGIRL]
const enemytier2 = [WEREWOLF]
const DEFAULTENEMIES = 2
#const ENEMY_AGGRO_RANGE = [0.0,2.0,3.5,5.0]
const ENEMY_AGGRO_RANGE = [1.0,3.3,5.0]
#var spawnenemypath = CATGIRL
#var spawnenemyend = FOXGIRL
#var spawnenemydepth = RAMGIRL
var overstar
var playerstar
const basemapsize = Vector2(14,14)
var mapsize = Vector2(14,14)
const BORDER = 2
var routes = 3
var routespoints = 3
var nodisabledistance = 4
var disablerand = 0.9
var routespointsrand = 1 #+/- value
var endpointvector = Vector2(3,3)
var endpoint
var startpointvector = Vector2(3,3)
var startpoint
#var enemyvectors = []
#var enemyobjects = []
#var occupy2darray = []
const region2darray = [
	[1,1,1,1,1,1,1,1,1,1,1,1],
	[1,1,1,1,1,1,1,1,1,1,1,1],
	[1,1,0,0,7,0,0,0,0,0,1,1],
	[1,1,0,0,1,0,0,0,0,0,1,1],
	[1,1,0,0,1,1,1,1,1,7,1,1],
	[1,1,0,0,1,0,0,0,0,0,1,1],
	[1,1,8,0,1,0,7,1,0,0,1,1],
	[1,1,0,0,0,0,0,1,0,0,1,1],
	[1,1,0,0,0,1,1,1,1,0,1,1],
	[1,1,1,0,0,0,0,7,0,0,1,1],
	[1,1,1,1,1,1,1,1,1,1,1,1],
	[1,1,1,1,1,1,1,1,1,1,1,1],
	]
const SCROLLBORDER = 0
#onready var method = Playervariables.debugmethod
#enum methods{EXPLORATION,PATHS,REGION}

var enemyobjects = []
var spawnlocarray = []
const tilemapnodes = PoolStringArray(["isometric","isometric/icons","isometric/tier","spawnloc"])
func loadthefilenow(mapstring):
	var file = File.new()
	file.open("res://fixedscenes/worldmap/"+mapstring+".bin", File.READ)
	mapsize.x = file.get_8()
	mapsize.y = file.get_8()
	for tilenode in tilemapnodes:
		if tilenode == "spawnloc":
			for x in mapsize.x:
				for y in mapsize.y:
					var tile = file.get_8()
					if tile > 0:
						add_enemy(Vector2(x,y),tile-1)
						#spawnlocarray.append(Vector3(x,y,tile-1))
		else:
			for x in mapsize.x:
				for y in mapsize.y:
					get_node(tilenode).set_cell(x,y,file.get_8()-1)
#	for x in mapsize.x:
#		for y in mapsize.y:
#			get_node("watermaptops").set_cell(x,y,file.get_8()-1)
	file.close()

var tiletypearray = []
#enum tiles{MOUNTAIN1,MOUNTAIN2,MOUNTAIN3,SLOPEUPLEFT1,SLOPEUPLEFT2,SLOPEUPRIGHT1,SLOPEUPRIGHT2,DOUBLESLOPE1,HILLS1,HILLS2,HILLS3,FOREST1,FOREST2,FOREST3,GRASSLAND1,GRASSLAND2,GRASSLAND3,GRASSLAND4,DOUBLESLOPE2,INNERSLOPE1,RIVERSTRAIGHT1,RIVERBEND1,RIVERBEND2,RIVERBEND3}
enum {VILLAGE=0,FOXSHRINE=1,MAPSPRINGS=2,CHEST=3,HIDDENSPRINGS=4,RURAL=5,PLACEHOLDER=6,ROAD=7,FOG=8,PARISH=9,HOME5=10,ICON=100,MOUNTAIN=101,HILL=102,FOREST=103,GRASSLAND=104,RIVER=105,WETLAND=106,DESERT=107} #also update on playervariables
const tilearray = [MOUNTAIN,MOUNTAIN,MOUNTAIN,MOUNTAIN,MOUNTAIN,MOUNTAIN,MOUNTAIN,MOUNTAIN,HILL,HILL,HILL,FOREST,FOREST,FOREST,GRASSLAND,GRASSLAND,GRASSLAND,GRASSLAND,MOUNTAIN,MOUNTAIN,RIVER,RIVER,RIVER,RIVER,RIVER,RIVER,RIVER,RIVER,WETLAND,DESERT,DESERT,DESERT]
#enum icons{VILLAGE,FOXSHRINE,MAPSPRINGS,CHEST,HIDDENSPRINGS}
func generate_region_map(mapstring = "Overworld"):
	if mapstring == "ALongWalk":
		$foglayer/fogofwar.clear()
		missionid = 4
		questinsertspecarray = [1]
		questinsertnumarray = [8]
	elif mapstring == "RouteA11":
		$backbuttoncanvas/questbacker/Label2.visible = false
		missionid = 11
		questinsertspecarray = [6]
		questinsertnumarray = [10]
		get_node("/root/Master").call("update_quest",4,true)
	elif mapstring == "Glassed Desert":
		missionid = 17
		$backbuttoncanvas/questbacker/Label2.visible = false
	if Playervariables.namemissiondict.has(missionid):
		mission_name(Playervariables.namemissiondict[missionid])
	else:
		mission_name("...")
#	method = methods.REGION
#	enemyvectors = []
	$isometric.clear()
	delete_selectors()
	$isometric/tilevisited.clear()
#	$map.clear()
#	$foglayer/fogofwar.clear()
#	$map/occupymap.clear()
#	for child in $map/selectorsdebug.get_children():
#		child.queue_free()
#	for child in $map/residents.get_children():
#		child.queue_free()
	loadthefilenow(mapstring)
	randomize()
	process_scroll_limit()
	overstar = AStar2D.new()
	playerstar = AStar2D.new()
	startpointvector = Vector2(3,3)
	endpointvector = Vector2(3,3)
#	occupy2darray = []
#	enemyvectors = []
#	enemyobjects = []
	tiletypearray = []
#	mapsize = basemapsize
	for y in range(mapsize.y):
#		occupy2darray.append([])
		tiletypearray.append([])
		for _x in range(mapsize.x):
#			occupy2darray[y].append(-1)
			tiletypearray[y].append(-1)
#			$map.set_cell(x,y,DEEPFOREST)
#	if method in [methods.EXPLORATION,methods.REGION]:
	for y in range(mapsize.y):
		for x in range(mapsize.x):
			var cell = $isometric.get_cell(x,y)
			if cell > -1:
#				if x >= BORDER and y >= BORDER and x < mapsize.x-BORDER and y < mapsize.y-BORDER:
				tiletypearray[y][x] = tilearray[cell]
				var currentpoint = overstar.get_available_point_id()
				var currentvector = Vector2(x+1,y+2)*tilesizexy
				overstar.add_point(currentpoint,currentvector)
				playerstar.add_point(currentpoint,currentvector)
				var leftpoint = overstar.get_closest_point(currentvector -Vector2(240,0))
				if leftpoint != currentpoint:
					overstar.connect_points(currentpoint,leftpoint)
					playerstar.connect_points(currentpoint,leftpoint)
				var abovepoint = overstar.get_closest_point(currentvector -Vector2(0,100))
				var diaguplpoint = overstar.get_closest_point(currentvector -Vector2(100,100))
				var diaguprpoint = overstar.get_closest_point(currentvector -Vector2(-100,100))
				if abovepoint != currentpoint:
					overstar.connect_points(currentpoint,abovepoint)
					playerstar.connect_points(currentpoint,abovepoint)
				if diaguplpoint != currentpoint:
					overstar.connect_points(currentpoint,diaguplpoint)
					playerstar.connect_points(currentpoint,diaguplpoint)
				if diaguprpoint != currentpoint:
					overstar.connect_points(currentpoint,diaguprpoint)
					playerstar.connect_points(currentpoint,diaguprpoint)
				playerstar.set_point_disabled(currentpoint,true)
#					if tiletypearray[y][x] in [MOUNTAIN]:
#						var cell2 = $isometric/icons.get_cell(x,y)
#						if cell2 == -1:
#							playerstar.set_point_disabled(currentpoint,true)
#		if method == methods.REGION:
	for y in range(mapsize.y):
		for x in range(mapsize.x):
			var cell = $isometric/icons.get_cell(x,y)
			if cell > -1:
				match cell:
					CHEST,ROAD,FOG:
						pass
					MAPSPRINGS:
						if Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] < 6:# or missionid == 11:
							secrettilearray.append(Vector3(x,y,MAPSPRINGS))
							$isometric/icons.set_cell(x,y,HIDDENSPRINGS)
						else:
							tiletypearray[y][x] = ICON
					_:
						tiletypearray[y][x] = ICON
			match cell:
				VILLAGE:
					startpointvector = Vector2(x,y)
					var closepoint = playerstar.get_closest_point((Vector2(x+1,y+2))*tilesizexy,true)
					playerstar.set_point_disabled(closepoint,false)
				FOXSHRINE:
					if missionid == 4:
						$isometric/tilehighlight.set_cell(x,y,$isometric.get_cell(x,y))
				FOG:
					tiletypearray[y][x] = RIVER
				RURAL:
					pass
				ROAD:
#					if Playervariables.goat_furry_mascot_character == false:
#						$isometric/icons.set_cell(x,y,-1)
#					else:
					playerstar.set_point_disabled(playerstar.get_closest_point((Vector2(x+1,y+2))*tilesizexy,true),false)
					$isometric/tilevisited.set_cell(x,y,$isometric.get_cell(x,y))
				HOME5:
					if Playervariables.Endings[Playervariables.EndingsRef.RAMGIRL] == 0:
						$isometric/icons.set_cell(x,y,-1)
				PARISH:
					if missionid == 17:
						startpointvector = Vector2(x,y)
						var closepoint = playerstar.get_closest_point((Vector2(x+1,y+2))*tilesizexy,true)
						playerstar.set_point_disabled(closepoint,false)
#				MAPSPRINGS:
#					var closepoint = playerstar.get_closest_point((Vector2(x+1,y+2))*tilesizexy,true)
#					playerstar.set_point_disabled(closepoint,false)
#					FOXSHRINE:
#						var closepoint = playerstar.get_closest_point((Vector2(x+1,y+2))*tilesizexy,false)
#						playerstar.set_point_disabled(closepoint,false)
#					$map.set_cell(x,y,cell)
#					occupy2darray[y][x] = 0
#					match cell:
#						VILLAGE:
#							if startpointvector == Vector2(3,3):
#								startpointvector = Vector2(x,y)
#						GOAL:
#							endpointvector = Vector2(x,y)
#						DEEPFOREST:
#							var point = overstar.get_closest_point(Vector2(x+0.5,y+0.5)*tilesizexy,true)
#							playerstar.set_point_disabled(point,true)
#							occupy2darray[y][x] = -1
#						_:
#							var pos = Vector2(x+0.5,y+0.5)*tilesizexy
#							if (randf()*8) +2 < (x+y):
#								add_enemy(pos,spawnenemypath)
#							if (randf()*20) + 5 < (x+y):
#								add_enemy(pos,spawnenemydepth)
#	if method in [methods.EXPLORATION,methods.REGION]:
#		create_fog(mapsize.y+2)
#		mindistance = tilesize
	if missionid != 4:
		create_fog()
	var endpointvectorv = (endpointvector+Vector2(1,2))*tilesizexy
	var startpointvectorv = (startpointvector+Vector2(1,2))*tilesizexy
	endpoint = overstar.get_closest_point(endpointvectorv)
	startpoint = overstar.get_closest_point(startpointvectorv)
#	for point in playerstar.get_points():
#		playerstar.set_point_disabled(point,true)
#	var mapnoborder = mapsize - Vector2(BORDER*2,BORDER*2)
#	$map.set_cellv(endpointvector,GOAL)
#	$map.set_cellv(startpointvector,VILLAGE)
#	add_enemy(endpointvectorv,spawnenemyend)
	$isometric/rulessprite.position = startpointvectorv
	playerloc = startpointvector
	preparedplayerloc = playerloc
#	$isometric.position = (get_viewport().size * 0.5)-($isometric/rulessprite.position * $isometric.scale)
	move_map_position((get_viewport().size * 0.5)-($isometric/rulessprite.position * $isometric.scale))
#	$foglayer/fogofwar.scale = $isometric.scale
#	$foglayer/fogofwar.position = $isometric.position
	clear_fog(playerloc,clamp(Playervariables.playervision-1,4,99))
	if Playervariables.CurrentClass == Playervariables.raceRAM:
		var homeloc = find_icon_loc(HOME5)
		if homeloc != null:
			clear_fog(homeloc+Vector2(0,2),3)
			playerstar.set_point_disabled(playerstar.get_closest_point((homeloc+Vector2(1,2))*tilesizexy,true),false)
			$isometric/tilevisited.set_cellv(homeloc,$isometric.get_cellv(homeloc))
#	for y in range(occupy2darray.size()):
#		for x in range(occupy2darray[y].size()):
#			if x >= BORDER and y >= BORDER and x < mapsize.x-BORDER and y < mapsize.y-BORDER:
#				var occupynum = occupy2darray[y][x]
#				if occupynum == 0:
#					playerstar.set_point_disabled(playerstar.get_closest_point(Vector2(x+0.5,y+0.5)*tilesizexy,true),false)
#				elif occupynum > -1:
#					$map/occupymap.set_cell(x,y,clamp(occupynum-1,0,3))
#
#func generate_map():
#	enemyvectors = []
#	$map.clear()
#	$foglayer/fogofwar.clear()
#	$map/occupymap.clear()
#	for child in $map/selectorsdebug.get_children():
#		child.queue_free()
#	for child in $map/residents.get_children():
#		child.queue_free()
#	randomize()
#	process_scroll_limit()
#	overstar = AStar2D.new()
#	playerstar = AStar2D.new()
#	startpointvector = Vector2(3,3)
#	endpointvector = Vector2(3,3)
#	occupy2darray = []
#	enemyvectors = []
#	enemyobjects = []
#	mapsize = basemapsize
#	if method == methods.PATHS:
#		mapsize.y += 6
#		mapsize.x -= 1
#		routes = 0
#	elif method == methods.REGION:
#		mapsize.y = region2darray.size()
#		mapsize.x = region2darray[0].size()
#		routes = 0
#	for y in range(mapsize.y):
#		occupy2darray.append([])
#		for x in range(mapsize.x):
#			occupy2darray[y].append(-1)
#			$map.set_cell(x,y,DEEPFOREST)
#	if method in [methods.EXPLORATION,methods.REGION]:
#		for y in range(mapsize.y):
#			for x in range(mapsize.x):
#				if x >= BORDER and y >= BORDER and x < mapsize.x-BORDER and y < mapsize.y-BORDER:
#					var currentpoint = overstar.get_available_point_id()
#					var currentvector = Vector2(x+0.5,y+0.5)*tilesize
#					overstar.add_point(currentpoint,currentvector)
#					playerstar.add_point(currentpoint,currentvector)
#					var leftpoint = overstar.get_closest_point(currentvector -Vector2(100,0))
#					if leftpoint != currentpoint:
#						overstar.connect_points(currentpoint,leftpoint)
#						playerstar.connect_points(currentpoint,leftpoint)
#					var abovepoint = overstar.get_closest_point(currentvector -Vector2(0,100))
#					var diaguplpoint = overstar.get_closest_point(currentvector -Vector2(100,100))
#					var diaguprpoint = overstar.get_closest_point(currentvector -Vector2(-100,100))
#					if abovepoint != currentpoint:
#						overstar.connect_points(currentpoint,abovepoint)
#						playerstar.connect_points(currentpoint,abovepoint)
#					if diaguplpoint != currentpoint:
#						overstar.connect_points(currentpoint,diaguplpoint)
#						playerstar.connect_points(currentpoint,diaguplpoint)
#					if diaguprpoint != currentpoint:
#						overstar.connect_points(currentpoint,diaguprpoint)
#						playerstar.connect_points(currentpoint,diaguprpoint)
#		if method == methods.REGION:
#			for y in range(mapsize.y):
#				for x in range(mapsize.x):
#					var cell = region2darray[y][x]
#					$map.set_cell(x,y,cell)
#					occupy2darray[y][x] = 0
#					match cell:
#						VILLAGE:
#							if startpointvector == Vector2(3,3):
#								startpointvector = Vector2(x,y)
#						GOAL:
#							endpointvector = Vector2(x,y)
#						DEEPFOREST:
#							var point = overstar.get_closest_point(Vector2(x+0.5,y+0.5)*tilesize,true)
#							playerstar.set_point_disabled(point,true)
#							occupy2darray[y][x] = -1
#						_:
#							var pos = Vector2(x+0.5,y+0.5)*tilesize
#							if (randf()*8) +2 < (x+y):
#								add_enemy(pos,spawnenemypath)
#							if (randf()*20) + 5 < (x+y):
#								add_enemy(pos,spawnenemydepth)
#	elif method == methods.PATHS:
#		endpointvector = Vector2(floor(mapsize.x/2),mapsize.y-(BORDER+3))
#		startpointvector = Vector2(floor(mapsize.x/2),BORDER+1)
#		var currentpoint = overstar.get_available_point_id()
#		overstar.add_point(currentpoint,(startpointvector+Vector2(0.5,0.5))*tilesize)
#		playerstar.add_point(currentpoint,(startpointvector+Vector2(0.5,0.5))*tilesize)
##		currentpoint = overstar.get_available_point_id()
##		overstar.add_point(currentpoint,(endpointvector+Vector2(0.5,0.5))*tilesize)
##		playerstar.add_point(currentpoint,(endpointvector+Vector2(0.5,0.5))*tilesize)
##		var currentvector = endpointvector
##		var abovepoint = overstar.get_closest_point(currentvector -Vector2(0,tilesize*3))
##		var diaguplpoint = overstar.get_closest_point(currentvector -Vector2(tilesize*3,tilesize*3))
##		var diaguprpoint = overstar.get_closest_point(currentvector -Vector2(tilesize*3,tilesize*3))
##		if abovepoint != currentpoint and overstar.get_point_position(abovepoint).y < currentvector.y-2:
##			overstar.connect_points(currentpoint,abovepoint)
##			playerstar.connect_points(currentpoint,abovepoint)
##		if diaguplpoint != currentpoint and overstar.get_point_position(diaguplpoint).y < currentvector.y-2:
##			overstar.connect_points(currentpoint,diaguplpoint)
##			playerstar.connect_points(currentpoint,diaguplpoint)
##		if diaguprpoint != currentpoint and overstar.get_point_position(diaguprpoint).y < currentvector.y-2:
##			overstar.connect_points(currentpoint,diaguprpoint)
##			playerstar.connect_points(currentpoint,diaguprpoint)
#		$map.set_cellv(startpointvector,WOODS)
#		$map.set_cellv(startpointvector+Vector2(1,1),DIAGPATH,false,false)
#		$map.set_cellv(startpointvector+Vector2(-1,1),DIAGPATH,true,false)
#		$map.set_cellv(startpointvector+Vector2(0,1),TREEDOWN)
#		$map.set_cellv(endpointvector - Vector2(-1,1),DIAGPATH,false,true)
#		$map.set_cellv(endpointvector - Vector2(1,1),DIAGPATH,true,true)
#		$map.set_cellv(endpointvector - Vector2(0,1),TREEUP)
#	if method == methods.EXPLORATION:
#		endpointvector = mapsize - Vector2(BORDER+2,BORDER+2)
#		startpointvector = Vector2(BORDER+1,BORDER+1)
#	if method == methods.PATHS:
#		for y3 in range(int(mapsize.y/3)): #x swapped with y for point connecting purposes
#			for x3 in range(int(mapsize.x/3)):
#				var y = y3*3
#				var x = x3*3
#				if (x >= BORDER and y >= (BORDER+3) and x < mapsize.x-BORDER and y < mapsize.y-(BORDER+3)) or Vector2(x,y) == endpointvector:
#					var currentpoint = overstar.get_available_point_id()
#					var currentvector = Vector2(x+0.5,y+0.5)*tilesize
#					overstar.add_point(currentpoint,currentvector)
#					playerstar.add_point(currentpoint,currentvector)
#					var abovepoint = overstar.get_closest_point(currentvector - Vector2(0,tilesize*3),true)
#					var diaguplpoint = overstar.get_closest_point(currentvector -Vector2(tilesize*3,tilesize*3),true)
#					var diaguprpoint = overstar.get_closest_point(currentvector -Vector2(-tilesize*3,tilesize*3),true)
#					if abovepoint != currentpoint and overstar.get_point_position(abovepoint).y < currentvector.y-2:
#						overstar.connect_points(currentpoint,abovepoint)
#						playerstar.connect_points(currentpoint,abovepoint)
#					if diaguplpoint != currentpoint and overstar.get_point_position(diaguplpoint).y < currentvector.y-2:
#						overstar.connect_points(currentpoint,diaguplpoint)
#						playerstar.connect_points(currentpoint,diaguplpoint)
#					if diaguprpoint != currentpoint and overstar.get_point_position(diaguprpoint).y < currentvector.y-2:
#						overstar.connect_points(currentpoint,diaguprpoint)
#						playerstar.connect_points(currentpoint,diaguprpoint)
#					$map.set_cell(x,y,WOODS)
#					occupy2darray[y][x] = 0
#					if Vector2(x,y) == endpointvector:
#						pass
#					else:
#						if x3 < 3:
#							$map.set_cell(x+1,y,DEEPFOREST)
#						if y3 > 1 and x3 < 3:
#							$map.set_cell(x+1,y-1,DIAGPATH,false,true)
#						if x3 < 3:
#							$map.set_cell(x+1,y+1,DIAGPATH,false,false)
#						if x3 > 1:
#							$map.set_cell(x-1,y,DEEPFOREST)
#						if y3 > 1 and x3 > 1:
#							$map.set_cell(x-1,y-1,DIAGPATH,true,true)
#						if x3 > 1:
#							$map.set_cell(x-1,y+1,DIAGPATH,true,false)
#						if x3 == 2 or (y3 > 1 and y3 < 4):
#							$map.set_cell(x,y+1,TREEDOWN)
#						if y3 > 2 or x3 == 2:
#							$map.set_cell(x,y-1,TREEUP)
#	if method in [methods.EXPLORATION,methods.REGION]:
#		create_fog(mapsize.y+2)
#		mindistance = tilesize
#	elif method == methods.PATHS:
#		mindistance = tilesize*3
#		for point in overstar.get_points():
#			var pointpos = overstar.get_point_position(point)
#			if pointpos.y > tilesize*10 and randf() > 0.5:
#				add_enemy(pointpos,spawnenemydepth)
#			elif pointpos.y > tilesize*7 and randf() > 0.5:
#				add_enemy(pointpos,spawnenemydepth)
#			if point != 0 and pointpos.y < tilesize*10 and randf() > 0.2:
#				add_enemy(pointpos,spawnenemypath)
#	var endpointvectorv = (endpointvector+Vector2(0.5,0.5))*tilesize
#	var startpointvectorv = (startpointvector+Vector2(0.5,0.5))*tilesize
#	endpoint = overstar.get_closest_point(endpointvector*tilesize+Vector2(0.5,0.5))
#	startpoint = overstar.get_closest_point(startpointvector*tilesize+Vector2(0.5,0.5))
#	for point in playerstar.get_points():
#		playerstar.set_point_disabled(point,true)
#	var mapnoborder = mapsize - Vector2(BORDER*2,BORDER*2)
#	for i in range(routes):
#		var randpoints = PoolIntArray([])
#		var lastpoint = startpoint
#		var nextpoint
#		var truepath = PoolIntArray([])
#		for _i2 in range(routespoints+ randi()%((routespointsrand*2)+1) - routespointsrand):
#			nextpoint = overstar.get_closest_point(Vector2(0.5,0.5)+tilesize*Vector2(randi()%int(mapnoborder.x+BORDER),randi()%int(mapnoborder.y+BORDER)))
#			if nextpoint == -1:
#				truepath = null
#				break
#			randpoints.append(nextpoint)
#			var nextpath = overstar.get_id_path(lastpoint,nextpoint)
#			if nextpath.size() > 0:
#				truepath += nextpath
#				lastpoint = nextpoint
#				truepath.remove(truepath.size()-1)
#		if truepath != null:
#			truepath += overstar.get_id_path(lastpoint,endpoint)
#			for point in (truepath):
#				if overstar.is_point_disabled(point) == false:
#					var pointpos = overstar.get_point_position(point)
#					var pointloc = (pointpos / tilesize) -  Vector2(0.5,0.5)
##					var newselector = load(Mainpreload.Selector).instance()
##					$map/selectorsdebug.add_child(newselector)
##					newselector.position = pointpos + Vector2((randf()-0.5)*90,(randf()-0.5)*90)
#					occupy2darray[pointloc.y][pointloc.x] = max(occupy2darray[pointloc.y][pointloc.x],0)
#					if i == 0:
#						$map.set_cellv(pointloc,WOODS)
##						newselector.modulate = Color(0.5,0.5,0.5,0.3)
#					elif i == 1:
#						$map.set_cellv(pointloc,HILLS)
##						newselector.modulate = Color(1,0.5,0.5,0.3)
#					elif i == 2:
#						$map.set_cellv(pointloc,ROAD)
##						newselector.modulate = Color(0.5,1,0.5,0.3)
#					elif i == 3:
#						$map.set_cellv(pointloc,LIGHTTILE)
##						newselector.modulate = Color(0.5,0.5,1,0.3)
#					else:
#						$map.set_cellv(pointloc,SHALLOWS)
##						newselector.modulate = Color(0.8,0.8,0.8,0.3)
##					if $map.get_cellv(pointloc) == DEEPFOREST:
##						print("Why, god?")
#					if (pointloc-startpointvector).length() > nodisabledistance and (pointloc-endpointvector).length() > nodisabledistance and randf() < disablerand:
#						overstar.set_point_disabled(point)
#						add_enemy(pointpos,spawnenemypath)
#					if randf() > 0.4:
#						var enddistance = (pointpos - endpointvectorv).length()
#						if enddistance < 3*tilesize and enddistance > 20:
#							add_enemy(pointpos,spawnenemydepth)
##					playerstar.set_point_disabled(point,false)
#	$map.set_cellv(endpointvector,GOAL)
#	$map.set_cellv(startpointvector,VILLAGE)
#	add_enemy(endpointvectorv,spawnenemyend)
#	$map/rulessprite.position = startpointvectorv
#	playerloc = startpointvector
#	clear_fog(playerloc,clamp(Playervariables.playervision-4,2,99))
#	for y in range(occupy2darray.size()):
#		for x in range(occupy2darray[y].size()):
#			if x >= BORDER and y >= BORDER and x < mapsize.x-BORDER and y < mapsize.y-BORDER:
#				var occupynum = occupy2darray[y][x]
#				if occupynum == 0:
#					playerstar.set_point_disabled(playerstar.get_closest_point(Vector2(x+0.5,y+0.5)*tilesize,true),false)
#				elif occupynum > -1:
#					$map/occupymap.set_cell(x,y,clamp(occupynum-1,0,3))

const OFFSETNUM = 2
func create_fog():
	for y in range(int((OFFSETNUM+mapsize.y))):
		for x in range(int((OFFSETNUM+mapsize.x))):
			$foglayer/fogofwar.set_cell(x-OFFSETNUM,y-OFFSETNUM,2)
	for i in ceil(2+((OFFSETNUM+mapsize.x)/3)):
		for iy in ceil(2+((OFFSETNUM+mapsize.y)/3)):
			$foglayer/fogofwar.update_bitmask_area(Vector2(-OFFSETNUM+i*3,-OFFSETNUM+iy*3))
	
func clear_fog(point,vision):
#	if method == methods.PATHS:
#		return#vision += 3
	for x in range(vision):
		for y in range(vision):
			var currentmagnitude = Vector2(x,y).length()
			if currentmagnitude < vision-1:
#				if state == -1:
				$foglayer/fogofwar.set_cellv(point+Vector2(-x,-y),-1)
				$foglayer/fogofwar.set_cellv(point+Vector2(x,y),-1)
				$foglayer/fogofwar.set_cellv(point+Vector2(x,-y),-1)
				$foglayer/fogofwar.set_cellv(point+Vector2(-x,y),-1)
#				currentfog.set_cellv(point+Vector2(-x,-y),state)
#				currentfog.set_cellv(point+Vector2(x,y),state)
#				currentfog.set_cellv(point+Vector2(x,-y),state)
#				currentfog.set_cellv(point+Vector2(-x,y),state)
			elif currentmagnitude <= vision:
				if $foglayer/fogofwar.get_cellv(point+Vector2(-x,-y)) != -1:
					$foglayer/fogofwar.set_cellv(point+Vector2(-x,-y),-1)
					$foglayer/fogofwar.update_bitmask_area(point+Vector2(-x,-y)) #remember, update area AFTER removing it.
				if $foglayer/fogofwar.get_cellv(point+Vector2(x,y)) != -1:
					$foglayer/fogofwar.set_cellv(point+Vector2(x,y),-1)
					$foglayer/fogofwar.update_bitmask_area(point+Vector2(x,y))
				if $foglayer/fogofwar.get_cellv(point+Vector2(x,-y)) != -1:
					$foglayer/fogofwar.set_cellv(point+Vector2(x,-y),-1)
					$foglayer/fogofwar.update_bitmask_area(point+Vector2(x,-y))
				if $foglayer/fogofwar.get_cellv(point+Vector2(-x,y)) != -1:
					$foglayer/fogofwar.set_cellv(point+Vector2(-x,y),-1)
					$foglayer/fogofwar.update_bitmask_area(point+Vector2(-x,y))

func add_enemy(loc,type):
#	var loc = (locv-Vector2(64,64))/tilesizexy
	var locv = (loc+Vector2(1,2))*tilesizexy
	if (Vector3(loc.x,loc.y,type) in spawnlocarray) == false:
		enemyobjects.append([])
		spawnlocarray.append(Vector3(loc.x,loc.y,type))
		var enemynumbers = 1
		match type:
			CATGIRL:
				enemynumbers = 5
			RAMGIRL:
				enemynumbers = 3
			FOXGIRL:
				enemynumbers = 2
			WEREWOLF:
				enemynumbers = 1
		var rotatestep = (2*PI)/clamp(enemynumbers,1,999)
		for i in range(enemynumbers):
			var enemy = load(Mainpreload.Enemy).instance()
			enemy.monsternum = type
			enemy.call("spawnpreview",true)
			enemy.position = locv + Vector2(30+ (randf())*100 ,30+ (randf())*100).rotated(rotatestep*i)
			enemy.scale = Vector2(0.5,0.5)
			$isometric/residents.add_child(enemy)
			enemyobjects[-1].append(enemy)
#		occupy2darray[loc.y][loc.x] += 1
#		playerstar.set_point_disabled(playerstar.get_closest_point(locv,true),true)

const smallboard = preload("res://Assets/ui/questboardsmall.png")
const bigboard = preload("res://Assets/ui/questboard.png")

var firstrun = true
var recentsizechange = false
func _on_viewport_size_changed():
	if recentsizechange == false:
		recentsizechange = true #this makes it so it only updates the viewport every 4 frames
		if firstrun == true:
			firstrun = false
		else:
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
		process_scroll_limit()
		$backbuttoncanvas/rewardbutton.rect_pivot_offset = ($backbuttoncanvas/rewardbutton.rect_size/2)
		var ratio = get_viewport_rect().size / Playervariables.basescreensize
		var transratiox = ratio.x
		var othertransratio = ratio.y
		var thirdtransratio = ratio.x
		if Playervariables.modarray[Playervariables.mods.TRANSLATION] == true:
			if Playervariables.default_font_dict["font_debuff_description"] != null:
				transratiox = transratiox*Playervariables.default_font_dict["font_debuff_description_size"]
			if Playervariables.default_font_dict["font_attack_announce"] != null:
				othertransratio = transratiox*Playervariables.default_font_dict["font_attack_announce_size"]
			if Playervariables.default_font_dict["font_attack_record"] != null:
				thirdtransratio = thirdtransratio*Playervariables.default_font_dict["font_attack_record_size"]
		var deployablescale = (ratio.x*0.45) + 0.1
		$RewardPicker/backing/board/rewardploy.rect_pivot_offset = $RewardPicker/backing/board/rewardploy.rect_size/2
		$backbuttoncanvas/questbacker.rect_pivot_offset = $backbuttoncanvas/questbacker.rect_size/2
		$RewardPicker/backing/board/rewardploy.rect_scale = Vector2(deployablescale,deployablescale)
		$backbuttoncanvas/rewardbutton/numleft.get("custom_fonts/font").size = 40*othertransratio
		$backbuttoncanvas/rewardbutton/numleft.get("custom_fonts/font").outline_size = 3*othertransratio
		$backbuttoncanvas/villagebutton/Label.get("custom_fonts/font").size = 23*transratiox
		$backbuttoncanvas/villagebutton/Label.get("custom_fonts/font").outline_size = round(2*transratiox)
		$backbuttoncanvas/questbacker/Label.get("custom_fonts/font").size = 16*transratiox
		$backbuttoncanvas/questbacker/Label.get("custom_fonts/font").outline_size = round(1.5*transratiox)
		$RewardPicker/backing/board/villagebutton/duplicatelabel.get("custom_fonts/font").size = 24*transratiox
		$RewardPicker/backing/board/villagebutton/duplicatelabel.get("custom_fonts/font").outline_size = 2*transratiox
		$RewardPicker/backing/board/Label.get("custom_fonts/font").size = 24*thirdtransratio
		$RewardPicker/backing/board/Label.get("custom_fonts/font").outline_size = round(2*thirdtransratio)
		_set_board()
#		$backbuttoncanvas/questbacker/Label.get("custom_fonts/font").size = 22*yratio
#		$backbuttoncanvas/villagebutton/Label.get("custom_fonts/font").outline_size = round(2*yratio)
		recentsizechange = false

enum{LOWERX,UPPERX,LOWERY,UPPERY}
var limits = [0,20,0,20]
func process_scroll_limit():
	var portsizehalf = get_viewport().size/2
	var tilesizeratio = tilesizexy*$isometric.scale
	limits[LOWERX] = portsizehalf.x - ((mapsize.x+SCROLLBORDER)*tilesizeratio.x)
	limits[UPPERX] = portsizehalf.x - (SCROLLBORDER * tilesizeratio.x)
	if $isometric.position.x < limits[LOWERX]:
		$isometric.position.x = limits[LOWERX]
	elif $isometric.position.x > limits[UPPERX]:
		$isometric.position.x = limits[UPPERX]
	limits[LOWERY] = portsizehalf.y - ((mapsize.y+SCROLLBORDER)*tilesizeratio.y)
	limits[UPPERY] = portsizehalf.y + (SCROLLBORDER * tilesizeratio.y)
	if $isometric.position.y < limits[LOWERY]:
		$isometric.position.y = limits[LOWERY]
	elif $isometric.position.y > limits[UPPERY]:
		$isometric.position.y = limits[UPPERY]
	$foglayer/fogofwar.position = $isometric.position

#var selectonclick = 0
#func _process(_delta):
#	var mousepos = get_local_mouse_position() / get_viewport().size
#	if (mousepos.y < 0.15 or keysheld[2] == true) and $map.position.y <= limits[UPPERY]:
#		$map.position.y += 4*($map.scale.y+1.5)
#	elif (mousepos.y > 0.85 or keysheld[0] == true) and $map.position.y >= limits[LOWERY]:
#		$map.position.y -= 4*($map.scale.y+1.5)
#	if (mousepos.x < 0.15 or keysheld[1] == true) and $map.position.x <= limits[UPPERX]:
#		$map.position.x += 4*($map.scale.x+1.5)
#	elif (mousepos.x > 0.85 or keysheld[3] == true) and $map.position.x >= limits[LOWERX]:
#		$map.position.x -= 4*($map.scale.x+1.5)

func delete_selectors():
	for child in $isometric/selectors.get_children():
		child.queue_free()
	lastpointed = -1
	pointedpath = []
	combat = false
	$isometric/tileselect.clear()
	$isometric/tiershow.clear()

var index0start = Vector2(0,0)
var index1start = Vector2(0,0)
var indexdiff = 0
#var index0temp = Vector2(0,0)
var doubletouch = false

var preparedplayerloc
var playerloc
var keysheld = [false,false,false,false]
var lastpointed = -1
var pointedpath = []
var prevmousepos = Vector2(0,0)
var rightclickheld = false
var combat = false
var mindistance = tilesizexy.length()
var touchupper = false
func _input(event):
	if backhovered == false and $RewardPicker.visible == false:
		if event is InputEventScreenTouch: #start of touchscreensection 1
			if event.position.y < $backbuttoncanvas/Forbidden.rect_position.y:# and ($backbuttoncanvas/Forbidden2.visible == false or event.position.y > $backbuttoncanvas/Forbidden2.rect_size.y):
				if event.pressed == true:
					if event.index == 0:
#						$touchtimer.start(0.75)
						$touchtimer.start(0.6)
						$isometric/selectors.set_modulate(Color(1,1,1,1))
					var positionratio = event.position / get_viewport_rect().size
					if Playervariables.touchscreenalt == true:
						if event.index == 0:
							index0start = positionratio
						elif event.index == 1:
							index1start = positionratio
							doubletouch = true
							indexdiff = (index0start - index1start).length()
		#			if positionratio.x < 0.20 and positionratio.y < 0.20:
					if positionratio.y < 0.23 and positionratio.x < 0.23:
#						print("Touchin' upper")
						touchupper = true
						get_node("/root/Master").call("show_touch_overlay")
						delete_selectors()
						return
				elif event.index == 1:
					doubletouch = false
				if touchupper == false:
					if Playervariables.touchscreenalt == false:
	#					print($touchtimer.get_time_left())
						if event.pressed == false and $touchtimer.get_time_left() > 0:
							var a = InputEventAction.new()
							a.action = "ui_click"
							a.pressed = true
							Input.parse_input_event(a)
							var a2 = InputEventAction.new()
							a2.action = "ui_click"
							a2.pressed = false
							Input.parse_input_event(a2)
					else:
						delete_selectors()
						if event.pressed == true:
							var a = InputEventAction.new()
							a.action = "ui_rightclick"
							a.pressed = true
							Input.parse_input_event(a)
						else:
							var a = InputEventAction.new()
							a.action = "ui_rightclick"
							a.pressed = false
							Input.parse_input_event(a)
				elif event.pressed == false:
					touchupper = false
#					print("Untouching upper")
		if event is InputEventMouseMotion:
			if doubletouch == false:
				if touchupper == false:
					var localmousepos = get_local_mouse_position()
					if rightclickheld == true:
						var totalmousemovement = (prevmousepos - localmousepos)
						$isometric.position -= totalmousemovement#(prevmousepos - get_viewport().get_mouse_position())*$Camera2D.zoom.x
						$foglayer/fogofwar.position = $isometric.position
						prevmousepos = localmousepos
						process_scroll_limit()
					elif localmousepos.y > $backbuttoncanvas/Forbidden.rect_position.y:# or($backbuttoncanvas/Forbidden2.visible == true and localmousepos.y < $backbuttoncanvas/Forbidden2.rect_size.y):
						delete_selectors()
					else:
						var currentlocv = (((localmousepos-Vector2(0,64)*$isometric.scale.y) - $isometric.position))/$isometric.scale
						var currentpointed = playerstar.get_closest_point(currentlocv,true)
						var adjustedlocv = playerstar.get_point_position(currentpointed)
#						if abs(currentlocv.y - adjustedlocv.y) > 32 and abs(currentlocv.x - adjustedlocv.x) < 64:
						if abs(currentlocv.y - adjustedlocv.y)*2 + abs(currentlocv.x - adjustedlocv.x) > 128:
							currentlocv.x = currentlocv.x + abs(currentlocv.y - adjustedlocv.y)*sign(currentlocv.x - adjustedlocv.x)#(currentlocv.x+32) + abs(currentlocv.y - adjustedlocv.y)*sign(currentlocv.x - adjustedlocv.x)
							currentpointed = playerstar.get_closest_point(currentlocv,true)
							adjustedlocv = playerstar.get_point_position(currentpointed)
#						elif (currentlocv.x - adjustedlocv.x) > 64:
#							currentlocv.x += (currentlocv.x - adjustedlocv.x)
#							currentpointed = playerstar.get_closest_point(currentlocv,true)
#							adjustedlocv = playerstar.get_point_position(currentpointed)
						var currentloc = (adjustedlocv/tilesizexy)-Vector2(1,2)
		#				if occupy2darray[currentloc.y][currentloc.x] == -1 or (adjustedlocv - currentlocv).length() > (mindistance*0.9): #for example, the corners are 0.707*tilesize from the centre
						if (adjustedlocv - currentlocv).length() > (mindistance*0.9): #for example, the corners are 0.707*tilesize from the centre
							currentpointed = playerstar.get_closest_point(currentlocv)
							currentloc = (playerstar.get_point_position(currentpointed)/tilesizexy)-Vector2(1,2)
						if currentpointed != lastpointed:
							if $foglayer/fogofwar.get_cellv(currentloc) < 2:
								var disabledpoint = -1
								delete_selectors()
								if playerstar.is_point_disabled(currentpointed) == true and (tiletypearray[currentloc.y][currentloc.x] in [MOUNTAIN,RIVER]) == false:# and occupy2darray[currentloc.y][currentloc.x] > 0:
									disabledpoint = currentpointed
									playerstar.set_point_disabled(disabledpoint,false)
									combat = true
	#							var generatepath = playerstar.get_id_path(playerstar.get_closest_point($isometric/rulessprite.position,true),currentpointed)
								var generatepath = playerstar.get_id_path(playerstar.get_closest_point((playerloc+Vector2(1,2))*tilesizexy,true),currentpointed)
								if generatepath.size() > 0:
	#								if method == methods.PATHS and generatepath.size() > 2:
	#								if generatepath.size() > 2:
	#									generatepath.resize(2)
	#									combat = false
									var skiponce = true
				#					var inc = 0
									for point in generatepath:
				#						inc += 1
										if skiponce == false:
											var pointpos = playerstar.get_point_position(point)
											var newselector = load(Mainpreload.Selector).instance()
											$isometric/selectors.add_child(newselector)
											newselector.position = pointpos
#											if combat == false:
#												newselector.modulate = Color(0.25,0.8,1,1)
#											else:
#												newselector.modulate = Color(1,0.45,0.35,1)
										else:
											skiponce = false
									var pointpos = playerstar.get_point_position(generatepath[-1])
									var newselector = load(Mainpreload.Endselector).instance()
									$isometric/selectors.add_child(newselector)
									newselector.position = pointpos
									var pointloc = (pointpos/tilesizexy)-Vector2(1,2)
									$isometric/tileselect.set_cellv(pointloc,$isometric.get_cellv(pointloc))
									var targettier = $isometric/tier.get_cellv(pointloc)
									$isometric/tiershow.set_cellv(pointloc,targettier)
									if $isometric/icons.get_cellv(pointloc) > -1 and ($isometric/icons.get_cellv(pointloc) in [CHEST,ROAD]) == false:
#										for selector in $isometric/selectors.get_children():
#											selector.modulate = Color(0.8,0.8,0.3,1)
										$isometric/tileselect.set_modulate(Color(0.9,0.9,0.4))
#										$isometric/selectors.set_modulate(Color(0.9,0.9,0.4))
										for child in $isometric/selectors.get_children():
											child.set_self_modulate(Color(0.9,0.9,0.4))
										match $isometric/icons.get_cellv(pointloc):
											VILLAGE:newselector.get_child(0).set_text(Playervariables.shortareastringdict[VILLAGE])
											MAPSPRINGS:newselector.get_child(0).set_text(Playervariables.shortareastringdict[MAPSPRINGS])
											FOXSHRINE:newselector.get_child(0).set_text(Playervariables.shortareastringdict[FOXSHRINE])
											RURAL:newselector.get_child(0).set_text(Playervariables.shortareastringdict[RURAL])
											PARISH:newselector.get_child(0).set_text(Playervariables.shortareastringdict[PARISH])
											HOME5:newselector.get_child(0).set_text(Playervariables.shortareastringdict[HOME5])
									elif combat == false:
#										$isometric/selectors.set_modulate(Color(0.25,0.8,1,1))
										for child in $isometric/selectors.get_children():
											child.set_self_modulate(Color(0.25,0.8,1,1))
#										newselector.modulate = Color(0.25,0.8,1,1)
										$isometric/tileselect.set_modulate(Color(0.5,0.5,1))
									else:
#										newselector.modulate = Color(1,0.45,0.35,1)
										$isometric/tileselect.set_modulate(Color(1,0.5,0.5))
										var targetcolor = Color(1,0.4-(0.2*targettier),0.4-(0.2*targettier))
#										$isometric/selectors.set_modulate(Color(1,0.4-(0.2*targettier),0.4-(0.2*targettier)))
										for child in $isometric/selectors.get_children():
											child.set_self_modulate(targetcolor)
										match targettier:
											-1:newselector.get_child(0).set_text(Playervariables.shortareastringdict["T1"])
											0:newselector.get_child(0).set_text(Playervariables.shortareastringdict["T2"])
											1:newselector.get_child(0).set_text(Playervariables.shortareastringdict["T3"])
											2:newselector.get_child(0).set_text(Playervariables.shortareastringdict["T4"])
											3:newselector.get_child(0).set_text(Playervariables.shortareastringdict["T5"])
											_:newselector.get_child(0).set_text(Playervariables.shortareastringdict["T?"])
#										newselector.get_child(0).rect_scale = Vector2(2.1,2.1)
									pointedpath = generatepath
								else:
									if $isometric/icons.get_cellv(currentloc) == VILLAGE:
										var newselector = load(Mainpreload.Selector).instance()
										$isometric/selectors.add_child(newselector)
										newselector.position = adjustedlocv
										newselector = load(Mainpreload.Endselector).instance()
										$isometric/selectors.add_child(newselector)
										newselector.position = adjustedlocv
										newselector.get_child(0).set_text(Playervariables.shortareastringdict[VILLAGE])
										for child in $isometric/selectors.get_children():
											child.set_self_modulate(Color(0.9,0.9,0.4))
										pointedpath = [currentpointed]
									elif (Playervariables.CurrentClass == Playervariables.raceRAM and $isometric/icons.get_cellv(currentloc) == HOME5):
										var newselector = load(Mainpreload.Selector).instance()
										$isometric/selectors.add_child(newselector)
										newselector.position = adjustedlocv
										newselector = load(Mainpreload.Endselector).instance()
										$isometric/selectors.add_child(newselector)
										newselector.position = adjustedlocv
										newselector.get_child(0).set_text(Playervariables.shortareastringdict[HOME5])
										for child in $isometric/selectors.get_children():
											child.set_self_modulate(Color(0.9,0.9,0.4))
										pointedpath = [currentpointed]
									else:
										var newselector = load(Mainpreload.Endselector).instance()
										$isometric/selectors.add_child(newselector)
										newselector.position = (currentloc+Vector2(1,2))*tilesizexy
	#									newselector.modulate = Color(0.3,0.3,0.3,0.8)
										for child in $isometric/selectors.get_children():
											child.set_self_modulate(Color(0.3,0.3,0.3,0.8))
	#									$isometric/selectors.set_modulate(Color(0.3,0.3,0.3,0.8))
										newselector.get_child(0).set_text(Playervariables.shortareastringdict["Blocked"])
								if disabledpoint > -1:
									playerstar.set_point_disabled(disabledpoint,true)
							lastpointed = currentpointed
			else: #start of touchscreen section
				var positionratio = event.position / get_viewport_rect().size
				var length1 = (positionratio-index0start).length()
				var length2 = (positionratio-index1start).length()
				if min(length1,length2) > 0.08:#sensitivity:
					if max(length1,length2) >= indexdiff: #opposite of how it is in main2d??? aaaaAAA
						if $isometric.scale.x < zoomcapmax.x:
							var beforescale = $isometric.scale
							$isometric.scale += Vector2(+0.25,+0.25)
							var change = $isometric.scale/beforescale
#							$isometric.position = (-get_viewport().size * 0.125 + $isometric.position) * change
							move_map_position((-get_viewport().size * 0.125 + $isometric.position) * change)
#							$foglayer/fogofwar.scale = $isometric.scale
#							$foglayer/fogofwar.position = $isometric.position
							process_scroll_limit()
							if max(length1,length2) == length1:
								index1start = positionratio
								indexdiff = length1
							else:
								index0start = positionratio
								indexdiff = length2
					else:
	#					print("Zoomin in")
						if $isometric.scale.x > zoomcapmin.x:
							var beforescale = $isometric.scale
							$isometric.scale += Vector2(-0.25,-0.25)
							var change = $isometric.scale/beforescale
#							$isometric.position = (get_viewport().size * 0.125 + $isometric.position) * change
							move_map_position((get_viewport().size * 0.125 + $isometric.position) * change)
#							$foglayer/fogofwar.scale = $isometric.scale
#							$foglayer/fogofwar.position = $isometric.position
							process_scroll_limit()
							if min(length1,length2) == length2:
								index1start = positionratio
								indexdiff = length1
							else:
								index0start = positionratio
								indexdiff = length2
		elif event.is_action_pressed("ui_click"):
			if $clickdelay.get_time_left() > 0:
				return
			var localmousepos = get_local_mouse_position()
			if localmousepos.y < $backbuttoncanvas/Forbidden.rect_position.y and pointedpath.size() > 0:# and ($backbuttoncanvas/Forbidden2.visible == false or localmousepos.y > $backbuttoncanvas/Forbidden2.rect_size.y) and pointedpath.size() > 0:
				#$map/rulessprite.position = playerstar.get_point_position(playerstar.get_closest_point((get_local_mouse_position() - $map.position)/$map.scale,false))
				prepare_player_move(overstar.get_point_position(pointedpath[-1]))
#				$isometric.position = (get_viewport().size * 0.5)-($isometric/rulessprite.position * $isometric.scale)
#				$foglayer/fogofwar.scale = $isometric.scale
#				$foglayer/fogofwar.position = $isometric.position
#				playerloc = preparedplayerloc#($isometric/rulessprite.position/tilesizexy)-Vector2(1,2)
				var currenticon = $isometric/icons.get_cellv(preparedplayerloc)
				if currenticon == VILLAGE:
					_enable_villagebutton(true,true)
				elif $isometric/tilevisited.get_cellv(preparedplayerloc) > -1:
					_enable_villagebutton(true,false)
				else:
					_enable_villagebutton(false,false)
				if combat == true or (currenticon > -1 and (currenticon in [CHEST,ROAD]) == false and $isometric/tilevisited.get_cellv(preparedplayerloc) == -1):
					if debugwalk == false:
						engage_combat(combat)
					else:
						neutralize_tile()
						Playervariables.tier = $isometric/tier.get_cellv(playerloc)
						offer_rewards()
				delete_selectors()
#				var currenticon = $isometric/icons.get_cellv(preparedplayerloc)
#				if combat == true:# or (currenticon > -1 and currenticon != CHEST):
#					$backbuttoncanvas/ConfirmButton.visible = true
#					$backbuttoncanvas/Forbidden2.visible = true
#					$backbuttoncanvas/detailsbacker.visible = true
#					_set_details()
#				else:
#					$backbuttoncanvas/ConfirmButton.visible = false
#					$backbuttoncanvas/Forbidden2.visible = false
#					$backbuttoncanvas/detailsbacker.visible = false
#				playerloc = ($isometric/rulessprite.position/tilesizexy)-Vector2(1,2)
##				clear_fog(playerloc,clamp(Playervariables.playervision-4,2,99))
##				if method == methods.PATHS:
##					create_fog(playerloc.y-1)
##				var deletearray = []
#				var currenticon = $isometric/icons.get_cellv(playerloc)
#				if $isometric/tilevisited.get_cellv(playerloc) > -1:
#					_enable_villagebutton(true,false)
#				elif currenticon == VILLAGE:
#					_enable_villagebutton(true,true)
#				else:
#					_enable_villagebutton(false,false)
#					if (currenticon in [MAPSPRINGS,HIDDENSPRINGS]) == false:
#						$isometric/tilevisited.set_cellv(playerloc,$isometric.get_cellv(playerloc))
#				if combat == true or (currenticon > -1 and currenticon != CHEST):# or currenticon > -1:
##					if currenticon != MAPSPRINGS or Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] >= 6:
#					playerstar.set_point_disabled(pointedpath[-1],false)
#					engage_combat(combat)
#				delete_selectors()
		elif event.is_action_pressed("ui_rightclick"):
			prevmousepos = get_local_mouse_position()
			rightclickheld = true
			delete_selectors()
			Input.set_custom_mouse_cursor(load(Mainpreload.cursorgrab),0,Vector2(8,8))
		elif event.is_action_released("ui_rightclick"):
			rightclickheld = false
			Input.set_custom_mouse_cursor(load(Mainpreload.cursorpoint))
			touchupper = false
#		elif Playervariables.debugmodeon and event.is_action_pressed("debug9"):
#			generate_map()
		elif event.is_action_pressed("ui_scrollup"):
			if $isometric.scale.x < zoomcapmax.x:
				var beforescale = $isometric.scale
				$isometric.scale += Vector2(+0.25,+0.25)
				var change = $isometric.scale/beforescale
#				$isometric.position = (-get_viewport().size * 0.125 + $isometric.position) * change 
				move_map_position((-get_viewport().size * 0.125 + $isometric.position) * change)
#				$foglayer/fogofwar.scale = $isometric.scale
#				$foglayer/fogofwar.position = $isometric.position
				process_scroll_limit()
		elif event.is_action_pressed("ui_scrolldown"):
			if $isometric.scale.x > zoomcapmin.x:
				var beforescale = $isometric.scale
				$isometric.scale += Vector2(-0.25,-0.25)
				var change = $isometric.scale/beforescale
#				$isometric.position = (get_viewport().size * 0.125 + $isometric.position) * change
				move_map_position((get_viewport().size * 0.125 + $isometric.position) * change)
#				$foglayer/fogofwar.scale = $isometric.scale
#				$foglayer/fogofwar.position = $isometric.position
				process_scroll_limit()
		elif event.is_action_pressed("cursor_left"):
			keysheld[1] = true
		elif event.is_action_pressed("cursor_right"):
			keysheld[3] = true
		elif event.is_action_pressed("cursor_up"):
			keysheld[2] = true
		elif event.is_action_pressed("cursor_down"):
			keysheld[0] = true
		elif event.is_action_released("cursor_left"):
			keysheld[1] = false
		elif event.is_action_released("cursor_right"):
			keysheld[3] = false
		elif event.is_action_released("cursor_up"):
			keysheld[2] = false
		elif event.is_action_released("cursor_down"):
			keysheld[0] = false
		elif Playervariables.debugmodeon == true and OS.is_debug_build() == true:
			if event.is_action_released("debug1"):
	#			for point in playerstar.get_points():
	#				playerstar.set_point_disabled(point,false)
				for x in range(mapsize.x):
					for y in range(mapsize.y):
						if $isometric.get_cellv(Vector2(x,y)) > -1:
							if randf() > 0.5:
								$isometric/tilevisited.set_cellv(Vector2(x,y),$isometric.get_cellv(Vector2(x,y)))
								playerstar.set_point_disabled(playerstar.get_closest_point(Vector2(Vector2(x+1,y+2))*tilesizexy,true),false)
							else:
								$isometric/tilevisited.set_cellv(Vector2(x,y),-1)
								playerstar.set_point_disabled(playerstar.get_closest_point(Vector2(Vector2(x+1,y+2))*tilesizexy,true),true)
				delete_selectors()
			elif event.is_action_released("debug2"):
				debugwalk = !debugwalk
				$isometric/rulessprite.modulate = Color(1,1,1)
				if debugwalk == true:
					$isometric/rulessprite.modulate = Color(0,0,0)
			

func move_map_position(pos):
	$isometric.position = pos
	$foglayer/fogofwar.scale = $isometric.scale
	$foglayer/fogofwar.position = $isometric.position

func _on_backbutton_pressed():
	if $clickdelay.get_time_left() > 0:
		return
	if masternode.mainscene_check() == true and masternode.currentscene.mapgenlocation == Playervariables.LevelSelect1 and Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] <= 5:
		Playervariables.playerinventory2darray = []
		Playervariables.playerconsumables2darray = []
	get_node("/root/Master/SFX/uib").play()
	enabledisable(false)
#	if playerloc != preparedplayerloc:
#		prepare_player_move((playerloc+Vector2(1,2))*tilesizexy)
	prepare_player_move(previous_position)
	if masternode.mainscene_check() == true:
		masternode.currentscene.retreat = false
		masternode.currentscene.levelcleared = false

var backhovered = false
func _on_backbutton_mouse_entered():
	delete_selectors()
	backhovered = true
	$backbuttoncanvas/backbutton.modulate = Color(1,1,1,1)
func _on_backbutton_mouse_exited():
	backhovered = false
	$backbuttoncanvas/backbutton.modulate = Color(1,1,1,0.47)
#
#func _on_villagebutton_mouse_entered():
#	delete_selectors()
#	backhovered = true
#func _on_villagebutton_mouse_exited():
#	backhovered = false


func _on_villagebutton_pressed():
	if $clickdelay.get_time_left() > 0:
		return
	get_node("/root/Master/SFX/uif").play()
	if $backbuttoncanvas/villagebutton/confirm.time_left <= 0 and villagebuttonstate == true:
		$backbuttoncanvas/villagebutton/confirm.start(4)
		$backbuttoncanvas/villagebutton/Label.set_text(Playervariables.shortareastringdict["Confirm?"])
		$backbuttoncanvas/villagebutton.set_self_modulate(Color(0.3,0.3,0.3))
	else:
		$backbuttoncanvas/villagebutton/confirm.stop()
		if Playervariables.nextlocation == Playervariables.Village and villagebuttonstate == true:
			if missionid == 4:
				Playervariables.nextlocation = Playervariables.LevelSelect1
			else:
				Playervariables.nextexit = 4
			enabledisable(false)
			if debug == false:
				masternode.currentscene.endscene() 
		else:
			engage_combat(true)

func _on_confirm_timeout():
	if $backbuttoncanvas/villagebutton.visible == true:
		_enable_villagebutton(true,villagebuttonstate)
#	$backbuttoncanvas/villagebutton.set_self_modulate(Color(1,1,1))
#	if villagebuttonstate == true:
#		$backbuttoncanvas/villagebutton/Label.set_text(Playervariables.shortareastringdict["End Expedition"])
#	else:
#		if $isometric/icons.get_cellv(preparedplayerloc) == HOME5:
#			$backbuttoncanvas/villagebutton/Label.set_text(Playervariables.shortareastringdict[HOME5])
#		else:
#			$backbuttoncanvas/villagebutton/Label.set_text(Playervariables.shortareastringdict["Retry This Tile"])


func _on_questbacker_mouse_entered():
	$backbuttoncanvas/questbacker.modulate = Color(0.7,0.7,0.7,0.4)
func _on_questbacker_mouse_exited():
	$backbuttoncanvas/questbacker.modulate = Color(1,1,1,1)

func _on_detailsbacker_mouse_entered():
	$backbuttoncanvas/detailsbacker.modulate = Color(0.7,0.7,0.7,0.4)
func _on_detailsbacker_mouse_exited():
	$backbuttoncanvas/detailsbacker.modulate = Color(1,1,1,1)


var villagebuttonstate = false
func _enable_villagebutton(onoff,returnyesno=false):
	$backbuttoncanvas/villagebutton/confirm.stop()
	if onoff == true:
		$backbuttoncanvas/villagebutton.visible = true
		if returnyesno == true:
			$backbuttoncanvas/villagebutton.rect_scale = Vector2(1,1)
			$backbuttoncanvas/villagebutton.anchor_left = 0.70
			$backbuttoncanvas/villagebutton/Label.set_text(Playervariables.shortareastringdict["End Expedition"])
			$backbuttoncanvas/villagebutton.set_modulate(Color(1,1,1))
			$backbuttoncanvas/villagebutton.set_self_modulate(Color(1,1,1))
		else:
			if $isometric/icons.get_cellv(preparedplayerloc) == HOME5:
				$backbuttoncanvas/villagebutton/Label.set_text(Playervariables.shortareastringdict[HOME5])
			else:
				$backbuttoncanvas/villagebutton/Label.set_text(Playervariables.shortareastringdict["Retry This Tile"])
			$backbuttoncanvas/villagebutton.rect_scale = Vector2(0.85,0.85)
			$backbuttoncanvas/villagebutton.anchor_left = 0.73
			$backbuttoncanvas/villagebutton.set_modulate(Color(0.8,0.8,1))
			$backbuttoncanvas/villagebutton.set_self_modulate(Color(1,1,1))
		villagebuttonstate = returnyesno
	else:
		$backbuttoncanvas/villagebutton.visible = false
		backhovered = false


var lastchest = Vector2(-1,-1)
func remove_chest():
#	print("SHOULD BE REMOVING CHEST.")
#	print($isometric/icons.get_cellv(lastchest))
	if $isometric/icons.get_cellv(lastchest) == CHEST:
		$isometric/icons.set_cellv(lastchest,-1)

#var tile_neutralized = false
func neutralize_tile():
#	if tile_neutralized == false:
	playerloc = preparedplayerloc
	clear_fog(playerloc,clamp(Playervariables.playervision-1,4,99))
	var playerpoint = playerstar.get_closest_point((playerloc+Vector2(1,2))*tilesizexy,true)
	$isometric/rulessprite.position = playerstar.get_point_position(playerpoint)
	playerstar.set_point_disabled(playerpoint,false)
	var currenticon = $isometric/icons.get_cellv(playerloc)
	if (currenticon in [MAPSPRINGS,HIDDENSPRINGS,VILLAGE,FOXSHRINE,FOG,PARISH,HOME5]) == false:
		$isometric/tilevisited.set_cellv(playerloc,$isometric.get_cellv(playerloc))
	delete_selectors()
#		tile_neutralized = true

var tier = 0
func engage_combat(docombat):
#	if $isometric/icons.get_cellv(playerloc) == MAPSPRINGS and Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] < 6:
#		return
	for key in Playervariables.escalationdict:
		Playervariables.escalationdict[key] = 0
	Playervariables.escalationdict["Map Width"] = 5
	Playervariables.escalationdict["Map Depth"] = 1
#	if $isometric/icons.get_cellv(playerloc) == -1:
#		Playervariables.questcondition = clamp(Playervariables.questcondition-1,0,999999)
#		if Playervariables.questcondition == 0:
#			get_node("/root/Master").call("update_quest",0,false)
#			get_node("/root/Master").call("update_quest",1,false)
#			get_node("/root/Master").call("update_quest",2,true)
#			print("Quest condition may have been cleared through time down")
	if $isometric/icons.get_cellv(preparedplayerloc) == CHEST:
		Playervariables.escalationdict["Chest"] = 1
		lastchest = preparedplayerloc
	tier = $isometric/tier.get_cellv(preparedplayerloc)
	Playervariables.tier = tier
#	$isometric/tier.set_cellv(preparedplayerloc,-1)
	var enemycount = DEFAULTENEMIES
	if missionid == 4:
		Playervariables.nextlocation = Playervariables.Mission4
	elif missionid == 11:
		Playervariables.nextlocation = Playervariables.RouteA11
	elif missionid == 17:
		Playervariables.nextlocation = Playervariables.GlassedDesert
	else:
		print("ERROR: Could not find mission id in escalatev2 map selection:"+str(missionid))
	if Playervariables.areastringdict.has($isometric/icons.get_cellv(preparedplayerloc)):
		Playervariables.stagedescriptor = Playervariables.areastringdict[$isometric/icons.get_cellv(preparedplayerloc)]
	elif Playervariables.areastringdict.has(tiletypearray[preparedplayerloc.y][preparedplayerloc.x]):
		Playervariables.stagedescriptor = Playervariables.areastringdict[tiletypearray[preparedplayerloc.y][preparedplayerloc.x]]
	else:
		Playervariables.stagedescriptor = ""
	Playervariables.mapshader_current = Playervariables.NEUTRAL
	match tiletypearray[preparedplayerloc.y][preparedplayerloc.x]:
		ICON:
			match $isometric/icons.get_cellv(preparedplayerloc):
				VILLAGE:
					Playervariables.nextlocation = Playervariables.Village #turned into levelselect1 if mission4
					Playervariables.nextexit = 2
					Playervariables.awaitingconversation = ""
	#									_enable_villagebutton(true,true)
#					$isometric/tilevisited.set_cellv(preparedplayerloc,-1)
#					Playervariables.stagedescriptor = "Fugitive's Hideaway"
				MAPSPRINGS:
					Playervariables.nextlocation = Playervariables.Springs
#					$isometric/tilevisited.set_cellv(preparedplayerloc,-1)
#					Playervariables.stagedescriptor = "Hotsprings"
				FOXSHRINE:
					if missionid == 17:
						Playervariables.nextlocation = Playervariables.ImpDebug
					else:
						Playervariables.nextlocation = Playervariables.Shrine
						if Playervariables.CurrentClass == Playervariables.raceRAM:
							Playervariables.nextexit = 1
						else:
							Playervariables.nextexit = 0
#					Playervariables.stagedescriptor = "Fox's Shrine"
				RURAL:
					Playervariables.nextlocation = Playervariables.WolfCombat
				PARISH:
					Playervariables.nextlocation = Playervariables.NewVillage
				HOME5:
					Playervariables.nextlocation = Playervariables.RamHome
					Playervariables.nextexit = 1
		MOUNTAIN:
			Playervariables.mapshader_current = Playervariables.MOUNTAINHSV
#			Playervariables.stagedescriptor = "Mountains"
			if missionid != 4:
				Playervariables.escalationdict["Enemy Harpy"] = 1
			Playervariables.escalationdict["Harpy Rank"] = clamp(tier+1,0,99)
		RIVER:Playervariables.escalationdict["Map Water"] = 10
#			Playervariables.stagedescriptor = "River"
		HILL:
			Playervariables.mapshader_current = Playervariables.MOUNTAINHSV
			Playervariables.escalationdict["Map Depth"] = 3
#			Playervariables.stagedescriptor = "Hills"
			if missionid != 4:
				Playervariables.escalationdict["Enemy Harpy"] = 1
			Playervariables.escalationdict["Harpy Rank"] = clamp(tier+1,0,99)
		FOREST:
			Playervariables.escalationdict["Map Depth"] = 2
			Playervariables.escalationdict["Trees"] = 4
			Playervariables.escalationdict["Hazard Obstruction"] = 2
			Playervariables.mapshader_current = Playervariables.FORESTHSV
#			Playervariables.stagedescriptor = "Forest"
		GRASSLAND:Playervariables.mapshader_current = Playervariables.PLAINSHSV
		WETLAND:Playervariables.escalationdict["Map Water"] = 3
		DESERT:
			Playervariables.mapshader_current = Playervariables.DESERTHSV
			Playervariables.escalationdict["Map Depth"] = 3
			Playervariables.escalationdict["Map Width"] = 3
			Playervariables.escalationdict["Enemy Imp"] = 4
#			Playervariables.stagedescriptor = "Grassland"
	if Playervariables.nextlocation != Playervariables.Village:
		if docombat == true:
			spawnerloc = Vector3(-1,-1,-1) #resets current spawner that could be removed
			var ballotarray = []
			var zerolengthvector = null
			for vec3 in spawnlocarray:
				if int(vec3.z) in enemytier1 or tier > -1:
					var length = (preparedplayerloc - Vector2(vec3.x,vec3.y)).length()
#					print(str(length)+"..."+str(int(vec3.z)))
#					if length <= ENEMY_AGGRO_RANGE[0]:
#						zerolengthvector = vec3
#						for _i in range(6):
#							ballotarray.append(int(vec3.z))
#						enemycount += 2
					if length <= ENEMY_AGGRO_RANGE[0]:
						for _i in range(4):
							ballotarray.append(int(vec3.z))
						enemycount += 2
					elif length <= ENEMY_AGGRO_RANGE[1]:
						for _i in range(2):
							ballotarray.append(int(vec3.z))
						enemycount += 1
					elif length <= ENEMY_AGGRO_RANGE[2]:
						for _i in range(1):
							ballotarray.append(int(vec3.z))
			if zerolengthvector != null:
				spawnerloc = zerolengthvector #will remove spawner if conditions are met
			if ballotarray.size() == 0:
	#								ballotarray.append(randi()%4)
				if missionid < 17:
					ballotarray.append(0)
				else:
					enemycount = 0
#			print("Ballots:"+str(ballotarray))
#			print("Tier:"+str(tier))
			for _i in range(enemycount):
				var randnum
				if ballotarray.size() <= 1:
					randnum = 0
					if ballotarray.size() == 0:
						break
				else:
					randnum = randi()%(ballotarray.size()-1)
				var chosenballot = ballotarray[randnum]
				ballotarray.remove(randnum)
#				print("Chose ballot:"+str(chosenballot))
				match chosenballot:
					CATGIRL:
						if Playervariables.escalationdict.has("Enemy Cat") == false:
							Playervariables.escalationdict["Enemy Cat"] = 0
							Playervariables.escalationdict["Cat Rank"] = 0
						Playervariables.escalationdict["Enemy Cat"] += 1
						var elitechance = ((tier*2)+3.0)*0.1
						if elitechance > randf():
							Playervariables.escalationdict["Cat Rank"] += 1
#							print("Wow, that's elite cat from elitechance value:"+str(elitechance))
					FOXGIRL:
						if Playervariables.escalationdict.has("Enemy Fox") == false:
							Playervariables.escalationdict["Enemy Fox"] = 0
							Playervariables.escalationdict["Fox Rank"] = 0
						Playervariables.escalationdict["Enemy Fox"] += 1
						var elitechance = ((tier*3.5)+3.5)*0.1
						if elitechance > randf():
							Playervariables.escalationdict["Fox Rank"] += 1
					WEREWOLF:
						if Playervariables.escalationdict.has("Enemy Wolf") == false:
							Playervariables.escalationdict["Enemy Wolf"] = 0
							Playervariables.escalationdict["Wolf Rank"] = 0
						Playervariables.escalationdict["Enemy Wolf"] += 1
						var elitechance = ((tier*2)-2)*0.1
						if elitechance > randf():
							Playervariables.escalationdict["Wolf Rank"] += 1
					RAMGIRL:
						if Playervariables.escalationdict.has("Enemy Ram") == false:
							Playervariables.escalationdict["Enemy Ram"] = 0
							Playervariables.escalationdict["Ram Rank"] = 0
						Playervariables.escalationdict["Enemy Ram"] += 1
						var elitechance = ((tier*2.5)+2.5)*0.1
						if elitechance > randf():
							Playervariables.escalationdict["Ram Rank"] += 1
		enabledisable(false,true)
#		tile_neutralized = false
		if debug == false:
			masternode.currentscene.endscene() #comes after enabledisable BECAUSE one will enable moveready, one disables it
	delete_selectors()

var spawnerloc = Vector3(-1,-1,-1)
func remove_spawner():
	if spawnerloc.x >= 0 and spawnerloc.y >= 0 and spawnerloc.z >= 0:
		var arraynum = spawnlocarray.find(spawnerloc)
		if arraynum > -1:
			spawnlocarray.remove(arraynum)
#			print("remove spawner test")
#			print(arraynum)
#			print(enemyobjects)
			for enemyobject in enemyobjects[arraynum]:
				enemyobject.queue_free()
			enemyobjects.remove(arraynum)
#			print(enemyobjects)
			if missionid == 4:
				var missioncomplete = true
				for vec3 in spawnlocarray:
					if vec3.z == RAMGIRL:
						missioncomplete = false
						break
				if missioncomplete == true:
					get_node("/root/Master").call("update_quest",3,true)
					get_node("/root/Master").call("update_quest",2,false)
					get_node("/root/Master").call("update_quest",1,false)
					get_node("/root/Master").call("update_quest",0,false)
					Playervariables.questcondition = 0
					print("Cleared mission through camp removal")
			get_node("/root/Master").register_event_via_master("Enemy encampment has cleared from the overworld.")
		spawnerloc = Vector3(-1,-1,-1)

var bigrewardqueue = []
var bigcorruptionqueue = []
func offer_rewards(already_processed=false):
	if $isometric/cleared.get_cellv(preparedplayerloc) == -1:
		$isometric/cleared.set_cellv(preparedplayerloc,0)
		if already_processed == false:
			var totalrewards = 3 + clamp(Playervariables.foxpendants,0,99999)
			$RewardPicker.pick_random_rewards(totalrewards)
			$backbuttoncanvas/rewardbutton/numleft.set_text(str(bigrewardqueue.size()))
			add_more = true
			$backbuttoncanvas/rewardbutton.visible = true
			$backbuttoncanvas/rewardbutton/AnimationPlayer.play("jiggle")
	#	$RewardPicker.queue_rewards([7,8,9],[true,false,true])
			get_node("/root/Master").register_event_via_master("New temp skill ready.")
			get_node("/root/Master").call("main_loot_gained")
			
			$backbuttoncanvas/rewardbutton.disabled = false
			$backbuttoncanvas/rewardbutton.set_modulate(Color(1.0,1.0,1.0))

#const Loot0Pool0 = [4,5,7,8,11]
#const Loot0Pool1 = [12,u,14,3,6]
#const Loot0Pool2 = [12,13,14,3,6]
#const Loot0Pool3 = [12,13,14,3,6]

#const extrapoolsdict0 = {
#func pick_random_rewards(num):
#	var chosenarray = []
#	var chosencorruption = []
#	var usepool = Playervariables.get("LootPool"+str(tier+1)).duplicate()
#	var backuppool = {}
#	if (tier+1) > 0:
#		backuppool = Playervariables.get("LootPool"+str(tier)).duplicate()
#	var usedict = Playervariables.get("extrapoolsdict"+str(tier+1))
#	var poollength = usepool.size()
##	print("Usepool:"+str(usepool))
##	print("Pool length:"+str(poollength))
#	for key in Playervariables.corruptiondict:
#		var testkey = key+str(Playervariables.corruptiondict[key])
#		if usedict.has(testkey):
#			usepool = usepool + usedict[testkey]
#		elif backuppool.has(testkey):
#			usepool = usepool + backuppool[testkey]
##			print("Usepool:"+str(usepool))
#	for _i in range(num):
#		var randomvalue = randi()%(usepool.size())
##		print("Random value:"+str(randomvalue)+"... which is:"+str(usepool[randomvalue]))
#		chosenarray.append(usepool[randomvalue])
#		if randomvalue+1 > poollength:
#			chosencorruption.append(true)
#		else:
#			chosencorruption.append(false)
##	print("Corruptarray:"+str(chosencorruption)+"... and chosenarray:"+str(chosenarray))
#	bigrewardqueue.append(chosenarray)
#	bigcorruptionqueue.append(chosencorruption)

enum{REWARD=0,STARTING=1}
func _on_rewardbutton_pressed(mode=REWARD):
	if $clickdelay.get_time_left() > 0 and mode == REWARD:
		return
	if mode == REWARD:
		$backbuttoncanvas/rewardbutton/open2.play()
	if bigrewardqueue.size() > 0 and bigcorruptionqueue.size() > 0:
		$RewardPicker.queue_rewards(bigrewardqueue[0],bigcorruptionqueue[0],mode)
		bigrewardqueue.remove(0)
		bigcorruptionqueue.remove(0)
	else:
		print("Queue error in escalate reward.")
		print("Big reward:"+str(bigrewardqueue) +"corruptqueue:"+str(bigcorruptionqueue))
		bigrewardqueue = []
		bigcorruptionqueue = []
	$backbuttoncanvas/rewardbutton/AnimationPlayer.stop()
	if bigrewardqueue.size() <= 0:
		$backbuttoncanvas/rewardbutton.disabled = true
		$backbuttoncanvas/rewardbutton.set_modulate(Color(0.6,0.6,0.6))
	$backbuttoncanvas/rewardbutton/numleft.set_text(str(bigrewardqueue.size()))

func starting_gear():
	var fakearray = []
	var fakecorruptionarray = []
	for item in Playervariables.playerinventory2darray:
		fakearray.append(item[0])
		if Playervariables.CurrentClass > Playervariables.raceHUMAN:
			fakecorruptionarray.append(true)
		else:
			fakecorruptionarray.append(false)
	for consumable in Playervariables.playerconsumables2darray:
		fakearray.append(consumable[0])
		fakecorruptionarray.append(false)
	bigrewardqueue = [fakearray]
	bigcorruptionqueue = [fakecorruptionarray]
	_on_rewardbutton_pressed(STARTING)

func reward_chosen():
	if bigrewardqueue.size() > 0:
		$backbuttoncanvas/rewardbutton/AnimationPlayer.play("jiggle")

#
#func _on_ConfirmButton_pressed():
#	if pointedpath.size() > 0:
#		playerloc = preparedplayerloc#($isometric/rulessprite.position/tilesizexy)-Vector2(1,2)
#		var currenticon = $isometric/icons.get_cellv(playerloc)
#		if $isometric/tilevisited.get_cellv(playerloc) > -1:
#			_enable_villagebutton(true,false)
#		elif currenticon == VILLAGE:
#			_enable_villagebutton(true,true)
#		else:
#			_enable_villagebutton(false,false)
#			if (currenticon in [MAPSPRINGS,HIDDENSPRINGS]) == false:
#				$isometric/tilevisited.set_cellv(playerloc,$isometric.get_cellv(playerloc))
#		if combat == true or (currenticon > -1 and currenticon != CHEST):
#			playerstar.set_point_disabled(pointedpath[-1],false)
#			engage_combat(combat)
#	delete_selectors()
func _on_ConfirmButton_mouse_entered():
	backhovered = true
func _on_ConfirmButton_mouse_exited():
	backhovered = false


func _on_touchtimer_timeout():
	$isometric/selectors.set_modulate(Color(1,1,1,0.5))
#	if $CanvasLayer3/touchsprite.visible == true:
#		$CanvasLayer3/touchsprite/toucheffect.play("speech")

func find_icon_loc(num):
	for x in range(mapsize.x):
		for y in range(mapsize.y):
			if $isometric/icons.get_cell(x,y) == num:
				return Vector2(x,y)
	return null

var previous_position = Vector2(0,0)
func prepare_player_move(newlocv):
	if $isometric/rulessprite.position != newlocv:
		previous_position = $isometric/rulessprite.position
		$isometric/rulessprite.position = newlocv
		move_map_position((get_viewport().size * 0.5)-($isometric/rulessprite.position * $isometric.scale))
		preparedplayerloc = ($isometric/rulessprite.position/tilesizexy)-Vector2(1,2)
		var generatepath = playerstar.get_id_path(playerstar.get_closest_point($isometric/rulessprite.position,true),playerstar.get_closest_point(previous_position,true))
		if generatepath.size() == 0:
			neutralize_tile()




var finalpose = 0
enum p{IDLE,IDLECENTRE,RUN,WALK,JUMP,ATTACKLEFTRIGHT,ATTACKDIAGONAL,SWIM,CLIMB,ATTACKUPDOWN,CRAWL,READY}
func map_emotion():
	var pose = p.IDLE
	
	if Playervariables.playerresistance <= 1 or Playervariables.playerabdomensize >= 5:
		pose = p.CRAWL
	elif Playervariables.playerabdomensize == 4 and randf() > 0.5:
		pose = p.WALK
	elif (Playervariables.CurrentClass == Playervariables.raceNEKO or (Playervariables.corruptiondict.has("armright") and Playervariables.corruptiondict["armright"] == Playervariables.raceNEKO)) and randf() > 0.5:
		pose = p.JUMP
	elif (Playervariables.corruptiondict.has("armright") and Playervariables.corruptiondict["armright"] == Playervariables.raceHARPY) and randf() > 0.5:
		pose = p.RUN
	elif (Playervariables.corruptiondict.has("tail") and Playervariables.corruptiondict["tail"] == Playervariables.raceKITSUNE) and randf() > 0.5:
		pose = p.READY
	elif (Playervariables.corruptiondict.has("horns") and Playervariables.corruptiondict["horns"] == Playervariables.raceKITSUNE) and randf() > 0.5:
		pose = p.IDLECENTRE
	
#	if pose != finalpose:
	finalpose = pose
	for child in $isometric/rulessprite/frames.get_children():
		child.useframe = pose
