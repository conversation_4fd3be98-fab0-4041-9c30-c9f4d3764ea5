[gd_scene load_steps=46 format=2]

[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf base.png" type="Texture" id=1]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf rules default.png" type="Texture" id=2]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf rules unbirth.png" type="Texture" id=3]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf belly half.png" type="Texture" id=4]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf fog.png" type="Texture" id=5]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf background light.png" type="Texture" id=6]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf face 1.png" type="Texture" id=7]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf armsdown.png" type="Texture" id=8]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf armsup.png" type="Texture" id=9]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf face 3.png" type="Texture" id=10]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf juices.png" type="Texture" id=11]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf face 2.png" type="Texture" id=12]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf belly full.png" type="Texture" id=13]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf rules ears.png" type="Texture" id=14]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf rules vore.png" type="Texture" id=15]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf background dark.png" type="Texture" id=16]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf tar.png" type="Texture" id=17]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolfbadend.gd" type="Script" id=18]
[ext_resource path="res://Assets/ui/translationflag.png" type="Texture" id=19]
[ext_resource path="res://Assets/materials/skinshader.gdshader" type="Shader" id=20]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf rules unbirth juicelayer.png" type="Texture" id=21]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf rules unbirth shadow.png" type="Texture" id=22]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf rules unbirth hairmask.png" type="Texture" id=23]
[ext_resource path="res://Assets/materials/eyehueshifter.shader" type="Shader" id=24]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf rules vore harpy.png" type="Texture" id=25]
[ext_resource path="res://Assets/abilityicons/Draining Kiss.png" type="Texture" id=26]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf rules vore wolf.png" type="Texture" id=27]
[ext_resource path="res://DialogueArt/CG/gameovers/specwolf/specwolf rules vore imp.png" type="Texture" id=28]

[sub_resource type="SpriteFrames" id=12]
animations = [ {
"frames": [ ExtResource( 6 ), ExtResource( 16 ) ],
"loop": false,
"name": "background",
"speed": 5.0
} ]

[sub_resource type="SpriteFrames" id=13]
animations = [ {
"frames": [ null, ExtResource( 4 ), ExtResource( 13 ) ],
"loop": false,
"name": "belly",
"speed": 5.0
} ]

[sub_resource type="SpriteFrames" id=14]
animations = [ {
"frames": [ ExtResource( 7 ), ExtResource( 12 ), ExtResource( 10 ) ],
"loop": false,
"name": "face",
"speed": 5.0
} ]

[sub_resource type="SpriteFrames" id=15]
animations = [ {
"frames": [ null, ExtResource( 8 ), ExtResource( 9 ) ],
"loop": false,
"name": "arms",
"speed": 5.0
} ]

[sub_resource type="ShaderMaterial" id=17]
shader = ExtResource( 20 )
shader_param/hue_shift = 0.0
shader_param/sat_mul = 0.0
shader_param/val_mul = 0.0

[sub_resource type="SpriteFrames" id=16]
animations = [ {
"frames": [ null, ExtResource( 2 ), ExtResource( 14 ), ExtResource( 15 ), ExtResource( 25 ), ExtResource( 27 ), ExtResource( 28 ) ],
"loop": false,
"name": "rules",
"speed": 5.0
} ]

[sub_resource type="Animation" id=25]
length = 1.3
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.3, 0.4, 0.7, 0.9, 1.3 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -10, 15 ), Vector2( 13, -32 ), Vector2( 4, -24 ), Vector2( -4, 15 ), Vector2( 24, -25 ), Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("../unb:position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.3, 0.4, 0.7, 0.9, 1.3 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -10, 15 ), Vector2( 13, -32 ), Vector2( 4, -24 ), Vector2( -4, 15 ), Vector2( 24, -25 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=26]
length = 1.3
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.3, 0.4, 0.7, 0.9, 1.3 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -3, 6 ), Vector2( 1, -2 ), Vector2( 2, -5 ), Vector2( -3, 5 ), Vector2( 6, -4 ), Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("../unb:position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.3, 0.4, 0.7, 0.9, 1.3 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -3, 6 ), Vector2( 1, -2 ), Vector2( 2, -5 ), Vector2( -3, 5 ), Vector2( 6, -4 ), Vector2( 0, 0 ) ]
}

[sub_resource type="ShaderMaterial" id=18]
shader = ExtResource( 24 )
shader_param/skin_hue_shift = 0.0
shader_param/skin_sat_mul = 0.0
shader_param/skin_val_mul = 0.0
shader_param/hair_hue_shift = 0.0
shader_param/hair_sat_mul = 0.0
shader_param/hair_val_mul = 0.0
shader_param/mask_texture = ExtResource( 23 )

[sub_resource type="Animation" id=23]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath(".:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ false ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ) ]
}

[sub_resource type="Animation" id=24]
length = 6.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1, 1.7, 2.4, 4.4, 6 ),
"transitions": PoolRealArray( 1.5, 2, 1, 1, 1.5, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 0.54902 ), Color( 1, 1, 1, 0.901961 ), Color( 0.784314, 0.784314, 0.784314, 0.803922 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}

[sub_resource type="Animation" id=19]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath("sprites:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites/background:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 2 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ) ]
}

[sub_resource type="Animation" id=2]
resource_name = "appear"
length = 1.5
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/background:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1.5 ),
"transitions": PoolRealArray( 2, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=22]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 2 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ) ]
}

[sub_resource type="Animation" id=11]
resource_name = "hover"
length = 4.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1, 2, 3, 4 ),
"transitions": PoolRealArray( 0.9, 1.3, 0.9, 1.3, 1 ),
"update": 0,
"values": [ Vector2( 0, -138 ), Vector2( 0, -238 ), Vector2( 0, -138 ), Vector2( 0, -38 ), Vector2( 0, -138 ) ]
}

[sub_resource type="Animation" id=21]
resource_name = "move_to_point"
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.8, 1 ),
"transitions": PoolRealArray( 0.8, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -2, -3 ), Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.8, 1 ),
"transitions": PoolRealArray( 0.8, 1, 1 ),
"update": 0,
"values": [ Vector2( 0.25, 0.25 ), Vector2( 0.25, 0.25 ), Vector2( 0.25, 0.25 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.7, 1 ),
"transitions": PoolRealArray( 2, 0.8, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.705882, 0.705882, 0.705882, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/3/type = "method"
tracks/3/path = NodePath("..")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"values": [ {
"args": [ -1, false ],
"method": "action"
} ]
}

[sub_resource type="Animation" id=20]
resource_name = "shake"
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.3, 0.6, 1 ),
"transitions": PoolRealArray( 0.9, 1.3, 0.9, 1.3, 1 ),
"update": 0,
"values": [ Vector2( -2, -3 ), Vector2( 4, -8 ), Vector2( -5, 3 ), Vector2( 4, -2 ), Vector2( 0, 0 ) ]
}

[node name="specwolfbadend" type="CanvasLayer"]
layer = 4
script = ExtResource( 18 )

[node name="testdonotdelete" type="TextureRect" parent="."]
visible = false
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource( 19 )
expand = true

[node name="sprites" type="Node2D" parent="."]
modulate = Color( 1, 1, 1, 0 )
position = Vector2( -2, -3 )
scale = Vector2( 0.25, 0.25 )

[node name="background" type="AnimatedSprite" parent="sprites"]
modulate = Color( 1, 1, 1, 0 )
frames = SubResource( 12 )
animation = "background"
frame = 1
centered = false

[node name="base" type="Sprite" parent="sprites"]
texture = ExtResource( 1 )
centered = false

[node name="juices" type="Sprite" parent="sprites"]
visible = false
texture = ExtResource( 11 )
centered = false
offset = Vector2( 505, 535 )

[node name="belly" type="AnimatedSprite" parent="sprites"]
frames = SubResource( 13 )
animation = "belly"
centered = false
offset = Vector2( 1190, 0 )

[node name="face" type="AnimatedSprite" parent="sprites"]
frames = SubResource( 14 )
animation = "face"
frame = 1
centered = false
offset = Vector2( 2690, 335 )

[node name="arms" type="AnimatedSprite" parent="sprites"]
frames = SubResource( 15 )
animation = "arms"
centered = false
offset = Vector2( 1170, 0 )

[node name="rules" type="AnimatedSprite" parent="sprites"]
material = SubResource( 17 )
use_parent_material = true
frames = SubResource( 16 )
animation = "rules"
centered = false
offset = Vector2( 0, 275 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="sprites/rules"]
anims/shakerules = SubResource( 25 )
anims/smallshakerules = SubResource( 26 )

[node name="tar" type="Sprite" parent="sprites"]
texture = ExtResource( 17 )
centered = false
offset = Vector2( 0, 1315 )

[node name="unb" type="Sprite" parent="sprites"]
visible = false
material = SubResource( 18 )
texture = ExtResource( 3 )
centered = false
offset = Vector2( 0, 392 )

[node name="unb2" type="Sprite" parent="sprites/unb"]
texture = ExtResource( 21 )
centered = false

[node name="unb3" type="Sprite" parent="sprites/unb"]
show_behind_parent = true
texture = ExtResource( 22 )
centered = false

[node name="fog" type="Sprite" parent="sprites"]
visible = false
self_modulate = Color( 1, 1, 1, 0 )
texture = ExtResource( 5 )
centered = false

[node name="fogplayer" type="AnimationPlayer" parent="sprites/fog"]
anims/RESET = SubResource( 23 )
anims/engage_fog = SubResource( 24 )

[node name="kiss" type="Sprite" parent="sprites"]
visible = false
self_modulate = Color( 0.552941, 0.690196, 1, 0.176471 )
position = Vector2( 1396, 528 )
rotation = 1.43292
scale = Vector2( 2, 2 )
texture = ExtResource( 26 )
centered = false

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
anims/RESET = SubResource( 19 )
anims/appear = SubResource( 2 )

[node name="hover" type="AnimationPlayer" parent="."]
root_node = NodePath("../sprites")
playback_speed = 0.1
anims/RESET = SubResource( 22 )
anims/hover = SubResource( 11 )
anims/move_to_point = SubResource( 21 )
anims/shake = SubResource( 20 )

[connection signal="animation_finished" from="hover" to="." method="_on_hover_animation_finished"]
