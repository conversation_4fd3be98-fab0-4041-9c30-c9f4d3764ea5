[gd_scene load_steps=7 format=2]

[ext_resource path="res://Assets/ui/blocksquareouter.png" type="Texture" id=1]
[ext_resource path="res://Assets/ui/blockcircleinner.png" type="Texture" id=2]
[ext_resource path="res://Assets/ui/blocksquareinner.png" type="Texture" id=3]
[ext_resource path="res://Assets/ui/blockcircleouter.png" type="Texture" id=4]
[ext_resource path="res://block.gd" type="Script" id=5]

[sub_resource type="Animation" id=1]
resource_name = "blockanim"
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("0:rect_position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1, 2 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, -15 ), Vector2( 0, -45 ), Vector2( 0, -15 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("1:rect_position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1, 2 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 15, 0 ), Vector2( 45, 0 ), Vector2( 15, 0 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("2:rect_position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1, 2 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 15 ), Vector2( 0, 45 ), Vector2( 0, 15 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("3:rect_position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 1, 2 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -15, 0 ), Vector2( -45, 0 ), Vector2( -15, 0 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath(".:modulate")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 0.5, 1, 1.5, 2 ),
"transitions": PoolRealArray( 0.5, 2, 0.5, 2, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}

[node name="block" type="Node2D"]
modulate = Color( 1, 1, 1, 0.36 )
script = ExtResource( 5 )

[node name="0" type="GridContainer" parent="."]
margin_top = -42.0
margin_right = 110.0
margin_bottom = 68.0
grow_horizontal = 0
grow_vertical = 0
rect_rotation = 225.0
columns = 3
__meta__ = {
"_edit_use_anchors_": false
}

[node name="1" type="TextureProgress" parent="0"]
visible = false
self_modulate = Color( 1, 0.454902, 0.454902, 1 )
margin_right = 34.0
margin_bottom = 34.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="2" type="TextureProgress" parent="0"]
visible = false
self_modulate = Color( 0.988235, 0.6, 0.372549, 1 )
margin_left = 38.0
margin_right = 72.0
margin_bottom = 34.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="3" type="TextureProgress" parent="0"]
visible = false
modulate = Color( 0.360784, 0.478431, 1, 1 )
margin_left = 38.0
margin_top = 38.0
margin_right = 72.0
margin_bottom = 72.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="4" type="TextureProgress" parent="0"]
visible = false
self_modulate = Color( 1, 0.407843, 0.760784, 1 )
margin_left = 76.0
margin_top = 38.0
margin_right = 110.0
margin_bottom = 72.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="5" type="TextureProgress" parent="0"]
visible = false
self_modulate = Color( 0.831373, 0.811765, 0.717647, 1 )
margin_top = 38.0
margin_right = 34.0
margin_bottom = 72.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="6" type="TextureProgress" parent="0"]
visible = false
self_modulate = Color( 1, 0.835294, 0.360784, 1 )
margin_top = 76.0
margin_right = 34.0
margin_bottom = 110.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="dummy" type="TextureProgress" parent="0"]
self_modulate = Color( 1, 1, 1, 0 )
margin_right = 34.0
margin_bottom = 34.0
max_value = 4.0
value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="ex" type="Node2D" parent="0"]
position = Vector2( 17, 17 )

[node name="pure" type="TextureProgress" parent="0/ex"]
visible = false
margin_right = 40.0
margin_bottom = 40.0
max_value = 4.0
texture_over = ExtResource( 4 )
texture_progress = ExtResource( 2 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="1" type="GridContainer" parent="."]
margin_left = 42.0
margin_right = 152.004
margin_bottom = 110.0
rect_rotation = -45.0
columns = 3
__meta__ = {
"_edit_use_anchors_": false
}

[node name="1" type="TextureProgress" parent="1"]
visible = false
self_modulate = Color( 1, 0.454902, 0.454902, 1 )
margin_right = 34.0
margin_bottom = 34.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="2" type="TextureProgress" parent="1"]
visible = false
self_modulate = Color( 0.988235, 0.6, 0.372549, 1 )
margin_right = 34.0
margin_bottom = 34.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="3" type="TextureProgress" parent="1"]
visible = false
modulate = Color( 0.360784, 0.478431, 1, 1 )
margin_left = 76.0
margin_top = -5.72205e-06
margin_right = 110.0
margin_bottom = 34.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="4" type="TextureProgress" parent="1"]
visible = false
self_modulate = Color( 1, 0.407843, 0.760784, 1 )
margin_left = 76.0
margin_top = 38.0
margin_right = 110.0
margin_bottom = 72.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="5" type="TextureProgress" parent="1"]
visible = false
self_modulate = Color( 0.831373, 0.811765, 0.717647, 1 )
margin_left = -3.8147e-06
margin_top = 38.0
margin_right = 34.0
margin_bottom = 72.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="6" type="TextureProgress" parent="1"]
visible = false
self_modulate = Color( 1, 0.835294, 0.360784, 1 )
margin_top = 76.0
margin_right = 34.0
margin_bottom = 110.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="dummy" type="TextureProgress" parent="1"]
self_modulate = Color( 1, 1, 1, 0 )
margin_right = 34.0
margin_bottom = 34.0
max_value = 4.0
value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="ex" type="Node2D" parent="1"]
position = Vector2( 17, 17 )

[node name="pure" type="TextureProgress" parent="1/ex"]
visible = false
margin_right = 40.0
margin_bottom = 40.0
max_value = 4.0
texture_over = ExtResource( 4 )
texture_progress = ExtResource( 2 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="2" type="GridContainer" parent="."]
margin_top = 42.0
margin_right = 110.0
margin_bottom = 152.004
rect_rotation = 45.0
columns = 3
__meta__ = {
"_edit_use_anchors_": false
}

[node name="1" type="TextureProgress" parent="2"]
visible = false
self_modulate = Color( 1, 0.454902, 0.454902, 1 )
margin_right = 34.0
margin_bottom = 34.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="2" type="TextureProgress" parent="2"]
visible = false
self_modulate = Color( 0.988235, 0.6, 0.372549, 1 )
margin_right = 34.0
margin_bottom = 34.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="3" type="TextureProgress" parent="2"]
visible = false
modulate = Color( 0.360784, 0.478431, 1, 1 )
margin_right = 34.0
margin_bottom = 34.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="4" type="TextureProgress" parent="2"]
visible = false
self_modulate = Color( 1, 0.407843, 0.760784, 1 )
margin_right = 34.0
margin_bottom = 34.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="5" type="TextureProgress" parent="2"]
visible = false
self_modulate = Color( 0.831373, 0.811765, 0.717647, 1 )
margin_right = 34.0
margin_bottom = 34.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="6" type="TextureProgress" parent="2"]
visible = false
self_modulate = Color( 1, 0.835294, 0.360784, 1 )
margin_right = 34.0
margin_bottom = 34.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="dummy" type="TextureProgress" parent="2"]
self_modulate = Color( 1, 1, 1, 0 )
margin_right = 34.0
margin_bottom = 34.0
max_value = 4.0
value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="ex" type="Node2D" parent="2"]
position = Vector2( 17, 17 )

[node name="pure" type="TextureProgress" parent="2/ex"]
visible = false
margin_right = 40.0
margin_bottom = 40.0
max_value = 4.0
texture_over = ExtResource( 4 )
texture_progress = ExtResource( 2 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": true
}

[node name="3" type="GridContainer" parent="."]
margin_left = -42.0
margin_right = 68.0
margin_bottom = 110.0
rect_rotation = 135.0
columns = 3
__meta__ = {
"_edit_use_anchors_": false
}

[node name="1" type="TextureProgress" parent="3"]
visible = false
self_modulate = Color( 1, 0.454902, 0.454902, 1 )
margin_right = 34.0
margin_bottom = 34.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="2" type="TextureProgress" parent="3"]
visible = false
self_modulate = Color( 0.988235, 0.6, 0.372549, 1 )
margin_left = 38.0
margin_right = 72.0
margin_bottom = 34.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="3" type="TextureProgress" parent="3"]
visible = false
modulate = Color( 0.360784, 0.478431, 1, 1 )
margin_right = 34.0
margin_bottom = 34.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="4" type="TextureProgress" parent="3"]
visible = false
self_modulate = Color( 1, 0.407843, 0.760784, 1 )
margin_right = 34.0
margin_bottom = 34.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="5" type="TextureProgress" parent="3"]
visible = false
self_modulate = Color( 0.831373, 0.811765, 0.717647, 1 )
margin_right = 34.0
margin_bottom = 34.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="6" type="TextureProgress" parent="3"]
visible = false
self_modulate = Color( 1, 0.835294, 0.360784, 1 )
margin_right = 34.0
margin_bottom = 34.0
max_value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="dummy" type="TextureProgress" parent="3"]
self_modulate = Color( 1, 1, 1, 0 )
margin_right = 34.0
margin_bottom = 34.0
max_value = 4.0
value = 4.0
texture_over = ExtResource( 1 )
texture_progress = ExtResource( 3 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="ex" type="Node2D" parent="3"]
position = Vector2( 17, 17 )

[node name="pure" type="TextureProgress" parent="3/ex"]
visible = false
margin_right = 40.0
margin_bottom = 40.0
max_value = 4.0
texture_over = ExtResource( 4 )
texture_progress = ExtResource( 2 )
fill_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="blockanim" type="AnimationPlayer" parent="."]
autoplay = "blockanim"
playback_speed = 0.25
anims/blockanim = SubResource( 1 )
