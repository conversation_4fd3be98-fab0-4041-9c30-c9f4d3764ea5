[gd_resource type="Animation" format=2]

[resource]
resource_name = "discard"
length = 0.5
tracks/0/type = "value"
tracks/0/path = NodePath(".:anchor_top")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 0.3, 0.3 ),
"update": 0,
"values": [ 0.2, 0.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:anchor_bottom")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 0.3, 0.3 ),
"update": 0,
"values": [ 0.2, 0.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": <PERSON>RealArray( 0, 0.5 ),
"transitions": <PERSON>RealArray( 0.3, 0.3 ),
"update": 0,
"values": [ Color( 0.588235, 0.588235, 0.588235, 0.784314 ), Color( 1, 1, 1, 1 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("Label3:self_modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 0.3 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath(".:rect_rotation")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
