Hi! I'm the dev for <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>.

If you're reading this, you may have just exported the game's text data. If you're a translator, good job! You should be here.
The goal of the built-in translation tools is to make it easy for anyone to translate and edit the game's text. You do not need to know programming, just follow the guide below.

You may also find attached a "MoncurseJSONViewer.zip". This isn't made by me, it has its own readme. I recommend trying it but the below instructions assume you're editing the .jsons in regular a text editor.

---------------------------- HOW TO TRANSLATE ----------------------------

Do this:
- I recommend using Studio Visual code. It'll color what needs translating. There will be a color for keys that shouldn't be translated and a color for translatable text, such as blue (keys) and orange (translatable).
- Export or download other translations to see what they've done or changed! You can export the translations, not just the English version of the game.
- Update old translations. It doesn't have to be your own, whenever I release updates to the game there'll be untranslated or changed text that needs updating.
- Keep an untranslated copy with the translated version. The exporter will give you two copies, the untranslated version is used so the game can see what has changed between an old, untranslated version and the current version. When you update your translation with the updater tool it'll also give you a new copy.
- Put your translation in a folder, either mods/your_translation_name, or mods/translation/your_translation_name. It keeps things tidy. Keep the OriginalDoNotAlter copy in here too!
- Contact me if you find a bug, need help or have a translation ready! I'm kmuu on discord, without the speech marks.
- Try and load your partial translation into the game every now and again. If your translation file has an error the game will tell you when you load it up at the top-middle of the main menu.
- Make sure your translation filename begins with "Translatable" so the game recognizes it.

Don't do these things:
- Change the keys. Studio Visual Code will color these a consistent color such as blue. for example, {"this_is_a_key":"this_is_translatable"} If you change the keys the game won't be able to find the translation.
- Remove "" speech marks [] brackets {} brackets anywhere in the file. This will cause the game to fail to load it.
- Remove , commas that are not inside of speech marks. Again, this will break the file.
- Change VAR1 VAR2 VAR3 VAR4 etc. These represent variable values that the game will put in this space.
- Change any .char or .old or .new or .debug key. You can do this but it has no effect at all on the game. For example, the character names are there as a guide and the altconditions show you under what circumstances it may jump to an alternate part of speech.
- Remove \n unless you know what you're doing. \n represent a line break, like
this. That was a line break. It goes to a new line.
- Replace all instances of single words with another without checking first. In visual studio code this is a great tool to instantly replace stuff, such as all instances of "foxgirl" with "fuxgal", but this will break any keys that use foxgirl, such as "badendfoxgirl.json". To avoid this, include the surrounding syntax and text such as ": "FoxGirl", so it only replaces the exact thing you want, and not any of the keys. Fortunately, this isn't too hard to fix if you do mess up and the auto-updater can fix any broken keys.


Player Name:
Conversation text is constructed like this: ["Hi there Mr.",0,", what a nice day today."] Notice two commas are not inside the speech marks, these separate the value 0 from the "strings". The numbers are replaced with the following:
0 - The player's name
1 - The player's name with a vowel drawn out
2 - The player's name without capital letters
3 - The player's name but the first character is "ny" for cat-girls.
You can replace all numbers with 0 if you feel like it doesn't work in your language. For example, 3 would look weird when added to non-roman text.
You can also remove it entirely: ["Hi there Mister, what a nice day today."]


Meta data:
	{"Icon_Path":null,
	"Translator_Comment":"No comment",
	"Game_Version":"V0.6.???"
	}
This info will be shown to players so use the Icon (must a .png image! recommended 588x210 size) to indicate to speakers of your language that this is what they want. Try and use a politically unbiased image that covers as many of the speakers of your language as possible.
"Icon_Path" should be the filename of a .png or .jpg image in the folder, such as "IconFlag.jpg".
Translator Comment can be anything. It can include your name. If your translation is machine translated, please say so here.
Game Version will attempt to automatically provide itself but please update it when your translation is done. It will be green in-game if it matches the game's version, yellow if it's close, red if it doesn't match or is unknown.

NOTE!! If it isn't fixed yet, "event_messages" and "chapter_text aren't exporting correctly, because these two come from the game's code and the game trying to access its own code is dicey. It works on the indev version so ask the dev for a copy of these if you get this far.

---------------------------- FONTS ----------------------------

The game's existing fonts work for the English language but they may not have all of your language's special characters. You'll likely need to change the font data.
font_parameters.json covers this. Here's an example of a font's reference:
{
	"font_speech_default":null,
	"font_speech_default_size":1.0,
	"font_speech_default_conversation_size":1.0,
	"font_speech_default_EXAMPLE_VALUE_NOT_USED":"Osaka Regular-Mono.otf",
}
That null can be replaced with the path to a font. Once you've downloaded a font (.ttf or .otf), put the font in the same folder as your translation and then write the font's filename like so:
"font_speech_default":"Example_Font_Name.ttf",
Do this for all "font_x_default" values.
Do not change EXAMPLE_VALUE_NOT_USED as these have no effect on the game, this is so you can search the font and try and find a similar font for your own language.
Now, "default_size" changes the size of the font. 1.0 is 1x size. 2.0 is 2x size. 0.5 is 1/2 size. Play around with it to get it just right if you need to.
"default_conversation_size" is a specific size only for dialogue in the chat box at the bottom of the screen.

Now to explain each font:
	{
	"font_speech_default"
This is used for speech bubbles and text boxes. It applies to just about any character's speech. The game's default font is capable of handling Japanese text, but only for this font.
	"font_speech_choices"
This appears on the choices you get during character dialog. It also affects the inventory tab info and the setting screen button text.
	"font_move_names"
This appears on the Skill cards you get on your hotbar and in your inventory. It also appears on inventory buttons.
	"font_event_messages"
This appears for messages that log on the top-right during play.
	"font_attack_record"
This appears for many UI elements such as on the main menu or on attack interactions between characters.
	"font_attack_announce"
This appears when you use a skill but it also appears on screens such as the translation menu, the color/name/preferences input screen or for large text that appears when you enter a new area or on the first-time-play disclaimer.
	"font_debuff_description"
This appears on the Status screen of the inventory when you have debuffs. It also appears on the color/name/preferences input screen underneath the button or on the 'pick a skill' screen.
	}


Already got a translation? Want to update it? Use the "Update an old translatable text file" button with your translation selected.

Notes about the "Update an old translatable text file" tool:
-Experiment with it! It doesn't delete data, it just creates new files.
Data inputs:
>OLD TRANSLATED DATA: This is an already translated file, partially or otherwise. There's no point in updating an untranslated file.
>ORIGINAL UNALTERED DATA: This is where the OriginalDoNotAlter text is used. This is only needed for LIST CHANGED DATA. This file should be in english or it'll confuse the tool.
Options:
>ADD NEW DATA: You should want this. It finds any new additions to the game and inserts them. It will also record this to the changelog as "ADDED_TO_NEW_DICTIONARY"
>LIST CHANGED DATA: Any data that is different between the game's English version and the OriginalDoNotAlter version will be listed in the changelog. If the value is not translated, it will update it automatically. If it is translated, it'll give full details in the changelog: OLD_ENGLISH is the old raw value, OLD_TRANSLATION is the old translated value and the final value is the new english value. This is "VALUE_CHANGED_FROM_OLD_TO_NEW"
>OVERWRITE CHANGED DATA: Changes the above LIST CHANGED DATA by instead, regardless of if text has been translated, overwriting the data with the new english version. You can still see your previous translation in the changelog.
>DELETE REMOVED DATA: Any translated text that is no longer used or has been relocated will be removed from the updated translation. This will also remove .DEBUG entries. Listed in changelog as "REMOVED_FROM_OLD_DICTIONARY"
>LIST UNTRANSLATED DATA: If you want to find values you haven't translated yet, use this. It's listed in the changelog as "VALUE_NOT_TRANSLATED", it has no effect on the updated file and will not update the file if used by itself. Just because it's listed here, doesn't mean it's incorrect, it just means the value hasn't changed from the game's original state.
>CREATE NEW VERSION + CHANGELOG: Once you're all set, press this. It'll create an updated file (unless you only clicked UNTRANSLATED DATA) and a changelog. Use the changelog to find what you need to fix to update the translation!


---------------------------- MISC ----------------------------

If you see "ENTRIES NOT FOUND IN ORIGINALDONOTALTER .json", you do not have to do anything. However, there is no way to tell if the value has been altered because without the original English data it has no reference to go off of.
Otherwise, it will say "Good Job" to tell you that you're using a perfectly matching OriginalDoNotAlter file.

Stuff that currently cannot be translated (may be changed in the future):
>The player's name input.
>NPC names.
>The main menu start/setting/exit/save buttons.
If you see English text and it's not one of these things, it may be a bug or you may have forgotten to translate it, make sure your translation copy is up to date and ctrl-f to search for the text.


Thank you for considering translating my game! I apologise if the game is a bit wordy. I've cut out most lines that don't appear in game.
I'll be trying to find good ways to distribute translations so people all over the world can enjoy my work.