extends Node2D


const tailback_options = PoolStringArray([])

func _ready():
	add_to_group("Abdomen")

#func load_default_parts():
#	$frames/tailback
#	$frames/wings
#	$frames/weaponback.new_part("rulesclothesweaponbehind lance")
#	$frames/hairback.new_part("ruleshairback drills Rose")
#	$frames/armright.new_part("rulesbodyarmright regular light")
#	$frames/gloveright
#	$frames/legright.new_part("rulesbodylegright regular light")
#	$frames/bootright
#	$frames/pantsundermirror
#	$frames/abdomen.new_part("rulesbodyabdomen regular light")
#	$frames/pantsunder
#	$frames/top.new_part("rulesclothestoplower fuschia",1)
#	$frames/legleft.new_part("rulesbodylegleft regular light")
#	$frames/pants.new_part("rulesclothespants fuschia")
#	$frames/bootleft
#	$frames/weapon.new_part("rulesclothesweapon lance")
#	$frames/armleft.new_part("rulesbodyarmleft regular light")
#	$frames/bust.new_part("rulesbodybust regular light")
#	$frames/topupper.new_part("rulesclothestop fuschia",1)
#	$frames/bell
#	$frames/backhorns
#	$frames/face.new_part("rulesface regular light")
#	$frames/facemark
#	$frames/ears
#	$frames/hair.new_part("ruleshairfront drills Rose")
#	$frames/eyes.new_part("ruleseyes regular Blue")
#	$frames/horns
#	$frames/wingsover
#	$frames/tailover


var pants_value = 1
var top_value = 1
func abdomen_mark_case(_type,_power):
	pass
func on_abdomen_size_changed(_delay=false,_bigger = false):
	var usevalue = Playervariables.playerabdomensize
	if Playervariables.consent == false and usevalue > 2:
		usevalue = 2
	usevalue = int(usevalue)
	if top_value == 1 and Playervariables.sealsarray[Playervariables.SLUT] > 0 or Playervariables.CurrentClass == Playervariables.raceKITSUNE:
		top_value = Playervariables.raceKITSUNE
		$frames/top.new_part("rulesclothestoplower fuschia croptop",1)
	match usevalue:
		1: 
			$frames/abdomen.new_part("rulesbodyabdomen regular light")
#			$frames/abdomen.set_animation("0.0")
			match top_value:
				Playervariables.raceNAMAN:$frames/top.new_part("rulesclothestoplower fuschia",1)
				Playervariables.raceRAM:$frames/top.new_part("rulesclothestoplower ram",1)
				Playervariables.raceWOLF:$frames/top.new_part("rulesclothestoplower wolf smmedlarge",1)
#			$frames/top.set_animation(str(top_value))
			if $frames/pantsundermirror.visible == true:
				$frames/pantsunder.visible = true
				$frames/pantsundermirror.visible = false
		2,3: 
			$frames/abdomen.new_part("rulesbodyabdomen regular light large")
#			$frames/abdomen.set_animation("0.1")
			match top_value:
				Playervariables.raceNAMAN:$frames/top.new_part("rulesclothestoplower fuschia large",1)
				Playervariables.raceRAM:$frames/top.new_part("rulesclothestoplower ram large",1)
				Playervariables.raceWOLF:$frames/top.new_part("rulesclothestoplower wolf smmedlarge",1)
#			$frames/top.set_animation(str(top_value)+".1")
			if $frames/pantsunder.visible == true:
				$frames/pantsundermirror.visible = true
				$frames/pantsunder.visible = false
		4,5: 
			$frames/abdomen.new_part("rulesbodyabdomen regular light huge")
#			$frames/abdomen.set_animation("0.2")
			match top_value:
				Playervariables.raceNAMAN:$frames/top.new_part("rulesclothestoplower fuschia huge",1)
				Playervariables.raceRAM:$frames/top.new_part("rulesclothestoplower ram huge",1)
				Playervariables.raceWOLF:$frames/top.new_part("rulesclothestoplower wolf hugemega",1)
#			$frames/top.set_animation(str(top_value)+".2")
			if pants_value == 1:
				$frames/pants.new_part("rulesclothespants fuschia huge",1)#$frames/pants.set_animation("1.2")
			if $frames/pantsunder.visible == true:
				$frames/pantsundermirror.visible = true
				$frames/pantsunder.visible = false
	if top_value == Playervariables.raceKITSUNE:
		top_value = 1
#	if top_value == 1 and Playervariables.sealsarray[Playervariables.SLUT] > 0 or Playervariables.CurrentClass == Playervariables.raceKITSUNE:
#		$frames/top.new_part("rulesclothestoplower fuschia croptop",1)
#		$frames/top.set_animation("4.0")
#	$frames/abdomen.useframe = $frames/hairback.useframe
#	$frames/top.useframe = $frames/hairback.useframe

func debugcureplayersprite():
	$frames/face.new_part("rulesface regular light")
	if Playervariables.corruptiondict.has("body") == false:
		if $frames/tailback.position.y != 0:
			if Playervariables.corruptiondict.has("leg") and Playervariables.corruptiondict["leg"] == Playervariables.raceWOLF:
				for node in $frames.get_children():
					node.position.y = -15
			else:
				for node in $frames.get_children():
					node.position.y = 0
			if $impjump.is_playing() == false:
				$jump.play("RESET")
			else:
				var current_animation = $impjump.current_animation
				$jump.play(current_animation)
	get_node("frames/wings").visible = Playervariables.corruptiondict.has("wings")
	get_node("frames/wingsover").visible = Playervariables.corruptiondict.has("wings")
	if Playervariables.corruptiondict.has("hair") == false:
		$frames/hair.new_part("ruleshairfront drills Rose")#get_node("frames/hair").set_animation("0")
		$frames/hairback.new_part("ruleshairback drills Rose")#get_node("frames/hairback").set_animation("0")
#	if Playervariables.corruptiondict.has("armleft") == false:
#		get_node("frames/armleft").set_animation("0")
	if Playervariables.corruptiondict.has("leg") == false:
		$frames/legleft.new_part("rulesbodylegleft regular light")#get_node("frames/legleft").set_animation("0")
		$frames/legright.new_part("rulesbodylegright regular light")#get_node("frames/legright").set_animation("0")
		$frames/bootright.blank()
		$frames/bootleft.blank()
		$frames/bootright.visible = false
		$frames/bootleft.visible = false
	if Playervariables.corruptiondict.has("armright") == false and Playervariables.curseditemdict[Playervariables.ROPEGAG] == false:
		$frames/gloveright.visible = false
		$frames/gloveright.blank()
		get_node("frames/weapon").material = null
#		get_node("frames/weapon").set_animiation("0")
		$frames/armleft.new_part("rulesbodyarmleft regular light")#get_node("frames/armleft").set_animation("0")
		$frames/armright.new_part("rulesbodyarmright regular light")#get_node("frames/armright").set_animation("0")
		get_tree().call_group("itemslot","skillseal",false,1,99,false)
		get_tree().call_group("itemslot","skillseal",false,2,99,false)
#		$frames/weapon.visible = true
		$frames/weaponback.new_part("rulesclothesweaponbehind lance",1)
		$frames/weaponback.visible = true
		$frames/weapon.new_part("rulesclothesweapon lance",1)
		Playervariables.curseditemdict[Playervariables.PAWS] = false
	if Playervariables.corruptiondict.has("tail") == false:
#		get_node("frames/tailback").set_animation("0")
#		get_node("frames/tailover").set_animation("0")
		get_node("frames/tailback").visible = false
		get_node("frames/tailback").blank()
		get_node("frames/tailover").visible = false
		get_node("frames/tailover").blank()
	if Playervariables.corruptiondict.has("ears") == false:
		get_node("frames/ears").blank()#set_animation("0")
		get_node("frames/ears").visible = false
	if Playervariables.corruptiondict.has("backhorns") == false:
		get_node("frames/backhorns").blank()#set_animation("0")
		get_node("frames/backhorns").visible = false
	if Playervariables.corruptiondict.has("horns") == false:
		get_node("frames/horns").blank()#set_animation("0")
		get_node("frames/horns").visible = false
	for framenode in $frames.get_children():
		framenode.z_index = 0
	if Playervariables.curseditemdict[Playervariables.ROPEGAG] == true or Playervariables.get_corruption("armright") > 0:
		$frames/weapon.z_index = 1
	if first_run == false:
		on_abdomen_size_changed()
	else:
		first_run = false
onready var spriteeyesnode = $frames/eyes
#onready var hairspritenodes = [$frames/hairfront,$frames/tailover,$frames/hairbackupper,$frames/hairback,$frames/ears,$frames/tailback]

var possessed = false
func possession(onoff):
	if onoff == true and possessed == false:
		possessed = true
#		lastcolourarray = null
#		update_colour()
	elif possessed == true:
		possessed = false
#		lastcolourarray = null
#		update_colour()

#var lastcolourarray = null #Playervariables.baseplayercolourarray.hash()
func update_colour():
	#if lastcolourarray == null or lastcolourarray != Playervariables.baseplayercolourarray.hash():
#		var colourarray = Playervariables.baseplayercolourarray
	var eyecolHSV = Playervariables.get_materials(Playervariables.pcol.EYES)#Playervariables.newcolorarrayeye[colourarray[0]]
#		var specialhair
#		if possessed == true:
#			if Playervariables.possessionrank == true:
#				specialhair = 15
#			else:
#				specialhair = 14
#		else:
#			specialhair = colourarray[1]
#		var haircolHSV = Playervariables.newcolorarrayhair[specialhair]
#		var skincolHSV = Playervariables.newcolorarrayskin[colourarray[2]]
#		if Playervariables.corruptiondict.has("eyes") and Playervariables.corruptiondict["eyes"] == Playervariables.raceRAM:
#			if Playervariables.makeuprank == true:
#				eyecolHSV = Playervariables.RANKhsv[3]
#				eyecolHSV.y -= 1
#				eyecolHSV.z -= 1
#			else:
#				eyecolHSV = Vector3(0,0,0)
#		eyecolHSV.y += 1.0
#		eyecolHSV.z += 1.0
#		$frames.material.set_shader_param("hue_shift",haircolHSV.x)
#		$frames.material.set_shader_param("sat_mul",haircolHSV.y)
#		$frames.material.set_shader_param("val_mul",haircolHSV.z)
	spriteeyesnode.material.set_shader_param("hue_shift",eyecolHSV.x)
	spriteeyesnode.material.set_shader_param("sat_mul",eyecolHSV.y)
	spriteeyesnode.material.set_shader_param("val_mul",eyecolHSV.z)
#		$frames/face.material.set_shader_param("hue_shift",skincolHSV.x)
#		$frames/face.material.set_shader_param("sat_mul",skincolHSV.y)
#		$frames/face.material.set_shader_param("val_mul",skincolHSV.z)
#		lastcolourarray = Playervariables.baseplayercolourarray.hash()
#	for key in Playervariables.tempcorruptiondict:
#		get_node("frames/"+key).set_animation(str(Playervariables.tempcorruptiondict.get(key))+"t")

var first_run = true
func updateplayersprite():
	update_colour()
	if Playervariables.wolf_collar == true:#if Playervariables.Class_Truth_Array[Playervariables.GameOverClass] == true and Playervariables.CurrentClass == Playervariables.raceWOLF:
		$frames/bell.new_part("curseditem collar",1)#$frames/bell.set_animation(str(Playervariables.raceWOLF))
		$frames/bell.visible = true
		$frames/bell.material = null
	else:
		if Playervariables.curseditemdict[Playervariables.COWBELL] == true:
			$frames/bell.new_part("curseditem bell",1)#$frames/bell.set_animation(str(Playervariables.raceNAMAN))
			$frames/bell.visible = true
		else:
			$frames/bell.blank()#set_animation("0")
			$frames/bell.visible = false
		$frames/bell.material = load("res://Assets/materials/global_clothesshift.tres")
#	if Playervariables.curseditemdict[Playervariables.PIXIEDUST] == true:
#		get_node("frames/facemark").set_animation(str(Playervariables.raceIMP))
#		get_node("frames/facemark").visible = true
#		get_node("frames/facemark").set_self_modulate(Color(1,1,1,1))
	if Playervariables.corruptiondict.has("body") == false:
		if $impjump.is_playing() == false:
			$jump.play("RESET")
		else:
			var current_animation = $impjump.current_animation
			$jump.play(current_animation)
		if Playervariables.corruptiondict.has("leg") and Playervariables.corruptiondict["leg"] == Playervariables.raceWOLF:
			for node in $frames.get_children():
				node.position.y = -15
		elif $frames/tailback.position.y != 0:
			for node in $frames.get_children():
				node.position.y = 0
	for key in Playervariables.corruptiondict:
		match key:
			"face":
				$frames/facemark.new_part("rulesfaceextra elf ears")#get_node("frames/facemark").set_animation(str(Playervariables.corruptiondict[key]))
				get_node("frames/facemark").visible = true
				get_node("frames/facemark").set_self_modulate(Color(1,1,1,1))
			"pants":pass
#				pants_value = Playervariables.corruptiondict[key]
#				match pants_value:
#					5:
#						if Playervariables.corruptiondict.has("leg"):
#							$frames/pants.set_animation(str(pants_value)+".1")
#						else:
#							$frames/pants.set_animation(str(pants_value))
#						$frames/pantsunder.set_animation(str(pants_value))
#					_:
#						pants_value = 1
			"body":
				if Playervariables.corruptiondict.has("leg") and Playervariables.corruptiondict["leg"] == Playervariables.raceWOLF:
					for node in $frames.get_children():
						node.position.y = 5#28
				else:
					for node in $frames.get_children():
						node.position.y = 17#28
				if Playervariables.corruptiondict[key] == Playervariables.raceIMP:
					if $jump.is_playing() == false:
						$impjump.play("RESET")
					else:
						var current_animation = $jump.current_animation
						$impjump.play(current_animation)
			"wings":
				$frames/wings.new_part("ruleswings imp")#get_node("frames/"+key).set_animation(str(Playervariables.corruptiondict.get(key)))
				$frames/wingsover.new_part("ruleswingsover imp")#get_node("frames/wingsover").set_animation(str(Playervariables.corruptiondict.get(key)))
				get_node("frames/wings").visible = Playervariables.corruptiondict.get(key) > 0
				get_node("frames/wingsover").visible = Playervariables.corruptiondict.get(key) > 0
			"armright":
				$frames/gloveright.visible = true
				$frames/weaponback.visible = false
				$frames/weaponback.blank()
				get_node("frames/weapon").z_index = 1
				match Playervariables.corruptiondict[key]:
					Playervariables.raceNEKO:
						$frames/armleft.new_part("rulesbodyarmleft cat")
						$frames/armright.new_part("rulesbodyarmright cat")
						if Playervariables.wolf_variants == true:
							$frames/weapon.new_part("rulesbodygloveleft werewolf")
							$frames/gloveright.new_part("rulesbodygloveright werewolf")
							get_node("frames/gloveright").material = load("res://Assets/materials/global_hairshift.tres")
							get_node("frames/weapon").material = load("res://Assets/materials/global_hairshift.tres")
						else:
							$frames/weapon.new_part("rulesbodygloveleft cat")
							$frames/gloveright.new_part("rulesbodygloveright cat")
							get_node("frames/gloveright").material = load("res://Assets/materials/global_rankshift.tres")
							get_node("frames/weapon").material = load("res://Assets/materials/global_rankshift.tres")
					Playervariables.raceHARPY:
						$frames/armleft.new_part("rulesbodyarmleft harpy")
						$frames/armright.new_part("rulesbodyarmright harpy")
						if Playervariables.harpy_variants == true:#Playervariables.CurrentClass == Playervariables.raceHARPY and Playervariables.AltClass == 1:
							$frames/gloveright.new_part("rulesbodygloveright dragonharpy")
							$frames/weapon.new_part("rulesbodygloveleft dragonharpy")
						else:
							$frames/gloveright.new_part("rulesbodygloveright harpy")
							$frames/weapon.new_part("rulesbodygloveleft harpy")
						get_node("frames/gloveright").material = null
						get_node("frames/weapon").material = null
			"horns":
				$frames/horns.new_part("ruleshorns ramgirl")
				$frames/horns.visible = true
			"backhorns":
				$frames/backhorns.new_part("ruleshornsback imp")
				$frames/backhorns.visible = true
			"ears":
				$frames/ears.visible = true
				match Playervariables.corruptiondict[key]:
					Playervariables.raceNEKO:
						$frames/ears.new_part("rulesears cat")
						#get_node("frames/tailback").use_parent_material = false
						get_node("frames/ears").use_parent_material = false
					Playervariables.raceKITSUNE:
						$frames/ears.new_part("rulesears fox Rose")
#						$frames/ears.material = load("res://Assets/materials/global_hairshift.tres")
						get_node("frames/ears").use_parent_material = true
					Playervariables.raceWOLF:
						$frames/ears.new_part("rulesears wolf")
#						$frames/ears.material = load("res://Assets/materials/global_hairshift.tres")
						get_node("frames/ears").use_parent_material = true
			"hair":
				$frames/hair.new_part("ruleshairfront fox Rose")
				$frames/hairback.new_part("ruleshairback fox Rose")
#				get_node("frames/"+key).set_animation(str(Playervariables.corruptiondict.get(key)))
#				get_node("frames/"+key).visible = true
#				get_node("frames/hairback").set_animation(str(Playervariables.corruptiondict.get(key)))
#				get_node("frames/hairback").set_animation(str(Playervariables.corruptiondict.get(key)))
#				get_node("frames/hairback").visible = true
			"leg":
				$frames/bootright.visible = true
				$frames/bootleft.visible = true
				if Playervariables.corruptiondict[key] == Playervariables.raceWOLF:
					$frames/legright.new_part("rulesbodylegright werewolf")#set_animation(str(Playervariables.corruptiondict.get(key)))
					$frames/legleft.new_part("rulesbodylegleft werewolf")#set_animation(str(Playervariables.corruptiondict.get(key)))
					$frames/bootright.new_part("rulesbodybootright werewolf")#set_animation(str(Playervariables.corruptiondict.get(key)))
					$frames/bootleft.new_part("rulesbodybootleft werewolf")#set_animation(str(Playervariables.corruptiondict.get(key)))
					$frames/bootright.material = load("res://Assets/materials/global_hairshift.tres")
					$frames/bootleft.material = load("res://Assets/materials/global_hairshift.tres")
				elif Playervariables.corruptiondict[key] == Playervariables.raceHARPY:
					$frames/legright.new_part("rulesbodylegright harpy")
					$frames/legleft.new_part("rulesbodylegleft harpy")
					if Playervariables.harpy_variants == true:#Playervariables.CurrentClass == Playervariables.raceHARPY and Playervariables.AltClass == 1:
						$frames/bootright.new_part("rulesbodybootright dragonharpy")
						$frames/bootleft.new_part("rulesbodybootleft dragonharpy")
					else:
						$frames/bootright.new_part("rulesbodybootright harpy")
						$frames/bootleft.new_part("rulesbodybootleft harpy")
					$frames/bootright.material = null
					$frames/bootleft.material = null
#					$frames/bootright.new_part("rulesbodybootright werewolf")
#					$frames/bootleft.new_part("rulesbodybootleft werewolf")
#					$frames/legright.set_animation(str(Playervariables.corruptiondict.get(key))+".1")
#					$frames/legleft.set_animation(str(Playervariables.corruptiondict.get(key))+".1")
				else:
					$frames/legright.new_part("rulesbodylegright imp")
					$frames/legleft.new_part("rulesbodylegleft imp")
					$frames/bootright.new_part("rulesbodybootright imp")
					$frames/bootleft.new_part("rulesbodybootleft imp")
					$frames/bootright.material = load("res://Assets/materials/global_pantsshift.tres")
					$frames/bootleft.material = load("res://Assets/materials/global_pantsshift.tres")
#					$frames/legright.set_animation(str(Playervariables.corruptiondict.get(key)))
#					$frames/legleft.set_animation(str(Playervariables.corruptiondict.get(key)))
			"tail":
				if Playervariables.get_corruption("face") == 0:
#				if Playervariables.curseditemdict[Playervariables.PIXIEDUST] == false:
					if Playervariables.corruptiondict[key] == Playervariables.raceKITSUNE:
						$frames/facemark.new_part("rulesfaceextra fox mark")
#						get_node("frames/facemark").set_animation(str(Playervariables.corruptiondict.get(key)))
						get_node("frames/facemark").visible = true
#						get_node("frames/facemark").set_self_modulate(Color(1,1,1,clamp(0.45 - (2*Playervariables.newcolorarrayskin[Playervariables.baseplayercolourarray[2]].z),0.45,1)))
					else:
						get_node("frames/facemark").blank()#set_animation("0")
						get_node("frames/facemark").visible = false
				match Playervariables.corruptiondict[key]:
					Playervariables.raceHARPY:
						get_node("frames/tailback").new_part("rulestail dragonharpy")
						get_node("frames/tailover").new_part("rulestailover dragonharpy")
					Playervariables.raceNEKO:
						get_node("frames/tailback").new_part("rulestail cat")
						get_node("frames/tailover").new_part("rulestailover cat")
					Playervariables.raceKITSUNE:
						get_node("frames/tailback").new_part("rulestail fox Rose")
						get_node("frames/tailover").new_part("rulestailover fox Rose")
					Playervariables.raceWOLF:
						get_node("frames/tailback").new_part("rulestail wolf Rose")
						get_node("frames/tailover").new_part("rulestailover wolf Rose")
					Playervariables.raceIMP:
						get_node("frames/tailback").new_part("rulestail imp")#set_animation(str(Playervariables.corruptiondict[key]))
						get_node("frames/tailover").new_part("rulestailover imp")#set_animation(str(Playervariables.corruptiondict[key]))
				get_node("frames/tailback").visible = true
				get_node("frames/tailover").visible = true
				get_node("frames/tailover/tailshift").play("tailshift")
#				if Playervariables.corruptiondict[key] == Playervariables.raceNEKO:
#					get_node("frames/tailback").material = load("res://Assets/materials/global_rankshift.tres")
#					get_node("frames/tailover").material = load("res://Assets/materials/global_rankshift.tres")
#				else:
#					get_node("frames/tailback").material = load("res://Assets/materials/global_hairshift.tres")
#					get_node("frames/tailover").material = load("res://Assets/materials/global_hairshift.tres")
				if (Playervariables.corruptiondict[key] in [Playervariables.raceHARPY,Playervariables.raceNEKO,Playervariables.raceIMP]) == true:
					if Playervariables.corruptiondict[key] == Playervariables.raceHARPY:
						get_node("frames/tailback").material = null
						get_node("frames/tailover").material = null
					else:
						get_node("frames/tailback").material = load("res://Assets/materials/global_rankshift.tres")
						get_node("frames/tailover").material = load("res://Assets/materials/global_rankshift.tres")
					get_node("frames/tailback").use_parent_material = false
					get_node("frames/tailover").use_parent_material = false
				else:
					get_node("frames/tailback").use_parent_material = true
					get_node("frames/tailover").use_parent_material = true
#			"horns":
#				get_node("frames/"+key).set_animation(str(Playervariables.corruptiondict.get(key)))
#			"ears":
#				get_node("frames/"+key).set_animation(str(Playervariables.corruptiondict.get(key))+str(Playervariables.baseplayercolourdescriptiondict.get(1)[Playervariables.baseplayercolourarray[1]]))
#			"tail":
#				get_node("frames/tailback").set_animation(str(Playervariables.corruptiondict.get(key))+str(Playervariables.baseplayercolourdescriptiondict.get(1)[Playervariables.baseplayercolourarray[1]]))
#				get_node("frames/tailover").set_animation(str(Playervariables.corruptiondict.get(key))+str(Playervariables.baseplayercolourdescriptiondict.get(1)[Playervariables.baseplayercolourarray[1]]))
	pants_value = 1
	top_value = 1
	if Playervariables.consent == true and Playervariables.corruptiondict.has("top"):
		top_value = Playervariables.corruptiondict["top"]
#	$frames/top.set_animation(str(outfittopnum))
	if Playervariables.consent == true and Playervariables.corruptiondict.has("pants"):
		pants_value = Playervariables.corruptiondict["pants"]
	match Playervariables.playerbustsize:
		1:
#			$frames/chest.set_animation("regular light small")
			$frames/bust.new_part("rulesbodybust regular light small")#set_animation("regular light small")
			match top_value:
				Playervariables.raceNAMAN:$frames/topupper.new_part("rulesclothestop fuschia small",1)
				Playervariables.raceRAM:$frames/topupper.new_part("rulesclothestop ram small",1)
				Playervariables.raceWOLF:$frames/topupper.new_part("rulesclothestop wolf smmedlarge",1)
#			$frames/topupper.set_animation(str(top_value)+".1")
		2:
#			$frames/chest.set_animation("regular light")
			$frames/bust.new_part("rulesbodybust regular light")#.set_animation("regular light")
			match top_value:
				Playervariables.raceNAMAN:$frames/topupper.new_part("rulesclothestop fuschia",1)
				Playervariables.raceRAM:$frames/topupper.new_part("rulesclothestop ram",1)
				Playervariables.raceWOLF:$frames/topupper.new_part("rulesclothestop wolf smmedlarge",1)
#			$frames/topupper.set_animation(str(top_value)+".2")
		3:
			if Playervariables.consent == true:
#				$frames/chest.set_animation("regular light")
				$frames/bust.new_part("rulesbodybust regular light large")#.set_animation("regular light large")
				match top_value:
					Playervariables.raceNAMAN:$frames/topupper.new_part("rulesclothestop fuschia large",1)
					Playervariables.raceRAM:$frames/topupper.new_part("rulesclothestop ram large",1)
					Playervariables.raceWOLF:$frames/topupper.new_part("rulesclothestop wolf smmedlarge",1)
#				$frames/topupper.set_animation(str(top_value)+".3")
			else:
#				$frames/chest.set_animation("regular light")
				$frames/bust.new_part("rulesbodybust regular light")#.set_animation("regular light")
				$frames/topupper.new_part("rulesclothestop fuschia",1)
#				$frames/topupper.set_animation("1.2")
		4:
			if Playervariables.consent == true:
#				$frames/chest.set_animation("regular light")
				$frames/bust.new_part("rulesbodybust regular light huge")#.set_animation("regular light huge")
				match top_value:
					Playervariables.raceNAMAN:$frames/topupper.new_part("rulesclothestop fuschia huge",1)
					Playervariables.raceRAM:$frames/topupper.new_part("rulesclothestop ram huge",1)
					Playervariables.raceWOLF:$frames/topupper.new_part("rulesclothestop wolf huge",1)
#				$frames/topupper.set_animation(str(top_value)+".4")
				if top_value == 1:
					$frames/top.visible = false
			else:
#				$frames/chest.set_animation("regular light")
				$frames/bust.new_part("rulesbodybust regular light")#.set_animation("regular light")
				$frames/topupper.new_part("rulesclothestop fuschia",1)
#				$frames/topupper.set_animation("1.2")
	if Playervariables.playeroutfit[0] == 0 and Playervariables.consent == true:
		$frames/topupper.visible = false
		$frames/top.visible = false
	else:
		$frames/topupper.visible = true
#		if top_value == 1 and Playervariables.sealsarray[Playervariables.SLUT] > 0 or Playervariables.CurrentClass == Playervariables.raceKITSUNE:
#			$frames/top.visible = false
		if (Playervariables.playerbustsize < 4 or top_value > 0) or Playervariables.consent == false:
			$frames/top.visible = true
		else:
			$frames/top.visible = false
	if Playervariables.playeroutfit[1] == 0 and Playervariables.consent == true:
		$frames/pants.visible = false
		$frames/pantsunder.visible = false
		$frames/pantsundermirror.visible = false
	else:
		match pants_value:
			Playervariables.raceNAMAN:
				$frames/pantsunder.blank()
				$frames/pants.new_part("rulesclothespants fuschia",1)
			Playervariables.raceRAM:
				$frames/pantsunder.new_part("rulesclothespantiesunder ram",1)
				if Playervariables.corruptiondict.has("leg"):
					$frames/pants.new_part("rulesclothespantiesover ram",1)
				else:
					$frames/pants.new_part("rulesclothespants ram",1)
				
			1: $frames/pantsunder.new_part("",1)
#		$frames/pantsunder.set_animation(str(pants_value))
#		$frames/pants.set_animation(str(pants_value))
		if pants_value == 5:
#			if Playervariables.corruptiondict.has("leg"):
#				$frames/pants.set_animation("5.1")
			$frames/pantsunder.visible = true
		else:
			$frames/pantsunder.visible = false
		$frames/pants.visible = true
#		$frames/pants.set_animation(str(pants_value))
	if ("doublesleep" in get_parent()) == false or get_parent().doublesleep == false:
		if Playervariables.corruptiondict.has("eyes") and Playervariables.corruptiondict["eyes"] == Playervariables.raceRAM:
			$frames/eyes.new_part("ruleseyes regular Yellow")#$frames/eyes.set_animation("regular Yellow")
		else:
			$frames/eyes.new_part("ruleseyes regular Blue")#$frames/eyes.set_animation("regular Blue")
	on_abdomen_size_changed()
	if Playervariables.curseditemdict[Playervariables.ROPEGAG] == true:
		$frames/weapon.new_part("rulesbodygloveleft rope")
		$frames/weapon.z_index = 1
		$frames/weapon.visible = true
		$frames/gloveright.new_part("rulesbodygloveright rope")
		$frames/gloveright.visible = true
		$frames/armleft.new_part("rulesbodyarmleft harpy")
		$frames/armright.new_part("rulesbodyarmright harpy")
		$frames/weaponback.visible = false
		$frames/weaponback.blank()
	if first_run == true:
		debugcureplayersprite()
