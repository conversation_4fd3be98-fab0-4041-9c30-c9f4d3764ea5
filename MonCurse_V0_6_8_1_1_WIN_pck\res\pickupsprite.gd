extends Node2D


# Declare member variables here. Examples:
# var a = 2
# var b = "text"


# Called when the node enters the scene tree for the first time.
func _ready():
	$pickup.visible = false
#	yield(get_tree(),"idle_frame")
#	if wild == true:
#		position.y += +15
	if Playervariables.nextlocation == Playervariables.Tutorial2:
		add_to_group("Refresh_Pedestals")

func refresh(ID,onoff):
	if currentitem == ID:
		if onoff == true:
			$pickup.visible = true
			if $barrier.get_animation() == "emptypedestal":
				$barrier.play("pedestal")
		else:
			$pickup.visible = false
			if $barrier.get_animation() == "pedestal":
				$barrier.play("emptypedestal")

#var Shopsign = load("res://shopsign.tscn")

var active = false
var currentitem = 0
var generatedbarred = 0
var itemname #for shop, now useless and defunct
var cost #for shop
var description = "Error"
var wild = false
#itemIDs: 5 = attackrange+1
func generateitem(locked,pool): #if pool is '0', means don't change the current item, -1 means item has been taken, #2 means it's a shop item
	if generatedbarred == 0:
		if locked == true:
			generatedbarred = 2
		else:
			generatedbarred = 1
			if pool != 2:
				get_parent().get_parent().wildpickuparray.append(position)
				get_parent().get_parent().wildpickuparray.append(self)
#				print(str(get_parent().get_parent().wildpickuparray))
				wild = true
				$barrier.play("chest")
				position.y += +18
	if locked == true:
		$barrier.play("barred")
		active = false
	else:
		if wild == false:
			$barrier.play("unbarred")
		active = true
	var randnum = randi()%13
	if randnum < 4:
		randnum = randi()%13
	if randnum < 4:
		randnum = randi()%13
	match pool:
		-1:
			$pickup.visible = false
			currentitem = 0
			active = false
		0:
			pass
		-7:
			$pickup.texture = load("res://Assets/pickups/lunardialhalf.png")
			$pickup.visible = true
			currentitem = -7
			itemname = "Lunar Dial Half"
			cost = 50
			$barrier.play("empty")
		-8:
			$pickup.texture = load("res://Assets/pickups/mappiece.png")
			$pickup.visible = true
			currentitem = -8
			itemname = "Map Piece"
			cost = 6
			$barrier.play("empty")
		1,2:
			match randnum:
				0:
					$pickup.texture = load("res://Assets/pickups/attackrange1.png")
					$pickup.visible = true
					currentitem = -5
					itemname = "Attack Range +1"
					cost = (randi()%3)+15
					if wild == true:
						$barrier.play("chest2")
				1:
					$pickup.texture = load("res://Assets/pickups/boots.png")
					$pickup.visible = true
					currentitem = -6
					itemname = "Sturdy Boots"
					cost = (randi()%3)+3
					if wild == true:
						$barrier.play("chest2")
				2,3:
					$pickup.texture = load("res://Assets/pickups/torchbuff.png")
					$pickup.visible = true
					currentitem = -4
					itemname = "Torchbearer"
					cost = (randi()%3)+6
					if wild == true:
						$barrier.play("chest2")
				4:
					currentitem = 4
					itemload()
				5:
					currentitem = 6
					itemload()
				6:
					currentitem = 7
					itemload()
				7:
					currentitem = 8
					itemload()
				8:
					currentitem = 9
					itemload()
				9:
					currentitem = 10
					itemload()
				10:
					currentitem = 12
					itemload()
				11:
					currentitem = 14
					itemload()
				12:
					currentitem = 15
					itemload()

var shopsign = null
func shopitem():
	shopsign = load(Mainpreload.Shopsign).instance()
	add_child(shopsign)
	shopsign.position = Vector2(0,128)
	if cost != null:
		shopsign.get_node("Label").set_text(str(cost))
		if cost > Playervariables.playershards:
			shopsign.set_modulate(Color(0.95,0.75,0.75))
			shopsign.get_node("Label").set_self_modulate(Color(0.95,0.75,0.75))
	else:
		shopsign.get_node("Label").set_text("err")

func itemload():
	var movedict = Playervariables.get("Move"+str(currentitem))
	$pickup.texture = load("res://Assets/abilityicons/"+str(movedict.get("name"))+".png")
	$pickup.visible = true
	itemname = str(movedict.get("name"))
	cost = (randi()%3)+2
func inventoryload(item):
	match item:
		-4: 
			$pickup.texture = load("res://Assets/pickups/torchbuff.png")
			description = "Gives 10 torches, more torch lifetime and more torch damage."
		-5:
			$pickup.texture = load("res://Assets/pickups/attackrange1.png")
			description = "Your basic attack can go one space farther."
		-6: 
			$pickup.texture = load("res://Assets/pickups/boots.png")
			description = "When you fall at least 4 spaces, does a regular 1 damage force attack."
		-7:
			$pickup.texture = load("res://Assets/pickups/lunardialhalf.png")
			description = "A broken moon's dial. Increases vision by 1."
		-8:
			$pickup.texture = load("res://Assets/pickups/mappiece.png")
			description = "Grants an additional path choice on the next round."
		Playervariables.CLIMBBUFF,Playervariables.WOLFPELT,Playervariables.COWBELL,Playervariables.PAWS,Playervariables.FOXPENDANT,Playervariables.NORMALPAWS,Playervariables.ROPEGAG:
			$pickup.texture = null#load("res://Assets/pickups/curseditems.png") #watch out, this shows the whole thing kinda buggy
			description = ""#You gain 'Running' when ending your turn climbing."
			var newshow = load(Mainpreload.Curseditem).instance()
			newshow.get_child(0).visible = false
			add_child(newshow)
			if Playervariables.curseditemstringdict.has(item):
				newshow.play(Playervariables.curseditemstringdict[item])
				description = Playervariables.curseditemdescribedict[item]
#				match item:
#					Playervariables.CLIMBBUFF:
#						description = "You gain 'Running' when ending your turn climbing."
#					Playervariables.WOLFPELT:
#						description = "Limited protection against wolf-girl."
#					Playervariables.COWBELL:
#						description = "Generates milk when hit by FORCE."
#					Playervariables.PAWS:
#						description = "Basic attack deals SWEET damage."
#					Playervariables.FOXPENDANT:
#						description = "+1 choice of Skill on the map."
#					Playervariables.NORMALPAWS:
#						description = "Basic attack hits three tiles."
		_:
			self.visible = false
			$pickup.visible = false
			$barrier.visible = false
			return
	$pickup.visible = true
	$barrier.visible = false

