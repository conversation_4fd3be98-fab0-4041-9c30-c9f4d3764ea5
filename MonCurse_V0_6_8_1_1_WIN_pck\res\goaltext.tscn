[gd_scene load_steps=5 format=2]

[ext_resource path="res://Assets/ui/goaltext.png" type="Texture" id=1]

[sub_resource type="AtlasTexture" id=1]
atlas = ExtResource( 1 )
region = Rect2( 128, 0, 128, 64 )

[sub_resource type="AtlasTexture" id=2]
atlas = ExtResource( 1 )
region = Rect2( 0, 0, 128, 64 )

[sub_resource type="SpriteFrames" id=3]
animations = [ {
"frames": [ SubResource( 1 ), SubResource( 2 ) ],
"loop": true,
"name": "goal",
"speed": 5.0
} ]

[node name="goaltext" type="AnimatedSprite"]
frames = SubResource( 3 )
animation = "goal"
