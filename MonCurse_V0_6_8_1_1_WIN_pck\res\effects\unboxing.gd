extends Node2D

const dropper = preload("res://effects/dropper.tscn")

const anim_positions = PoolVector2Array([Vector2(7,20),Vector2(100,-30),Vector2(128,0),Vector2(128,-200)])
func drop_items(IDarray):
	var use_x = 0
	var use_y = 0
	for item in IDarray:
		print("Usex:"+str(use_x)+"... Usey:"+str(use_y))
		var newdrop = dropper.instance()
		add_child(newdrop)
		newdrop.set_texture(load("res://Assets/abilityicons/"+Playervariables.get("Move"+str(item))["name"]+".png"))

		var anim_node = newdrop.get_node("player")
		var newanim = anim_node.get_animation("collect").duplicate()
		
		for i in range(newanim.track_get_key_count(0)):
			if i == 0:
				newanim.track_set_key_value(0,i,Vector2(anim_positions[i].x*use_x,anim_positions[i].y))
			else:
				newanim.track_set_key_value(0,i,Vector2(anim_positions[i].x*use_x,anim_positions[i].y+(128*use_y)))
		
		anim_node.add_animation("drop", newanim)
		anim_node.playback_speed = 0.9+(randf()*0.2)
		newdrop.get_node("player").play("drop")
		use_x += 1
		if use_x > 1:
			use_x = -1
		elif use_x == 0:
			use_y -= 1
		if use_x == 0:
			if use_y == -2:
				use_y = 1
			elif use_y == 0:
				use_y = -2
	print("Usex:"+str(use_x)+"... Usey:"+str(use_y))
	$Timer.start(2.5)

func _on_Timer_timeout():
	queue_free()
