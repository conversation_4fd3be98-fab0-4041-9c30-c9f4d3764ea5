[gd_scene load_steps=40 format=2]

[ext_resource path="res://modularenemy.gd" type="Script" id=1]
[ext_resource path="res://effects/heartparticles.png" type="Texture" id=2]
[ext_resource path="res://Assets/materials/hairhueshifter.shader" type="Shader" id=3]
[ext_resource path="res://effects/stripe.png" type="Texture" id=4]
[ext_resource path="res://Assets/ui/enemyhealthfill.png" type="Texture" id=5]
[ext_resource path="res://Assets/ui/enemyhealth.png" type="Texture" id=6]
[ext_resource path="res://Enemy/menacingaura.png" type="Texture" id=9]
[ext_resource path="res://Assets/ui/quipbubble.png" type="Texture" id=19]
[ext_resource path="res://Enemy/dropshadow.png" type="Texture" id=20]
[ext_resource path="res://Assets/ui/rankicon.png" type="Texture" id=21]

[sub_resource type="ShaderMaterial" id=1]
resource_local_to_scene = true
shader = ExtResource( 3 )
shader_param/hue_shift = 0.0
shader_param/sat_mul = 1.0
shader_param/val_mul = 1.0

[sub_resource type="Animation" id=67]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath(".:rotation_degrees")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 2 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=68]
resource_name = "engage"
length = 3.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:rotation_degrees")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.6, 1.4, 2.2, 3 ),
"transitions": PoolRealArray( 0.5, 2, 0.5, 2, 2 ),
"update": 0,
"values": [ 0.0, 7.0, 0.0, -7.0, 0.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1.2, 2.2, 3 ),
"transitions": PoolRealArray( 2, 0.5, 2, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 1.2, 1.2 ), Vector2( 0.95, 0.95 ), Vector2( 1, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:self_modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1.2, 3 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 0.901961, 0.901961, 0.901961, 1 ), Color( 0.705882, 0.705882, 0.705882, 1 ), Color( 0.901961, 0.901961, 0.901961, 1 ) ]
}

[sub_resource type="Animation" id=3]
length = 0.3
tracks/0/type = "value"
tracks/0/path = NodePath(".:rotation_degrees")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.2, 0.3 ),
"transitions": PoolRealArray( 2, 2, 2, 2 ),
"update": 0,
"values": [ 10.0, 0.0, -10.0, 0.0 ]
}

[sub_resource type="Animation" id=71]
resource_name = "bighurt"
length = 1.5
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 1.5 ),
"transitions": PoolRealArray( 1, 0.8, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 3.2, 1.6, 1.6, 1 ), Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=69]
resource_name = "death"
length = 0.7
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.7 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.7 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 1e-05, 1e-05 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:rotation_degrees")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.7 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ 0.0, 270.0 ]
}

[sub_resource type="Animation" id=4]
length = 0.5
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.117647, 0.117647, 0.117647, 0.843137 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:offset")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 20, -15 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("tail:offset")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 20, -15 ) ]
}

[sub_resource type="Animation" id=5]
length = 0.5
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 0.784314, 0.882353, 0.686275, 1 ), Color( 0.117647, 0.117647, 0.117647, 0.705882 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:offset")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 0, -102 ), Vector2( 20, -117 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("tail:offset")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 0, -102 ), Vector2( 20, -117 ) ]
}

[sub_resource type="Animation" id=72]
resource_name = "hugehurt"
length = 2.5
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3, 2.5 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 4, 1.7, 1.7, 1 ), Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=70]
resource_name = "hurt"
length = 0.7
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.7 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 3, 1.5, 1.5, 1 ), Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=74]
resource_name = "hypno"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3, 2 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 3.5, 1.5, 3.5, 1 ), Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=73]
resource_name = "megahurt"
length = 3.5
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5, 3.5 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 6, 2, 2, 1 ), Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=6]
length = 4.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:rotation_degrees")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 1,
"values": [ 0.0, 10.0, 0.0, -10.0, 0.0 ]
}

[sub_resource type="Animation" id=7]
length = 2.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1, 2 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.627451 ), Color( 1, 1, 1, 0.235294 ), Color( 1, 1, 1, 0.627451 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1, 2 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 0.8, 0.8 ), Vector2( 1, 1 ) ]
}

[sub_resource type="Animation" id=8]
length = 2.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1, 2 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.627451 ), Color( 1, 1, 1, 0.235294 ), Color( 1, 1, 1, 0.627451 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1, 2 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 1.5, 1.5 ), Vector2( 1.2, 1.2 ), Vector2( 1.5, 1.5 ) ]
}

[sub_resource type="AtlasTexture" id=16]
flags = 4
atlas = ExtResource( 19 )
region = Rect2( 0, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=17]
flags = 4
atlas = ExtResource( 19 )
region = Rect2( 128, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=15]
flags = 4
atlas = ExtResource( 19 )
region = Rect2( 0, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=18]
flags = 4
atlas = ExtResource( 19 )
region = Rect2( 128, 128, 128, 128 )

[sub_resource type="SpriteFrames" id=9]
animations = [ {
"frames": [ SubResource( 16 ) ],
"loop": true,
"name": "conversation",
"speed": 5.0
}, {
"frames": [ SubResource( 17 ) ],
"loop": true,
"name": "exclamation",
"speed": 5.0
}, {
"frames": [ null ],
"loop": true,
"name": "none",
"speed": 5.0
}, {
"frames": [ SubResource( 15 ) ],
"loop": true,
"name": "normal",
"speed": 5.0
}, {
"frames": [ SubResource( 18 ) ],
"loop": true,
"name": "question",
"speed": 5.0
} ]

[sub_resource type="Animation" id=19]
resource_name = "quipanim"
length = 1.2
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("quip:rotation_degrees")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3, 0.7, 1.2 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, -8.0, 3.0, 0.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("quip:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.3, 0.6, 0.8, 1.2 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0.65, 0.65 ), Vector2( 1, 0.7 ), Vector2( 0.8, 0.9 ), Vector2( 1, 0.7 ), Vector2( 0.65, 0.65 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("quip:offset")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.7, 1.2 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -6, 7 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=20]
resource_name = "quipanim2"
length = 3.5
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("quip:offset")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.3, 3.5 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0, -30 ), Vector2( 0, 0 ), Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("quip:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1, 2.5, 3.5 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, -14.0, 14.0, 0.0 ]
}

[sub_resource type="Animation" id=63]
resource_name = "block"
length = 4.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.470588 ), Color( 1, 1, 1, 0.784314 ), Color( 1, 1, 1, 0.470588 ), Color( 1, 1, 1, 0.784314 ), Color( 1, 1, 1, 0.470588 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:rect_scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 2, 4 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 1.05, 1.1 ), Vector2( 1, 1 ) ]
}

[sub_resource type="Gradient" id=64]
offsets = PoolRealArray( 0, 0.0986842, 0.611842, 1 )
colors = PoolColorArray( 0, 0, 0, 0, 1, 0.588235, 0.611765, 0.705882, 1, 0.784314, 0.784314, 0.705882, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=65]
gradient = SubResource( 64 )

[sub_resource type="ParticlesMaterial" id=66]
emission_shape = 2
emission_box_extents = Vector3( 80, 30, 1 )
flag_disable_z = true
direction = Vector3( 0, 1, 0 )
spread = 10.0
gravity = Vector3( 0, -300, 0 )
initial_velocity = 80.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
color_ramp = SubResource( 65 )

[sub_resource type="Gradient" id=611]
offsets = PoolRealArray( 0, 0.209459, 0.641892, 1 )
colors = PoolColorArray( 1, 1, 1, 0, 0.784314, 0.784314, 0.784314, 0.784314, 1, 1, 1, 0.588235, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=612]
gradient = SubResource( 611 )

[sub_resource type="ParticlesMaterial" id=610]
emission_shape = 2
emission_box_extents = Vector3( 30, 10, 1 )
flag_disable_z = true
direction = Vector3( 0, -1, 0 )
spread = 180.0
gravity = Vector3( 0, -98, 0 )
initial_velocity = 130.0
angular_velocity = 20.0
angular_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
damping = 50.0
scale = 0.5
scale_random = 1.0
color_ramp = SubResource( 612 )

[node name="enemy" type="AnimatedSprite"]
light_mask = -2147483647
material = SubResource( 1 )
use_parent_material = true
scale = Vector2( 1.02808, 1.02808 )
offset = Vector2( 20, -15 )
script = ExtResource( 1 )

[node name="AnimationFlinch" type="AnimationPlayer" parent="."]
playback_speed = 1.3
anims/RESET = SubResource( 67 )
anims/engage = SubResource( 68 )
anims/flinch = SubResource( 3 )

[node name="AnimationPlayer2" type="AnimationPlayer" parent="."]
playback_speed = 2.0
anims/bighurt = SubResource( 71 )
anims/death = SubResource( 69 )
anims/fadeout = SubResource( 4 )
anims/fadeoutpitch = SubResource( 5 )
anims/hugehurt = SubResource( 72 )
anims/hurt = SubResource( 70 )
anims/hypno = SubResource( 74 )
anims/megahurt = SubResource( 73 )

[node name="enemymoving" type="Timer" parent="."]
one_shot = true

[node name="turnwait" type="Timer" parent="."]

[node name="tail" type="AnimatedSprite" parent="."]
show_behind_parent = true
use_parent_material = true
offset = Vector2( 20, -15 )

[node name="tail" type="AnimationPlayer" parent="tail"]
anims/tailwag = SubResource( 6 )

[node name="aura" type="Sprite" parent="."]
visible = false
self_modulate = Color( 1, 1, 1, 0.627451 )
z_index = -1
texture = ExtResource( 9 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="aura"]
anims/menacing = SubResource( 7 )
anims/menacingpitch = SubResource( 8 )

[node name="quip" type="AnimatedSprite" parent="."]
visible = false
self_modulate = Color( 1, 1, 1, 0.784314 )
position = Vector2( 40, -124 )
scale = Vector2( 0.65, 0.65 )
z_index = 15
frames = SubResource( 9 )
animation = "normal"

[node name="shadow" type="Sprite" parent="."]
self_modulate = Color( 1, 1, 1, 0.784314 )
position = Vector2( 0, 78 )
z_index = -2
texture = ExtResource( 20 )

[node name="rank" type="Sprite" parent="."]
visible = false
self_modulate = Color( 1, 1, 1, 0.784314 )
use_parent_material = true
position = Vector2( -64, -80 )
scale = Vector2( 0.5, 0.5 )
texture = ExtResource( 21 )

[node name="CanvasLayer3" type="CanvasLayer" parent="."]
layer = 3
follow_viewport_enable = true

[node name="raisezlevel" type="Node2D" parent="CanvasLayer3"]

[node name="attackgraphs" type="VBoxContainer" parent="CanvasLayer3/raisezlevel"]
visible = false
anchor_left = 0.5
anchor_right = 0.5
margin_left = -32.0
margin_top = -256.0
margin_right = 32.0
margin_bottom = -56.0
grow_horizontal = 2
grow_vertical = 0
mouse_filter = 2
custom_constants/separation = 64
alignment = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="notice" type="Node2D" parent="CanvasLayer3/raisezlevel"]

[node name="quipanim" type="AnimationPlayer" parent="."]
playback_speed = 0.85
anims/quipanim = SubResource( 19 )
anims/quipanim2 = SubResource( 20 )

[node name="blocks" type="GridContainer" parent="."]
modulate = Color( 1, 1, 1, 0.470588 )
margin_left = -72.0
margin_top = -92.0
margin_right = 72.0
margin_bottom = 92.0
grow_horizontal = 2
grow_vertical = 2
columns = 2

[node name="blockanim" type="AnimationPlayer" parent="."]
root_node = NodePath("../blocks")
anims/block = SubResource( 63 )

[node name="ready" type="Particles2D" parent="."]
visible = false
position = Vector2( 0, 70 )
emitting = false
lifetime = 1.5
preprocess = 0.9
process_material = SubResource( 66 )
texture = ExtResource( 4 )

[node name="healthouter" type="Sprite" parent="."]
visible = false
modulate = Color( 0.203922, 1, 0.196078, 0.705882 )
position = Vector2( 0, 90 )
z_index = 8
texture = ExtResource( 6 )

[node name="healthinner" type="Sprite" parent="healthouter"]
texture = ExtResource( 5 )

[node name="enemyhearts" type="Particles2D" parent="."]
visible = false
position = Vector2( 0, -20 )
z_index = 3
emitting = false
amount = 4
lifetime = 2.0
one_shot = true
preprocess = 0.3
speed_scale = 0.7
explosiveness = 0.7
randomness = 0.5
process_material = SubResource( 610 )
texture = ExtResource( 2 )

[connection signal="animation_finished" from="AnimationPlayer2" to="." method="_on_AnimationPlayer2_animation_finished"]
[connection signal="timeout" from="enemymoving" to="." method="_on_enemymoving_timeout"]
