extends Node

const Main = "res://Main.tscn" #MainScene

const cursorpoint = "res://Assets/pointcursor.png" #Global
const cursorhand = "res://Assets/handcursor.png" #Global
const cursorgrab = "res://Assets/grabcursor.png" #Global
const cursoraim = "res://Assets/aimcursor.png" #Global
const cursorhandscroll = "res://Assets/handcursorwscroll.png" #Global

const movebarempty = "res://Assets/movebarempty.png" #MainScene
const movebaremptybonus = "res://Assets/movebaremptybonus.png" #MainScene
const movebarfill = "res://Assets/movebarfill.png" #MainScene
const movebarfillbonus = "res://Assets/movebarfillbonus.png" #MainScene

const GododLight = "res://Assets/lightgodod.png" #MainScene
const Lightnode = "res://LightNode.tscn" #MainScene
const defaultbackground = "res://Background/mainbackgroundtest.png" #MainScene
const sunsetbackground = "res://Background/mainbackgroundsunset.png" #SunsetZone
const nightbackground = "res://Background/mainbackgroundnight.png" #NightZone
const nightsky = "res://Background/nightsky.png" #ConversationCG

const headCG = "res://DialogueArt/CG/headscene.tscn" #MainScene

const petalmenumiddle = "res://Assets/ui/petalmenumiddle.png" #ConversationCG
const petalmenupetal = "res://Assets/ui/petalmenupetal.png" #ConversationCG
const petalmenupetalhover = "res://Assets/ui/petalmenupetalhover.png" #ConversationCG
const petalmenupetalburned = "res://Assets/ui/petalmenupetalburned.png" #ConversationCG

const ConversationV1 = "res://ConversationV1.tscn" #ConversationCG
const SettingScreen = "res://Settingscreen.tscn" #Master (NOT in preloadscenes!)

const BlinkEffect = "res://DialogueArt/CG/blinkeffect.tscn" #ConversationCG

const ResetEffect = "res://reseteffect.tscn" #MainScene
const ReloadEffect = "res://reloadeffect.tscn" #MainScene

const RewardSymbol = "res://DialogueArt/CG/shikasymbol.tscn" #ConversationCG

const Fireflies = "res://effects/fireflies.tscn" #MainScene

const GodRay = "res://Background/godray.tscn" #MainScene
const FrustrationEffect = "res://effects/frustration.tscn" #MainScene
const DebuffHypno = "res://effects/hypno.tscn" #MainScene
const LightningEffect = "res://effects/lightning.tscn" #MainScene
const CorruptedText = "res://effects/corruptedtext.tscn" #MainScene
const KissEffect = "res://effects/kisseffect.tscn" #MainScene
const LeafEffect = "res://effects/leaves.tscn" #MainScene
const Qadesminisprite = "res://Enemy/qades/qadesshopsprite.png" #Qades
const Curseditem = "res://curseditem.tscn" #MainScene
const Graph = "res://damagegraph.tscn" #MainScene
const Hurtaura = "res://hurt.tscn" #MainScene
const Bird = "res://Background/bird.tscn" #MainScene
const Butterfly = "res://Background/butterfly.tscn" #MainScene
const Gecko = "res://Background/gecko.tscn" #MainScene
const Escalationscreen = "res://escalation.tscn" #MainScene
const Escalation2 = "res://escalatev2.tscn" #MainScene
const Debuffsprite = "res://debuffsprite.tscn" #MainScene
const Talltree = "res://tree.tscn" #MainScene
const Bigtree = "res://bigtree.tscn" #MainScene
const Indicator = "res://indicator.tscn" #MainScene
const Hitmarker = "res://hitmarker.tscn" #MainScene
const Pickupsprite = "res://pickupsprite.tscn" #MainScene
const Attackeffect = "res://attackeffectmap.tscn" #MainScene
const Selector = "res://selector.tscn" #MainScene
const Endselector = "res://endselector.tscn" #MainScene
const DropIndicator = "res://dropindicator.tscn" #MainScene
const Enemy = "res://modularenemy.tscn" #MainScene
const NPC = "res://modularnpc.tscn" #MainScene
const divergence = "res://Assets/ui/divergence.png" #MainScene
const locationfog = "res://Background/locationfog.png" #MainScene
const goaltext = "res://goaltext.tscn" #MainScene
const Obj1 = "res://obj1.tscn" #MainScene
const Obj2 = "res://obj2.tscn" #MainScene
const Obj3 = "res://obj3.tscn" #MainScene
const Obj4 = "res://obj4.tscn" #DragonHarpy
const Obj5 = "res://obj5.tscn" #MainScene
const Obstructionmap = "res://obstructionmap.tscn" #MainScene
const Block = "res://block.tscn" #MainScene
const Blockprevieweffect = "res://blockprevieweffect.tscn" #MainScene
const Traffic = "res://trafficlight.tscn" #MainScene
const Dummy = "res://dummy.tscn" #MainScene

const ChapterText = "res://DialogueArt/CG/chaptertext.tscn" #ConversationCG
const floatingtext = "res://floatingtext.tscn" #MainScene
const tutorialcamera = "res://Assets/ui/tutorial camera drag.png" #MainScene
const tutorialtouch = "res://Assets/ui/tutorial camera touch.png" #MainScene
const tutorialhelp = "res://Assets/ui/tutorial camera helpmeplease.png" #MainScene

const InventoryButton = "res://Assets/ui/inventory.png" #MainScene
const InventoryButtonHover = "res://Assets/ui/inventoryhover.png" #MainScene
const ConvoInventoryButton = "res://Assets/ui/convov2inventorybutton.png" #MainScene
const ConvoInventoryButtonHover = "res://Assets/ui/convov2inventorybuttonhover.png" #MainScene

const deployable = preload("res://Assets/ui/deployabledouble.png") #MainScene
const deployablehover = preload("res://Assets/ui/deployabledoublehover.png") #MainScene
#const deployableitem = "res://Assets/ui/deployablev2.png" #MainScene
#const deployableinnate = "res://Assets/ui/deployablev2spec.png" #MainScene
const deployablepassive = "res://Assets/ui/passivedeployable.png" #MainScene

const Friendly = "res://effects/friendly.tscn"
const CorruptionSmoke = "res://effects/corruptionsmoke.tscn" #MainScene
const UseEffect = "res://effects/useitem.tscn" #MainScene
const Trash = "res://trash.tscn" #MainScene
const DebuffTrigger = "res://effects/debufftrigger.tscn" #MainScene
const HitParticles = "res://effects/hitparticles.tscn" #MainScene
const Particle0 = "res://effects/particle0.png" #MainScene
const Particle1 = "res://effects/particle1.png" #MainScene
const Particle2 = "res://effects/particle2.png" #MainScene
const Particle3 = "res://effects/particle3.png" #MainScene
const Particle4 = "res://effects/particle4.png" #MainScene
const Particle5 = "res://effects/particle5.png" #MainScene
const Particle6 = "res://effects/particle6.png" #MainScene
const RockParticleAlt = "res://effects/rock.png" #MainScene
const RockParticle = "res://effects/rockalt.png" #MainScene
const WoodParticle = "res://effects/wood.png" #MainScene
const WoodParticleAlt = "res://effects/woodalt.png" #MainScene

const Shopsign = "res://shopsign.tscn" #MainScene
const SparkleEffect = "res://effects/sparkle.tscn" #MainScene

const PossessionScreen = "res://effects/possessionscreen.tscn" #FoxGirl
const HypnosisScreen = "res://effects/hypnoscreen.tscn" #Ramgirl
const CumScreen = "res://effects/cumscreen.tscn" #MainScene

const DebugPoint = "res://debugpoint.tscn" #MainScene


const SandCloud = "res://Background/foreground sandcloud.png" #Desert
const Cloudspawn = "res://Background/foreground cloud.tscn" #MainScene
const Onibi = "res://effects/onibi.tscn" #MainScene, maybe move to foxgirl later?

const RamBadEnd1 = "res://DialogueArt/CG/gameovers/rambadend.gd" #RamGirl
const FoxBadEnd1 = "res://DialogueArt/CG/gameovers/foxbadend.tscn" #FoxGirl
const CatBadEnd1 = "res://DialogueArt/CG/gameovers/catbadend1rerig.tscn" #CatKnight
const CatScene = "res://DialogueArt/CG/catscene.tscn" #CatKnight
const catknightsprites = "res://Enemy/cat/catknightframes.tres" #CatKnight
const catknightspritestail = "res://Enemy/cat/catknightframestail.tres" #CatKnight
const SelfScene = "res://DialogueArt/CG/selfscene.tscn" #Rules
const BustScene = "res://DialogueArt/CG/bustscene.tscn" #Rules
const RamScene = "res://DialogueArt/CG/ramscene.tscn" #RamGirl
const FoxScene = "res://DialogueArt/CG/foxscene.tscn" #FoxGirl
const HarpyScene = "res://DialogueArt/CG/harpyscene.tscn" #DragonHarpy
const ImpScene = "res://DialogueArt/CG/impscene.tscn" #Imp
const ramgirlsprites = "res://Enemy/ram/ramgirlframes.tres" #RamGirl
const ramgirlspritesunclothed = "res://Enemy/ram/ramgirlframes unclothed.tres" #RamGirl
const ramgirlspritesspecial = "res://Enemy/ram/ramgirlframes special.tres" #RamGirl
const foxgirlsprites = "res://Enemy/shika/shikaframes.tres" #FoxGirl
const foxgirlspritesnocloth = "res://Enemy/shika/shikaframesnocloth.tres" #FoxGirl
const foxgirlspritesdick = "res://Enemy/shika/shikaframesdick.tres" #FoxGirl
const foxgirlspritesnoclothdick = "res://Enemy/shika/shikaframesnoclothdick.tres" #FoxGirl
const foxgirlspritestail = "res://Enemy/shika/shikaframestail.tres" #FoxGirl
const ghostfoxsprites = "res://Enemy/shika/ghostfoxframes.tres" #FoxGirl
const pitchspritesnotar = "res://Enemy/pitch/pitchframesnotar.tres" #WereWolf
const pitchsprites = "res://Enemy/pitch/pitchframes.tres" #WereWolf
const pitchspritestail = "res://Enemy/pitch/pitchframestail.tres" #WereWolf
const pitchattack = "res://Enemy/pitch/pitchattack.tscn" #WereWolf
const VoiceBedScene = "res://DialogueArt/CG/voicebed.tscn" #Gallery CG
const dragonharpysprites = "res://Enemy/dragonharpy/dragonharpyframes.tres" #DragonHarpy
const dragonharpyspritesstripped = "res://Enemy/dragonharpy/dragonharpyframesstripped.tres" #DragonHarpy
const elitedragonharpysprites = "res://Enemy/dragonharpy/elitedragonharpyframes.tres" #DragonHarpy
const elitedragonharpyspritesstripped = "res://Enemy/dragonharpy/elitedragonharpyframesstripped.tres" #DragonHarpy
const elitedragonharpyspritestail = "res://Enemy/dragonharpy/elitedragonharpyframestail.tres" #DragonHarpy

const impsprites_spade_clothes = "res://Enemy/imp/imp_spade_clothes.tres" #Imp
const impsprites_spade_bare = "res://Enemy/imp/imp_spade_bare.tres" #Imp
const impsprites_diamond_clothes = "res://Enemy/imp/imp_diamond_clothes.tres" #Imp
const impsprites_diamond_bare = "res://Enemy/imp/imp_diamond_bare.tres" #Imp
const impsprites_heart_clothes = "res://Enemy/imp/imp_heart_clothes.tres" #Imp
const impsprites_heart_bare = "res://Enemy/imp/imp_heart_bare.tres" #Imp
const impsprites_club_clothes = "res://Enemy/imp/imp_club_clothes.tres" #Imp
const impsprites_club_bare = "res://Enemy/imp/imp_club_bare.tres" #Imp
const impsprites_club_clothes_female = "res://Enemy/imp/imp_club_clothes_female.tres" #Imp
const impsprites_club_bare_female = "res://Enemy/imp/imp_club_bare_female.tres" #Imp

const touchcamera = "res://Assets/ui/touchcamera.png" #TouchScreen
const touchgear = "res://Assets/ui/touchgear.png" #TouchScreen
const touchplay = "res://Assets/ui/touchplay.png" #TouchScreen
const touchzoomin = "res://Assets/ui/touchzoomin.png" #TouchScreen
const touchzoomout = "res://Assets/ui/touchzoomout.png" #TouchScreen
const touchoverlay = "res://touchoverlay.tscn" #TouchScreen
const touchtrashsprite = "res://touchtrashsprite.tscn" #TouchScreen
const touchtrash = "res://Assets/ui/touchtrash.png" #TouchScreen
const touchcycle = "res://Assets/ui/touchtrash.png" #TouchScreen

const warning = "sfwwarning.tscn" #GUI
const sfwwarning = "res://Assets/sfwwarning.png" #GUI
const meatwarning = "res://Assets/ui/escalationcards/obtainedmeat.png" #GUI
const mapwarning = "res://Assets/ui/escalationcards/obtainedmap.png" #GUI
const nsfwwarning = "res://Assets/nsfwwarning.png" #GUI

#now for damagegraphstuff
#
#const hoofcon = "res://Assets/ui/debufficons/hoofcon.png" #MAINSCENE
#const flycon = "res://Assets/ui/debufficons/flycon.png" #MAINSCENE
#const convcon = "res://Assets/convoicon.png" #MAINSCENE

const big_debuff_border = "res://Assets/ui/debufficons/bigdebuffborder.png" #MAINSCENE
const big_debuff_border_fill = "res://Assets/ui/debufficons/bigdebuffborderfill.png" #MAINSCENE
#const debuffdn1 = "res://Assets/ui/debufficons/-1d.png" #GUI
#const debuffdn2 = "res://Assets/ui/debufficons/-2d.png" #GUI
#const debuffd1 = "res://Assets/ui/debufficons/1d.png" #GUI
#const debuffd2 = "res://Assets/ui/debufficons/2d.png" #GUI
#const debuffd3 = "res://Assets/ui/debufficons/3d.png" #GUI
#const debuffd4 = "res://Assets/ui/debufficons/4d.png" #GUI
#const debuffd5 = "res://Assets/ui/debufficons/5d.png" #GUI
#const debuffd6 = "res://Assets/ui/debufficons/6d.png" #GUI
#const debuffd7 = "res://Assets/ui/debufficons/7d.png" #GUI
#const debuffd8 = "res://Assets/ui/debufficons/8d.png" #GUI
#const debuffd9 = "res://Assets/ui/debufficons/9d.png" #GUI
#
#const debuffn1 = "res://Assets/ui/debufficons/-1.png" #GUI
#const debuffn2 = "res://Assets/ui/debufficons/-2.png" #GUI
#const debuff1 = "res://Assets/ui/debufficons/1.png" #GUI
#const debuff2 = "res://Assets/ui/debufficons/2.png" #GUI
#const debuff3 = "res://Assets/ui/debufficons/3.png" #GUI
#const debuff4 = "res://Assets/ui/debufficons/4.png" #GUI
#const debuff5 = "res://Assets/ui/debufficons/5.png" #GUI
#const debuff6 = "res://Assets/ui/debufficons/6.png" #GUI
#const debuff7 = "res://Assets/ui/debufficons/7.png" #GUI
#const debuff8 = "res://Assets/ui/debufficons/8.png" #GUI
#const debuff9 = "res://Assets/ui/debufficons/9.png" #GUI

const attackdir1 = "res://Assets/ui/attackdir1.png" #GUI
const attackdir2 = "res://Assets/ui/attackdir2.png" #GUI
const attackdir4 = "res://Assets/ui/attackdir4.png" #GUI
const attackdir5 = "res://Assets/ui/attackdir5.png" #GUI
const attackdir8 = "res://Assets/ui/attackdir8.png" #GUI

const noticeevent = "res://Assets/ui/Attackedtileevent.png" #GUI
const noticeattack = "res://Assets/ui/Attackedtile.png" #GUI
const noticeblock = "res://Assets/ui/Attackedtileblocked.png" #GUI

const damagepill = "res://Assets/ui/damagecount.png" #GUI
const damageicon0 = "res://Assets/ui/damageicons/0.png" #GUI
const damageicon1 = "res://Assets/ui/damageicons/1.png" #GUI
const damageicon2 = "res://Assets/ui/damageicons/2.png" #GUI
const damageicon3 = "res://Assets/ui/damageicons/3.png" #GUI
const damageicon4 = "res://Assets/ui/damageicons/4.png" #GUI
const damageicon5 = "res://Assets/ui/damageicons/5.png" #GUI
const damageicon6 = "res://Assets/ui/damageicons/6.png" #GUI
const directionauto = "res://Assets/ui/directionauto.png" #GUI
const directionthrough = "res://Assets/ui/directionthrough.png" #GUI
const direction0 = "res://Assets/ui/direction0.png" #GUI
const direction1 = "res://Assets/ui/direction1.png" #GUI
const direction2 = "res://Assets/ui/direction2.png" #GUI
const direction3 = "res://Assets/ui/direction3.png" #GUI
const blocktilenone = "res://Assets/ui/blocktilenone.png" #GUI
const blocktile0 = "res://Assets/ui/blocktile0.png" #GUI
const blocktile1 = "res://Assets/ui/blocktile1.png" #GUI
const blocktile2 = "res://Assets/ui/blocktile2.png" #GUI
const blocktile3 = "res://Assets/ui/blocktile3.png" #GUI
const blocktile4 = "res://Assets/ui/blocktile4.png" #GUI
const blocktile5 = "res://Assets/ui/blocktile5.png" #GUI
#const safem = "res://Assets/ui/debufficons/safem.png" #GUI
#const debuffnm1 = "res://Assets/ui/debufficons/-1m.png" #GUI
#const debuffnm2 = "res://Assets/ui/debufficons/-2m.png" #GUI
#const debuffm1 = "res://Assets/ui/debufficons/1m.png" #GUI
#const debuffm2 = "res://Assets/ui/debufficons/2m.png" #GUI
#const debuffm3 = "res://Assets/ui/debufficons/3m.png" #GUI
#const debuffm4 = "res://Assets/ui/debufficons/4m.png" #GUI
#const debuffm5 = "res://Assets/ui/debufficons/5m.png" #GUI
#const debuffm6 = "res://Assets/ui/debufficons/6m.png" #GUI
#const debuffm7 = "res://Assets/ui/debufficons/7m.png" #GUI
#const debuffm8 = "res://Assets/ui/debufficons/8m.png" #GUI
#const debuffm9 = "res://Assets/ui/debufficons/9m.png" #GUI

const caon = "res://Assets/ui/resource0.png" #GUI

#music
const monmusuforestop = "res://music/monmusu forest2op.ogg" #MainScene
const monmusuforestloop = "res://music/monmusu forest2loop.ogg" #MainScene
const mountainfox = "res://music/mountain fox.ogg" #foxgirl
const otherfox = "res://music/other fox.ogg" #foxgirl
const talkingremorse = "res://music/talkingremorse.ogg" #Conversation
const talkingsavvy = "res://music/talkingsavvy.ogg" #Conversation
const talkingharp = "res://music/rambadend1.ogg" #Conversation
const talkingpiano = "res://music/rambadend2.ogg" #Conversation
const movement_theme = "res://music/movement theme.ogg" #Mainscene
const monmusuforest = "res://music/monmusu forest.ogg" #Mainscene
const sunsetforest = "res://music/undecided project sunsetv2.ogg" #sunsetzone
const nightforest = "res://music/monmusu forestpitch.ogg" #nightzone
const hometheme = "res://music/I love our little hideawaysv2.ogg" #nightzone
const titletheme = "res://music/I love the morning rain.ogg" #nightzone

const SoundRepeater = "res://soundrepeater.tscn" #mainscene

const enemyreticule = "res://enemyreticule.tscn" #already contained within the main scene

const Unboxing = "res://effects/unboxing.tscn" #mainscene

#below here is for dialogart
const CorruptionParticlesSmall = "res://effects/corruption_particle_small.tscn" #MainScene
const CorruptionParticlesFinish = "res://effects/corruption_particle_finish.tscn" #MainScene
const Newdebufftile = "res://debufftilefixed.tscn" #GUI
const Energyorb = "res://energyorbeffect.tscn" #GUI
const Healthorb = "res://effects/healthorb.tscn" #GUI
const DebuffMeter = "res://debuffmeter.tscn" #GUI
const DebuffMeterDescription = "res://debuffmeterdescription.tscn" #GUI
const NewDebuffDescriber = "res://newdebuffdescriber.tscn" #GUI
const LockedResource = "res://Assets/ui/lockedresource.png" #MainScene
const LockedBoard = "res://Assets/ui/questboardsmalllocked.png" #MainScene
const Speechbubble = "res://Conversations/speechbubble.tscn" #GUI
const SpeechCatKnight = "res://Conversations/speechbubblerectCatKnight.png" #CatKnight
const SymbolCatKnight = "res://Conversations/symbolCatKnight.png" #CatKnight
const SpeechFoxGirl = "res://Conversations/speechbubblerectFoxGirl.png" #FoxGirl
const SymbolFoxGirl = "res://Conversations/symbolFoxGirl.png" #FoxGirl
const SpeechRamGirl = "res://Conversations/speechbubblerectRamGirl.png" #RamGirl
const SymbolRamGirl = "res://Conversations/symbolRamGirl.png" #RamGirl
const SpeechRules = "res://Conversations/speechbubblerectRules.png" #Rules
const SymbolRules = "res://Conversations/symbolRules.png" #Rules
const SpeechWereWolf = "res://Conversations/speechbubblerectWereWolf.png" #WereWolf
const SymbolWereWolf = "res://Conversations/symbolWereWolf.png" #WereWolf
const SpeechVoice = "res://Conversations/speechbubblerectVoice.png" #Voice
const SymbolVoice = "res://Conversations/symbolVoice.png" #Voice

const SymbolImpGirl = "res://Conversations/symbolImpGirl.png" #ImpGirl
const SpeechImpGirl = "res://Conversations/speechbubblerectImpGirl.png" #ImpGirl
const KnightShifter = "res://zapsplat/knightshifter.tscn" #ImpGirl

const SpeechDragonHarpy = "res://Conversations/speechbubblerectDragonHarpy.png" #DragonHarpy
const SymbolDragonHarpy = "res://Conversations/symbolDragonHarpy.png" #DragonHarpy


const SymbolVillagerShort = "res://Conversations/symbolVillager.png" #Villager
const SpeechVillagerShort = "res://Conversations/speechbubblerectVillager.png" #Villagers
const SymbolVillagerOnee = "res://Conversations/symbolVillager.png" #Villager
const SpeechVillagerOnee = "res://Conversations/speechbubblerectVillager.png" #Villagers
const SymbolVillagerBag = "res://Conversations/symbolVillager.png" #Villager
const SpeechVillagerBag = "res://Conversations/speechbubblerectVillager.png" #Villagers

const Pop = "res://alertlabel.tscn" #GUI
const Passiveshow = "res://passivedeploy.tscn" #GUI
const Backer = "res://backer.tscn" #GUI
const Deployables = "res://Deployable.tscn" #GUI
const Eventtext = "res://eventtext.tscn" #GUI
const EnemyArt = "res://enemyart.tscn" #GUI
const AnnounceAttack = "res://effects/announceattack2.tscn" #GUI

const Enemydebuff = "res://enemydebuff.tscn" #GUI
#const Enemytext = "res://Assets/ui/debufficons/enemytext.png" #GUI

const deployable_over_handsize = "res://Assets/materials/Deployable_over_handsize.tres" #MainScene

const SpriteVillagerBag = "res://DialogueArt/villagerbag.tscn"
const SpriteVillagerOnee = "res://DialogueArt/villageronee.tscn"
const SpriteVillagerShort = "res://DialogueArt/villagershort.tscn"
const SpriteDoppelganger = "res://DialogueArt/rulesart.tscn" #NA, just a pointer
const SpriteRules = "res://DialogueArt/rulesart.tscn" #Rules ; NOTE dialogue and colorinput currently has this by default
const SpritePitchTar = "res://Enemy/pitch/pitchtar.tscn" #WereWolf
const SpriteWereWolf = "res://DialogueArt/werewolfart.tscn" #WereWolf
const SpriteQades = "res://DialogueArt/qadesart.tscn" #Qades
const SpriteFoxGirl = "res://DialogueArt/shikaart.tscn" #FoxGirl
const SpriteRamGirl = "res://DialogueArt/ramgirlart.tscn" #RamGirl
const SpriteCatKnight = "res://DialogueArt/catknightart.tscn" #CatKnight
const SpriteVoice= "res://DialogueArt/voiceart.tscn" #Voice
const SpriteIdoll = "res://DialogueArt/idollart.tscn" #Idoll
const SpriteRulesMale = "res://DialogueArt/rulesmaleart.tscn" #RulesMale
const SpriteGhostFox = "res://DialogueArt/ghostfoxart.tscn" #FoxGirl
const SpriteDragonHarpy = "res://DialogueArt/dragonharpyart.tscn" #DragonHarpy
const SpriteImpGirl = "res://DialogueArt/impgirlart.tscn" #ImpGirl

const HarpyEgg = "res://Enemy/dragonharpy/eggr.png" #DragonHarpy

const baroverst = "res://Assets/ui/stonebarmiddle.png" #GUI
const barst = "res://Assets/ui/stonebarover.png" #GUI
const barunderst = "res://Assets/ui/stonebarunder.png" #GUI
const buttonst = "res://Assets/ui/monmusubuttonhalf.png" #GUI
const buttonsthover = "res://Assets/ui/monmusubuttonhalfhover.png" #GUI
const buttonsthoverpress = "res://Assets/ui/monmusubuttonhalfhoverpress.png" #GUI
const closest = "res://Assets/ui/monmusuclose.png" #GUI
const closesthover = "res://Assets/ui/monmusuclosehover.png" #GUI

#
##below here is maybe getting a bit excessive, and won't be called directly, it's experimental?
#const Lance = "res://Assets/abilityicons/Lance.png" #Deployables (Spec)
#const Piton = "res://Assets/abilityicons/Piton.png" #Deployables (Spec)
#const Torch = "res://Assets/abilityicons/Torch.png" #Deployables (Spec)

#and deployablestuff
const IconRun = "res://Assets/ui/deployableicons/run.png" #Deployables (Spec)
const IconFast = "res://Assets/ui/deployableicons/fast.png" #Deployables (Spec)
const IconHeavy = "res://Assets/ui/deployableicons/heavy.png" #Deployables (Spec)
const IconRecover = "res://Assets/ui/deployableicons/recover.png" #Deployables (Spec)
const IconUnrun = "res://Assets/ui/deployableicons/unrun.png" #Deployables (Spec)
const IconBlocku = "res://Assets/ui/deployableicons/blocku.png" #Deployables (Spec)
const IconBlock = "res://Assets/ui/deployableicons/block.png" #Deployables (Spec)
const IconRam = "res://Assets/ui/deployableicons/ram.png" #Deployables (Spec)
const IconDrainheal = "res://Assets/ui/deployableicons/drainheal.png" #Deployables (Spec)
const IconWindstep = "res://Assets/ui/deployableicons/windstep.png" #Deployables (Spec)
const IconEcho0 = "res://Assets/ui/deployableicons/echo0.png" #Deployables (Spec)
const IconEcho1 = "res://Assets/ui/deployableicons/echo1.png" #Deployables (Spec)
const IconEcho2 = "res://Assets/ui/deployableicons/echo2.png" #Deployables (Spec)
const IconEcho3 = "res://Assets/ui/deployableicons/echo3.png" #Deployables (Spec)
const IconEcho4 = "res://Assets/ui/deployableicons/echo4.png" #Deployables (Spec)
const IconEcho5 = "res://Assets/ui/deployableicons/echo5.png" #Deployables (Spec)
const IconEcho6 = "res://Assets/ui/deployableicons/echo6.png" #Deployables (Spec)

const SkillPreview = "res://skillpreview.tscn" #Deployables 

const number0 = "res://Assets/ui/0.png" #GUI
const number1 = "res://Assets/ui/1.png" #GUI
const number2 = "res://Assets/ui/2.png" #GUI
const number3 = "res://Assets/ui/3.png" #GUI
const number4 = "res://Assets/ui/4.png" #GUI
const number5 = "res://Assets/ui/5.png" #GUI
const number6 = "res://Assets/ui/6.png" #GUI
const number7 = "res://Assets/ui/7.png" #GUI
const number8 = "res://Assets/ui/8.png" #GUI
const number9 = "res://Assets/ui/9.png" #GUI


const DesertBackground = "res://Background/desertunderground static.png" #DESERTZONE
