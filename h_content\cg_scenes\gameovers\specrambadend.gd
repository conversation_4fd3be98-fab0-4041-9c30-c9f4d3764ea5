extends CanvasLayer

#const truebasesize = Vector2(1920,1080)
var basesize = Vector2(1860,1020)

const preload_mask_array = [preload("res://DialogueArt/CG/gameovers/testresspecram/rules_expression_angry_mask.png"),"res://DialogueArt/CG/gameovers/testresspecram/rules_expression_entranced_mask.png","res://DialogueArt/CG/gameovers/testresspecram/rules_expression_grimace_mask.png","res://DialogueArt/CG/gameovers/testresspecram/rules_expression_shock2_mask.png","res://DialogueArt/CG/gameovers/testresspecram/rules_expression_shock_mask.png","res://DialogueArt/CG/gameovers/testresspecram/rules_expression_weakened_mask.png","res://DialogueArt/CG/gameovers/testresspecram/rules_expression__mask.png"]

#OFFSETS:
#ENVIRONMENT---OFFSETS---
#Curtains have a -10Y offset because I can't allow bordered items to be polygon'd.
#CURTAINS: 1605x, -10y
#STOOL: 0x,860y
#WALLPROPS: 445x,65y
#TABLEPROPS: 410x, 440y
#TABLE MANTLE: 415x, 510y
#TABLE: 220x, 495y
#KETTLE: 1795x, 410y
#POKER: 1640x, 615y
#BOX: 1285x, 715y
#BOX UTENSILS: 1330x, 610y
#PANTIES: 525x, 1050y  + 433x, -87y, = 958x, 963 y

#RULES---OFFSETS---

#TORSO: 630x,710y
## TORSO DISPLACEMENT AFFECTS:
## BOOBS: 435x,810y = -195x,100y
## SHOULDER: 605x,725y = -25x, 15y
### SHOULDER AFFECTS:
### ARM: 615x,765y = 10x,40y
### ROPE:  660x,795y = 55x,70y
## SHOULDER_BACK: 765x,740y = 135x,30y
### SHOULDER BACK AFFECTS:
### ARM_BACK: 685x,775y = -80x,35y
### ROPE_BACK: 840x,825y = 75x,85y

# CLOTHES_TOP: 655x,935y
## CLOTHES TOP AFFECTS:
## CLOTHES_COLLAR: 655x,710y = -255y
## CLOTHES_GEM: 580x,1130y = 95x, 145y (custom: something else idk)
## CLOTHES_BACK: 750x,1080y = 95x,145y

# SWEAT: 520x,750y
# TREMBLES: 455x,405y

#HEAD/FACE/MOUTH: 575x,510y
## HEAD DISPLACEMENT AFFECTS:
## HORN_BACK: 740x,470y = 165x,-45y +5y
## EAR: 690x,625y = 115x,110y +5y
## GAG: 565x,685y = -10x,170y +5y
## GAG_STRAP: 580x,675y = 5x,160y +5y
## TEARS: 605x,655 = 30x,140y +5y
## EXPRESSION: 590x,555y = 15x,40y +5y
## SALIVA: 530x,720y = -45x,205y +5y
## EMOTION_EFFECT: 345x,365y -(575,515) = -230x,-150y +5y nooo -5y again forgot sparks
## HORN: 580x, 450y = 5x,-65y +5y

#HAIR: 550X,490Y
## HAIR DISPLACEMENT AFFECTS:
## HAIRPIN_BACK: 780x, 475y = 230x,-15y
## HAIRPIN: 640x,465y = 90x, -25y
## HAIR_DRILL: 515x,520y = -35x,30y
## HAIR_DRILL_BACK: 730x,530y = 180x,40y
## HAIR_BANGS: 585x,585y = 35x,95y

#LEGS: 515x, 970y
## LEGS DISPLACEMENT AFFECTS:
## ASS: 710x,905y - (515,970) = 195x, -65y
### ASS DISPLACEMENT is thus (710x, 905y)
### TAIL:830x,740y - 710,905 = 120x,-165y
### PUSSY and PUSSY_SPREAD: 970x,1050y = 260x,145y
### CUM: 820x,975y = 110x,70y
### SPANK and SPANK_BLUSH: 740x,935y = 30x,30y
### SPANK_EFFECT: 605x,840y = -105x,-65y
### SPANK_RIPPLE: 745x,975y = 35x,70y
### PANTIES: 775x,960y = 65x,55y
###PANTIES_DROPPED: 610x,1660y = -100x,755y

#RAMGIRL---OFFSETS---

# DEFAULT_AFTER_OFFSET | BACKING(no offset) | FORWARD(no offset) | DEFAULT(SIDE) | BACKING(SIDE) | FORWARD(SIDE)
# = MODIFIEDDEFAULT | MODIFIEDBACKING | MODIFIEDFORWARD

# TORSO: 870x,470y
## TORSO AFFECTS:
## BOOBS: 790x,625y|10x,47y|2x,-77y #ROT = -80x,155y|-70x,202y|78x,78y
## ARM_UP: 660x,265y|0x,0y|-19x,0y = -210x,-205y|-210x,-205y|-229x,-205y
## ARM_SPANK 760x,560y|-8x,54y|-38x,-18y = -110x,90y|-118x,144y|148x,72y
### ARM_SPANK AFFECTS:
### SPANK_EFFECT: 735x,705y = -25x,145y
## ARM_GRAB: 610x,430y = -260x,-40y
## SHOULDER_BACK: 1100x,550y|8x,12y|-11x,5y = 230x,80y|238x,92y|219x,85y
## ARM_BACK: 755x,475y + (8x,12y for some reason) = -107x,17y
### ARM_BACK AFFECTS:
### HAND_BACK: 740x,475y = -15x,0y
# CLOTHES_TOP: 715x,695y
## CLOTHES_TOP AFFECTS:
## CLOTHES_COLLAR:  985x,495y|-89x,239y|110x,-158y|0x,0y|-92x,239y|-45x,108y #ROT = 270x,-200y|252x,-140y|291x,-210y|270x,-200y|249x,-140y|264x,-180y
# FACE: 890x,270y|-1x,27y|48x,-68y|0x,0y|-18x,62y|105x,-159y #ROT = 890x,270y|889x,297y|917x,263y|890x,270y|872x,332y|942x,247y
## FACE AFFECTS:
## HORN_BACK: 1070x,245y|0x,0y|0x,0y|158x,-375y|157x,-375y|169x,-392y #ROT = 180x,-25y|180x,-25y|180x,-25y|189x,-49y|189x,-49y|190x,-50y
## HAIR: 855x,260y|0x,0y|50x,-125y|0x,0y|0x,0y|0x,0y #ROT = -35x,-10y|-35x,-10y|-17x,-47y|-35x,-10y|-35x,-10y|-35x,-10y
### HAIR AFFECTS:
### HAIR_AHOGE: 935x,150y|0x,0y|38x,11y|29x,-91y|29x,-90y|29x,-90y #ROT = 80x,-110y|80x,-110y|118x,-99y|93x,-120y|92x,-119y|92x,-119y
### HAIR_MAIN: 970x,330y|0x,0y|-29x,125y|0x,0y|0x,0y|-2x,-2y #ROT = 115x,70y|115x,70y|111x,110y|115x,70y|115x,70y|113x,72y
### HAIR_BACK 1140x,730y|0x,0y|-26x,150y|0x,0y|161x,-175y|190x,-201y #ROT = 285x,470y|285x,475y|340x,481y|285x,475y|333x,451y|346x,443y
### HAIR_BANGS: 905x,340y|0x,0y|-26x,149y|0x,0y|0x,0y|0x,0y #ROT = 50x,80y|50x,80y|55x,133y|50x,80y|50x,80y|50x,80y
### HAIR_BANGS_SMALL: 910x,380y|0x,0y|107x,-165y|-66x,258y|-67x,258y|-67x,258y #ROT = 55x,120y|55x,120y|73x,122y|60x,95y|59x,95y|59x,95y
## EAR: 845x,405y|1x,15y|-68x,180y|0x,0y|0x,0y|0x,0y #ROT = -45x,135y|-44x,150y|-56x,163y|-45x,135y|-45x,135y|-45x,135y
### EAR AFFECTS:
### EARRING: 870x,510y|0x,0y|1414x,-367y|222x,-34y|222x,-34y|221x,-35y #ROT = 25x,105y|25x,105y|59x,98y|247x,71y|265x,9y|246x,70y
## EAR_BACK: 1100x,410y|1x,15y|-5x,17y|-2x,-36y|-11x,-30y|-5x,17y = 210x,150y|211x,155y|205x,158y|208x,104y|199x,110y|207x,159y//adjusted fs to be 214x,114y
## EXPRESSION: 900x,355y = 10x,85y
## HORN: 835x,200y = -55x,-70y

# LEGS: 715x,975y
## LEGS AFFECTS:
## ASS: 835x,870y = 120x,-105y
### ASS AFFECTS:
### THRUST_EFFECT: 725x,845y = 605x,950y
### PANTIES: 875x,860y = 40x,-10y
### TAIL: 1005x,835y = 

var selfchristmas = false

var elite = false
var futa = false
func _ready():
	$sprites.position = Vector2(0,0)
	selfchristmas = Playervariables.christmas
	if Playervariables.gallery_mode == true:
		selfchristmas = false
#	for i in range($sprites/environment/curtains.get_bone_count()):
#		print($sprites/environment/curtains.get_bone_weights(i))
	var _c = get_tree().root.connect("size_changed", self, "_on_viewport_size_changed")
	_on_viewport_size_changed()
	set_hsv()
	engage_hover()
	rankcall(Playervariables.makeuprank)
	$sprites/environment/wallprops.visible = !selfchristmas
	$sprites/environment/christmas.visible = selfchristmas
	$sprites/ramgirl/face/hair/hat.visible = selfchristmas
	$sprites/environment/background/christmas_window.visible = selfchristmas
	$sprites/environment/box.visible = !selfchristmas
	$sprites/environment/stool.visible = !selfchristmas
	if Playervariables.prefdict[Playervariables.pref.FUTAENEMY] == false:
		$sprites/rules/legs/ass/cum.set_modulate(Color(0,0,0,0))
		$sprites/environment/juice2.pitch_scale = 1.4
		$sprites/environment/juice2.volume_db = -28
		futa = false
	else:
		futa = true
#	$sprites/ramgirl/legs/ass/panties.visible = !Playervariables.prefdict[Playervariables.pref.FUTAENEMY]#strapon. don't forget to remove cum if strapon
	
#	print("DEBUG RAMSCEN - use DEBUGEQUALS to progress. When you remove this, reorganise the unused SFX folders.")
#	action(0)

func mute(): #WARNING, THERE IS NOT YET ANY UN-MUTE FUNCTION IN CASE FULLSKIP IS INTERRUPTED!!
	for child in $sprites/environment.get_children():
		if child.get_class() == "AudioStreamPlayer":
#			child.pitch_scale = 2
			child.volume_db = -25

#var test_actions = 1
#func _input(event):
#	if event.is_action_pressed("debugequals"):
#		print($testdonotdelete.get_viewport_rect().size)
#		print($sprites.position)
#		print($sprites.scale)
#	if event.is_action_pressed("debugequals"):
#		action(int(test_actions))
#		test_actions += 1
#	if event.is_action_pressed("debug-"):
#		print("Stress tester time. I hope you like suffering.")
#		var new_scene = load("res://DialogueArt/CG/gameovers/specrambadend.tscn").instance()
#		add_child(new_scene)
#		$fadeshow.play("appear")
#	if event.is_action_pressed("debug-"):
##		move_to_location(Vector2(randf(),randf()))
#		current_zoom = 1+0.6*(randi()%2)
##		move_to_location(Vector2(randi()%2,randi()%2))
#		if current_zoom > 1:
#			move_to_location(Vector2(0.1,0.6))
#		else:
#			move_to_location(Vector2(0,0))

func rankcall(onoff):
	$sprites/ramgirl.use_parent_material = !onoff
	elite = onoff

func set_hsv():
#	var colourarray = Playervariables.baseplayercolourarray
#	print(colourarray)
#	var skincolHSV = Playervariables.newcolorarrayskin[colourarray[2]]
##	print(Playervariables.newcolorarrayskinnames[colourarray[2]])
#	$sprites/rules.material.set_shader_param("hue_shift",skincolHSV.x)
#	$sprites/rules.material.set_shader_param("sat_mul",skincolHSV.y+1)
#	$sprites/rules.material.set_shader_param("val_mul",skincolHSV.z+1)
	
	var hair_material = Playervariables.get_materials(Playervariables.pcol.HAIR)
#	var haircolHSV = Playervariables.newcolorarrayhair[colourarray[1]]
##	print(Playervariables.newcolorarrayhairnames[colourarray[1]])
	$sprites/rules/face/expression.material.set_shader_param("hair_hue_shift",hair_material.x)
	$sprites/rules/face/expression.material.set_shader_param("hair_sat_mul",hair_material.y-1)#special case since the shader is eye-like
	$sprites/rules/face/expression.material.set_shader_param("hair_val_mul",hair_material.z-1)#special - 1 case
	var eyecolHSV = Playervariables.get_materials(Playervariables.pcol.EYES)#var eyecolHSV = Playervariables.newcolorarrayeye[colourarray[0]]
#	print(Playervariables.newcolorarrayeyenames[colourarray[0]])
	$sprites/rules/face/expression.material.set_shader_param("hue_shift",eyecolHSV.x)
	$sprites/rules/face/expression.material.set_shader_param("sat_mul",eyecolHSV.y)
	$sprites/rules/face/expression.material.set_shader_param("val_mul",eyecolHSV.z)

#const ordinary_hover_offset = Vector2(-30,-30)
const hover_offset = Vector2(-30,-30)
var firstrun = true
var recentsizechange = false
func _on_viewport_size_changed():
	if recentsizechange == false:
		recentsizechange = true #this makes it so it only updates the viewport every 4 frames
		if firstrun == false:
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
#		var viewportrect = get_parent().get_viewport_rect().size
		engage_hover(true)
		recentsizechange = false
		firstrun = false

var saved_animation_point = 0.0
func engage_hover(from_viewport=false):
	var viewportrect = $testdonotdelete.get_viewport_rect().size
	var proportion = viewportrect/basesize
#	if from_viewport == false:
#		hover_offset = ordinary_hover_offset
#	elif hover_offset != ordinary_hover_offset:
#		proportion = viewportrect/truebasesize
	var maxproportion = max(proportion.x,proportion.y)
	$sprites.scale = Vector2(maxproportion,maxproportion)*current_zoom
	var newhoveranim
	var actual_hover_offset = hover_offset*proportion*current_zoom
	if $hover.is_playing() == true:
		saved_animation_point = $hover.current_animation_position
	if ($hover.is_playing() == true and $hover.get_current_animation() == "newhover") or from_viewport == false:
		
		if $hover.has_animation("newhover"):
			newhoveranim = $hover.get_animation("newhover")
		else:
			newhoveranim = $hover.get_animation("hover").duplicate()
#			newhoveranim.track_set_key_value(0,0,Vector2(0,0))
#			newhoveranim.track_set_key_value(0,1,Vector2(0,0))
#			newhoveranim.track_set_key_value(0,2,Vector2(0,0))
#			newhoveranim.track_set_key_value(0,3,Vector2(0,0))
#			newhoveranim.track_set_key_value(0,4,Vector2(0,0))
#		print("x greater than y:")
#		print(Vector2(0,0)+actual_hover_offset)
#		print(Vector2(proportion.x*30,viewportrect.y - (basesize.y * maxproportion))+actual_hover_offset)
#		print(Vector2(0,0)+actual_hover_offset)
#		print(Vector2(-proportion.x*30,viewportrect.y - (basesize.y * maxproportion))+actual_hover_offset)
#		print(Vector2(0,0)+actual_hover_offset)
#		print("y greater:")
#		print(Vector2(0,0)+actual_hover_offset)
#		print(Vector2(viewportrect.x - (basesize.x * maxproportion),proportion.y*30)+actual_hover_offset)
#		print(Vector2(0,0)+actual_hover_offset)
#		print(Vector2(viewportrect.x - (basesize.x * maxproportion),-proportion.y*30)+actual_hover_offset)
#		print(Vector2(0,0)+actual_hover_offset)
		if (proportion.x > proportion.y):#error in this case: cuts off extreme y
			newhoveranim.track_set_key_value(0,0,$sprites.position)#+actual_hover_offset)
			newhoveranim.track_set_key_value(0,1,Vector2(proportion.x*30,actual_hover_offset.y+viewportrect.y - (basesize.y * maxproportion))+actual_hover_offset)
			newhoveranim.track_set_key_value(0,2,Vector2(0,0))#+actual_hover_offset)
			newhoveranim.track_set_key_value(0,3,Vector2(-proportion.x*30,actual_hover_offset.y+viewportrect.y - (basesize.y * maxproportion))+actual_hover_offset)
			newhoveranim.track_set_key_value(0,4,Vector2(0,0))#+actual_hover_offset)
		else:#error in this case: cuts off extreme X
			newhoveranim.track_set_key_value(0,0,$sprites.position)#+actual_hover_offset)
			newhoveranim.track_set_key_value(0,1,Vector2(actual_hover_offset.x+viewportrect.x - (basesize.x * maxproportion),proportion.y*30)+actual_hover_offset)
			newhoveranim.track_set_key_value(0,2,Vector2(0,0))#+actual_hover_offset)
			newhoveranim.track_set_key_value(0,3,Vector2(actual_hover_offset.x+viewportrect.x - (basesize.x * maxproportion),-proportion.y*30)+actual_hover_offset)
			newhoveranim.track_set_key_value(0,4,Vector2(0,0))#actual_hover_offset)
#		$hover.playback_speed = 2 - ((maxproportion + 1) / (min(proportion.x,proportion.y)+1))
		var x = ((maxproportion+0.4) / (min(proportion.x,proportion.y)+0.4))
		$hover.playback_speed = (0.15 / (x*x))
		if $hover.has_animation("newhover") == false:
			$hover.add_animation("newhover",newhoveranim)
		$hover.stop()
		$hover.play("newhover")
		$hover.advance(saved_animation_point/$hover.playback_speed)
	elif $hover.is_playing() and $hover.get_current_animation() == "move_to_point":
		move_to_location(last_move_location,$hover.current_animation_position,true)
	elif from_viewport == true:
		move_to_location(last_move_location,0,true)

func pause_hover():
	saved_animation_point = $hover.current_animation_position
	$hover.stop(false)

#func engage_shake_rules(bigiftrue = true):
#	var viewportrect = $testdonotdelete.get_viewport_rect().size
#	var proportion = viewportrect/basesize
#	pass
#
#	var oldshakeanim
#	if bigiftrue == true:
#		oldshakeanim = $sprites/rules/AnimationPlayer.get_animation("shakerules")
#	else:
#		oldshakeanim = $sprites/rules/AnimationPlayer.get_animation("smallshakerules")
#	var newshakeanim
#	if $sprites/rules/AnimationPlayer.has_animation("newshake"):
#		newshakeanim = $sprites/rules/AnimationPlayer.get_animation("newshake")
#	else:
#		newshakeanim = $sprites/rules/AnimationPlayer.get_animation("shakerules").duplicate()
#
#	for i in range(5):
#		newshakeanim.track_set_key_value(0,i+1,oldshakeanim.track_get_key_value(0,i+1)*3*proportion*current_zoom)
#		newshakeanim.track_set_key_value(1,i+1,oldshakeanim.track_get_key_value(1,i+1)*3*proportion*current_zoom)
#
#	if $sprites/rules/AnimationPlayer.has_animation("newshake") == false:
#		$sprites/rules/AnimationPlayer.add_animation("newshake",newshakeanim)
#	$sprites/rules/AnimationPlayer.play("newshake")

func engage_shake():
#	hover_offset = ordinary_hover_offset
	var viewportrect = $testdonotdelete.get_viewport_rect().size
	var proportion = viewportrect/basesize
	var maxproportion = max(proportion.x,proportion.y)
#	var actual_hover_offset = hover_offset*proportion*current_zoom
	
	$sprites.scale = Vector2(maxproportion,maxproportion)
	var newshakeanim
#	if $hover.is_playing() == true:
#		saved_animation_point = $hover.current_animation_position
	var oldshakeanim = $hover.get_animation("shake")
	if $hover.has_animation("newshake"):
		newshakeanim = $hover.get_animation("newshake")
	else:
		newshakeanim = $hover.get_animation("shake").duplicate()
	var before_pos = $sprites.position
	for i in range(4):
		newshakeanim.track_set_key_value(0,i,oldshakeanim.track_get_key_value(0,i)*3*proportion*current_zoom + before_pos)#does not include actual_hover_effect because before_pos has it
	newshakeanim.track_set_key_value(0,4,hover_offset*proportion*current_zoom)#does not include actual_hover_effect because before_pos has it

	if $hover.has_animation("newshake") == false:
		$hover.add_animation("newshake",newshakeanim)
	$hover.stop()
	$hover.play("newshake")
	$hover.playback_speed = 2
#	$hover.advance(saved_animation_point/$hover.playback_speed)

var last_move_location = Vector2(0,0)
var current_zoom = 1.0
func move_to_location(percentage_vector = Vector2(0.5,0.5),advance_value = 0,straight = false):
	var viewportrect = $testdonotdelete.get_viewport_rect().size
	var proportion = viewportrect/basesize
#	if percentage_vector == Vector2(0.0,0.0):
#		hover_offset = Vector2(0,0)
#		proportion = viewportrect/truebasesize
#	else:
#		hover_offset = ordinary_hover_offset
	var use_anim = $hover.get_animation("move_to_point")
	var actual_hover_offset = hover_offset*proportion*current_zoom
	var maxproportion = max(proportion.x,proportion.y)
	var intended_vector = Vector2(0,0)
	var overshoot_vector = Vector2(0,0)
	var prevscale = $sprites.scale.x
	
	#NEW VERSION FROM FOXSCENE:
	if (proportion.x > proportion.y):
		intended_vector = Vector2(0,-((maxproportion*current_zoom) - proportion.y) * percentage_vector.y * basesize.y)# * current_zoom
		var direction = (intended_vector - $sprites.position).normalized()
		overshoot_vector = Vector2(30*proportion.x,-(maxproportion*current_zoom - proportion.y) * clamp(percentage_vector.y+direction.y*0.05,0,1) * basesize.y)# * current_zoom
		if current_zoom > 1.0:
			intended_vector.x = -(current_zoom-1.0) * percentage_vector.x * basesize.x * maxproportion
			direction = (intended_vector - $sprites.position).normalized()
			overshoot_vector.x = -(current_zoom-1.0) * clamp(percentage_vector.x+direction.x*0.05,0,1) * basesize.x * maxproportion
		elif randf() > 0.5:
			overshoot_vector.x = -overshoot_vector.x
	else:
		intended_vector = Vector2(-((maxproportion*current_zoom) - proportion.x) * percentage_vector.x * basesize.x,0)# * current_zoom
		var direction = (intended_vector - $sprites.position).normalized()
		overshoot_vector = Vector2(-(maxproportion*current_zoom - proportion.x) * clamp(percentage_vector.x+direction.x*0.05,0,1) * basesize.x,30*proportion.y)# * current_zoom
		if current_zoom > 1.0:
			intended_vector.y = -(current_zoom-1.0) * percentage_vector.y * basesize.y * maxproportion
			direction = (intended_vector - $sprites.position).normalized()
			overshoot_vector.y = -(current_zoom-1.0) * clamp(percentage_vector.y+direction.y*0.05,0,1) * basesize.y * maxproportion
		elif randf() > 0.5:
			overshoot_vector.y = -overshoot_vector.y
	#OLD VERSION: note I only left in lines about INTENDEDVECTOR because INTENDEDVECTOR is the only thing that changed
#	if (proportion.x > proportion.y):
#		intended_vector = Vector2(0,-(maxproportion - proportion.y) * percentage_vector.y * basesize.y) * current_zoom
#	else:
#		intended_vector = Vector2(-(maxproportion - proportion.x) * percentage_vector.x * basesize.x,0) * current_zoom
			
			
	use_anim.track_set_key_value(0,0,$sprites.position)
	if straight == false:
		use_anim.track_set_key_value(0,1,overshoot_vector+actual_hover_offset)
	else:
		use_anim.track_set_key_value(0,1,intended_vector+actual_hover_offset)
	use_anim.track_set_key_value(0,2,intended_vector+actual_hover_offset)#+actual_hover_offset)
	use_anim.track_set_key_value(1,0,Vector2(prevscale,prevscale))
	if straight == false:
		use_anim.track_set_key_value(1,1,Vector2(maxproportion,maxproportion)*current_zoom*1.15)
	else:
		use_anim.track_set_key_value(1,1,Vector2(maxproportion,maxproportion)*current_zoom)
	use_anim.track_set_key_value(1,2,Vector2(maxproportion,maxproportion)*current_zoom)
	if $fadeshow.is_playing() == true:
		$fadeshow.advance(4)
	use_anim.track_set_key_value(2,0,$sprites.get_modulate())
	if current_zoom > 1.0:
		use_anim.track_set_key_value(2,1,Color(0.7,0.7,0.7,1.0))
		use_anim.track_set_key_value(2,2,Color(0.85,0.85,0.85,1.0))
	else:
		use_anim.track_set_key_value(2,1,Color(0.85,0.85,0.85,1.0))
		use_anim.track_set_key_value(2,2,Color(1.0,1.0,1.0,1.0))
#	if percentage_vector == Vector2(0.0,0.0):
#		use_anim.track_set_key_value(0,3,Vector2(0,0))
#		var trueproportion = viewportrect/truebasesize
#		var truemaxproportion = max(trueproportion.x,trueproportion.y)
#		use_anim.track_set_key_value(1,3,Vector2(truemaxproportion,truemaxproportion)*current_zoom)
#	else:
#		use_anim.track_set_key_value(0,3,use_anim.track_get_key_value(0,2))
#		use_anim.track_set_key_value(1,3,use_anim.track_get_key_value(1,2))
	$hover.playback_speed = 0.6
	$hover.stop()
	$hover.play("move_to_point")
	$hover.advance(advance_value/$hover.playback_speed)
	last_move_location = percentage_vector

func fix_zoom(alt = false):
	if current_zoom > 1.0:
		current_zoom = 1.0
		if alt == false:
			move_to_location(Vector2(0,0))
		else:
			move_to_location(Vector2(1,1))

var cum_level = 0
var queued_action = -1
func action(num = 0,queued = false):
	if queued_action > -1:
		var save_action = queued_action
		queued_action = -1
		action(save_action,true)
	match num:
		-1: pass
		0:
			$sprites/Skeleton2D/skeleton_handler.play("RESET")
			fix_zoom()
			$fadeshow.play("RESET")
			$fadeshow.advance(10)
			$fadeshow.play("appear environment")
			engage_hover()
			$sprites/environment/background/dust_handler.play("dust")
		1:
			fix_zoom()
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$fadeshow.advance(12)
			$fadeshow.play("appear rules")
		2:
			move_to_location(Vector2(0.5,0.5),0,true)
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$fadeshow.advance(12)
			$fadeshow.play("appear ram")
		3:
			fix_zoom()
			$fadeshow.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.play("shock")
			
		4:
			fix_zoom()
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.play("before_preparation")
		5:
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			if futa == true:
				$sprites/Skeleton2D/skeleton_handler.play("preparation_futa")
			else:
				$sprites/Skeleton2D/skeleton_handler.play("preparation_nofuta")
		6:
			#this is a point where you can go back to. Make sure everything is repeatable from here!
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$sprites/environment/background/dust_handler.play("dust")
			$sprites/environment/environment_shake.play("RESET")
#			$fadeshow.play("RESET")
			$fadeshow.advance(10)
			$fadeshow.play("appear ram")
			$fadeshow.advance(20)
			fix_zoom()
			$sprites/Skeleton2D/skeleton_handler.play("RESET")
			$sprites/Skeleton2D/skeleton_handler.advance(10)
			$sprites/Skeleton2D/skeleton_handler.play("before_remove_panties")
		7:
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.play("light thrust")
#			current_zoom = 1.2
		8:
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.play("remove panties")
#			current_zoom = 1.0
#			move_to_location(Vector2(0.8,0.2),0,true)
		9: #fully taken in
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.play("light thrust phase 2")
#			current_zoom = 1.2
#			if queued == false:
#				queued_action = num
#				move_to_location(Vector2(0.7,0.4))
#				return
		10: #once again relaxing
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.play("pause")
#			fix_zoom(true)
		11: #oxygen
			if queued == false:
				queued_action = num
				current_zoom = 1.2
				move_to_location(Vector2(0.5,0.2))
				return
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.play("pauseconfusion")
#			engage_hover()
		12:
			move_to_location(Vector2(0.7,0.4),0,true)
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.play("pauseslap")
		13:
			fix_zoom()
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.play("insertion")
		14:
			engage_hover()
#			current_zoom = 1.1
#			move_to_location(Vector2(0.55,0.4),0,true)
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.play("medium thrust")
		15:
#			current_zoom = 1.2
#			move_to_location(Vector2(0.55,0.4),0,true)
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.play("medium thrust phase 2")
			$spank_handler.play("3")
		16:
#			current_zoom = 1.4
#			move_to_location(Vector2(0.55,0.4),0,true)
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.play("hard thrust")
			$sprites/environment/background/dust_handler.play("intensedust")
		17:
			current_zoom = 1.1
			move_to_location(Vector2(0.7,0.3),0,true)
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.play("hard thrust pause")
		18:
#			if queued == false:
#				queued_action = num
#				current_zoom = 1.2
#				move_to_location(Vector2(0.5,0.5))
#				return
			current_zoom = 1.2
			move_to_location(Vector2(0.4,0.3),0,true)
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.play("hard thrust pause phase 2")
		19:
			current_zoom = 1.3
			move_to_location(Vector2(0.6,0.55),0,true)
#			fix_zoom()
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$spank_handler.play("4")
			$sprites/Skeleton2D/skeleton_handler.play("hard thrust pause phase 3")
		20:
			if queued == false:
				queued_action = num
				current_zoom = 1.4
				move_to_location(Vector2(0.55,0.4))
				return
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$spank_handler.play("4")
			$sprites/Skeleton2D/skeleton_handler.play("hard thrust round 2")
		21:
			fix_zoom()
#			if queued == false:
#				queued_action = num
#				current_zoom = 1.0
#				move_to_location(Vector2(0,0),0,true)
#				return
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.play("finish_one")
			cum_level = 1
		22:
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
#			$fadeshow.play("RESET")
			$fadeshow.advance(10)
			$fadeshow.play("appear ram")
			$fadeshow.advance(20)
			fix_zoom()
			match cum_level:
				4:
					$sprites/Skeleton2D/skeleton_handler.play("finish_three")
					cum_level = 3
				3:
					$sprites/Skeleton2D/skeleton_handler.play("finish_two")
					cum_level = 2
				2:
					$sprites/Skeleton2D/skeleton_handler.play("finish_one")
					cum_level = 1
				_:
					$sprites/Skeleton2D/skeleton_handler.play("finish_one")
					cum_level = 1
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.play("prepare_for_second")
#			fix_zoom()
#			$spank_handler.advance(12)
#			$sprites/Skeleton2D/skeleton_handler.advance(12)
#			$sprites/Skeleton2D/skeleton_handler.play("prepare_for_second")
		23:
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.play("hardest thrust v3")
		24:
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			match cum_level:
				3,4:
					$sprites/Skeleton2D/skeleton_handler.play("finish_four")
					cum_level = 4
				2:
					$sprites/Skeleton2D/skeleton_handler.play("finish_three")
					cum_level = 3
				_:
					$sprites/Skeleton2D/skeleton_handler.play("finish_two")
					cum_level = 2
		25:
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.play("soft plead")
		26:
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.play("plead")
		27:
			$fadeshow.play("appear ram")
			$fadeshow.advance(20)
			fix_zoom()
			match cum_level:
				4:
					$sprites/Skeleton2D/skeleton_handler.play("finish_three")
					cum_level = 3
				3:
					$sprites/Skeleton2D/skeleton_handler.play("finish_two")
					cum_level = 2
				2:
					$sprites/Skeleton2D/skeleton_handler.play("finish_one")
					cum_level = 1
				_:
					$sprites/Skeleton2D/skeleton_handler.play("finish_one")
					cum_level = 1
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.play("plead")
		50:engage_hover()
		80:
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
#			$fadeshow.play("RESET")
			$fadeshow.advance(10)
			$fadeshow.play("appear ram")
			$fadeshow.advance(20)
			fix_zoom()
			$sprites/Skeleton2D/skeleton_handler.play("finish_one")
			cum_level = 1
#		81:
#			$spank_handler.advance(12)
#			$sprites/Skeleton2D/skeleton_handler.advance(12)
#			$fadeshow.play("RESET")
#			$fadeshow.advance(10)
#			$fadeshow.play("appear ram")
#			$fadeshow.advance(20)
#			fix_zoom()
#			match cum_level:
#				4:
#					$sprites/Skeleton2D/skeleton_handler.play("finish_three")
#					cum_level = 3
#				3:
#					$sprites/Skeleton2D/skeleton_handler.play("finish_two")
#					cum_level = 2
#				2:
#					$sprites/Skeleton2D/skeleton_handler.play("finish_one")
#					cum_level = 1
#				_:
#					$sprites/Skeleton2D/skeleton_handler.play("finish_one")
#					cum_level = 1
#			$spank_handler.advance(12)
#			$sprites/Skeleton2D/skeleton_handler.advance(12)
#			$sprites/Skeleton2D/skeleton_handler.play("prepare_for_second")
		98:
			$fadeshow.advance(10)
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			fix_zoom(true)
			$hover.advance(4)
			$hover.stop(false)
			$fadeshow.play("unload ram")
		99: #unload screen
			$fadeshow.advance(10)
			$spank_handler.advance(12)
			$sprites/Skeleton2D/skeleton_handler.advance(12)
			$fadeshow.advance(12)
			fix_zoom(true)
			$hover.advance(4)
			$hover.stop(false)
			$fadeshow.play("unload scene")
#			$fadeshow.play_backwards("appear")
		
#		30:$sprites.modulate = Color(1,1,1,0)
		
#		32:engage_hover()
#		33:engage_shake()
#		60:engage_shake_rules(true)
#		61:engage_shake_rules(false)


func _on_hover_animation_finished(anim_name):
	if anim_name == "newhover":
		$hover.get_animation("newhover").track_set_key_value(0,0,$sprites.position)
		$hover.play("newhover")
#	if anim_name == "move_to_point" and last_move_location == Vector2(0,0):
#		saved_animation_point = 0
#		engage_hover()
	

func _process(_delta):
	$sprites.position = $sprites.position.round()
#	for node in $sprites.get_children():
#		node.position = node.position.round()#Vector2(floor(node.position.x),floor(node.position.y))

