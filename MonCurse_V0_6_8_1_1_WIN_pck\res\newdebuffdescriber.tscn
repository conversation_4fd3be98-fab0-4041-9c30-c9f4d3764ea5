[gd_scene load_steps=35 format=2]

[ext_resource path="res://font/Verily-Serif-Mono/VerilySerifMono.otf" type="DynamicFontData" id=1]
[ext_resource path="res://Assets/ui/stonebarover.png" type="Texture" id=2]
[ext_resource path="res://Assets/ui/questboardsmall.png" type="Texture" id=3]
[ext_resource path="res://Assets/ui/debufficons/debuffborder.png" type="Texture" id=4]
[ext_resource path="res://Assets/blacksquare.png" type="Texture" id=5]
[ext_resource path="res://font/liberation-mono/LiberationMono-Bold.ttf" type="DynamicFontData" id=6]
[ext_resource path="res://Assets/ui/shardresource.png" type="Texture" id=7]
[ext_resource path="res://shoptab.gd" type="Script" id=8]
[ext_resource path="res://effects/healthparticle.png" type="Texture" id=9]
[ext_resource path="res://effects/corruptionsmoke.png" type="Texture" id=10]
[ext_resource path="res://Assets/ui/colorpicker.png" type="Texture" id=11]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_underwater_bubble_wet_collect_item_earn_point_bonus_001_78395.ogg" type="AudioStream" id=12]
[ext_resource path="res://Assets/ui/debufficons/5d.png" type="Texture" id=13]

[sub_resource type="DynamicFont" id=1]
size = 13
outline_size = 2
outline_color = Color( 0, 0, 0, 0.705882 )
font_data = ExtResource( 1 )

[sub_resource type="DynamicFont" id=2]
outline_size = 2
outline_color = Color( 0, 0, 0, 0.705882 )
font_data = ExtResource( 1 )

[sub_resource type="DynamicFont" id=3]
size = 32
outline_size = 3
outline_color = Color( 0, 0, 0, 1 )
font_data = ExtResource( 6 )

[sub_resource type="Animation" id=5]
resource_name = "disable"
length = 0.01
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("TextureButton:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("TextureButton/Label:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("Particles2D:emitting")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("TextureProgress:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}

[sub_resource type="Animation" id=4]
resource_name = "enable"
length = 1.6
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.8, 1.6 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.54902 ), Color( 0, 0, 1, 0.823529 ), Color( 1, 1, 1, 0.54902 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("TextureButton:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.8, 1.6 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.509804 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0.509804 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("TextureButton/Label:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("Particles2D:emitting")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("TextureProgress:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}

[sub_resource type="Animation" id=6]
resource_name = "invalid"
length = 0.01
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 0, 0, 0.333333 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("TextureButton:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 0.392157, 0.392157, 0.784314 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("TextureButton/Label:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("Particles2D:emitting")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("TextureProgress:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}

[sub_resource type="Animation" id=12]
resource_name = "removing"
length = 2.3
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1, 2.3 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.54902 ), Color( 1, 0, 0, 0.823529 ), Color( 1, 1, 1, 0.54902 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("TextureButton:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1, 2.3 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.509804 ), Color( 1, 0.666667, 0.666667, 0.509804 ), Color( 1, 1, 1, 0.196078 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("TextureButton/Label:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("Particles2D:emitting")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("TextureProgress:value")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 2.3 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ 0.0, 100.0 ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("TextureProgress:visible")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}

[sub_resource type="Gradient" id=7]
colors = PoolColorArray( 1, 1, 1, 1, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=8]
gradient = SubResource( 7 )

[sub_resource type="Curve" id=9]
_data = [ Vector2( 0, 0.378409 ), 0.0, 0.0, 0, 0, Vector2( 0.505814, 1 ), 0.0, 0.0, 0, 0, Vector2( 1, 0.405682 ), 0.0, 0.0, 0, 0 ]

[sub_resource type="CurveTexture" id=10]
curve = SubResource( 9 )

[sub_resource type="ParticlesMaterial" id=11]
emission_shape = 2
emission_box_extents = Vector3( 150, 50, 1 )
flag_disable_z = true
gravity = Vector3( 0, -200, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale_curve = SubResource( 10 )
color = Color( 0.54902, 0.898039, 1, 1 )
color_ramp = SubResource( 8 )

[sub_resource type="AtlasTexture" id=13]
atlas = ExtResource( 10 )
region = Rect2( 0, 0, 256, 256 )

[sub_resource type="AtlasTexture" id=14]
atlas = ExtResource( 10 )
region = Rect2( 256, 0, 256, 256 )

[sub_resource type="AtlasTexture" id=15]
atlas = ExtResource( 10 )
region = Rect2( 512, 0, 256, 256 )

[sub_resource type="AtlasTexture" id=16]
atlas = ExtResource( 10 )
region = Rect2( 768, 0, 256, 256 )

[sub_resource type="AtlasTexture" id=17]
atlas = ExtResource( 10 )
region = Rect2( 0, 256, 256, 256 )

[sub_resource type="AtlasTexture" id=18]
atlas = ExtResource( 10 )
region = Rect2( 256, 256, 256, 256 )

[sub_resource type="AtlasTexture" id=19]
atlas = ExtResource( 10 )
region = Rect2( 512, 256, 256, 256 )

[sub_resource type="AtlasTexture" id=20]
atlas = ExtResource( 10 )
region = Rect2( 768, 256, 256, 256 )

[sub_resource type="SpriteFrames" id=21]
animations = [ {
"frames": [ SubResource( 13 ), SubResource( 14 ), SubResource( 15 ), SubResource( 16 ), SubResource( 17 ), SubResource( 18 ), SubResource( 19 ), SubResource( 20 ), null ],
"loop": false,
"name": "smoke",
"speed": 14.0
} ]

[node name="Control" type="Control"]
anchor_right = 0.3
anchor_bottom = 0.2
script = ExtResource( 8 )

[node name="Board" type="NinePatchRect" parent="."]
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = 8.0
margin_top = 8.0
margin_right = -8.0
margin_bottom = -8.0
texture = ExtResource( 3 )
patch_margin_left = 64
patch_margin_top = 40
patch_margin_right = 64
patch_margin_bottom = 40
axis_stretch_horizontal = 2
axis_stretch_vertical = 2

[node name="Info" type="Label" parent="Board"]
anchor_left = 0.3
anchor_top = 0.25
anchor_right = 0.95
anchor_bottom = 0.9
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 0.862745, 0.784314, 0.941176, 1 )
custom_fonts/font = SubResource( 1 )
text = "Knowledge and movement are that of the Kitsune's."
align = 1
valign = 1
autowrap = true

[node name="TitleBar" type="TextureRect" parent="Board"]
anchor_left = 0.2
anchor_top = 0.04
anchor_right = 0.8
anchor_bottom = 0.18
texture = ExtResource( 2 )
expand = true

[node name="Title" type="Label" parent="Board/TitleBar"]
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 0.941176, 0.588235, 0.588235, 1 )
custom_fonts/font = SubResource( 2 )
text = "Status: Milk"
align = 1
valign = 1

[node name="Icon" type="TextureRect" parent="Board"]
anchor_left = 0.05
anchor_top = 0.2
anchor_right = 0.3
anchor_bottom = 0.9
texture = ExtResource( 13 )
expand = true
stretch_mode = 6

[node name="border" type="TextureRect" parent="Board/Icon"]
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource( 4 )
expand = true

[node name="shoptab" type="TextureRect" parent="."]
self_modulate = Color( 1, 1, 1, 0 )
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = 14.0
margin_top = 14.0
margin_right = -14.0
margin_bottom = -14.0
texture = ExtResource( 5 )
expand = true

[node name="TextureProgress" type="TextureProgress" parent="shoptab"]
visible = false
modulate = Color( 0, 0.745098, 1, 1 )
anchor_right = 1.0
anchor_bottom = 1.0
texture_progress = ExtResource( 11 )
fill_mode = 5
nine_patch_stretch = true

[node name="TextureButton" type="TextureButton" parent="shoptab"]
self_modulate = Color( 1, 1, 1, 0 )
anchor_left = 0.1
anchor_right = 0.9
anchor_bottom = 1.0
mouse_filter = 1
action_mode = 0
texture_normal = ExtResource( 7 )
expand = true
stretch_mode = 5

[node name="Label" type="Label" parent="shoptab/TextureButton"]
visible = false
anchor_right = 1.0
anchor_bottom = 1.0
custom_colors/font_color = Color( 0.870588, 0.439216, 0.537255, 1 )
custom_fonts/font = SubResource( 3 )
text = "-1"
align = 1
valign = 1

[node name="AnimationPlayer" type="AnimationPlayer" parent="shoptab"]
anims/disable = SubResource( 5 )
anims/enable = SubResource( 4 )
anims/invalid = SubResource( 6 )
anims/removing = SubResource( 12 )

[node name="Particles2D" type="Particles2D" parent="shoptab"]
position = Vector2( 150, 50 )
z_index = 5
emitting = false
process_material = SubResource( 11 )
texture = ExtResource( 9 )

[node name="AudioStreamPlayer" type="AudioStreamPlayer" parent="."]
stream = ExtResource( 12 )
volume_db = -7.0
pitch_scale = 0.5
bus = "SFX"

[node name="center" type="Control" parent="."]
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="disappear" type="AnimatedSprite" parent="center"]
visible = false
z_index = 5
frames = SubResource( 21 )
animation = "smoke"

[connection signal="mouse_entered" from="shoptab" to="." method="_on_shoptab_mouse_entered"]
[connection signal="mouse_exited" from="shoptab" to="." method="_on_shoptab_mouse_exited"]
[connection signal="button_up" from="shoptab/TextureButton" to="." method="_on_TextureButton_button_up"]
[connection signal="pressed" from="shoptab/TextureButton" to="." method="_on_TextureButton_pressed"]
[connection signal="animation_finished" from="shoptab/AnimationPlayer" to="." method="_on_AnimationPlayer_animation_finished"]
