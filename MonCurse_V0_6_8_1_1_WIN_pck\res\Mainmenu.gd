extends Control


# Declare member variables here. Examples:
# var a = 2
# var b = "text"

var credits_num = -1
var playback_modifier = 1.0
const credits = PoolStringArray(["A game by <PERSON><PERSON><PERSON>","Guest Artists:\nAnchors (<PERSON> end)\nD<PERSON>lad<PERSON> (Parish villagers)\nD_Floe (Webpage art)","Guest Artists:\n<PERSON><PERSON>uru (Ram end)\n<PERSON><PERSON> (Fox Scene)","SFX:\nZapSplat (zapsplat.com)","Special thanks to:\nThe translators!\nPatreon supporters!\nSubscribestar supporters!","Extra thanks to:\nArtists who've drawn pieces of the tileset!\nArtists who've drawn fanart!"])
func next_credits():
	credits_num = (credits_num +1)%credits.size()
	if credits_num >= 0 and credits_num <= credits.size():
		$logo/credits.set_text(credits[credits_num])
		playback_modifier = sqrt(((float(credits[credits_num].length())+15.0) / 60.0))
		if playback_modifier > 0:
			$logo/credits/creditsanim.playback_speed = 1/playback_modifier

var savedeleting = false
var hoveredoversave = false
func _on_saveremover_pressed():
	if $Settings.visible == false:
		if Playervariables.touchscreenmode == false:
			if donotpickhammerbackup == false:
				donotpickhammerbackup = true
				hoveredoversave = false
				savedeleting = true
				veilstate = veilDELETE
				$saveremover.set_modulate(Color(0.5,0.5,0.5,0.6))
				Input.set_custom_mouse_cursor(load("res://Assets/ui/mainmenu/saveremovercursor.png"))
				$TextureRect2/start.disabled = true
				$TextureRect2/setting.disabled = true
				$TextureRect2/exit.disabled = true
				$fullscreenbutton.disabled = true
				$translationbutton.disabled = true
				$saveremover.disabled = true
				$shilling/DiscordButton/DiscordInvis.disabled = true
				$shilling/subpatbuttons/PatreonButton/PatreonInvis.disabled = true
				$shilling/subpatbuttons/SubscribestarButton/SubscribestarInvis.disabled = true
				$logo.disabled = true
				$shilling.visible = false
				$logo.visible = false
		else:
			veilstate = veilDELETE
			if Playervariables.newsave == true:
				$veil/Label.set_text(Playervariables.veiltextdict["noreset"].replace("VAR1",Playervariables.currentsavenum))#"You cannot reset the save slot ("+str(Playervariables.currentsavenum)+ ") as it has no data.")
				$veil/veilbutton2.visible = true
				$veil/veilbutton1.visible = false
			else:
				$veil/Label.set_text(Playervariables.veiltextdict["touchreset"].replace("VAR1",Playervariables.currentsavenum))#"You are about to reset the save slot ("+str(Playervariables.currentsavenum)+ ") to the start. Continue?\nPress 'Yes' 3 times to confirm.")
				$veil/veilbutton2.visible = true
				$veil/veilbutton1.visible = true
			$veil.visible = true

# Called when the node enters the scene tree for the first time.
func _ready():
#	Playervariables.call("resetvariables")
	if Playervariables.removesex == false:
		$shilling/shillinganim.play("shilling")
	if Playervariables.christmas == true:
		$background/monmusuforest.queue_free()
		$background.add_child(load("res://DialogueArt/CG/monmusuforestsnow.tscn").instance())
	_fullscreencheckboxcheck()
	add_to_group("fullscreencheck")
	var _c = get_tree().root.connect("size_changed", self, "_on_viewport_size_changed")
	$veil.visible = false
#	$VBoxContainer2/HSlider.value = Playervariables.baseplayercolourarray[1]
#	$VBoxContainer2/HSlider2.value = Playervariables.baseplayercolourarray[0]
#	$VBoxContainer2/Label.set_text("MC hair color: "+Playervariables.baseplayercolourdescriptiondict.get(1)[Playervariables.baseplayercolourarray[1]])
#	$VBoxContainer2/Label2.set_text("MC eye color: "+Playervariables.baseplayercolourdescriptiondict.get(0)[Playervariables.baseplayercolourarray[0]])
#	$VBoxContainer2/Label3.set_text("MC name: "+Playervariables.playername)
#	$TextureRect2/Label.set_text(str(Playervariables.currentsavenum))
#	if Playervariables.firstload == true:
#		add_child(load("res://disclaimermenu.tscn").instance())
#	$Title.rect_pivot_offset = $Title.rect_size/2
#	_on_viewport_size_changed()
#	yield(get_tree(),"idle_frame")
#	if Playervariables.modarray[Playervariables.mods.TRANSLATION] == true and Playervariables.transdict.has("mainmenu") == true:
#		if Playervariables.default_font_dict["font_attack_record"] != null:
##			$progressdata.set("custom_fonts/font", Playervariables.default_font_dict["font_attack_record"])
#			$progressdata.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_record"].font_data
##			$veil/veilbutton2/Label.set("custom_fonts/font", Playervariables.default_font_dict["font_attack_record"])
#			$veil/veilbutton2/Label.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_record"].font_data
##			$veil/Label.set("custom_fonts/font", Playervariables.default_font_dict["font_attack_record"])
#			$veil/Label.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_record"].font_data
#			$savename.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_record"].font_data
##			$savename.set("custom_fonts/font", Playervariables.default_font_dict["font_attack_record"])
##			$debugbutton/Label.set("custom_fonts/font", Playervariables.default_font_dict["font_attack_record"])
#			$debugbutton/Label.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_record"].font_data
#			$shilling/DiscordButton/Label.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_record"].font_data
#		if Playervariables.transdict["mainmenu"].has("patreonbutton"):
#			$shilling/subpatbuttons/supportlabel.set_text(Playervariables.transdict["mainmenu"]["patreonbutton"])
#		if Playervariables.transdict["mainmenu"].has("discordbutton"):
#			$shilling/DiscordButton/Label.set_text(Playervariables.transdict["mainmenu"]["discordbutton"])
#		if Playervariables.transdict["mainmenu"].has("yes"):
#			$veil/veilbutton1/Label.set_text(Playervariables.transdict["mainmenu"]["yes"])
#		if Playervariables.transdict["mainmenu"].has("cancel"):
#			$veil/veilbutton2/Label.set_text(Playervariables.transdict["mainmenu"]["cancel"])
	_on_viewport_size_changed()
#	yield(get_tree(),"idle_frame")
	# warning-ignore:return_value_discarded
#	pass # Replace with function body.

func translate_main_menu(reset = false,quick_purge = false):
	if reset == true:
		var data = DynamicFontData.new()
		data.font_path = "res://font/Audiowide-Regular.ttf"
		$progressdata.get("custom_fonts/font").font_data = data
		$veil/veilbutton2/Label.get("custom_fonts/font").font_data = data
		$veil/Label.get("custom_fonts/font").font_data = data
		$savename.get("custom_fonts/font").font_data = data
		$debugbutton/Label.get("custom_fonts/font").font_data = data
		$shilling/DiscordButton/Label.get("custom_fonts/font").font_data = data
		if Playervariables.transdict.has("mainmenu"):
			if Playervariables.transdict["mainmenu"].has("patreonbutton"):
				$shilling/subpatbuttons/supportlabel.set_text("Support \nthe Game")
			if Playervariables.transdict["mainmenu"].has("discordbutton"):
				$shilling/DiscordButton/Label.set_text("Join the\n Community")
			if Playervariables.transdict["mainmenu"].has("yes"):
				$veil/veilbutton1/Label.set_text("Yes")
			if Playervariables.transdict["mainmenu"].has("cancel"):
				$veil/veilbutton2/Label.set_text("Cancel")
	elif Playervariables.modarray[Playervariables.mods.TRANSLATION] == true and Playervariables.transdict.has("mainmenu") == true:
		if Playervariables.default_font_dict["font_attack_record"] != null:
#			$progressdata.set("custom_fonts/font", Playervariables.default_font_dict["font_attack_record"])
			$progressdata.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_record"].font_data
#			$veil/veilbutton2/Label.set("custom_fonts/font", Playervariables.default_font_dict["font_attack_record"])
			$veil/veilbutton2/Label.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_record"].font_data
#			$veil/Label.set("custom_fonts/font", Playervariables.default_font_dict["font_attack_record"])
			$veil/Label.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_record"].font_data
			$savename.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_record"].font_data
#			$savename.set("custom_fonts/font", Playervariables.default_font_dict["font_attack_record"])
#			$debugbutton/Label.set("custom_fonts/font", Playervariables.default_font_dict["font_attack_record"])
			$debugbutton/Label.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_record"].font_data
			$shilling/DiscordButton/Label.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_record"].font_data
		if Playervariables.transdict["mainmenu"].has("patreonbutton"):
			$shilling/subpatbuttons/supportlabel.set_text(Playervariables.transdict["mainmenu"]["patreonbutton"])
		if Playervariables.transdict["mainmenu"].has("discordbutton"):
			$shilling/DiscordButton/Label.set_text(Playervariables.transdict["mainmenu"]["discordbutton"])
		if Playervariables.transdict["mainmenu"].has("yes"):
			$veil/veilbutton1/Label.set_text(Playervariables.transdict["mainmenu"]["yes"])
		if Playervariables.transdict["mainmenu"].has("cancel"):
			$veil/veilbutton2/Label.set_text(Playervariables.transdict["mainmenu"]["cancel"])
	if quick_purge == false:
		firstrun = true
		setname()
		_on_viewport_size_changed()

var firstrun = true
var recentsizechange = false
func _on_viewport_size_changed():
	if recentsizechange == false:
		recentsizechange = true #this makes it so it only updates the viewport every 4 frames
		if firstrun == false:
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
		else:
			firstrun = false
		recentsizechange= false
		var newsize = get_viewport_rect().size
#		var proportiony = (newsize.length()/600)
		var proportionfull = (newsize.length()/1024)
		var proportionx = (newsize.x/1024)
		if Playervariables.modarray[Playervariables.mods.TRANSLATION] == true and Playervariables.default_font_dict["font_attack_record"] != null:
			proportionfull = proportionfull*Playervariables.default_font_dict["font_attack_record_size"]
#		$Idoll/Speech.get("custom_fonts/font").size = int(16*proportionx)
#		$Idoll/Speech.get("custom_fonts/font").outline_size = int(ceil(proportionx))
#		$veil/veilbutton1/Label.get("custom_fonts/font").size = int(20*ceil(proportion))
		$logo/credits.get("custom_fonts/font").size = int(13*proportionfull)
		$veil/veilbutton2/Label.get("custom_fonts/font").size = int(20*proportionfull)
		$veil/Label.get("custom_fonts/font").size = int(((24.0*proportionfull) + 24.0) /2)
		$shilling/DiscordButton/Label.get("custom_fonts/font").size = int(12*proportionx)
		$savename.get("custom_fonts/font").size = int(32*proportionfull)
		$progressdata.get("custom_fonts/font").size = int(24*proportionfull)
		$debugbutton/Label.get("custom_fonts/font").size = int(12*proportionfull)
		$logo/logo2.margin_top = proportionfull*2
		$logo/logo2.margin_bottom = proportionfull*4
#		$titlename.get("custom_fonts/font").size = int(24*ceil(proportion))
#		$TextureRect2/Label.get("custom_fonts/font").size = int(32*proportion)
#		$TextureRect2/Label.get("custom_fonts/font").outline_size = int(2*proportion)
#		var scaleproportion = newsize.x/1048
#		$Idoll/Speech.rect_scale = Vector2(scaleproportion,scaleproportion)


#add music, dork

#func _on_TextureButton_pressed():
#	$buttonsfx.play()
##	if rightclickcounter > 1:
##		get_parent().call("start_game",1)
##	else:
#		get_parent().call("start_game",0)


#func _on_TextureButton2_pressed():
#	$quitbuttonsfx.play()
#	get_parent().call("close_game")

#var rightclickcounter = 0
func _input(event):
#	if event.is_action_pressed("ui_rightclick"):
#		rightclickcounter += 1
#	if event is InputEventScreenTouch:
#		if event.pressed == false:
#			if savedeleting == true:
#				if donotpickhammerbackup == false:
#					undisable(true)
#				else:
#					donotpickhammerbackup = false
#			else:
#				var a = InputEventAction.new()
#				a.action = "ui_click"
#				a.pressed = false
#				Input.parse_input_event(a)
	if (event.is_action_pressed("ui_click") or event.is_action_pressed("ui_rightclick")): #WATCH OUT! Might not work on tablets??
		if savedeleting == true:
			$saveremover.set_modulate(Color8(255,255,255,200))
			Input.set_custom_mouse_cursor(load(Mainpreload.cursorpoint))
			savedeleting = false
			if $logo/credits/creditsanim.is_playing() == false:
				$shilling.visible = true
			$logo.visible = true
			if event.is_action_pressed("ui_click") and hoveredoversave == true:
				if Playervariables.newsave == true:
					$veil/Label.set_text(Playervariables.veiltextdict["noreset"].replace("VAR1",Playervariables.currentsavenum))
					$veil/veilbutton2.visible = true
					$veil/veilbutton1.visible = false
				else:
					$veil/Label.set_text(Playervariables.veiltextdict["reset"].replace("VAR1",Playervariables.currentsavenum))#"You are about to reset the save slot ("+str(Playervariables.currentsavenum)+ ") to the start. Continue?")
					$veil/veilbutton2.visible = true
					$veil/veilbutton1.visible = true
				$veil.visible = true
	elif event.is_action_released("ui_click") or event.is_action_released("ui_rightclick"):
		if $TextureRect2/start.disabled == true:
			undisable()
#	if event.is_action_pressed("ui_middleclick"):
#		step += 1
#		match step:
#			1:
#				$TextureRect.set_modulate(Color(1,1,1))
#			2:
#				$TextureRect.set_modulate(Color(0.8,0.8,0.8))
#			3:
#				$TextureRect.set_modulate(Color(0.6,0.6,0.6))
#			4:
#				$TextureRect.set_modulate(Color(0.4,0.4,0.4))
#				step = 0
#var step = 0
func undisable(touch=false):
	if touch == true:
		$saveremover.set_modulate(Color8(255,255,255,200))
		Input.set_custom_mouse_cursor(load(Mainpreload.cursorpoint))
		savedeleting = false
		if $logo/credits/creditsanim.is_playing() == false:
			$shilling.visible = true
		$logo.visible = true
		donotpickhammerbackup = false
	else:
		yield(get_tree(),"idle_frame")
	$TextureRect2/start.disabled = false
	$TextureRect2/setting.disabled = false
	$TextureRect2/exit.disabled = false
	$fullscreenbutton.disabled = false
	$translationbutton.disabled = false
	$saveremover.disabled = false
	$shilling/DiscordButton/DiscordInvis.disabled = false
	$shilling/subpatbuttons/PatreonButton/PatreonInvis.disabled = false
	$shilling/subpatbuttons/SubscribestarButton/SubscribestarInvis.disabled = false
	$logo.disabled = false
	if $logo/credits/creditsanim.is_playing() == false:
		$shilling.visible = true
	$logo.visible = true
	hoveredoversave = false
#func _on_controlguide_pressed():
#	$controlguide.visible = false
#func _on_controlopen_pressed():
#	$controlguide/controls.play("controlshow")
#	$controlguide.visible = true


#func _on_creditbutton_pressed():
#	if $Settings.visible == false:
#		$buttonsfx.play()
#		$creditbutton/Label2.visible = $creditbutton/Label.is_visible()
#		$creditbutton/Label.visible = !($creditbutton/Label.is_visible())


func _on_fullscreenbutton_pressed():
	if $Settings.visible == false:
		$buttonsfx.play()
		if Playervariables.touchscreenmode == true:
#			Playervariables.batterysaver = !(Playervariables.batterysaver)
			Playervariables.set_battery_saver_size(!(Playervariables.batterysaver))
		else:
			Playervariables.fullscreen = !(Playervariables.fullscreen)
			OS.set_window_fullscreen(Playervariables.fullscreen)
		#	$Settings/Settingscreen/Scrollcontainer/settings/cen0/VBoxContainer/CheckBox6.pressed = Playervariables.fullscreen
		_fullscreencheckboxcheck()

#var powersaver = preload("res://Assets/ui/powersaver.png")
#var powersaverhovered = preload("res://Assets/ui/powersaverhover.png")
#var fullres = preload("res://Assets/ui/fullres.png")
#var fullreshovered = preload("res://Assets/ui/fullreshover.png")
var fullscreen = preload("res://Assets/ui/mainmenu/Fullscreenfullscreen.png")
var fullscreenhovered = preload("res://Assets/ui/mainmenu/Fullscreenfullscreenhovered.png")
var window = preload("res://Assets/ui/mainmenu/Fullscreenwindowed.png")
var windowhovered = preload("res://Assets/ui/mainmenu/Fullscreenwindowedhovered.png")
func _fullscreencheckboxcheck():
	if Playervariables.touchscreenmode == true:
		$fullscreenbutton.disabled = true
		$fullscreenbutton.visible = false
#		if Playervariables.batterysaver == true:
#			$fullscreenbutton.texture_normal = fullres
#			$fullscreenbutton.texture_hover = fullreshovered
#		else:
#			$fullscreenbutton.texture_normal = powersaverhovered
#			$fullscreenbutton.texture_hover = powersaverhovered
	else:
		if Playervariables.fullscreen == false:
			$fullscreenbutton.texture_normal = fullscreen
			$fullscreenbutton.texture_hover = fullscreenhovered
		else:
			$fullscreenbutton.texture_normal = window
			$fullscreenbutton.texture_hover = windowhovered



func _on_save_mouse_entered():
	hoveredoversave = true
	if savedeleting == false:
		get_node("/root/Master/SFX/hoverclick").play()
		hoveredoversave = true
	else:
		pass
func _on_save_mouse_exited():
	hoveredoversave = false
func _on_exit_mouse_entered():
	if savedeleting == false:
		get_node("/root/Master/SFX/hoverclick").play()
func _on_setting_mouse_entered():
	if savedeleting == false:
		get_node("/root/Master/SFX/hoverclick").play()
func _on_start_mouse_entered():
	if savedeleting == false:
		get_node("/root/Master/SFX/hoverclick").play()
func _on_VBoxContainer2_mouse_entered():
	pass
#func _on_fullscreenbutton_mouse_entered():
#	if savedeleting == false:
#		pass
var donotpickhammerbackup = false
func _on_saveremover_mouse_entered():
	if savedeleting == true:
		donotpickhammerbackup = true
	else:
		donotpickhammerbackup = false


enum{veilDELETE = 0, veilNEW = 1, veilQUIT = 2, veilSETTINGS = 3,veilDISCORD = 4,veilPATREON = 5,veilSUBSCRIBESTAR=6}
var veilstate = veilNEW
func _on_start_pressed():
	if $Settings.visible == false:
		$buttonsfx.play()
		if Playervariables.newsave == true:
			veilstate = veilNEW
			$veil/Label.set_text(Playervariables.veiltextdict["start"].replace("VAR1",Playervariables.currentsavenum))#"This save file (save slot "+str(Playervariables.currentsavenum)+ ") has no data on it yet. Would you like to start from the beginning?")
			$veil.visible = true
			$veil/veilbutton1.visible = true
			$veil/veilbutton2.visible = true
		else:
			if debugstart == true:
				get_parent().call("start_game",1)
			else:
				get_parent().call("start_game",0)

func _on_setting_pressed():
	if $Settings.visible == false:
		$buttonsfx.play()
		if $Settings.get_children().size() > 0:
			if $Settings.get_child(0).get_name() == "Settingscreen":
				$Settings.get_child(0).queue_free()
		var newsettings = load(Mainpreload.SettingScreen).instance()
		$Settings.add_child(newsettings)
		newsettings.visible = true
		newsettings.get_node("Scrollcontainer/settings/qcontrol").visible = false
#		newsettings.get_node("Scrollcontainer/settings/mcontrol").visible = true
		newsettings.load_settings()
		$Settings.visible = true
		$veil.visible = true
		$veil/Label.set_text("")
		veilstate = veilSETTINGS

func _on_exit_pressed():
	if $Settings.visible == false:
		$quitbuttonsfx.play()
		$veil/Label.set_text(Playervariables.veiltextdict["exit"])#"Close the application?")
		$veil/veilbutton2.visible = true
		$veil/veilbutton1.visible = true
	#	$veil/veilbutton1/Label.set_text("Quit Game")
		$veil.visible = true
		veilstate = veilQUIT

const savenumarray = [preload("res://Assets/ui/mainmenu/1save.png"),preload("res://Assets/ui/mainmenu/2save.png"),preload("res://Assets/ui/mainmenu/3save.png")]
func _on_save_pressed():
	if $Settings.visible == false:
		$quitbuttonsfx.play()
		var loadnum = Playervariables.currentsavenum + 1
		if loadnum >= 4:
			loadnum = 1
		Playervariables.loadprogress(loadnum) #make sure to CORRUPT FROM STORED after load progress
		Playervariables.corruptiondict = Playervariables.corrupt_from_stored(Playervariables.StoredTransformations,Playervariables.corruptiondict)
		setname()
		if Playervariables.bit_to_array(Playervariables.Endings,Playervariables.EndingsRef.FOXGIRL,8)[2] == true and Playervariables.Home and Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] < Playervariables.tutorialstages.SHIKATALK:
			Playervariables.altfoxconvo = true
		else:
			Playervariables.altfoxconvo = false

var yes = 0
func _on_veilbutton1_pressed():
	$buttonsfx.play()
	if veilstate == veilNEW:
		$veil/Label.set_text(Playervariables.veiltextdict["loading"])#"Now loading...")
		$veil/veilbutton1.visible = false
		$veil/veilbutton2.visible = false
#			Playervariables.newsave = false
#			Playervariables.saveprogress()
		get_parent().call("start_game",0)
	elif veilstate == veilDELETE:
		if Playervariables.touchscreenmode == true and yes < 3:
			yes += 1
		else:
			Playervariables.loaddefaultprogress()
			Playervariables.saveprogress()
			Playervariables.resetvariables(true)
			$veil/Label.set_text(Playervariables.veiltextdict["resetdone"])#"Save has been reset.")
	#		$savename.set_text("New Save")
			set_progress_data(true)
			$veil/veilbutton1.visible = false
			$veil/veilbutton2.visible = true
	elif veilstate == veilQUIT:
		get_parent().call("close_game")
	elif veilstate == veilDISCORD:
		var _asdf = OS.shell_open("https://discord.gg/8Np5Fjw4wp")
		$veil.visible = false
	elif veilstate == veilPATREON:
		var _asdf = OS.shell_open("https://www.patreon.com/moncurse")
		$veil.visible = false
	elif veilstate == veilSUBSCRIBESTAR:
		var _asdf = OS.shell_open("https://subscribestar.adult/moncurse")
		$veil.visible = false

func _on_veilbutton2_pressed():
	yes = 0
	$quitbuttonsfx.play()
	$veil.visible = false

func check_errors():
	if (Playervariables.errorlog.size() + Playervariables.tlerrorlog.size()) > 0:
		$errorlog.visible = true
		var errormessage = "ERROR LOG"
		for error in Playervariables.tlerrorlog:
			errormessage += "\n\n" + error
		for error in Playervariables.errorlog:
			errormessage += "\n\n" + error
		$errorlog.set_text(errormessage)
	else:
		$errorlog.visible = false

var currentmaxlevels = 7
var currentmaxtransformations = 19
var currentmaxcgs = 10
var currentmaxendings = 8
func setname():
	if Playervariables.newsave == true and (Playervariables.playername == "Rules" or Playervariables.playername == "Reset"):
		set_progress_data(true)
	else:
		set_progress_data(false)
	$TextureRect2/savenum.texture = savenumarray[Playervariables.currentsavenum-1]
	if Playervariables.debugmodeon == true and OS.is_debug_build() == true:
		$debugbutton.visible = true
		$debugbutton2.visible = true
	else:
		$debugbutton.visible = false
		$debugbutton2.visible = false
		debugstart = false

func set_progress_data(blankiftrue):
	if Playervariables.removesex == true:
		currentmaxtransformations = 26
	else:
		currentmaxtransformations = 19
	if Playervariables.mobile_ads == true:
		currentmaxcgs = 14#scissorlocked,implocked,muglocked
	else:
		currentmaxcgs = 11
	if Playervariables.prefdict[Playervariables.pref.UNBIRTH] == true:
		currentmaxendings = 8
	else:
		currentmaxendings = 7
	if blankiftrue == true:
		get_node("savename").set_text("New Save")
		if Playervariables.modarray[Playervariables.mods.TRANSLATION] == true and Playervariables.transdict.has("mainmenu") and Playervariables.transdict["mainmenu"].has("progress"):
#			get_node("progressdata").set_text("Progress:\n\nLevels Cleared: "+str(0)+" / "+str(currentmaxlevels)+"\nTransformations: "+str(0)+" / "+str(currentmaxtransformations)+"\nCGs: "+str(0)+" / "+str(currentmaxcgs)+"\nEndings: "+str(0)+" / "+str(currentmaxendings))
			get_node("progressdata").set_text(Playervariables.transdict["mainmenu"]["progress"].replace("VAR1","...").replace("VAR2",str(0)).replace("VAR3",str(currentmaxlevels)).replace("VAR4",str(0)).replace("VAR5",str(currentmaxtransformations)).replace("VAR6",str(0)).replace("VAR7",str(currentmaxcgs)).replace("VAR8",str(0)).replace("VAR9",str(currentmaxendings)))
		else:
			get_node("progressdata").set_text("Progress:\n\nLevels Cleared: "+str(0)+" / "+str(currentmaxlevels)+"\nTransformations: "+str(0)+" / "+str(currentmaxtransformations)+"\nCGs: "+str(0)+" / "+str(currentmaxcgs)+"\nEndings: "+str(0)+" / "+str(currentmaxendings))
	else:
		get_node("savename").set_text((Playervariables.tempname).strip_escapes())
		var currentcg = 0
		for i in range(Playervariables.GalleryArrayHeadCG.size()):
			if Playervariables.GalleryArrayHeadCG[i] > 0:
				currentcg += 1
		if currentcg > currentmaxcgs:
			currentcg = currentmaxcgs
		var currenttf = 0
		for race in Playervariables.racetfsdict:
			var useenum = Playervariables.get(Playervariables.racetfsdict[race])
			var enumkeys = useenum.keys()
			var usearray = Playervariables.bit_to_array(Playervariables.UniqueTransformations,race)
			for i in enumkeys.size():
				var num = useenum[enumkeys[i]]
				if usearray[num] == true and num != 7:
#					print("Adding TF for: "+str(race)+"'s value of "+str(i))
					currenttf += 1
			if enumkeys.has("PANTS") and usearray[useenum["PANTS"]] == true:
				currenttf -= 1
			elif enumkeys.has("BACKHORNS") and usearray[useenum["BACKHORNS"]] == true and race == Playervariables.raceHARPY:
				currenttf -= 1
		var cupidarray = Playervariables.bit_to_array(Playervariables.UniqueTransformations,Playervariables.raceCUPID)
		if cupidarray[0] == true:
			currenttf += 1#ropegag
		if currenttf > currentmaxtransformations:
			currenttf = currentmaxtransformations
#		for i in range(Playervariables.UniqueTransformations.size()):
#			var trutharray = Playervariables.bit_to_array(Playervariables.UniqueTransformations,i)
#			for i2 in range(trutharray.size()):
#				if trutharray[i2] == true:
#					currenttf += 1
#		for i in range(Playervariables.BustSizesTransformations.size()):
#			if Playervariables.BustSizesTransformations[i] > 0:
#				currenttf += 1
#		for i in range(Playervariables.MonsterGirlTransformations.size()):
#			if Playervariables.MonsterGirlTransformations[i] > 0:
#				currenttf += 1
		var currentstages = 0
		if Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] >= Playervariables.tutorialstages.ROUTEA11:
			currentstages = 7
		elif Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] >= Playervariables.tutorialstages.VOICE:
			currentstages = 6
		elif Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] >= Playervariables.tutorialstages.ALONGWALK:
			currentstages = 5
		elif Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] >= Playervariables.tutorialstages.PUSSYGALORE:
			currentstages = 4
		elif Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] >= Playervariables.tutorialstages.QADES:
			currentstages = 3
		elif Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] >= Playervariables.tutorialstages.TUTORIAL2:
			currentstages = 2
		elif Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] >= Playervariables.tutorialstages.TUTORIAL1:
			currentstages = 1
#		for i in range(Playervariables.StagesCleared.size()):
#			currentstages += Playervariables.StagesCleared[i]
#			if i == 0:
#				if currentstages > 4:
#					currentstages -= 1
#				currentstages = min(Playervariables.StagesCleared[i],7)
#				if Playervariables.StagesCleared[i] >= 10 and Playervariables.hungeasdfrmecshanics == true:
#					currentstages += 1
#		if currentstages > 4:
#			currentstages -= 1
#		if currentstages > 7:
#			currentstages -= 1
		var currentendings = 0
		for i in range(Playervariables.Endings.size()-1):
			var trutharray = Playervariables.bit_to_array(Playervariables.Endings,i+1)
			for i2 in range(trutharray.size()):
				if trutharray[i2] == true:
					if i+1 == Playervariables.EndingsRef.WEREWOLF and i2 == 1 and Playervariables.prefdict[Playervariables.pref.UNBIRTH] == false:
						pass
					else:
						currentendings += 1
#		if currentstages > 6:
#			get_node("progressdata").set_text("Class:"+Playervariables.racearray[Playervariables.CurrentClass]+"\n\nProgress:\n\nTransformations: "+str(currenttf)+" / "+str(currentmaxtransformations)+"\nCGs: "+str(currentcg)+" / "+str(currentmaxcgs)+"\nEndings: "+str(currentendings)+" / "+str(currentmaxendings))
#		else:
		var classdescriber = Playervariables.racearray[Playervariables.CurrentClass]
		if Playervariables.CurrentClass <= Playervariables.raceclassarray.size():
			classdescriber = Playervariables.raceclassarray[Playervariables.CurrentClass]
#		match Playervariables.CurrentClass:
#			Playervariables.raceHUMAN:classdescriber = "Lancer"
#			Playervariables.raceNAMAN:classdescriber = "Cow"
#			Playervariables.raceNEKO:classdescriber = "Tiger"
#			Playervariables.raceKITSUNE:classdescriber = "Priestess"
#			Playervariables.raceWOLF:classdescriber = "Skirmisher"
#			Playervariables.raceRAM:classdescriber = "Bard"
#			Playervariables.raceHARPY:classdescriber = "Harpy"
		if Playervariables.modarray[Playervariables.mods.TRANSLATION] == true and Playervariables.transdict.has("mainmenu") and Playervariables.transdict["mainmenu"].has("progress"):
			get_node("progressdata").set_text(Playervariables.transdict["mainmenu"]["progress"].replace("VAR1",classdescriber).replace("VAR2",str(currentstages)).replace("VAR3",str(currentmaxlevels)).replace("VAR4",str(currenttf)).replace("VAR5",str(currentmaxtransformations)).replace("VAR6",str(currentcg)).replace("VAR7",str(currentmaxcgs)).replace("VAR8",str(currentendings)).replace("VAR9",str(currentmaxendings)))
		else:
			get_node("progressdata").set_text("Class:"+classdescriber+"\n\nProgress:\n\nStages Cleared: "+str(currentstages)+" / "+str(currentmaxlevels)+"\nTransformations: "+str(currenttf)+" / "+str(currentmaxtransformations)+"\nCGs: "+str(currentcg)+" / "+str(currentmaxcgs)+"\nEndings: "+str(currentendings)+" / "+str(currentmaxendings))
		$debugbutton2/Label.set_text("DEBUG: Class\n"+Playervariables.racearray[Playervariables.CurrentClass])

var debugstart = false
func _on_debugbutton_pressed():
	if debugstart == true:
		debugstart = false
		$debugbutton/Label.set_text("DEBUG START:\nOFF")
	else:
		debugstart = true
		$debugbutton/Label.set_text("DEBUG START:\nON")



#
#var faded = true
#func _on_LinkBacker_mouse_entered():
#	if faded == true:
#		$shilling/shillscroll.stop(false)
#		$shilling/shillscroll.play("shilling",-1,1,false)
#		faded = false
#func _on_LinkBacker_mouse_exited():
#	if faded == false:
#		if $shilling/shillscroll.get_playing_speed() != 0:
#			$shilling/shillscroll.play("shilling",-1,-1,false)
#		else:
#			$shilling/shillscroll.play("shilling",-1,-1,true)
#		faded = true

func _on_DiscordInvis_pressed():
	veilstate = veilDISCORD
	$veil/Label.set_text(Playervariables.veiltextdict["discord"])#"This will open a link to the official MonCurse discord server.\nYou can report bugs or just talk about games there.\n")
	$veil.visible = true
	$veil/veilbutton1.visible = true
	$veil/veilbutton2.visible = true
func _on_PatreonInvis_pressed():
	veilstate = veilPATREON
	$veil/Label.set_text(Playervariables.veiltextdict["patreon"])#"This will open a link to the game's Patreon page:\nhttps://www.patreon.com/moncurse\n\nHelp keep the game going!\n")
	$veil.visible = true
	$veil/veilbutton1.visible = true
	$veil/veilbutton2.visible = true
func _on_SubscribestarInvis_pressed():#PLEASE NOTE: subscribestar button has -1 margin on left/right, subscribestarinvis has +1 margin on left/right
	veilstate = veilSUBSCRIBESTAR
	$veil/Label.set_text(Playervariables.veiltextdict["subscribestar"])#"This will open a link to the game's Subscribestar page:\nhttps://subscribestar.adult/moncurse\n\nHelp keep the game going!\n",
	$veil.visible = true
	$veil/veilbutton1.visible = true
	$veil/veilbutton2.visible = true

func _on_DiscordInvis_mouse_entered():
	if $shilling/shillinganim.is_playing() == true:
		$shilling/shillinganim.advance(5)
	$shilling/DiscordButton.set_modulate(Color(1,1,1,1))
func _on_DiscordInvis_mouse_exited():
	$shilling/DiscordButton.set_modulate(Color(1,1,1,0.470588))
var subpatentered = false
func _on_PatreonInvis_mouse_entered():
	if $shilling/shillinganim.is_playing() == true:
		$shilling/shillinganim.advance(5)
	if subpatentered == false:
		subpatentered = true
		$shilling/subpatbuttons/PatreonButton.set_self_modulate(Color(1,1,1,1))
		$shilling/subpatbuttons/supportlabel.set_self_modulate(Color(1,1,1,1))
func _on_PatreonInvis_mouse_exited():
	if subpatentered == true:
		subpatentered = false
		$shilling/subpatbuttons/PatreonButton.set_self_modulate(Color(1,1,1,0.470588))
		$shilling/subpatbuttons/supportlabel.set_self_modulate(Color(1,1,1,0.470588))
func _on_SubscribestarInvis_mouse_entered():
	if $shilling/shillinganim.is_playing() == true:
		$shilling/shillinganim.advance(5)
	if subpatentered == false:
		subpatentered = true
		$shilling/subpatbuttons/SubscribestarButton.set_self_modulate(Color(1,1,1,1))
		$shilling/subpatbuttons/supportlabel.set_self_modulate(Color(1,1,1,1))
func _on_SubscribestarInvis_mouse_exited():
	if subpatentered == true:
		subpatentered = false
		$shilling/subpatbuttons/SubscribestarButton.set_self_modulate(Color(1,1,1,0.470588))
		$shilling/subpatbuttons/supportlabel.set_self_modulate(Color(1,1,1,0.470588))


func _on_debugbutton2_pressed():
	Playervariables.CurrentClass = (Playervariables.CurrentClass+1) % 9
	Playervariables.AltClass = randi()%2
	Playervariables.saveprogress()
	$debugbutton2/Label.set_text("DEBUG: Class\n"+Playervariables.racearray[Playervariables.CurrentClass])
	Playervariables.resetvariables(true)
	set_progress_data(false)


func _on_translationbutton_pressed():
	$buttonsfx.play()
	get_node("/root/Master").show_disclaimer(true,true)

#
#func _on_logo_mouse_entered():
#	$logo.modulate = Color(0.8,0.8,0.8,0.8)
#
#func _on_logo_mouse_exited():
#	$logo.modulate = Color(1,1,1)


func _on_logo_pressed():
	if $logo/logohover.is_playing() == false:
		$logo/logohover.play("logo",-1,1.0,false)
		$logo/credits.visible = true
		$buttonsfx.play()
		if credits_num == -1:
			next_credits()
		$logo/credits/creditsanim.play("rollcredits")
		$shilling.visible = false
		$background.set_modulate(Color(0.5,0.5,0.5))
	else:
		$quitbuttonsfx.play()
		$logo/logohover.stop(false)
		$logo/credits.visible = false
		$logo/credits/creditsanim.stop(false)
		$shilling.visible = true
		$background.set_modulate(Color(1.0,1.0,1.0))
