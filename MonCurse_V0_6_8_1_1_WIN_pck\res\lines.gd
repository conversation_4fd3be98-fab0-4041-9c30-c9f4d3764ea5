extends Node2D


# Declare member variables here. Examples:
# var a = 2
# var b = "text"


# Called when the node enters the scene tree for the first time.
var drawID = null
func _draw():
	if drawID != null:
		var drawcolor = Color(1,1,1)
		if drawID.recordmovetype == 1:
			drawcolor = Color(0.3,0.6,0.3,0.6)
		else:
			drawcolor = Color(0.25,0.8,1,0.6)
		var drawarray = drawID.recordmovearray
		for line in drawarray.size()-1:
			draw_line(drawarray[0+line],drawarray[1+line],drawcolor,5,true)
