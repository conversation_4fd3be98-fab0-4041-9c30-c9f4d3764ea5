[gd_scene load_steps=7 format=2]

[ext_resource path="res://Assets/ui/attackbg.png" type="Texture" id=1]
[ext_resource path="res://Assets/ui/attackbgarrow.png" type="Texture" id=2]
[ext_resource path="res://Assets/ui/attackbgcross.png" type="Texture" id=3]
[ext_resource path="res://hurt.gd" type="Script" id=4]

[sub_resource type="SpriteFrames" id=1]
animations = [ {
"frames": [ ExtResource( 2 ) ],
"loop": true,
"name": "arrow",
"speed": 5.0
}, {
"frames": [ ExtResource( 1 ) ],
"loop": true,
"name": "aura",
"speed": 5.0
}, {
"frames": [ ExtResource( 3 ) ],
"loop": true,
"name": "cross",
"speed": 5.0
} ]

[sub_resource type="Animation" id=2]
resource_name = "hurtauratemplate"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 2 ),
"transitions": PoolRealArray( 1, 0.4, 1 ),
"update": 0,
"values": [ Color( 0.156863, 0.156863, 0.156863, 0 ), Color( 1, 1, 1, 0.156863 ), Color( 0.156863, 0.156863, 0.156863, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.6, 2 ),
"transitions": PoolRealArray( 0.5, 1, 1 ),
"update": 0,
"values": [ Vector2( 0.7, 0.7 ), Vector2( 1.2, 1.2 ), Vector2( 1, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( -45, -45 ), Vector2( 180, 180 ) ]
}

[node name="hurt" type="AnimatedSprite"]
self_modulate = Color( 0.156863, 0.156863, 0.156863, 0 )
position = Vector2( -45, -45 )
scale = Vector2( 0.7, 0.7 )
z_index = 70
frames = SubResource( 1 )
animation = "cross"
script = ExtResource( 4 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
playback_speed = 0.6
anims/hurtauratemplate = SubResource( 2 )

[connection signal="animation_finished" from="AnimationPlayer" to="." method="_on_AnimationPlayer_animation_finished"]
