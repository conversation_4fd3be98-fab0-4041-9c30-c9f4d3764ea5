[gd_scene load_steps=14 format=2]

[ext_resource path="res://effects/dissolve_texture.jpg" type="Texture" id=1]
[ext_resource path="res://DialogueArt/rules/rules ramgirl hornleft.png" type="Texture" id=2]
[ext_resource path="res://effects/corruption particle.png" type="Texture" id=3]
[ext_resource path="res://effects/corruption_part_effect.gd" type="Script" id=4]

[sub_resource type="Shader" id=3]
code = "shader_type canvas_item;

// --- Uniforms --- //
uniform float percentage: hint_range(0.0, 1.0, 0.01) = 1.0;

uniform sampler2D burn_texture;
//group_uniforms layer_1;
uniform vec4 source_color1 = vec4(0.2, 0.2, 0.2, 1.0);
uniform float size_1 = 0.05;
//group_uniforms layer_2;
uniform vec4 source_color2 = vec4(1.0, 0.0, 0.0, 1.0);
uniform float size_2 = 0.05;
//group_uniforms layer_3;
uniform vec4 source_color3 = vec4(1.0, 0.5, 0.0, 1.0);
uniform float size_3 = 0.05;

// --- Functions --- //
void fragment() {
	vec4 texture_color = texture(TEXTURE, UV);
	float noise = texture(burn_texture, UV).r * (1.0 - (size_1 + size_2 + size_3 + 0.01));

	COLOR.a = min(COLOR.a-step(percentage, noise),texture_color.a);
	COLOR.rgb = mix(COLOR.rgb, source_color3.rgb, step(percentage, noise + size_1 + size_2 + size_3));
	COLOR.rgb = mix(COLOR.rgb, source_color2.rgb, step(percentage, noise + size_1 + size_2));
	COLOR.rgb = mix(COLOR.rgb, source_color1.rgb, step(percentage, noise + size_1));
}"

[sub_resource type="ShaderMaterial" id=2]
shader = SubResource( 3 )
shader_param/percentage = 0.0
shader_param/source_color1 = Plane( 0.2, 0.2, 0.3, 1 )
shader_param/size_1 = 0.2
shader_param/source_color2 = Plane( 0.45, 0.25, 0.6, 1 )
shader_param/size_2 = 0.15
shader_param/source_color3 = Plane( 0.7, 0.5, 0.7, 1 )
shader_param/size_3 = 0.15
shader_param/burn_texture = ExtResource( 1 )

[sub_resource type="Animation" id=4]
resource_name = "corruption"
length = 3.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1.1, 3 ),
"transitions": PoolRealArray( 0.5, 2, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 0.745098, 0.654902, 0.968627, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:material:shader_param/percentage")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1.1, 3 ),
"transitions": PoolRealArray( 1, 1.57, 1 ),
"update": 0,
"values": [ 0.0, 1.0, 0.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("Particles2D:emitting")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.1, 2.4 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ false, true, false ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("glowsprite:scale")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 1.4, 3 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 1.25, 1.25 ), Vector2( 1, 1 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("glowsprite:self_modulate")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 1.4, 3 ),
"transitions": PoolRealArray( 3, 1, 1 ),
"update": 0,
"values": [ Color( 0, 0, 0, 0 ), Color( 0.168627, 0.0941176, 0.192157, 0.54902 ), Color( 0, 0, 0, 0 ) ]
}

[sub_resource type="CanvasItemMaterial" id=12]
particles_animation = true
particles_anim_h_frames = 9
particles_anim_v_frames = 1
particles_anim_loop = true

[sub_resource type="Gradient" id=7]
offsets = PoolRealArray( 0, 0.326316, 0.631579, 1 )
colors = PoolColorArray( 0.191406, 0.160751, 0.188772, 1, 0.815812, 0.687805, 0.921875, 1, 0.638983, 0.43042, 0.640625, 0.756863, 0.85297, 0.762207, 0.875, 0 )

[sub_resource type="GradientTexture" id=8]
gradient = SubResource( 7 )

[sub_resource type="Curve" id=13]
_data = [ Vector2( 0, 0.781818 ), 0.0, 0.0, 0, 0, Vector2( 0.550847, 1 ), 0.0, 0.0, 0, 0, Vector2( 1, 0.6 ), 0.0, 0.0, 0, 0 ]

[sub_resource type="CurveTexture" id=14]
curve = SubResource( 13 )

[sub_resource type="ParticlesMaterial" id=11]
emission_shape = 1
emission_sphere_radius = 100.0
flag_disable_z = true
spread = 180.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 20.0
initial_velocity_random = 1.0
orbit_velocity = -0.2
orbit_velocity_random = 1.0
linear_accel = 50.0
scale_curve = SubResource( 14 )
color_ramp = SubResource( 8 )
anim_speed = 2.0
anim_speed_random = 1.0

[node name="corruption_part" type="Sprite"]
self_modulate = Color( 1, 1, 1, 0 )
material = SubResource( 2 )
texture = ExtResource( 2 )
script = ExtResource( 4 )

[node name="glowsprite" type="Sprite" parent="."]
self_modulate = Color( 0, 0, 0, 0 )
show_behind_parent = true
texture = ExtResource( 2 )

[node name="animation" type="AnimationPlayer" parent="."]
autoplay = "corruption"
anims/corruption = SubResource( 4 )

[node name="Particles2D" type="Particles2D" parent="."]
material = SubResource( 12 )
emitting = false
amount = 16
lifetime = 5.0
process_material = SubResource( 11 )
texture = ExtResource( 3 )

[node name="Timer" type="Timer" parent="."]
wait_time = 7.5
one_shot = true
autostart = true

[connection signal="timeout" from="Timer" to="." method="_on_Timer_timeout"]
