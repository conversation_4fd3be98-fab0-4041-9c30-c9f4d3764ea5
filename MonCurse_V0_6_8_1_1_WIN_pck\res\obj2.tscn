[gd_scene load_steps=8 format=2]

[ext_resource path="res://Background/piton.png" type="Texture" id=1]
[ext_resource path="res://obj2piton.gd" type="Script" id=2]
[ext_resource path="res://Background/pitonbreak.png" type="Texture" id=3]
[ext_resource path="res://Background/pitonside.png" type="Texture" id=4]

[sub_resource type="SpriteFrames" id=1]
animations = [ {
"frames": [ ExtResource( 1 ), ExtResource( 3 ), ExtResource( 4 ) ],
"loop": true,
"name": "pitons",
"speed": 5.0
} ]

[sub_resource type="Animation" id=3]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath(".:z_index")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ -3 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:offset")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 2 ),
"update": 0,
"values": [ Vector2( 0, 0 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:self_modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=2]
length = 0.5
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.5 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 3, 3, 3, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:offset")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 2, 2 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0, 120 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:rotation_degrees")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:z_index")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ 6 ]
}

[node name="piton" type="AnimatedSprite"]
z_index = -3
frames = SubResource( 1 )
animation = "pitons"
script = ExtResource( 2 )

[node name="break" type="AnimationPlayer" parent="."]
anims/RESET = SubResource( 3 )
anims/pitonbreak = SubResource( 2 )

[connection signal="animation_finished" from="break" to="." method="_on_break_animation_finished"]
