extends CanvasLayer


# Declare member variables here. Examples:
# var a = 2
# var b = "text"


# Called when the node enters the scene tree for the first time.
#func _ready():
#	if Playervariables.current_translation_code != Playervariables.TL_DEFAULT:
#		$veil/TextureButton.disabled = true
#		$veil/TextureButton2.disabled = true
#		$veil/TextureButton.set_self_modulate(Color(0.4,0.4,0.4,1))
#		$veil/TextureButton2.set_self_modulate(Color(0.4,0.4,0.4,1))
#		$veil/TextureButton/Label.set_text("please switch\n to english")
#		$veil/TextureButton2/Label2.set_text("please switch\n to english")


# Called every frame. 'delta' is the elapsed time since the previous frame.
#func _process(delta):
#	pass

var modscreen = null
func _on_TextureButton_pressed():
	get_node("/root/Master/SFX/uif").play()
	if $veil2.get_children().size() == 0:
		modscreen = null
	if modscreen == null:
		modscreen = load("res://exportedtools/Editor Tools Textify.tscn").instance()
		$veil2.add_child(modscreen)
		$veil2.visible = true


func _on_TextureButton2_pressed():
	get_node("/root/Master/SFX/uif").play()
	if $veil2.get_children().size() == 0:
		modscreen = null
	if modscreen == null:
		modscreen = load("res://exportedtools/newauto.tscn").instance()
		$veil2.add_child(modscreen)
		$veil2.visible = true


func _on_TextureButton3_pressed():
	get_node("/root/Master/SFX/uib").play()
	queue_free()
