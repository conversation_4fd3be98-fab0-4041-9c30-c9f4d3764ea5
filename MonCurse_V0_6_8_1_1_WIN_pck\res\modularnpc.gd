extends Node2D

var targetposition = Vector2(0,0)
var loc = Vector2(0,0)
var quip = null
var locinc = -1
var quipnum = "000"
var quiparray = []#[200,000,100,300,800,400,700,600,500]
var enemyname = "Voice"
var displayname = "Voice"

var altquipdict = {} #add quipnums with non-normal quiptypes here

const tilesize = 128
const VOICE = -1
const QADES = -2
const RULES = -3
const VILLAGERSHORT = -4
const VILLAGERONEE = -5
const VILLAGERBAG = -6

var monsternum = VOICE #-1 is Voice

func _ready():
	add_to_group("Quippers")

func spawnready(newpos,monsterID):
	position = newpos
	targetposition = newpos
	loc =  (targetposition/tilesize) - Vector2(0.5,0.5)
	monsternum = monsterID
	$Extra.visible = false
	match int(monsternum):
		VOICE:
			$Extra.visible = true
			if Playervariables.Voice_Cat == true:
				$Character.set_animation("VoiceCat")
			else:
				$Character.set_animation("Voice")
			enemyname = "Voice"
			displayname = "Voice"
		QADES:
			$Character.set_animation("Qades")
			enemyname = "Qades"
			displayname = "Qades"
			$Character.position.y = -15
			$Character/dropshadow.offset.y = 30
			$Character/dropshadow.set_self_modulate(Color(1,1,1,0.5))
			$Character/dropshadow.scale = Vector2(1,1)
		RULES:
			pass
		VILLAGERSHORT:
			$Character.set_animation("VillagerShort")
			enemyname = "VillagerShort"
			displayname = "Oshea"
			$Character.position.y = -50
			$Character/dropshadow.offset.y = 128
			$Character/dropshadow.set_self_modulate(Color(1,1,1,0.5))
			$Character/dropshadow.scale = Vector2(1,1)
		VILLAGERONEE:
			$Character.set_animation("VillagerOnee")
			enemyname = "VillagerOnee"
			displayname = "Sadbh"
			$Character.position.y = -50
			$Character/dropshadow.offset.y = 110
			$Character/dropshadow.set_self_modulate(Color(1,1,1,0.75))
			$Character/dropshadow.scale = Vector2(1.2,1.2)
			if get_parent().mapgenlocation == Playervariables.WolfCombat:
				$Character/dropshadow.scale = Vector2(1.8,1.2)
				$Character.frame = 1
			else:
				$Character.frame = 0
		VILLAGERBAG:
			$Character.set_animation("VillagerBag")
			enemyname = "VillagerBag"
			displayname = "Seild"
			$Character.position.y = -52
			$Character/dropshadow.offset.y = 109
			$Character/dropshadow.set_self_modulate(Color(1,1,1,0.75))
			$Character/dropshadow.scale = Vector2(1.2,1.2)

var convotilelocvarray = []
var conversationhadarray = []
func convo_on_tile(locv):
	if convotilelocvarray.find(locv) == -1:
		convotilelocvarray.append(locv)
		if locv == position:
			enablequip("conversation")
func execute_convo(locv):
	if conversationhadarray.find(locv) == -1:
		conversationhadarray.append(locv)
		quipused = true
		unhovered()
#		$quip.set_self_modulate(Color(0.2,0.2,0.2,0.50))
#		quipused = true
#		enablequip("conversation")

func relocate(vector):
	targetposition = vector
	loc =  (targetposition/tilesize) - Vector2(0.5,0.5)
	if position != vector:
#		loc =  (targetposition/tilesize) - Vector2(0.5,0.5)
		hideunhidequip(false,monsternum,true)
		convolock = false
		get_parent().relocateNPC(self,targetposition)
		if $AnimationPlayer.is_playing() == true and $AnimationPlayer.get_current_animation() == "hopup":
			if $AnimationPlayer.get_playing_speed() > 0:
				$AnimationPlayer.stop()
				$AnimationPlayer.play_backwards()
				$AnimationPlayer.playback_speed = 1.3
		elif $Character.is_visible() == true:
			$AnimationPlayer.stop()
			$AnimationPlayer.play_backwards("hopup")
			$AnimationPlayer.playback_speed = 1.3
		else:
			$AnimationPlayer.play("hopup")
			$AnimationPlayer.playback_speed = 1.3

func _on_AnimationPlayer_animation_finished(anim_name):
	if position != targetposition or $Character.visible == false or $Character.get_modulate().a == 0:
#		if anim_name == "squish":
#			pass
		if anim_name == "hopup" and $Character.is_visible() == false:
			position = targetposition
			$AnimationPlayer.play("hopup")
			$AnimationPlayer.playback_speed = 1.3
			hideunhidequip(true,monsternum,true)
			if convotilelocvarray.find(targetposition) != -1:
				enablequip("conversation")
#		elif anim_name == "hopup" and $Character.is_visible() == true:
#			get_parent().ensureNPC(self,(position/tilesize) - Vector2(0.5,0.5))
#			print("EnsureNPC.")
		else:
			relocate(targetposition)
			print("modularnpc messed up, relocating")
	elif squishy == true:
		$AnimationPlayer.play("squish")

func alter_array(num,versions = 1,specpoint = 0):
	if versions > 0 and hypnosis == false:
		for i in range(versions):
			if quiparray.size() > (i+specpoint):#versions:
				quiparray[i+specpoint] = int(num*100 + (i*20))
			else:
				quiparray.append(int(num*100 + (i*20)))
			if specpoint+i == locinc:
				assignquip(locinc)
	
func assignquip(num,quipname = null):
	$quip.offset.y = -8
	locinc = num
	if quipname != null and quip != quipname:
		quip = quipname
		if quipname == "quiptutorial2":
			quiparray = [200,000,100,400,600,500]
		elif quipname == "quiptutorial1":
			quiparray = [000,100,300]
		elif quipname == "quipvoicevillage":
			quiparray = [000,100,200,300]
		elif quipname == "quiplevelselect1":
			quiparray = [000,100]
		else:
			quiparray = [000]
	if num < quiparray.size():
		quipnum = quiparray[num]
		if int(quipnum) < 100:
			if int(quipnum) < 10:
				quipnum = "00" + str(quipnum)
			else:
				quipnum = "0" + str(quipnum)
		else:
			quipnum = str(quipnum)
#			if quipnum == "0":
#				quipnum = "000"
#			elif quipnum == "10":
#				quipnum = "010"
	else:
		print("ModularNPC Could not find quiparray entry, array too small:" +str(num))
	if Playervariables.usedquipdict.has(monsternum) == false:
		Playervariables.usedquipdict[monsternum] = {}
	if Playervariables.usedquipdict[monsternum].has(quip):
		currentusedquiparray = Playervariables.usedquipdict[monsternum][quip]
	else:
		Playervariables.usedquipdict[monsternum][quip] = []
		currentusedquiparray = Playervariables.usedquipdict[monsternum][quip]
	enablequip("normal")
func disablequip(_disable):
	if $quip.visible == true:
		$quip.visible = false
#		if get("quip") != null:
#			if disable == true:
#				if quip.length() > 10 and quip.substr(0,11) == "quipmeeting":
#					donotquiparray.append(Playervariables.indexMEETING)
var currentusedquiparray = []
var convolock = false
func enablequip(quiptype):
	if altquipdict.has(quipnum):
#		$quip.play(altquipdict[quipnum])
		quiptype = altquipdict[quipnum]
	$quip.play(quiptype)
	$quip.visible = true
	unhovered()
	if quiptype == "conversation":
		convolock = true
		if conversationhadarray.find(position) == -1:
			$quip.set_self_modulate(Color(1,1,1,0.8))
			quipused = false
		else:
			$quip.set_self_modulate(Color(0.2,0.2,0.2,0.50))
			quipused = true
	else:
		if currentusedquiparray.find(int(quipnum)) == -1:
			if $AnimationPlayer.is_playing() == false or $AnimationPlayer.get_current_animation() == "squish":
				$quip.set_self_modulate(Color(1,1,1,0.8))
			quipused = false
			if get_parent().mapgenlocation == Playervariables.Tutorial1 and quiptype == "exclamation":
				get_parent().make_indicator(loc,get_parent().indi.RIGHTCLICK)
		else:
			if $AnimationPlayer.is_playing() == false or $AnimationPlayer.get_current_animation() == "squish":
				$quip.set_self_modulate(Color(0.2,0.2,0.2,0.50))
			quipused = true
	#		match quipindex:
	#			Playervariables.indexMEETING:
	#				quip = "quipmeeting"+enemyname
func hideunhidequip(onoff,mnum,override = false,qnum = ""):
	if (mnum == monsternum or override == true) and get("quip") != null and (qnum.length() == 0 or qnum == quipnum):
		if onoff == true:
			if quipused == false:
				$quip.set_self_modulate(Color(1,1,1,0.8))
			else:
				$quip.set_self_modulate(Color(0.2,0.2,0.2,0.50))
		else:
			if override == true:
				$quip.set_self_modulate(Color(0.3,0.3,0.3,0))
			else:
				$quip.set_self_modulate(Color(0.2,0.2,0.2,0.50))
				if $quip.get_animation() != "converse" and currentusedquiparray.find(int(quipnum)) == -1:
					currentusedquiparray.append(int(quipnum))
				unhovered()
#				else:
#					unhovered()
				quipused = true
var quipused = false

func hovered():
	if $quip.visible == true and quipused == false and $quip.get_animation() != "converse":
		$quipanim.play("quipanim2")
func unhovered():
	$quip.offset.y = 0
	$quipanim.stop()

var hypnosis = false
func taking_damage(_targetarray,typedamage,ID,debuff,_friendnums,_hardcodeddirection,_attackname):
	var hurt = false
	if typedamage.y > 0 and int(typedamage.x) in [Playervariables.FORCE,Playervariables.PRECISION,Playervariables.ENERGY,Playervariables.BITTER]:
		$AnimationPlayer.advance(9)
		$AnimationPlayer.play("hurt")
		hurt = true
	match monsternum:
		VILLAGERSHORT:
			if quip != null:
				if quip == "parishquipshortonee":
					if hypnosis == false and hurt == true:
						interrupt_convo()
						alter_array(2)
		VOICE:
			if quip != null:
				if quip == "quipvoicevillage":
					for debuffvector in debuff:
						if debuffvector.x == Playervariables.HYPNOSIS:
							if hypnosis == false:
								hypnosis_effect(ID == get_parent())
								interrupt_convo()
								alter_array(6,2)
								hypnosis = true #must come last
								$AnimationPlayer.advance(9)
								$AnimationPlayer.play("hypno")
								break
				elif quip == "quiptutorial2" and hurt == true:
					if locinc == 1:
						interrupt_convo()
						alter_array(10,1,1)
					elif locinc == 2:
						interrupt_convo()
						alter_array(11,1,2)
					
		VILLAGERBAG:
			if quip != null and quip == "parishquipbag":
				if hypnosis == false and hurt == true:
					alter_array(2)
					interrupt_convo()
				for debuffvector in debuff:
					if debuffvector.x == Playervariables.HYPNOSIS:
						if hypnosis == false:
							hypnosis_effect(ID == get_parent())
							interrupt_convo()
							alter_array(3)
							hypnosis = true #must come last
							$AnimationPlayer.advance(9)
							$AnimationPlayer.play("hypno")
							break

func hypnosis_effect(corruption):
	var newhypno = load(Mainpreload.DebuffHypno).instance()
	add_child(newhypno)
	newhypno.emitting = true
	newhypno.get_child(0).emitting = true
	if corruption == true:
		get_parent().get_node("CanvasLayer2/Dialogue").corruptplayerwithbar(Playervariables.raceRAM,25)
		if Playervariables.corruptiondict.has("horns"):
			get_parent().get_node("CanvasLayer2/Dialogue").register_event("Girls look cutest when under hypnosis~",[],true)
		else:
			get_parent().get_node("CanvasLayer2/Dialogue").register_event("This doesn't feel right.",[],true)

func interrupt_convo():
	if get_parent().lockweakref != null and get_parent().lockweakref.get_ref() == self:
		get_parent().get_node("CanvasLayer2/Dialogue").call("_on_closeconvo_pressed")

var squishy = false
func squish_unsquish(onoff):
	squishy = onoff
	if onoff == true:
		if $AnimationPlayer.is_playing() == true:
			return
		else:
			$AnimationPlayer.play("squish")
	else:
		if $AnimationPlayer.get_current_animation() == "squish":
			$AnimationPlayer.play("RESET")
