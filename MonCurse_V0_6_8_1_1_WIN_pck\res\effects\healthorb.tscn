[gd_scene load_steps=5 format=2]

[ext_resource path="res://effects/healthparticle.png" type="Texture" id=1]
[ext_resource path="res://effects/healthorb.gd" type="Script" id=2]

[sub_resource type="ParticlesMaterial" id=1]
emission_shape = 2
emission_box_extents = Vector3( 40, 40, 1 )
flag_disable_z = true
direction = Vector3( 1, -1, 0 )
spread = 90.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 300.0
orbit_velocity = 0.2
orbit_velocity_random = 0.0
damping = 50.0
scale = 2.0
scale_random = 1.0

[sub_resource type="Animation" id=2]
resource_name = "flyin"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 2, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -500, 500 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:emitting")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1.6, 2 ),
"transitions": PoolRealArray( 1, 2, 1 ),
"update": 0,
"values": [ Vector2( 0.8, 0.8 ), Vector2( 0.6, 0.6 ), Vector2( 0.1, 0.1 ) ]
}

[node name="orbsprite" type="Particles2D"]
position = Vector2( -500, 500 )
scale = Vector2( 1e-05, 1e-05 )
z_index = 80
emitting = false
amount = 9
lifetime = 3.5
one_shot = true
explosiveness = 0.9
process_material = SubResource( 1 )
texture = ExtResource( 1 )
script = ExtResource( 2 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
anims/flyin = SubResource( 2 )

[connection signal="animation_finished" from="AnimationPlayer" to="." method="_on_AnimationPlayer_animation_finished"]
