[gd_scene load_steps=7 format=2]

[ext_resource path="res://Assets/hologramtestdeletelater.png" type="Texture" id=1]
[ext_resource path="res://Assets/abilityicons/Dagger.png" type="Texture" id=2]
[ext_resource path="res://useitem.gd" type="Script" id=3]

[sub_resource type="Shader" id=1]
code = "shader_type canvas_item;

uniform vec4 linesColor: hint_color = vec4(0.633232, 0.910156, 0.555693, 1.);
uniform float linesColorIntensity = 5.;
uniform float speed = 0.5;
uniform sampler2D hologramTexture;

vec2 tilingAndOffset(vec2 uv, vec2 offset) {
    return mod(uv + offset, 1);
}

void fragment() {
    vec2 offset = vec2(TIME * speed / 100.0);
    vec2 tiling = tilingAndOffset(UV, offset);
    
    vec4 noise = texture(hologramTexture, tiling);
//    vec4 colorLines = linesColor * vec4(vec3(linesColorIntensity), 1.0);
    vec4 colorLines = linesColor * vec4(vec3(linesColorIntensity), 1.0);
    float fresnel = 0.71;
    vec4 emission = colorLines * fresnel * noise;
    
    vec4 albedo = vec4(0,0,0,0);
    float alpha = dot(noise.rgb, vec3(1.0));
    vec4 hologram;
    hologram.rgb = emission.rgb + (1.0 - emission.rgb) * albedo.rgb * albedo.a;
    hologram.a = emission.a + (1.0 - emission.a) * alpha;
    hologram.a = hologram.a + (1.0 - hologram.a) * albedo.a;
    COLOR = texture(TEXTURE, UV);
    COLOR.rgb = COLOR.rgb + (1.0 - COLOR.rgb) * hologram.rgb;
    COLOR.a = min(COLOR.a, hologram.a);
}"

[sub_resource type="ShaderMaterial" id=2]
shader = SubResource( 1 )
shader_param/linesColor = Color( 0.247059, 0.509804, 0.227451, 1 )
shader_param/linesColorIntensity = 1.0
shader_param/speed = 7.0
shader_param/hologramTexture = ExtResource( 1 )

[sub_resource type="Animation" id=3]
resource_name = "New Anim"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath("use2:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.4, 1, 2 ),
"transitions": PoolRealArray( 0.8, 2, 1, 1 ),
"update": 0,
"values": [ Vector2( 30, 0 ), Vector2( 15, 0 ), Vector2( 70, 0 ), Vector2( 110, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.2, 2 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}

[node name="useitem" type="Node2D"]
modulate = Color( 1, 1, 1, 0.944444 )
material = SubResource( 2 )
rotation = -1.5708
scale = Vector2( 0.5, 0.5 )
z_index = 7
script = ExtResource( 3 )

[node name="use2" type="Sprite" parent="."]
use_parent_material = true
position = Vector2( 17.6516, 0 )
rotation = 1.5708
texture = ExtResource( 2 )
offset = Vector2( 10, 0 )
flip_h = true

[node name="use1" type="Sprite" parent="use2"]
use_parent_material = true
texture = ExtResource( 2 )
offset = Vector2( -10, 0 )

[node name="useanim" type="AnimationPlayer" parent="."]
autoplay = "New Anim"
"anims/New Anim" = SubResource( 3 )

[connection signal="animation_finished" from="useanim" to="." method="_on_useanim_animation_finished"]
