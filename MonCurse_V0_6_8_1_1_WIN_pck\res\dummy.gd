extends AnimatedSprite

func _ready():
	add_to_group("Turnends")

func set_start_position(locv):
	position = locv
	enemymovepoint = Vector2((locv.x - 128) + ((randi()%2) * 256),locv.y)
	startpoint = locv

onready var animspeed = (0.5+Playervariables.gameslowness)/(2)
func _end_turn():
	$enemyturn.start(0.3*randf()+Playervariables.gameslowness)
	yield($enemyturn,"timeout")
	set_physics_process(true)
	self.playing = true
	if enemymovepoint.x > startpoint.x:
		enemymovepoint.x = startpoint.x - 128
	else:
		enemymovepoint.x = startpoint.x + 128

var startpoint = Vector2(0,0)
var enemymovepoint = Vector2(0,0)
func _physics_process(_delta):
	position = lerp(position,enemymovepoint,((0.51+Playervariables.gameslowness)-animspeed)/(2+Playervariables.gameslowness*4.2))
	if (position-enemymovepoint).length() < 2:
		position = enemymovepoint
		set_physics_process(false)
		self.playing = false
