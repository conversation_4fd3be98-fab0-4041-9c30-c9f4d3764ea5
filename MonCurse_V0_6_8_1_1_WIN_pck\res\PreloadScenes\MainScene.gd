extends Node

const Main = preload("res://Main.tscn") #MainScene

const CumScreen = preload("res://effects/cumscreen.tscn") #MainScene

const movebarempty = preload("res://Assets/movebarempty.png")
const movebaremptybonus = preload("res://Assets/movebaremptybonus.png")
const movebarfill = preload("res://Assets/movebarfill.png")
const movebarfillbonus = preload("res://Assets/movebarfillbonus.png")

const GododLight = preload("res://Assets/lightgodod.png") #MainScene
const Lightnode = preload("res://LightNode.tscn") #MainScene

const monmusuforestop = preload("res://music/monmusu forest2op.ogg") #MainScene
const monmusuforestloop = preload("res://music/monmusu forest2loop.ogg") #MainScene

const Cloudspawn = preload("res://Background/foreground cloud.tscn") #MainScene
const Onibi = preload("res://effects/onibi.tscn") #MainScene, maybe move to foxgirl later?

const Fireflies = preload("res://effects/fireflies.tscn") #MainScene

const ResetEffect = preload("res://reseteffect.tscn") #MainScene
const ReloadEffect = preload("res://reloadeffect.tscn") #MainScene

const Friendly = preload("res://effects/friendly.tscn") #MainScene

const Unboxing = preload("res://effects/unboxing.tscn") #mainscene

const CorruptionParticlesSmall = preload("res://effects/corruption_particle_small.tscn") #MainScene
const CorruptionParticlesFinish = preload("res://effects/corruption_particle_finish.tscn") #MainScene

const deployable_over_handsize = preload("res://Assets/materials/Deployable_over_handsize.tres") #MainScene

const GodRay = preload("res://Background/godray.tscn") #MainScene
const FrustrationEffect = preload("res://effects/frustration.tscn") #MainScene
const DebuffHypno = preload("res://effects/hypno.tscn") #MainScene
const LightningEffect = preload("res://effects/lightning.tscn") #MainScene
const CorruptedText = preload("res://effects/corruptedtext.tscn") #MainScene
const CorruptionSmoke = preload("res://effects/corruptionsmoke.tscn") #MainScene
const LeafEffect = preload("res://effects/leaves.tscn")
#const Qadesminisprite = preload("res://Enemy/qades/qadesshopsprite.png") #Qades???? why is it here??
const Curseditem = preload("res://curseditem.tscn") #MainScene
const Graph = preload("res://damagegraph.tscn") #MainScene
const Hurtaura = preload("res://hurt.tscn") #MainScene
const Bird = preload("res://Background/bird.tscn") #MainScene
const Butterfly = preload("res://Background/butterfly.tscn") #MainScene
const Gecko = preload("res://Background/gecko.tscn") #MainScene
const Escalationscreen = preload("res://escalation.tscn") #MainScene
const Debuffsprite = preload("res://debuffsprite.tscn") #MainScene
const Talltree = preload("res://tree.tscn") #MainScene
const Bigtree = preload("res://bigtree.tscn") #MainScene
const Indicator = preload("res://indicator.tscn") #MainScene
const Hitmarker = preload("res://hitmarker.tscn") #MainScene
const Pickupsprite = preload("res://pickupsprite.tscn") #MainScene
const Attackeffect = preload("res://attackeffectmap.tscn") #MainScene
const Selector = preload("res://selector.tscn") #MainScene
const Endselector = preload("res://endselector.tscn") #MainScene
const DropIndicator = preload("res://dropindicator.tscn") #MainScene
const Enemy = preload("res://modularenemy.tscn") #MainScene
const NPC = preload("res://modularnpc.tscn") #MainScene
const divergence = preload("res://Assets/ui/divergence.png") #MainScene
const locationfog = preload("res://Background/locationfog.png") #MainScene
const goaltext = preload("res://goaltext.tscn") #MainScene
const Obj1 = preload("res://obj1.tscn") #MainScene
const Obj2 = preload("res://obj2.tscn") #MainScene
const Obj3 = preload("res://obj3.tscn") #MainScene
const Obj5 = preload("res://obj5.tscn") #MainScene
const Obstructionmap = preload("res://obstructionmap.tscn") #MainScene
const Block = preload("res://block.tscn") #MainScene
const Blockprevieweffect = preload("res://blockprevieweffect.tscn") #MainScene
const Traffic = preload("res://trafficlight.tscn") #MainScene
const Dummy = preload("res://dummy.tscn") #MainScene

const LockedResource = preload("res://Assets/ui/lockedresource.png") #MainScene
const LockedBoard = preload("res://Assets/ui/questboardsmalllocked.png") #MainScene


const floatingtext = preload("res://floatingtext.tscn") #MainScene
const tutorialcamera = preload("res://Assets/ui/tutorial camera drag.png") #MainScene
const tutorialtouch = preload("res://Assets/ui/tutorial camera touch.png") #MainScene
const tutorialhelp = preload("res://Assets/ui/tutorial camera helpmeplease.png") #MainScene

const InventoryButton = preload("res://Assets/ui/inventory.png") #MainScene
const InventoryButtonHover =  preload("res://Assets/ui/inventoryhover.png") #MainScene
const ConvoInventoryButton =  preload("res://Assets/ui/convov2inventorybutton.png") #MainScene
const ConvoInventoryButtonHover =  preload("res://Assets/ui/convov2inventorybuttonhover.png") #MainScene

const SoundRepeater = preload("res://soundrepeater.tscn") #mainscene

const KissEffect = preload("res://effects/kisseffect.tscn") #MainScene

const UseEffect = preload("res://effects/useitem.tscn") #MainScene
const Trash = preload("res://trash.tscn") #MainScene
const DebuffTrigger = preload("res://effects/debufftrigger.tscn") #MainScene
const HitParticles = preload("res://effects/hitparticles.tscn") #MainScene
const Particle0 = preload("res://effects/particle0.png") #MainScene
const Particle1 = preload("res://effects/particle1.png") #MainScene
const Particle2 = preload("res://effects/particle2.png") #MainScene
const Particle3 = preload("res://effects/particle3.png") #MainScene
const Particle4 = preload("res://effects/particle4.png") #MainScene
const Particle5 = preload("res://effects/particle5.png") #MainScene
const Particle6 = preload("res://effects/particle6.png") #MainScene
const RockParticleAlt = preload("res://effects/rock.png") #MainScene
const RockParticle = preload("res://effects/rock.png") #MainScene
const WoodParticle = preload("res://effects/wood.png") #MainScene
const WoodParticleAlt = preload("res://effects/woodalt.png") #MainScene

const Shopsign = preload("res://shopsign.tscn") #MainScene
const SparkleEffect = preload("res://effects/sparkle.tscn") #MainScene

const DebugPoint = preload("res://debugpoint.tscn") #MainScene

const defaultbackground = preload("res://Background/mainbackgroundtest.png") #MainScene

const movement_theme = preload("res://music/movement theme.ogg") #Mainscene #MUSIC
const monmusuforest = preload("res://music/monmusu forest.ogg") #Mainscene #MUSIC

#const deployableitem = preload("res://Assets/ui/deployablev2.png") #MainScene
#const deployableinnate = preload("res://Assets/ui/deployablev2spec.png") #MainScene
const deployable = preload("res://Assets/ui/deployabledouble.png") #MainScene
const deployablehover = preload("res://Assets/ui/deployabledoublehover.png") #MainScene
const deployablepassive = preload("res://Assets/ui/passivedeployable.png") #MainScene

#const hoofcon = "res://Assets/ui/debufficons/hoofcon.png" #MAINSCENE
#const flycon = preload("res://Assets/ui/debufficons/flycon.png") #MAINSCENE
#const convcon = preload("res://Assets/convoicon.png") #MAINSCENE

#const big_debuff_border = preload("res://Assets/ui/debufficons/bigdebuffborder.png") #MAINSCENE
#const big_debuff_border_fill = preload("res://Assets/ui/debufficons/bigdebuffborderfill.png") #MAINSCENE

func _ready():
	var filesdebuff = list_files_in_directory("res://Assets/ui/debufficons/")
	var files = filesdebuff
	for file in files:
		$ResourcePreloader.add_resource("RP",load(file))
	
func list_files_in_directory(path):
	var files = []
	var dir = Directory.new()
	dir.open(path)
	dir.list_dir_begin()

	while true:
		var file = dir.get_next()
		if file == "":
			break
		elif not file.begins_with(".") and file.ends_with(".png"):
			files.append(path+file)

	dir.list_dir_end()

	return files
