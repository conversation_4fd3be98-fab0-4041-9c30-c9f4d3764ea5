[gd_scene load_steps=4 format=2]

[ext_resource path="res://Assets/sfwwarning.png" type="Texture" id=1]
[ext_resource path="res://sfwwarning.gd" type="Script" id=2]

[sub_resource type="Animation" id=1]
resource_name = "sfwwarning"
length = 5.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5, 1, 1.5, 5 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 0, 0, 0, 0.752941 ), Color( 0.882353, 0.219608, 0.219608, 0.752941 ), Color( 0.176471, 0.290196, 0.815686, 0.603922 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}

[node name="sfwwarning" type="Sprite"]
self_modulate = Color( 0, 0, 0, 0.752941 )
texture = ExtResource( 1 )
script = ExtResource( 2 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
autoplay = "sfwwarning"
anims/sfwwarning = SubResource( 1 )
[connection signal="animation_finished" from="AnimationPlayer" to="." method="_on_AnimationPlayer_animation_finished"]
