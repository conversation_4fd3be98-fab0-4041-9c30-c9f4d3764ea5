[gd_scene load_steps=28 format=2]

[ext_resource path="res://DialogueArt/CG/gameovers/rambadend.gd" type="Script" id=1]
[ext_resource path="res://DialogueArt/CG/gameovers/ram/body lower.png" type="Texture" id=2]
[ext_resource path="res://DialogueArt/CG/gameovers/ram/body upper hold.png" type="Texture" id=3]
[ext_resource path="res://DialogueArt/CG/gameovers/ram/body head.png" type="Texture" id=4]
[ext_resource path="res://DialogueArt/CG/gameovers/ram/rules silhouette held.png" type="Texture" id=5]
[ext_resource path="res://DialogueArt/CG/gameovers/ram/expression open.png" type="Texture" id=6]
[ext_resource path="res://DialogueArt/CG/gameovers/ram/hair back.png" type="Texture" id=7]
[ext_resource path="res://DialogueArt/CG/gameovers/ram/body upper hold over.png" type="Texture" id=8]
[ext_resource path="res://Background/bedsheets.png" type="Texture" id=9]
[ext_resource path="res://DialogueArt/CG/gameovers/ram/body dick.png" type="Texture" id=10]
[ext_resource path="res://Assets/materials/hairhueshifter.shader" type="Shader" id=11]
[ext_resource path="res://DialogueArt/CG/gameovers/ram/steam dick.png" type="Texture" id=12]
[ext_resource path="res://DialogueArt/CG/gameovers/ram/steam body.png" type="Texture" id=13]
[ext_resource path="res://DialogueArt/CG/gameovers/ram/sheen.png" type="Texture" id=14]
[ext_resource path="res://DialogueArt/CG/gameovers/ram/effect cum.png" type="Texture" id=15]
[ext_resource path="res://DialogueArt/CG/gameovers/ram/effect splatter half.png" type="Texture" id=16]

[sub_resource type="ShaderMaterial" id=1]
shader = ExtResource( 11 )
shader_param/hue_shift = 0.9
shader_param/sat_mul = 1.2
shader_param/val_mul = 1.1

[sub_resource type="Gradient" id=5]
offsets = PoolRealArray( 0, 0.36129, 0.690323, 1 )
colors = PoolColorArray( 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=6]
gradient = SubResource( 5 )

[sub_resource type="ParticlesMaterial" id=7]
flag_disable_z = true
direction = Vector3( 0, -1, 0 )
gravity = Vector3( 0, 0, 0 )
initial_velocity = 5.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
color_ramp = SubResource( 6 )

[sub_resource type="Animation" id=2]
resource_name = "appear"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/onbed:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites/background:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("sprites/onbed:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1.8, 2 ),
"transitions": PoolRealArray( 0.5, 1.6, 1 ),
"update": 0,
"values": [ Vector2( 50, 200 ), Vector2( 0, -50 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=3]
resource_name = "appearbg"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/onbed:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites/background:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 1.6, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=4]
resource_name = "shake"
length = 0.5
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/onbed:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.2, 0.3, 0.5 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 5, 0 ), Vector2( 0, -5 ), Vector2( 0, 0 ), Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites/background:position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=8]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/onbed/steamdick:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( -20, -6 ) ]
}

[sub_resource type="Animation" id=9]
resource_name = "steam"
length = 2.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/onbed/steambody:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 20, -100 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites/onbed/steambody:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.4, 2 ),
"transitions": PoolRealArray( 1, 2, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}

[sub_resource type="Animation" id=10]
resource_name = "cumming"
length = 10.0
tracks/0/type = "value"
tracks/0/path = NodePath("onbed/dick/cum:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 3 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ false, true, false ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("splatter:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 3, 10 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 1,
"values": [ false, true, true, false ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("onbed/dick/cum:self_modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.2, 0.8, 1.1, 3 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0.470588 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("splatter:self_modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.2, 0.5, 1, 1.4, 3, 10 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 0.313726 ), Color( 1, 1, 1, 0.392157 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}

[sub_resource type="Animation" id=11]
resource_name = "hover"
length = 4.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1, 2, 3, 4 ),
"transitions": PoolRealArray( 1.5, 0.7, 1.3, 0.8, 1 ),
"update": 0,
"values": [ Vector2( 0, -138 ), Vector2( 0, -238 ), Vector2( 0, -138 ), Vector2( 0, -38 ), Vector2( 0, -138 ) ]
}

[node name="rambadend" type="CanvasLayer"]
layer = 4
script = ExtResource( 1 )

[node name="sprites" type="Node2D" parent="."]
position = Vector2( 0, -138 )

[node name="background" type="Sprite" parent="sprites"]
texture = ExtResource( 9 )
centered = false

[node name="onbed" type="Node2D" parent="sprites"]
modulate = Color( 1, 1, 1, 0 )
material = SubResource( 1 )
use_parent_material = true
position = Vector2( 50, 200 )

[node name="back" type="Sprite" parent="sprites/onbed"]
use_parent_material = true
texture = ExtResource( 7 )
centered = false
offset = Vector2( 203, 244 )

[node name="lower" type="Sprite" parent="sprites/onbed"]
use_parent_material = true
texture = ExtResource( 2 )
centered = false
offset = Vector2( 745, 675 )

[node name="upper" type="Sprite" parent="sprites/onbed"]
use_parent_material = true
texture = ExtResource( 3 )
centered = false
offset = Vector2( 310, 190 )

[node name="head" type="Sprite" parent="sprites/onbed"]
use_parent_material = true
texture = ExtResource( 4 )
centered = false

[node name="dick" type="Sprite" parent="sprites/onbed"]
visible = false
use_parent_material = true
position = Vector2( 910, 90 )
texture = ExtResource( 10 )
centered = false

[node name="sheen" type="Sprite" parent="sprites/onbed/dick"]
visible = false
texture = ExtResource( 14 )
centered = false

[node name="cum" type="Sprite" parent="sprites/onbed/dick"]
visible = false
self_modulate = Color( 1, 1, 1, 0 )
texture = ExtResource( 15 )
centered = false

[node name="silhouette" type="Sprite" parent="sprites/onbed"]
visible = false
texture = ExtResource( 5 )
centered = false
offset = Vector2( 435, 400 )

[node name="upperover" type="Sprite" parent="sprites/onbed"]
use_parent_material = true
texture = ExtResource( 8 )
centered = false
offset = Vector2( 620, 335 )

[node name="expression" type="Sprite" parent="sprites/onbed"]
use_parent_material = true
texture = ExtResource( 6 )
centered = false
offset = Vector2( 330, 265 )

[node name="steambody" type="Sprite" parent="sprites/onbed"]
visible = false
modulate = Color( 1, 1, 1, 0 )
texture = ExtResource( 13 )
centered = false
offset = Vector2( 0, 120 )

[node name="steamdick" type="Particles2D" parent="sprites/onbed"]
visible = false
position = Vector2( -20, -6 )
emitting = false
amount = 2
lifetime = 8.0
process_material = SubResource( 7 )
texture = ExtResource( 12 )

[node name="splatter" type="Sprite" parent="sprites"]
visible = false
self_modulate = Color( 1, 1, 1, 0 )
scale = Vector2( 2, 2 )
texture = ExtResource( 16 )
centered = false

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
anims/appear = SubResource( 2 )
anims/appearbg = SubResource( 3 )
anims/shake = SubResource( 4 )

[node name="steam" type="AnimationPlayer" parent="."]
playback_speed = 0.3
anims/RESET = SubResource( 8 )
anims/steam = SubResource( 9 )

[node name="cum" type="AnimationPlayer" parent="."]
root_node = NodePath("../sprites")
anims/cumming = SubResource( 10 )

[node name="hover" type="AnimationPlayer" parent="."]
root_node = NodePath("../sprites")
playback_speed = 0.05
anims/hover = SubResource( 11 )
