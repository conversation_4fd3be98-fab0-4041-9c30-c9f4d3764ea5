extends Sprite


onready var MinionOwner = get_parent().get_parent()
onready var MinionOwnerWeakRef = weakref(MinionOwner)

var explosive = false
var charge = 0
# Declare member variables here. Examples:
# var a = 2
# var b = "text"
const tilesize = 128

var lifetime = 5 #turns the torch lasts for
# Called when the node enters the scene tree for the first time.
func _ready():
	add_to_group("Torches")
	var lightarray = get_parent().get_parent().lightarray
	for i in range(lightarray.size()):
		if lightarray[i] == false:
			lightarray[i] = true
			lightnum = i
			break
	if explosive == false:
		$AnimatedSprite.set_self_modulate(Color(clamp(1-(Playervariables.torchlight*0.1),0,1),1,1))
		lifetime += Playervariables.torchlight *2
	else:
		lifetime = 4
		$AnimatedSprite.play("default")
		update_explosive()

func update_explosive():
	if $flash.is_playing() == false:
		$flash.play("charge")
	$AnimatedSprite.scale = Vector2(1.4,1.4) - Vector2(0.4,0.4)*charge
	$AnimatedSprite.position = Vector2(-(5+charge*0.5),78-(charge*3))

func disable_torch():
	lightnum = -1
	lifetime = -1

func update_torch():
	if lightnum > -1:
		get_parent().get_parent().mask_light(lightnum,sqrt(lifetime)*0.3,position,int(lifetime*0.6) + 3)

const wet_explosion = [Vector2(0,0),Vector2(-1,0),Vector2(1,0),Vector2(0,1),Vector2(0,-1),Vector2(1,1),Vector2(-1,-1),Vector2(-1,1),Vector2(1,-1)]
#var firstburn
var loc = Vector2(0,0)
var lightnum = -1
func torchburns():
#	if firstburn == lifetime:
	get_parent().get_parent().call("torch",position,lifetime,self,explosive)
#	else:
#		get_parent().get_parent().call("torch",position,lifetime,true,self)
#	yield(get_tree(),"idle_frame")
	lifetime += -1
	if explosive == true:
		$AnimatedSprite.set_self_modulate(Color(1,1,1))
		charge += 1
		if charge >= 3 or lifetime <= 0:
			if charge >= 3:
				$fusesfx/fuse.stop()
				$fusesfx.stop()
				loc = (position/tilesize) - Vector2(0.5,0.5)
				var torcharray = wet_explosion.duplicate()
				for entry in range(torcharray.size()):
					torcharray[entry] = loc + torcharray[entry]
				get_parent().get_parent().attacking(torcharray,Vector2(Playervariables.FORCE,3),self,[Vector2(Playervariables.KNOCKBACK,3)],[-1],4,"Explosive Blast")
				get_parent().get_parent().attacking(torcharray,Vector2(Playervariables.ENERGY,2),self,[Vector2(Playervariables.DROWSY,3)],[-1],4,"Explosive Energy")
				$explode.play()
				$flash.play("explode")
			if lightnum > -1:
				get_parent().get_parent().remove_torch(lightnum)
			remove_from_group("Torches")
			$AnimatedSprite.visible = false
			$expire.start(4)
		elif 3 > charge+lifetime:
			$AnimatedSprite.visible = false
			remove_from_group("Torches")
			$expire.start(4)
			$fusesfx/fuse.stop()
			$fusesfx.stop()
		else:
			update_explosive()
	else:
		$AnimatedSprite.scale = Vector2(1,1)*0.25*lifetime
		if lifetime <= 0:
			if lightnum > -1:
				get_parent().get_parent().remove_torch(lightnum)
			queue_free()
		else:
#		elif lightnum > -1:
			update_torch()
#			get_parent().get_parent().mask_light(lightnum,sqrt(lifetime)*0.3,position,lifetime)


func _on_expire_timeout():
	queue_free()
