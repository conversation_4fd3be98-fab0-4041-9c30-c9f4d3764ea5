[gd_scene load_steps=5 format=2]

[ext_resource path="res://effects/lightning.gd" type="Script" id=1]
[ext_resource path="res://Assets/selectorpathwhite.png" type="Texture" id=4]

[sub_resource type="Gradient" id=3]
colors = PoolColorArray( 0.752941, 0.764706, 0.776471, 0.541176, 0.278431, 0.341176, 0.419608, 0 )

[sub_resource type="Animation" id=1]
resource_name = "zap"
length = 1.3
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = false
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 1.3 ),
"transitions": PoolRealArray( 0.5, 2, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:gradient:colors")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.3, 0.8, 1.3 ),
"transitions": PoolRealArray( 1, 1, 0.6, 1 ),
"update": 0,
"values": [ PoolColorArray( 0.752941, 0.764706, 0.776471, 0.541176, 0.278431, 0.341176, 0.419608, 0 ), PoolColorArray( 0.752941, 0.764706, 0.776471, 1, 0.87451, 0.890196, 0.901961, 1 ), PoolColorArray( 0.447059, 0.6, 0.752941, 0, 0.87451, 0.890196, 0.901961, 0.72549 ), PoolColorArray( 0.752941, 0.764706, 0.776471, 0, 0.87451, 0.890196, 0.901961, 0 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("light:self_modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.3, 1.3 ),
"transitions": PoolRealArray( 2, 0.5, 1 ),
"update": 0,
"values": [ Color( 0.407843, 0.509804, 0.666667, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}

[node name="lightning" type="Line2D"]
z_index = 9
width = 6.0
default_color = Color( 1, 1, 1, 1 )
gradient = SubResource( 3 )
antialiased = true
script = ExtResource( 1 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
anims/zap = SubResource( 1 )

[node name="light" type="Sprite" parent="."]
self_modulate = Color( 0.407843, 0.509804, 0.666667, 0 )
scale = Vector2( 2, 2 )
z_index = -1
texture = ExtResource( 4 )

[connection signal="animation_finished" from="AnimationPlayer" to="." method="_on_AnimationPlayer_animation_finished"]
