extends AudioStreamPlayer2D

const possible_shifts = PoolStringArray(["res://zapsplat/zapsplat_foley_footstep_single_female_walking_leather_shoe_on_wood_012_50949.ogg","res://zapsplat/zapsplat_foley_footstep_single_female_walking_leather_shoe_on_wood_018_50955.ogg","res://zapsplat/zapsplat_foley_footstep_single_female_walking_leather_shoe_on_wood_019_50956.ogg","res://zapsplat/zapsplat_foley_footstep_barefoot_single_wooden_step_005_19184.ogg"])

# Called when the node enters the scene tree for the first time.
func _ready():
	randomize()
	self.stream = load(possible_shifts[randi()%possible_shifts.size()])
	self.pitch_scale = 1.35+randf()*0.35
	self.play()


func _on_knightshifter_finished():
	queue_free()
