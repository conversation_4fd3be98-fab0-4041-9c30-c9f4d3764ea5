[gd_scene load_steps=11 format=2]

[ext_resource path="res://font/Verily-Serif-Mono/VerilySerifMono.otf" type="DynamicFontData" id=1]
[ext_resource path="res://Assets/blacksquare.png" type="Texture" id=2]
[ext_resource path="res://Assets/ui/stonebarunder.png" type="Texture" id=3]
[ext_resource path="res://Assets/ui/stonebarover.png" type="Texture" id=4]
[ext_resource path="res://Assets/ui/monmusulowerui.png" type="Texture" id=5]
[ext_resource path="res://RewardPicker.gd" type="Script" id=6]
[ext_resource path="res://font/Audiowide-Regular.ttf" type="DynamicFontData" id=7]

[sub_resource type="DynamicFont" id=146]
size = 22
outline_size = 2
outline_color = Color( 0, 0, 0, 0.705882 )
font_data = ExtResource( 1 )

[sub_resource type="DynamicFont" id=147]
size = 24
outline_size = 2
outline_color = Color( 0, 0, 0, 0.705882 )
font_data = ExtResource( 7 )

[sub_resource type="Animation" id=148]
resource_name = "reward"
length = 0.4
tracks/0/type = "value"
tracks/0/path = NodePath("board:anchor_bottom")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ 0.9, 0.8 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("board:anchor_top")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.2 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ 0.27, 0.2 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("board:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.2 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.666667 ), Color( 1, 1, 1, 1 ) ]
}

[node name="RewardPicker" type="CanvasLayer"]
layer = 8
script = ExtResource( 6 )

[node name="backing" type="TextureRect" parent="."]
self_modulate = Color( 1, 1, 1, 0.705882 )
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 0
texture = ExtResource( 2 )
expand = true

[node name="board" type="NinePatchRect" parent="backing"]
self_modulate = Color( 0.752941, 0.752941, 0.752941, 1 )
anchor_left = 0.2
anchor_top = 0.2
anchor_right = 0.8
anchor_bottom = 0.8
texture = ExtResource( 5 )
patch_margin_left = 128
patch_margin_top = 74
patch_margin_right = 128
patch_margin_bottom = 74
axis_stretch_horizontal = 2
axis_stretch_vertical = 2

[node name="rewardploy" type="HBoxContainer" parent="backing/board"]
anchor_left = -1.3
anchor_top = 0.5
anchor_right = 2.3
anchor_bottom = 0.5
grow_horizontal = 2
grow_vertical = 2
custom_constants/separation = 40
alignment = 1

[node name="villagebutton" type="TextureButton" parent="backing/board"]
modulate = Color( 0.784314, 0.784314, 1, 1 )
anchor_left = 0.2
anchor_top = 1.05
anchor_right = 0.8
anchor_bottom = 1.2
texture_normal = ExtResource( 4 )
texture_hover = ExtResource( 3 )
expand = true

[node name="duplicatelabel" type="Label" parent="backing/board/villagebutton"]
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
custom_fonts/font = SubResource( 146 )
text = "Ignore & Discard"
align = 1
valign = 1

[node name="acceptbutton" type="TextureButton" parent="backing/board"]
visible = false
modulate = Color( 0.784314, 0.784314, 1, 1 )
anchor_left = 0.2
anchor_top = 0.8
anchor_right = 0.8
anchor_bottom = 0.95
texture_normal = ExtResource( 4 )
texture_hover = ExtResource( 3 )
expand = true

[node name="duplicatelabel" type="Label" parent="backing/board/acceptbutton"]
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
custom_fonts/font = SubResource( 146 )
text = "Accept & Continue"
align = 1
valign = 1

[node name="Label" type="Label" parent="backing/board"]
anchor_left = 0.1
anchor_top = 0.05
anchor_right = 0.9
anchor_bottom = 0.05
custom_colors/font_color = Color( 0.843137, 0.843137, 0.843137, 1 )
custom_fonts/font = SubResource( 147 )
text = "Choose One Skill:"
align = 1
valign = 1

[node name="Label2" type="Label" parent="backing/board"]
self_modulate = Color( 0.705882, 0.705882, 0.705882, 1 )
anchor_left = 0.1
anchor_top = 0.9
anchor_right = 0.9
anchor_bottom = 0.95
grow_vertical = 2
custom_colors/font_color = Color( 0.843137, 0.843137, 0.843137, 1 )
custom_fonts/font = SubResource( 147 )
text = "Lasts until end of mission"
align = 1
valign = 1

[node name="appear" type="AnimationPlayer" parent="backing"]
anims/reward = SubResource( 148 )

[connection signal="pressed" from="backing/board/villagebutton" to="." method="_on_villagebutton_pressed"]
[connection signal="pressed" from="backing/board/acceptbutton" to="." method="_on_villagebutton_pressed"]
