extends Can<PERSON>Layer

#OFFSETS:
#X then Y
#CUM: 885x565y
#RULES DICK(1/2): 885x565y (Same as cum)
#RULES HANDS: 775X,685Y
#RULES LEGS (And shadow): 665X, 685Y
#SILHOUETTE CAT LEFT: 500X, 445Y
#SILHOUETTE CAT MIDDLE: 865X, 405Y
#SILHOUETTE CAT RIGHT: 1160 X, 530Y
#SILHOUETTE RULES: 790X, 450Y
#SWEAT: 715x, 320y
#VOICE CAT TAIL: 825X, 680Y
#VOICE CAT EARS: 790X, 125Y (same as head)
#VOICE EXPRESSION (1/2/3): 870X, 255Y
#VOICE HEAD (1/2): 790X, 125Y
#VOICE LEGS: 490x, 430y
#VOICE MOUTH (1/2/3): 920X, 330Y
#VOICE PUSSY (1/2): 885x565y (Same as cum)
#VOICE TITS: 715X,345Y

const basesize = Vector2(1920,1080)
func _ready():
	var _c = get_tree().root.connect("size_changed", self, "_on_viewport_size_changed")
	$player.visible = false
	$voice.visible = false
	$both.visible = false
	_on_viewport_size_changed()
#	set_player_hsv()

#var skinnodes = ["player/legs","player/dick","player/hands"]
#func set_player_hsv():
#	var colourarray = Playervariables.baseplayercolourarray
#	var skincolHSV = Playervariables.newcolorarrayskin[colourarray[2]]
##	for node in skinnodes: #I really don't need to do this for every node, it's redundant...
#	$player/legs.material.set_shader_param("hue_shift",skincolHSV.x)
#	$player/legs.material.set_shader_param("sat_mul",skincolHSV.y)
#	$player/legs.material.set_shader_param("val_mul",skincolHSV.z)

var sized_nodes = ["backgrounds","Skeleton2D","silhouette","player","voice","both"]
var firstrun = true
var recentsizechange = false
func _on_viewport_size_changed():
	if recentsizechange == false:
		recentsizechange = true #this makes it so it only updates the viewport every 4 frames
		if firstrun == false:
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
		else:
			firstrun = false
		var proportion = get_parent().get_viewport_rect().size/basesize
		var min_proportion = min(proportion.x,proportion.y)
		var offset_x
		if proportion.y < proportion.x:
			offset_x = $backgrounds/day.texture.get_size().x * (proportion.x - proportion.y) * 0.5
		else:
			offset_x = 0
		for nodestring in sized_nodes:
			var node = get_node(nodestring)
			node.scale = Vector2(min_proportion,min_proportion)
			node.position.x = offset_x
		recentsizechange = false

const pussyspread = preload("res://DialogueArt/CG/gameovers/cat/voice pussy spread.png")
const pussyout = preload("res://DialogueArt/CG/gameovers/cat/voice pussy out.png")
const dickin = preload("res://DialogueArt/CG/gameovers/cat/rules dick in.png")
const dickout = preload("res://DialogueArt/CG/gameovers/cat/rules dick out.png")
const headears = preload("res://DialogueArt/CG/gameovers/cat/voice head catears.png")
const head = preload("res://DialogueArt/CG/gameovers/cat/voice head.png")

const expression1 = preload("res://DialogueArt/CG/gameovers/cat/voice expression small.png")
const expression2 = preload("res://DialogueArt/CG/gameovers/cat/voice expression medium.png")
const expression3 = preload("res://DialogueArt/CG/gameovers/cat/voice expression big.png")
const mouth1 = preload("res://DialogueArt/CG/gameovers/cat/voice mouth small.png")
const mouth2 = preload("res://DialogueArt/CG/gameovers/cat/voice mouth medium.png")
const mouth3 = preload("res://DialogueArt/CG/gameovers/cat/voice mouth big.png")

#var lockface = false
var stuckinside = false
enum part{MOUTH,EXPRESSION,DICKPUSSY,CAT}
enum mouth{SMALL,MEDIUM,BIG}
enum expression{BIG,MEDIUM,SMALL}
enum dickpussy{OUT,IN}
enum cat{NONE,EARS,TAIL}
func switch_texture(area = 0,num = 0):
	match area:
		part.MOUTH: #mouth
#			if lockface == true:
#				num = 2
			if num == mouth.SMALL:
				$voice/mouth.texture = mouth1
			elif num == mouth.MEDIUM:
				$voice/mouth.texture = mouth2
			else:
				$voice/mouth.texture = mouth3
		part.EXPRESSION: #expression
#			if lockface == true:
#				num = 2
			if num == expression.SMALL:
				if randf() > 0.2 and targetspeed > 1:
					$voice/expression.texture = expression1
				else:
					$voice/expression.texture = expression3
			elif num == expression.MEDIUM:
				if targetspeed < 1.4:
					$voice/expression.texture = expression2
				else:
					$voice/expression.texture = expression3
			else:
				$voice/expression.texture = expression3
		part.DICKPUSSY: #dickpussy
			if num == dickpussy.OUT and stuckinside == false:
#				$player/dick.z_index = 2
				$player/dick.texture = dickout
				$voice/pussy.texture = pussyout
			else:
#				$player/dick.z_index = 1
				$player/dick.texture = dickin
				$voice/pussy.texture = pussyspread
		part.CAT: #cat features
			if num == cat.NONE:
				$voice/head.texture = head
			elif num == cat.EARS:
				$voice/head.texture = headears
			elif num == cat.TAIL:
				$voice/cattail.visible = true
		_:
			pass

func action(num = 0):
	match num:
		16: #reset
			targetspeed = 1
			stuckinside = false
			switch_texture(part.MOUTH,mouth.SMALL)
			switch_texture(part.EXPRESSION,expression.BIG)
			switch_texture(part.DICKPUSSY,dickpussy.OUT)
			switch_texture(part.CAT,cat.NONE)
			$voice/cattail.visible = false
			currentanimation = "idle"
			_on_voiceanims_animation_finished("")
			$player.visible = false
			$voice.visible = false
			$both.visible = false
#			if $voiceanims.is_playing() == false:
#				$voiceanims.play(currentanimation)
		17:
			$backgrounds/AnimationPlayer.play("nighttime")
		0: 
#			$silhouette.visible = true
			$silhouette/silhouettesfade.play("appear")
		1:
			#$silhouette.visible = false
			$silhouette/silhouettesfade.play_backwards("appear")
		2: 
			$otheranims.play("black")
			$player.visible = true
			$voice.visible = true
			$both.visible = true
			$both/sweat.visible = false
			$both/cum.visible = false
		3:
#			stuckinside = false
#			switch_texture(part.MOUTH,mouth.SMALL)
#			switch_texture(part.EXPRESSION,expression.BIG)
#			switch_texture(part.DICKPUSSY,dickpussy.OUT)
#			switch_texture(part.CAT,cat.NONE)
#			$voice/cattail.visible = false
#			$voiceanims.play("idle")
			currentanimation = "idle"
			_on_voiceanims_animation_finished("")
#			if $voiceanims.is_playing() == false:
#				$voiceanims.play(currentanimation)
			$otheranims.play("white")
			$player.visible = true
			$voice.visible = true
			$both.visible = true
		4:
			$both/sweat.visible = true
		5: #idle -> slow
#			$voiceanims.play("slow")
#			$voiceanims.playback_speed = max(2,$voiceanims.playback_speed) #to get to the next anim faster
			currentanimation = "slow"
			_on_voiceanims_animation_finished("")
#			if $voiceanims.is_playing() == false:
#				$voiceanims.play(currentanimation)
		6: #cum
#			$voiceanims.play("idle")
			targetspeed = 1
#			lockface = false
			currentanimation = "idle"
			_on_voiceanims_animation_finished("")
#			if $voiceanims.is_playing() == false:
#				$voiceanims.play(currentanimation)
			$both/materialanims.play("appear")
			switch_texture(part.EXPRESSION,expression.MEDIUM)
			switch_texture(2,1)
			stuckinside = true
		7: #slow->fast
#			$voiceanims.playback_speed = max(2,$voiceanims.playback_speed) #to get to the next anim faster
			currentanimation = "fast"
			_on_voiceanims_animation_finished("")
#			if $voiceanims.is_playing() == false:
#				$voiceanims.play(currentanimation)
		8: #cat-ears appear
			switch_texture(3,1)
		9: #cat-tail appears
			switch_texture(3,2)
		10:
			$voiceanims.advance(99)
			$voiceanims.stop()
		11: #speed 1
			targetspeed = 1
		12: #speed 2
			currentanimation = "fast"
			targetspeed = 1.5
			_on_voiceanims_animation_finished("")
#			if $voiceanims.is_playing() == false:
#				$voiceanims.play(currentanimation)
		13: #speed 4
			stuckinside = true
			currentanimation = "fast"
			targetspeed = 2
#			lockface = true
			_on_voiceanims_animation_finished("")
#			if $voiceanims.is_playing() == false:
#				$voiceanims.play(currentanimation)
		14:
			$otheranims.play_backwards("white")
		15:
			$otheranims.play_backwards("black")
		_: 
			print("Error, no action number:" +str(num) + "... in catbadend1")

var targetspeed = 1
var currentanimation = "idle"
func _on_voiceanims_animation_finished(_anim_name):
	$voiceanims.playback_speed = targetspeed
	$voiceanims.play(currentanimation)
#	print("status report on animation DUE TO ANIMATION FINISH:"+str($voiceanims.get_current_animation()))
