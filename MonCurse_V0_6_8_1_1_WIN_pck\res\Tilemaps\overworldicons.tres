[gd_resource type="TileSet" load_steps=2 format=2]

[ext_resource path="res://Tilemaps/isometriclandscape icons.png" type="Texture" id=1]

[resource]
0/name = "isometriclandscape icons.png 0"
0/texture = ExtResource( 1 )
0/tex_offset = Vector2( 85, 102 )
0/modulate = Color( 1, 1, 1, 1 )
0/region = Rect2( 0, 0, 96, 96 )
0/tile_mode = 0
0/occluder_offset = Vector2( 0, 0 )
0/navigation_offset = Vector2( 0, 0 )
0/shape_offset = Vector2( 0, 0 )
0/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
0/shape_one_way = false
0/shape_one_way_margin = 1.0
0/shapes = [ {
"autotile_coord": Vector2( 0, 0 ),
"one_way": false,
"one_way_margin": 1.0,
"shape": null,
"shape_transform": Transform2D( 1, 0, 0, 1, 0, 0 )
} ]
0/z_index = 0
1/name = "isometriclandscape icons.png 1"
1/texture = ExtResource( 1 )
1/tex_offset = Vector2( 70, 138 )
1/modulate = Color( 1, 1, 1, 1 )
1/region = Rect2( 96, 0, 128, 64 )
1/tile_mode = 0
1/occluder_offset = Vector2( 0, 0 )
1/navigation_offset = Vector2( 0, 0 )
1/shape_offset = Vector2( 0, 0 )
1/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
1/shape_one_way = false
1/shape_one_way_margin = 0.0
1/shapes = [  ]
1/z_index = 0
2/name = "isometriclandscape icons.png 2"
2/texture = ExtResource( 1 )
2/tex_offset = Vector2( 50, 107 )
2/modulate = Color( 1, 1, 1, 1 )
2/region = Rect2( 224, 0, 160, 96 )
2/tile_mode = 0
2/occluder_offset = Vector2( 0, 0 )
2/navigation_offset = Vector2( 0, 0 )
2/shape_offset = Vector2( 0, 0 )
2/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
2/shape_one_way = false
2/shape_one_way_margin = 0.0
2/shapes = [  ]
2/z_index = 0
3/name = "isometriclandscape icons.png 3"
3/texture = ExtResource( 1 )
3/tex_offset = Vector2( 100, 140 )
3/modulate = Color( 1, 1, 1, 1 )
3/region = Rect2( 96, 64, 64, 64 )
3/tile_mode = 0
3/occluder_offset = Vector2( 0, 0 )
3/navigation_offset = Vector2( 0, 0 )
3/shape_offset = Vector2( 0, 0 )
3/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
3/shape_one_way = false
3/shape_one_way_margin = 0.0
3/shapes = [  ]
3/z_index = 0
4/name = "isometriclandscape icons.png 4"
4/texture = ExtResource( 1 )
4/tex_offset = Vector2( 0, 32 )
4/modulate = Color( 1, 1, 1, 1 )
4/region = Rect2( 0, 128, 256, 192 )
4/tile_mode = 0
4/occluder_offset = Vector2( 0, 0 )
4/navigation_offset = Vector2( 0, 0 )
4/shape_offset = Vector2( 0, 0 )
4/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
4/shape_one_way = false
4/shape_one_way_margin = 0.0
4/shapes = [  ]
4/z_index = 0
5/name = "isometriclandscape icons.png 5"
5/texture = ExtResource( 1 )
5/tex_offset = Vector2( 95, 134 )
5/modulate = Color( 1, 1, 1, 1 )
5/region = Rect2( 160, 64, 64, 64 )
5/tile_mode = 0
5/occluder_offset = Vector2( 0, 0 )
5/navigation_offset = Vector2( 0, 0 )
5/shape_offset = Vector2( 0, 0 )
5/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
5/shape_one_way = false
5/shape_one_way_margin = 0.0
5/shapes = [  ]
5/z_index = 0
7/name = "isometriclandscape icons.png 7"
7/texture = ExtResource( 1 )
7/tex_offset = Vector2( 32, 161 )
7/modulate = Color( 1, 1, 1, 1 )
7/region = Rect2( 256, 96, 224, 96 )
7/tile_mode = 0
7/occluder_offset = Vector2( 0, 0 )
7/navigation_offset = Vector2( 0, 0 )
7/shape_offset = Vector2( 0, 0 )
7/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
7/shape_one_way = false
7/shape_one_way_margin = 0.0
7/shapes = [  ]
7/z_index = 0
8/name = "isometriclandscape icons.png 8"
8/texture = ExtResource( 1 )
8/tex_offset = Vector2( 0, 32 )
8/modulate = Color( 1, 1, 1, 1 )
8/region = Rect2( 256, 192, 256, 192 )
8/tile_mode = 0
8/occluder_offset = Vector2( 0, 0 )
8/navigation_offset = Vector2( 0, 0 )
8/shape_offset = Vector2( 0, 0 )
8/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
8/shape_one_way = false
8/shape_one_way_margin = 0.0
8/shapes = [  ]
8/z_index = 0
9/name = "isometriclandscape icons.png 9"
9/texture = ExtResource( 1 )
9/tex_offset = Vector2( 70, 120 )
9/modulate = Color( 1, 1, 1, 1 )
9/region = Rect2( 384, 0, 128, 96 )
9/tile_mode = 0
9/occluder_offset = Vector2( 0, 0 )
9/navigation_offset = Vector2( 0, 0 )
9/shape_offset = Vector2( 0, 0 )
9/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
9/shape_one_way = false
9/shape_one_way_margin = 0.0
9/shapes = [  ]
9/z_index = 0
10/name = "isometriclandscape icons.png 10"
10/texture = ExtResource( 1 )
10/tex_offset = Vector2( 0, 128 )
10/modulate = Color( 1, 1, 1, 1 )
10/region = Rect2( 0, 320, 256, 64 )
10/tile_mode = 0
10/occluder_offset = Vector2( 0, 0 )
10/navigation_offset = Vector2( 0, 0 )
10/shape_offset = Vector2( 0, 0 )
10/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
10/shape_one_way = false
10/shape_one_way_margin = 0.0
10/shapes = [  ]
10/z_index = 0
