extends Node2D


func announce(namestring,friendly=false,delay=0):
	if delay > 0:
		$Label.visible = false
		$Label/Timer.start(delay)
		yield($Label/Timer,"timeout")
		$Label.visible = true
	$Label.set_text(namestring)
	$Label/Label.set_text(namestring)
	if friendly == false:
		$Label.set_modulate(Color(1,1,1,1))
	$Label/AnimationPlayer.play("attack")



func _on_AnimationPlayer_animation_finished(_anim_name):
	queue_free()
