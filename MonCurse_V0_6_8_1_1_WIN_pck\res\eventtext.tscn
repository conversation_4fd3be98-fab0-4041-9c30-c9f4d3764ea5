[gd_scene load_steps=4 format=2]

[ext_resource path="res://font/OpenSans-Bold.ttf" type="DynamicFontData" id=1]

[sub_resource type="DynamicFont" id=1]
size = 28
font_data = ExtResource( 1 )

[sub_resource type="Animation" id=2]
resource_name = "newtext"
length = 0.5
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}

[node name="eventtext" type="Label"]
margin_right = 984.0
margin_bottom = 39.0
custom_colors/font_color = Color( 1, 1, 1, 1 )
custom_fonts/font = SubResource( 1 )
align = 2
valign = 1

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
anims/newtext = SubResource( 2 )
