{"customization.json": {"000.CHOICE": ["<PERSON>den", "<PERSON>ben ä<PERSON>n", "<PERSON>n ä<PERSON>n"], "000.char": "Regeln", "000": ["Von hier aus kann ich auf Anpassungsfunktionen zugreifen. "], "001.char": "Erzählung", "001": [""], "002.char": "Erzählung", "002": [""]}, "endcatmission.json": {"100": ["<PERSON>ätten Sie irgendwelche Beschwerden erlitten, hätte ich mein Bestes getan, um sie zu beseitigen. "], "101": ["<PERSON>ie auch immer, ich werde die Anrufleitung eine Weile überwachen, ich muss hier sein, um alle Nachrichten abzufangen."], "102": ["<PERSON><PERSON> mehr?"], "103": ["Es war nur eine kleine Aufgabe, ich wusste nicht, dass es so gefährlich sein würde! "], "104": ["Hi<PERSON>, eine <PERSON>! "], "105": ["Das bedeutet nicht, dass Si<PERSON> toben können, selbst wenn Si<PERSON> jetzt frei sind. "], "1060": ["Der Endlosmodus ist jetzt freigeschaltet. Sie finden ihn über den Wegweiser in der Nähe der Galerie."], "000.char": "Stimme", "000": ["Du bist zurück! "], "001.char": "Stimme... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Katzenschwanz, <PERSON><PERSON><PERSON> hat Katzenohren, <PERSON>pieler hat Katzenpfoten, ", "001": ["Da draußen war eine Söldnerzelle! "], "001.1.char": "Stimme", "001.1": ["Ah. "], "001.2.char": "Stimme", "001.2": [0, ", du warst die ganze Zeit ein Katzenmädchen? "], "001.3.char": "Stimme", "001.3": ["Damit siehst du lächerlich aus. "], "002.char": "Stimme... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Katzenschwanz, <PERSON><PERSON><PERSON> hat Katzenohren, <PERSON>pieler hat Katzenpfoten, ", "002": ["Du bist überraschend fähig? "], "002.1.char": "Regeln", "002.1": ["Nya? "], "002.2.char": "Regeln", "002.2": ["Nyo?! "], "002.3.char": "Regeln", "002.3": ["Ich wollte nicht! "], "003.char": "Stimme... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Katzenschwanz, <PERSON><PERSON><PERSON> hat Katzenohren, <PERSON>pieler hat Katzenpfoten, ", "003": ["<PERSON><PERSON><PERSON><PERSON>, es mir zu sagen, wenn <PERSON><PERSON>gen, sich komisch zu fühlen, okay? "], "003.1.char": "Stimme... alt unter Bedingungen: <PERSON>pieler hat Katzenpfoten, ", "003.1": ["'Nya'? "], "003.1.1.char": "Stimme", "003.1.1": ["'Nya'? "], "003.2.char": "Stimme... alt unter Bedingungen: <PERSON>pieler hat Katzenpfoten, ", "003.2": ["Glücklicherweise hat sich das Leiden noch nicht in Ihren albernen Kopf eingenistet."], "003.2.1.char": "Stimme", "003.2.1": ["Es liegt an diesen Handschuhen, nicht wahr? "], "003.3.char": "Stimme", "003.3": ["<PERSON><PERSON> lassen Sie verdächtige Gegenstände in Ruhe! "], "004.char": "Stimme... alt unter Bedingungen: <PERSON>pieler hat Katzenpfoten, ", "004": ["Mmm. "], "004.1.char": "Stimme", "004.1": ["<PERSON>uerst lasst uns die Pfoten los. "], "005.char": "Stimme... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Katzenschwanz, <PERSON><PERSON><PERSON> hat Katzenohren, ", "005": ["Die Bindungen sind ziemlich eng, also lassen Sie sie unbedingt in Ruhe! "], "005.1.char": "Stimme", "005.1": ["Als ich dich zum ersten Mal fand, hattest du einen Makel in dir, der deine Titten ungefähr so ​​groß machte."], "005.2.char": "Stimme", "005.2": ["Als ich dich zum ersten Mal fand, hattest du einen Makel in dir, der deine Titten ungefähr so ​​groß machte."], "006.char": "Stimme", "006": ["Ich kann diesem Maß an Neko-Katzen-Korruption in dir widerstehen, es ist der Bimbo-Saft, der dir aufgemotzt wurde, der auf einer ganz anderen Ebene ist ..."], "007.char": "Stimme", "007": ["<PERSON>zt sitzen Si<PERSON> still, das sollte nicht schaden."], "008.char": "Regeln... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Katzenschwanz, ", "008": ["Nnmh?!"], "008.1.char": "Regeln", "008.1": ["Nyaa?!"], "009.char": "Erzählung", "009": ["..."], "010.char": "Stimme", "010": ["Bweh. "], "100.char": "Stimme", "101.char": "Stimme", "102.char": "Regeln... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Katzenschwanz, ", "103.char": "Stimme", "104.char": "Stimme", "105.char": "Stimme", "1060.char": "Erzählung"}, "galleryconfirm.json": {"100": ["Aufzeichnungen über das schlechte Ende der Ram Girls. "], "000.CHOICE": ["Zurückkehren"], "000.char": "Erzählung... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Cat Ending 0 g<PERSON><PERSON>, ", "000": ["Aufzeichnungen über das schlechte Ende der Cat Knights. "], "000.1.CHOICE": ["Zurückkehren", "Cat-Girl-<PERSON><PERSON>"], "000.1.char": "Erzählung", "000.1": ["Aufzeichnungen über das schlechte Ende der Cat Knights. "], "100.CHOICE": ["Zurückkehren"], "100.char": "Erzählung... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat <PERSON> Ending 0 gesehen, <PERSON><PERSON><PERSON> hat <PERSON> Ending 1 gesehen, ", "100.1.CHOICE": ["Zurückkehren", "Ram-Girl-<PERSON><PERSON>"], "100.1.char": "Erzählung... alt unter Bedingungen: <PERSON><PERSON>ler hat Ram Ending 1 gesehen, ", "100.1": ["Aufzeichnungen über das schlechte Ende der Ram Girls. "], "100.1.1.CHOICE": ["Zurückkehren", "Ram-Girl-<PERSON><PERSON>", "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"], "100.1.1.char": "Erzählung", "100.1.1": ["Aufzeichnungen über das schlechte Ende der Ram Girls. "], "100.2.CHOICE": ["Zurückkehren", "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"], "100.2.char": "Erzählung", "100.2": ["Aufzeichnungen über das schlechte Ende der Ram Girls. "]}, "levelselect1confirm.json": {"100": ["Hier können Sie die Missionen spielen, in denen wir das Kommunikationsrelais einrichten. "], "200": ["Voice muss einiges erklären."], "300": ["Die Startmissionen sind vorbei! "], "500": ["Als ich mich umdrehe, steht dort eine Tür, deren <PERSON> kaputt ist und die nur einen Spaltbreit geöffnet ist. ", 0, " klopft darauf, ohne Antwort."], "501": ["Ich komme herein."], "502": [0, "?! "], "503": ["Ich sagte, ich komme rein! "], "504": ["Das ist auch deine <PERSON>. "], "505": ["... Die Einheimischen mögen mich nicht? "], "506": ["Kann man <PERSON><PERSON><PERSON> wirklich nicht von uns unterscheiden? "], "507": ["Wie Sie g<PERSON>hen haben, machen uns diese Monster Jagd. "], "508": ["Ich habe gehört, dass die reinsten Körper als Zuchtvieh verwendet werden."], "509": ["Das war nicht immer so. "], "510": ["Angesi<PERSON>s der Tatsache, dass sie hierhergekommen ist, um gegen mich zu kämpfen, wussten wir es nicht. "], "511": ["<PERSON><PERSON> ist schwer, sich zu konzen<PERSON>eren, wenn man diese riesigen Titten zur Schau stellt ..."], "512": ["Ihre Hoheit hat die ganze Zeit über Befehle entgegengenommen und sich vor einer höheren Autorität gebeugt. "], "513": ["Du schickst mich auf eine andere Mission?"], "514": ["NEIN?! "], "515": ["Gegen diese Autorität haben wir keine Chance. "], "516": ["<PERSON>s gibt immer noch viele Naman im ganzen Land, die festhalten. "], "517": ["Du musst mich verlassen und eine überlebende Festung finden. "], "518": ["Noch eine Mission... <PERSON>ch bin müde, lass mich schlafen."], "519": ["Hä? "], "520": ["Eine unermessliche Wärme hat das Bett durchdrungen, Voices Körper ist alles andere als kalt."], "521": ["<PERSON>gal wie ", 0, " verschiebt sich unter der Decke, Marshmallow-Weichheit umhüllt."], "522": ["Hey- Hey! "], "523": ["Zzz..."], "524": ["<PERSON><PERSON><PERSON>, wirklich. "], "525": ["Da hilft nichts.  ", 0, "."], "1300": ["<PERSON><PERSON><PERSON><PERSON> Si<PERSON> wissen, was los ist? "], "1301": ["Hier ist ein Geheimnis für Sie. "], "1302": ["Also. "], "1303": ["Wir arbeiten immer noch an einem neuen Dorf, neuen Gebieten. "], "1304": ["<PERSON>n Ihre Brust größer wäre, könnten Si<PERSON> es sogar schon spüren."], "1305": ["Wenn Ihnen das Spiel gefällt und Sie seine Entwicklung unterstützen möchten, schauen <PERSON> sich Patreon an!"], "1306": ["Dort gibt es neue experimentelle Inhalte und Umfragen zu neuen Inhalten. "], "1307": ["Das ist alles, ich lasse Si<PERSON> jetzt zum Spiel zurückkehren. "], "1400": ["Hast du das Katzenmädchen und das Widdermädchen auf der Seite des Spiels g<PERSON>hen? "], "1401": ["Vielen Dank für Ihr Vertrauen in die Entwicklung des Spiels. "], "000.CHOICE": ["<PERSON><PERSON>", "Tutorial 1 – Bewegung", "Tutorial 2 – Fertigkeiten"], "000.char": "Stimme", "000": ["Dies sind die Wiedergabedateien für die „Tutorial“-Level. "], "100.CHOICE": ["<PERSON><PERSON>", "Mission 0-1 – <PERSON><PERSON>", "Mission 0-2 – Pussy Galore"], "100.char": "Stimme... alt unter Bedingungen: Playervariables.StagesCleared[0] < 3, ", "100.1.CHOICE": ["<PERSON><PERSON>", "Mission 0-1 – <PERSON><PERSON>"], "100.1.char": "Stimme", "100.1": ["Die erste Mission! "], "200.CHOICE": ["<PERSON><PERSON>", "Mission 0-3 – Ein langer Spaziergang", "Mission 0-4 – Stimme"], "200.char": "Regeln... alt unter Bedingungen: Playervariables.StagesCleared[0] < 6, Playervariables.StagesCleared[0] > 6, ", "200.1.CHOICE": ["<PERSON><PERSON>", "Mission 0-3 – Ein langer Spaziergang"], "200.1.char": "Regeln", "200.1": ["<PERSON>cht weit von hier sollte es einen Schrein geben. "], "200.2.CHOICE": ["<PERSON><PERSON>", "Mission 0-3 – Ein langer Spaziergang", "Mission 0-4 – Stimme"], "200.2.char": "Regeln", "200.2": ["<PERSON>ch kann die Ram-Girl-Mission oder Voice hier noch einmal besuchen."], "500.char": "Erzählung", "501.char": "Regeln... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Katzenschwanz, ", "501.1.char": "Regeln", "501.1": ["<PERSON>ch komme rein, nein."], "502.char": "Stimme... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat <PERSON><PERSON><PERSON>h<PERSON>rner, <PERSON><PERSON><PERSON> hat Widderkleidungsoberteil, <PERSON><PERSON><PERSON> hat Katzenschwanz, ", "502.1.char": "Stimme", "502.1": ["Ein Satyr?!  ", 0, "? "], "502.2.char": "Stimme", "502.2": ["<PERSON><PERSON> – <PERSON>-<PERSON>?!  ", 0, "? "], "502.3.char": "Stimme", "502.3": [0, "?! "], "503.char": "Regeln... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Katzenschwanz, ", "503.1.char": "Regeln", "503.1": ["Katzenmädchen sind nett! "], "504.char": "Stimme... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Katzenschwanz, ", "504.1.char": "Stimme", "504.1": ["Du bist ein echtes Katzenmädchen geworden, nicht wahr? "], "505.char": "Stimme... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Katzenohren, <PERSON><PERSON><PERSON> hat Widder-Kleidungsoberteil, ", "505.1.char": "Stimme", "505.1": ["... Die Einheimischen mögen mich nicht? "], "505.2.char": "Stimme", "505.2": ["... Die Einheimischen mögen mich nicht?  ", 0, "."], "506.char": "Stimme... alt unter Bedingungen: Die Oberweite des Spielers ist > 2, ", "506.1.char": "Stimme", "506.1": ["Der Grund für das plötzliche Anschwellen deiner Brust sind die Monstermädchen, nicht wahr? "], "507.char": "Stimme... alt unter Bedingungen: Die Oberweite des Spielers ist > 2, ", "507.1.char": "Stimme", "507.1": ["Über Ihr Milchproblem sprechen wir später. ", 0, ", ich werde das dieses Mal klar erklären. "], "508.char": "Stimme... alt unter Bedingungen: Die Oberweite des Spielers ist > 3, ", "508.1.char": "Stimme", "508.1": ["Deine B<PERSON> wächst bereits wieder auf die Größe zurück, die du hattest, als ich dich zum ersten Mal gefunden habe ... Zweifellos wollen sie ein beeindruckendes Mädchen wie dich als Zuchttier verwenden."], "509.char": "Stimme", "510.char": "Stimme", "511.char": "Erzählung", "512.char": "Stimme", "513.char": "Regeln", "514.char": "Stimme... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Cat Ending 0 gese<PERSON>, ", "514.1.char": "Stimme", "514.1": ["NEIN?! "], "515.char": "Stimme", "516.char": "Stimme", "517.char": "Stimme", "518.char": "Regeln", "519.char": "Stimme", "520.char": "Erzählung", "521.char": "Erzählung", "522.char": "Stimme... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat W<PERSON>derhörner, <PERSON><PERSON><PERSON> hat Katzenschwanz, ", "522.1.char": "Stimme", "522.1": ["Ghh- <PERSON><PERSON> dummen <PERSON> stoßen mich an, wo hast du die überhaupt her!"], "522.2.char": "Stimme", "522.2": ["<PERSON><PERSON><PERSON> au<PERSON>, deinen Schwanz über mein Gesicht zu streichen, es kitzelt – Muss ich dich wirklich noch einmal auslaugen?!"], "523.char": "Regeln", "524.char": "Stimme", "525.char": "Stimme", "300.CHOICE": ["<PERSON><PERSON>", "Mission 0-5 – Route A11", "Updateinformation"], "300.char": "Stimme... alt unter Bedingungen: Playervariables.StagesCleared[0] > 7, ", "300.1.CHOICE": ["<PERSON><PERSON>", "Mission 0-5 – Route A11", "Informationen zum V0.6-Update"], "300.1.char": "Stimme", "300.1": ["Hier können Sie das Eröffnungsgespräch der Route A11 mit dem Fuchs noch einmal abspielen."], "1300.char": "Stimme", "1301.char": "Stimme", "1302.char": "Stimme", "1303.char": "Stimme", "1304.char": "Stimme... alt unter Bedingungen: Die Oberweite des Spielers ist > 2, ", "1304.1.char": "Stimme", "1304.1": ["Da Ihre Oberweite so groß ist, ist Ihnen möglicherweise bereits ein zusätzlicher „Nachteil“ aufgefallen."], "1305.char": "Stimme... alt unter Bedingungen: Playervariables.removesex == true, ", "1306.char": "Stimme", "1307.char": "Stimme", "1305.1.char": "Stimme", "1305.1": ["... Hier schicke ich den Patreon, nicht wahr? "], "1400.char": "Stimme", "1401.char": "Stimme"}, "shikaroutea11talk.json": {"000.char": "FoxGirl", "000": ["Hey! "], "001.char": "FoxGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Fuchsohren, ", "001": ["Ignoriere mich nicht! "], "001.1.char": "FoxGirl", "001.1": ["Tun wir für einen Moment so, als hätte ich Si<PERSON> nicht bereits verarscht, damit wir dieses Gespräch wiederholen können ... Erklären <PERSON> sich! "], "002.char": "Regeln... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> hat Katz<PERSON>chwanz, <PERSON><PERSON><PERSON> hat Widderkleidungsoberteil, <PERSON><PERSON><PERSON> hat Katzenohren, ", "002": ["Du hast gesagt, wir sollen in die Mitte des Landes gehen."], "002.1.char": "Regeln", "002.1": ["Sind es wieder die Hörner?"], "002.2.char": "Regeln", "002.2": ["Nya? "], "002.3.char": "Regeln", "002.3": ["Ich gehöre wirklich nicht zu den Widdern..."], "002.4.char": "Regeln", "002.4": ["Die Ohren? "], "003.char": "FoxGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat <PERSON><PERSON><PERSON>h<PERSON>rner, <PERSON><PERSON><PERSON> hat <PERSON>chwanz, <PERSON><PERSON><PERSON> hat Widderkleidungsoberteil, <PERSON><PERSON><PERSON> hat Katzenohren, ", "003": ["<PERSON>cht das, <PERSON><PERSON><PERSON><PERSON>! "], "003.1.char": "FoxGirl", "003.1": ["Du hast mir geholfen, also ist mir das egal! "], "003.2.char": "FoxGirl", "003.2": ["Hat dich der Tigersaft dumm gemacht? "], "003.3.char": "FoxGirl", "003.3": ["Du hast mir geholfen, also ist mir das egal! "], "003.4.char": "FoxGirl", "003.4": ["<PERSON>cht das <PERSON> du hast keinen Katzenschwanz? "], "004.char": "Regeln... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat <PERSON><PERSON><PERSON><PERSON><PERSON>rner, <PERSON><PERSON><PERSON> hat Katz<PERSON>chwanz, <PERSON><PERSON><PERSON> hat Widderkleidungsoberteil, ", "004": ["Stimme? "], "004.1.char": "Regeln", "004.1": ["Ich sorge für die Sicherheit von Voice."], "004.2.char": "Regeln", "004.2": ["<PERSON><PERSON><PERSON><PERSON>, ich verstehe es nicht."], "004.3.char": "Regeln", "004.3": ["Stimme? "], "005.char": "FoxGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat <PERSON><PERSON><PERSON>h<PERSON>rner, <PERSON><PERSON><PERSON> hat <PERSON>chwanz, <PERSON><PERSON><PERSON> hat Widderkleidungsoberteil, ", "005": ["W- du hast Mu<PERSON>, nach dem Gespräch mit ihr loszulaufen! "], "005.1.char": "FoxGirl", "005.1": ["WARUM?! "], "005.2.char": "FoxGirl", "005.2": ["Dieser Vampir. "], "005.3.char": "FoxGirl", "005.3": ["Auch ohne Hörner bist du ein echter Flaumkopf! "], "006.char": "Regeln... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat <PERSON><PERSON><PERSON><PERSON><PERSON>rner, <PERSON><PERSON><PERSON> hat Katz<PERSON>chwanz, <PERSON><PERSON><PERSON> hat Widderkleidungsoberteil, ", "006": ["Du bist auch ein Monster, weißt du?"], "006.1.char": "FoxGirl", "006.1": ["Aus diesem Land kommt nichts Gutes, wenn du ihr einen <PERSON> gibst, ihr Wolkenhirne."], "006.2.char": "Regeln", "006.2": ["Eigentlich ich-"], "006.3.char": "Regeln", "006.3": ["Entspannen."], "007.char": "FoxGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat <PERSON><PERSON><PERSON>h<PERSON>rner, <PERSON><PERSON><PERSON> hat <PERSON>chwanz, <PERSON><PERSON><PERSON> hat Widderkleidungsoberteil, ", "007": ["Den<PERSON>ch ist es eine Verschwendung, dich zum Stammvater eines anderen werden zu lassen. "], "007.1.char": "FoxGirl", "007.1": ["Du solltest nicht ihr Eigentum oder das der Satyrn sein. "], "007.2.char": "FoxGirl", "007.2": ["Ich muss dich selbst ausbilden! "], "007.3.char": "FoxGirl", "007.3": ["<PERSON>s ist eine Verschwendung, dich von den Widdern haben zu lassen. "], "008.char": "FoxGirl", "008": ["Wir sorgen dafür, dass Sie überlaufen und ein treuer Kitsune-Anhänger werden. "], "009.char": "Regeln... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat <PERSON><PERSON><PERSON><PERSON><PERSON>rner, <PERSON><PERSON><PERSON> hat Katz<PERSON>chwanz, <PERSON><PERSON><PERSON> hat Widderkleidungsoberteil, ", "009": ["Sie möchten sich nicht selbst um <PERSON> kümmern?"], "009.1.char": "Regeln", "009.1": ["<PERSON>ch bin hierher gekommen, um nach Naman zu suchen. "], "009.2.char": "Regeln", "009.2": ["Kopfschmerzen. "], "009.3.char": "Regeln", "009.3": ["Lasst uns gemeinsam mit Voice reden...?"], "010.char": "FoxGirl", "010": ["Idiot! "], "011.char": "Regeln... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat <PERSON><PERSON><PERSON><PERSON><PERSON>rner, <PERSON><PERSON><PERSON> hat Katz<PERSON>chwanz, <PERSON><PERSON><PERSON> hat Widderkleidungsoberteil, ", "011": ["<PERSON><PERSON>. "], "011.1.char": "Erzählung", "011.1": ["Die Korruption des Widders scheint zu drängen ", 0, " aufladen."], "011.2.char": "Erzählung", "011.2": [0, " <PERSON>ch kann nicht sagen, ob der Fuchs nur dumm und verwirrend ist oder ob es an der Korruption liegt."], "011.3.char": "Erzählung", "011.3": ["Die Kleidung des Widders scheint zu halten ", 0, " ruhig."], "012.char": "FoxGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat <PERSON><PERSON><PERSON>h<PERSON>rner, <PERSON><PERSON><PERSON> hat <PERSON>chwanz, <PERSON><PERSON><PERSON> hat Widderkleidungsoberteil, ", "012": ["<PERSON><PERSON> <PERSON> g<PERSON>h, versuch es e<PERSON>ch, <PERSON><PERSON><PERSON><PERSON>, meine Geisterfüchse lassen dich nicht."], "012.1.char": "FoxGirl", "012.1": ["Aaa...? "], "012.2.char": "FoxGirl", "012.2": ["Nur keine Krallen und kein Klettern auf dem Dach, verstanden?"], "012.3.char": "FoxGirl", "012.3": ["Du musst mehr tun, als mich nur zu hypnotisieren, sobald ich die Geisterfüchse raus habe."]}, "shikashrinetalk.json": {"1008": ["<PERSON>ch hatte geh<PERSON>t, du würdest mir sagen, was los ist. "], "1009": ["<PERSON><PERSON><PERSON>, das. "], "1010": ["<PERSON><PERSON> verdammten „Söldner“ nutzen es als Vorwand, um Leute in ihre Reihen zu rekrutieren! "], "1011": ["... <PERSON><PERSON><PERSON>? "], "1012": ["Das sind viele große W<PERSON>ds. "], "1013": ["Siiiigh. "], "1014": ["Von allen mythischen Tieren haben sowohl die Werwölfe als auch die Vampire die Fähigkeit zur „Verderbnis“ weitergegeben, damit wir mehr von unserer Art und Familie haben können, ohne neues Leben gründen zu müssen."], "1015": ["<PERSON><PERSON>? "], "1016": ["<PERSON><PERSON> <PERSON> weißt, dass das illegal ist!! "], "1017": ["Ist mir egal. "], "1018": ["...? "], "1019": [""], "2009": ["Es ist der Fluch von Gaia. "], "2010": ["Wir mögen kein G<PERSON>tz, aber es ist notwendig. "], "2011": ["Die Fortpflanzung muss eingeschränkt werden, aber wir können nach Belieben konvertieren und korrumpieren."], "2012": ["<PERSON><PERSON> kam sie, ein 30-j<PERSON><PERSON><PERSON> Jugendlicher auf dem Höhepunkt seiner Rebellion. "], "2013": ["<PERSON><PERSON> sei denn, du willst bei mir bleiben, ", 0, ", du solltest hier wirklich nicht rumhängen. "], "2014": ["Aah. "], "2015": ["...die <PERSON>ucht eingeschränkt ist? "], "2016": ["<PERSON>er würde einen Menschen zum Teufel züchten, wenn er einen fände, dann wäre er nicht mehr lange ein <PERSON>sch. "], "2017": ["Habe es. "], "2018": ["Nya! "], "2019": ["Aaaaaa?! "], "2020": ["T<PERSON><PERSON>ss."], "000.char": "FoxGirl... alt unter Bedingungen: Playervariables.queststart == false, hadconversation == true, ", "000": ["<PERSON>s ist endlich vorbei, ich konnte überhaupt nicht schlafen. "], "000.1.char": "FoxGirl", "000.1": ["<PERSON><PERSON><PERSON>, da wir uns gerade erst kennengelernt haben. "], "000.2.CHOICE": ["Verlassen", "<PERSON>espr<PERSON><PERSON> wied<PERSON>"], "000.2.char": "FoxGirl", "000.2": ["<PERSON><PERSON><PERSON> Si<PERSON> besser nach <PERSON>, wenn Sie nicht mein Ego zerstören wollen. "], "001.char": "FoxGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat <PERSON><PERSON><PERSON>ren, <PERSON><PERSON><PERSON> hat <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> hat <PERSON>, ", "001": ["Sind Si<PERSON> ein Re<PERSON>nder? "], "001.1.char": "FoxGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, ", "001.1": ["Was treibt ein <PERSON>chs wie du eigentlich umher? "], "001.1.1.char": "FoxGirl", "001.1.1": ["Sie sollten es besser wissen, als Ihren Schrein zu verlassen. "], "001.2.char": "FoxGirl... alt unter Bedingungen: <PERSON>pieler hat Katzenohren, ", "001.2": ["Du bist selbst immer noch ein Ram-Girl. "], "001.2.1.char": "FoxGirl... alt unter Bedingungen: <PERSON>pieler hat Katzenohren, ", "001.2.1": ["Du bist selbst immer noch so eine Art gehässiges Widdermädchen. "], "001.3.char": "FoxGirl... alt unter Bedingungen: <PERSON>pieler hat Katzenschwanz, ", "001.3": ["Seid ihr nicht Katzenmädchen... Hey. "], "001.3.1.char": "FoxGirl", "001.3.1": ["Hängt ihr Katzenmädchen nicht immer mit den Rams rum? "], "002.char": "Regeln... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Katzenschwanz, ", "002": ["Ich habe diesen Schrein gemacht und möchte nicht, dass andere Monster ihn ruinieren."], "002.1.char": "Regeln", "002.1": ["Nyaaa. "], "003.char": "FoxGirl", "003": ["Was."], "004.char": "FoxGirl", "004": ["Bist du ein Idiot? "], "005.char": "FoxGirl", "005": ["<PERSON>n wurde den Jungfrauen, die die Schreine bewachten, die Macht der großen Neunschwänzigen verliehen, ebenso wie ihren Schülern und Nachkommen!"], "006.char": "Regeln... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Katzenschwanz, ", "006": ["...Die menschlichen Schreinmädchen wurden ausgetrickst und zu verschwitzten Perversen korrumpiert?"], "006.1.char": "Regeln", "006.1": ["Die Schreinmädchen wurden vom Fuchsdämon in verschwitzte Perverse verwandelt, nya ..."], "007.char": "FoxGirl... alt unter Bedingungen: <PERSON>pieler hat Katzenschwanz, ", "007": ["Warum sonst sollten Mädchen den ganzen Tag an einem Schrein herumhängen, wenn nicht, um diese Macht zu erlangen?"], "007.1.char": "FoxGirl", "007.1": ["Warum benimmst du dich so selbstgefällig, wenn du nur eine dumme <PERSON> bist?!"], "008.char": "FoxGirl", "008": [0, ", war es? "], "009.char": "FoxGirl", "009": ["V<PERSON> Werwolf-Clan, vom Vampir-Clan. "], "010.char": "FoxGirl", "010": ["Dann hat ihre Königin Mist gebaut und ihr Haustier ist freigekommen. "], "011.char": "FoxGirl", "011": ["<PERSON><PERSON> evakuiert die Einheimischen, aber sie hat diese verdammten Tigers and Rams angeheuert, um das zu tun."], "012.char": "FoxGirl", "012": ["Wie soll ich evakuieren? "], "013.char": "FoxGirl", "013": ["...Du willst wissen, wie du nach unten kommst?"], "014.char": "FoxGirl", "014": ["Wenn Sie es in die Mitte der Insel schaffen – zum Teersumpf rund um den Turm – kann Ihnen die Matriarchin des Werwolfs den Weg zeigen."], "015.char": "FoxGirl", "015": ["Hier ist ein Extra für Ihre Mithilfe. "], "016.char": "FoxGirl... alt unter Bedingungen: Die Oberweite des Spielers ist > 2, ", "016": ["Es ist der beste Ort, um Erschöpfung, Korruption und Schweiß loszuwerden!"], "016.1.char": "FoxGirl", "016.1": ["Es ist der beste Ort, um Erschöpfung und Korruption zu beseitigen. "], "017.char": "FoxGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Katzenohren, 5<PERSON><PERSON>, ", "017": ["<PERSON><PERSON> <PERSON><PERSON> v<PERSON>g korrupt werden, fallen Sie immer noch auf die Seite des Feindes. "], "017.1.char": "FoxGirl... alt unter Bedingungen: <PERSON>pieler hat Katzenschwanz, ", "017.1": ["Tatsächlich könnte es helfen, die Gehässigkeit, die Sie überall haben, zu beseitigen. "], "017.1.1.char": "FoxGirl", "017.1.1": ["Möglicherweise sind Si<PERSON> zu weit in den Fängen der Tiger, um die Katze an den Quellen loszuwerden. "], "017.2.char": "FoxGirl", "017.2": ["<PERSON><PERSON><PERSON><PERSON> mich nicht falsch, ich vertraue dir immer noch nicht ganz. "], "018.char": "FoxGirl", "018": ["Das ist alles. "], "2009.char": "FoxGirl", "2010.char": "FoxGirl", "2011.char": "FoxGirl", "2012.char": "FoxGirl", "2013.char": "FoxGirl", "2014.char": "FoxGirl", "2015.char": "Regeln... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Katzenschwanz, ", "2015.1.char": "Regeln", "2015.1": ["Nn? "], "2016.char": "FoxGirl", "2017.char": "Regeln... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Katzenschwanz, ", "2018.char": "Regeln", "2019.char": "FoxGirl", "2020.char": "Regeln", "1008.char": "Regeln", "1009.char": "FoxGirl", "1010.char": "FoxGirl", "1011.char": "Regeln... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Katzenschwanz, ", "1012.char": "Regeln", "1013.char": "FoxGirl", "1014.char": "FoxGirl", "1015.char": "Regeln... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Katzenschwanz, ", "1015.1.char": "Regeln", "1015.1": ["Aber ich will z<PERSON>chten?!"], "1016.char": "FoxGirl", "1017.char": "Regeln", "1018.char": "FoxGirl", "1019.char": "FoxGirl"}, "tutorialabilitysecret.json": {"100": ["Ich werde Qades fragen, ob ich die Inhaltseinstellungen ändern muss."], "200": ["Ich werde Qades fragen, ob ich die Inhaltseinstellungen ändern muss."], "300": ["Ich werde Qades fragen, ob ich die Inhaltseinstellungen ändern muss."], "000.CHOICE": ["<PERSON><PERSON>", "<PERSON><PERSON> zur Cat-Girl-Mission", "<PERSON><PERSON> zur Ram-Girl-Mission", "<PERSON><PERSON> zur Fox-Girl-Mission"], "000.char": "Regeln", "000": ["Steckt hinter diesen Ranken ein <PERSON>? "], "100.char": "Regeln", "200.char": "Regeln", "300.char": "Regeln"}, "tutorialmission1s1voicetake2.json": {"100": ["Wirk<PERSON>? "], "101": ["<PERSON>en auf den preis,", 0, "! "], "000.CHOICE": ["Warum sind wir hier?"], "000.char": "Stimme... alt unter Bedingungen: hadconversation == true, ", "000": ["<PERSON>, ", 0, "! "], "000.1.CHOICE": ["Warum sind wir hier?", "<PERSON>er draußen gibt es Feinde.", "Egal."], "000.1.char": "Stimme", "000.1": ["Muss ich es noch einmal durch<PERSON>hen?"], "001.char": "Stimme", "001": ["<PERSON>n wir dich wieder auf die Be<PERSON> bringen wollen, wird es gut sein, die wenigen Leute kennenzulernen, die dich nicht ins Jenseits ficken wollen."], "002.char": "Stimme", "002": ["Aber! "], "003.char": "Stimme", "003": ["Also, uhhh, du musst es für mich zu Ende bringen! "], "004.char": "Stimme... alt unter Bedingungen: hadconversation == true, ", "004": ["<PERSON><PERSON><PERSON>, diese schwebenden <PERSON>. "], "004.1.char": "Stimme", "004.1": ["<PERSON><PERSON><PERSON>, diese schwebenden <PERSON>. "], "100.char": "Stimme", "101.char": "Stimme"}, "tutorialmission1s2qades.json": {"100": ["Was auch immer, <PERSON>e haben es bis hierher gescha<PERSON>t, ohne aufzugeben. "], "110": ["Okayokayokay-- <PERSON> alles, wo du da draußen kämpfen könntest, wird ein Monster-<PERSON><PERSON><PERSON><PERSON> sein, oder? "], "111": ["<PERSON><PERSON>, ich bin ein <PERSON>. "], "120": ["<PERSON><PERSON>ön für dich! "], "121": ["Das macht meine Arbeit einfacher. "], "150": ["Das Ändern dieser Optionen wird Ihnen nicht helfen. "], "151": ["Du kannst NSFW deaktivieren und niemand wird dich ficken. "], "200": ["<PERSON>ch muss also entscheiden, ob du Sex-Sachen willst oder nicht. "], "210": [""], "211": ["Okay. "], "240": ["Du... willst es nicht anhaben? "], "241": ["<PERSON><PERSON> bin sicher, <PERSON><PERSON> haben Ihre Gründe, aber Si<PERSON> können jederzeit zurückkehren, wenn <PERSON>e Ihre Meinung ändern, oder mich später im Dorf finden. "], "250": ["Es gibt noch eine ganze Menge weiterer Informationen, die Voice noch nicht behandelt hat. "], "300": ["Hier treibt sich ein Pärchen herum. "], "301": ["<PERSON>n sie dich mit einem Angriff treffen, verl<PERSON>t du HP in Höhe des Schadens. "], "302": ["Hast du eine Katze bei dir? "], "303": ["Was ihre Korruption angeht, nehmen Sie einfach nichts Seltsames auf. "], "400": ["Die<PERSON>? "], "401": ["Waffen sind innerhalb einer Mission oder eines Laufs dauerhaft, <PERSON><PERSON> können sie jedoch nur einmal pro Karte verwenden. "], "402": ["<PERSON><PERSON><PERSON> Sie die Weltkarte aufrufen, werden Sie einige dauerhafte Gegenstände sehen. "], "500": ["Wenn Sie 0 er<PERSON><PERSON>n, gewinnen Sie in der nächsten Phase keine Preise. Manchmal sind spezielle Bedingungen erforderlich, um ein Ende zu erreichen. "], "501": ["Die Katzenmädchen fliehen nach nur ein oder zwei Schlägen, aber sie bekämpfen dich auch nicht auf Leben und Tod. "], "502": ["Du verstehst es, nicht wahr? "], "503": ["<PERSON><PERSON>, ich habe gehört, dass Menschen auch sehr schnell erwachsen werden, ein endloser Vorrat an Korruptionsködern. "], "600": ["Gibt es noch etwas, bevor <PERSON>?"], "700": ["Das ist ja schön und gut, aber kannst du diesen mädchenhaften Fluch wenigstens rückgängig machen?"], "701": ["Was. "], "702": ["Warum vergessen alle, dass ich ein Mann sein soll?! "], "703": ["Hallo jetzt. "], "704": ["<PERSON>n <PERSON> zu weit fallen, verlieren Sie möglicherweise Ihr ursprüngliches Ziel und werden indoktriniert. "], "705": ["... Du hast doch zumindest deinen Körper angeschaut, oder? "], "800": ["<PERSON><PERSON><PERSON><PERSON> auf dich, ich habe dir den ganzen Tag zugehört, wie du mich herabwürdigst. "], "801": ["Gut, fiiiiine! "], "900": ["Wenn ich meinen Schwanz zurückbekomme, werde ich dich auf den Boden drücken und dich ficken, „Cupido“. "], "901": ["Ist das ein Versprechen? "], "902": ["Habe ich gestottert?"], "903": ["<PERSON><PERSON>, <PERSON>, ja, Sir, bringen wir Sie zur Mission."], "1000": ["<PERSON><PERSON>, dass <PERSON>cht ve<PERSON>, jeden zu ve<PERSON>, den <PERSON>. <PERSON><PERSON> müssen diesen Kristall hineinbekommen, um dem Vampir Seelenfrieden zu geben. "], "000.char": "Qades... alt unter Bedingungen: Playervariables.playershards < 1, hadconversation == true, ", "000": ["<PERSON><PERSON><PERSON><PERSON>.  ", 0, ", nicht wahr? "], "000.1.char": "Qades... alt unter Bedingungen: hadconversation == true, ", "000.1": ["Bist du gerade... direkt über die Scherbe gesprungen?? "], "000.1.1.char": "<PERSON><PERSON>", "000.1.1": ["<PERSON><PERSON> schon, was hast du gedacht, was passieren würde? Ich würde weiter reden, als hättest du tatsächlich das große leuchtende Ding in die Hand genommen und wir würden ohne es weitermachen? "], "000.2.char": "<PERSON><PERSON>", "000.2": ["Das tut mir leid. "], "001.char": "Qades... alt unter Bedingungen: hadconversation == true, ", "001": ["<PERSON><PERSON> bin <PERSON>. "], "001.1.char": "<PERSON><PERSON>", "001.1": ["<PERSON>ur weil „klein und rosa!“ "], "100.CHOICE": ["Was??", "Sex ist gut."], "100.char": "<PERSON><PERSON>", "110.char": "<PERSON><PERSON>", "111.CHOICE": ["<PERSON><PERSON>", "Mehr Info"], "111.char": "<PERSON><PERSON>", "120.char": "<PERSON><PERSON>", "121.CHOICE": ["<PERSON><PERSON>", "Mehr Info"], "121.char": "<PERSON><PERSON>", "150.char": "<PERSON><PERSON>", "151.char": "<PERSON><PERSON>", "200.CHOICE": ["<PERSON><PERSON>"], "200.char": "<PERSON><PERSON>", "210.char": "<PERSON><PERSON>", "211.char": "Qades... alt unter Bedingungen: Playervariables.consent == false, ", "211.1.char": "<PERSON><PERSON>", "211.1": ["Sexuelle Dinge sind also aus. "], "240.CHOICE": ["<PERSON>a, weiter.", "<PERSON><PERSON>, nimm mich zurück."], "240.char": "<PERSON><PERSON>", "241.char": "<PERSON><PERSON>", "250.CHOICE": ["<PERSON>e Monster-Girls?", "Wo sind meine Waffen?", "Was ist, wenn ich verliere?", "<PERSON>h weiter."], "250.char": "<PERSON><PERSON>", "300.char": "<PERSON><PERSON>", "301.char": "<PERSON><PERSON>", "302.char": "<PERSON><PERSON>", "303.char": "<PERSON><PERSON>", "400.char": "<PERSON><PERSON>", "401.char": "<PERSON><PERSON>", "402.char": "<PERSON><PERSON>", "500.char": "<PERSON><PERSON>", "501.char": "<PERSON><PERSON>", "502.char": "<PERSON><PERSON>", "503.char": "<PERSON><PERSON>", "600.CHOICE": ["Weitermachen", "<PERSON>e Monster-Girls?", "Wo sind meine Waffen?", "Was ist, wenn ich verliere?"], "600.char": "<PERSON><PERSON>", "700.char": "Regeln", "701.char": "<PERSON><PERSON>", "702.char": "Regeln", "703.char": "<PERSON><PERSON>", "704.char": "<PERSON><PERSON>", "705.CHOICE": ["<PERSON>ck dich", "Ich werde dich ficken", "Was auch immer"], "705.char": "<PERSON><PERSON>", "800.char": "Regeln", "801.char": "<PERSON><PERSON>", "900.char": "Regeln", "901.char": "Qades... alt unter Bedingungen: Playervariables.consent == false, ", "901.1.char": "<PERSON><PERSON>", "901.1": [0, "... Du erinn<PERSON>t dich doch, dass du mich gebeten hast, den sexuellen Inhalt auszuschalten, oder? "], "902.char": "Regeln", "903.char": "<PERSON><PERSON>", "1000.char": "<PERSON><PERSON>"}, "villageqades.json": {"000.CHOICE": ["Verlassen", "Was ist das?", "MENÜ ÖFFNEN"], "000.char": "Qades... alt unter Bedingungen: Playervariables.consent == false, ", "000": ["Hey-hey. "], "000.1.CHOICE": ["Verlassen", "Was ist das?", "MENÜ ÖFFNEN"], "000.1.char": "<PERSON><PERSON>", "000.1": ["NSFW-Inhalte sind deaktiviert. Öffnen Sie das Menü, wenn Si<PERSON> das ändern möchten. "], "001.char": "Erzählung", "001": [""], "002.char": "<PERSON><PERSON>", "002": ["Mal sehen... Wenn Si<PERSON> den gesamten sexuellen Inhalt ausschalten, werden alle anzüglichen Szenen ausgeblendet. "], "003.char": "<PERSON><PERSON>", "003": ["Der Schalter „Futa – Feind“ betrifft derzeit nur einen oder zwei Feinde. "], "004.char": "<PERSON><PERSON>", "004": ["Die extreme Brustvergrößerung verhindert, dass Ihre Oberweite über die größte Größe hinausgeht, falls das für Sie sinnvoll ist. "], "005.char": "<PERSON><PERSON>", "005": ["Das ist alles für den Moment. "]}, "voiceintroconversation.json": {"110": ["Etwas Weiches – und SCHWERES – rammt mich."], "111": ["<PERSON><PERSON><PERSON> auf her<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ich habe dich schon fertig gemacht! "], "112": ["<PERSON>ch schrecke ins Gras zurück, meine Sicht verschwimmt. "], "150": ["Zum Teufel? "], "151": ["Higuu-?! "], "300": ["Ein Monster...?"], "301": ["„Monmusu“ nannten wir sie – die Töchter der Monsterart."], "302": ["Diese Dame vor mir entfacht meine Jägerinstinkte wie ein Pflock, der mir ins Innerste sticht."], "303": ["Oh ja! "], "304": ["Was ist mit den feindseligen Augen, <PERSON><PERSON><PERSON><PERSON>? "], "420": ["Bist du blind? Ich bin aa- n- hey-"], "440": ["Es gibt nichts außer m- mmme- Hör auf damit- Aaah...!"], "460": ["Du kannst dich nicht täuschen, <PERSON> <PERSON>ch- Kannst du -?!"], "700": ["Da ist sie wieder, eine <PERSON>im<PERSON>, die meine Worte nachplappert. "], "701": ["<PERSON><PERSON>, warte. "], "702": ["Wir sind nur zu zweit hier. "], "703": ["<PERSON><PERSON><PERSON><PERSON> mir nicht, dass ich als Mädchen wiedergeboren wurde?! "], "704": ["Ein Spiegel... Sogar eine Pfütze – gib mir alles!"], "705": [""], "706": ["<PERSON>ink<PERSON><PERSON><PERSON>? "], "707": ["Was deine <PERSON> an<PERSON>, sie waren v<PERSON><PERSON><PERSON> du<PERSON>, also habe ich sie ausgebürstet und so zusammengebunden, damit sie or<PERSON>lich bleiben. "], "708": ["Da du wie Beute fallen gelassen wurdest, heißt das, dass du jetzt mein Eigentum bist! "], "750": ["Und was, dich den Monster-Girls als Fleisch zu überlassen, damit sie sie überall in die Finger bekommen können?! "], "751": ["Was hast du dann mit meinem Körper gemacht?! "], "752": ["So etwas habe ich nicht gemacht! "], "753": ["<PERSON><PERSON> bin zu müde da<PERSON>ür. "], "754": ["Ja! "], "800": ["Also! "], "810": ["<PERSON>n <PERSON> aufh<PERSON><PERSON> wü<PERSON>, mich finster an<PERSON>, gibt es hier einen ausgegrabenen Bereich, in dem <PERSON> sich orientieren können."], "900": ["<PERSON><PERSON> also zumindest umziehen. "], "901": ["Ich soll nicht wie ein Monster aussehen? "], "902": ["<PERSON><PERSON><PERSON><PERSON>. "], "903": ["<PERSON><PERSON><PERSON>. "], "904": ["<PERSON>ch ein Monster zu nennen ist einfach geschmacklos! "], "905": ["<PERSON><PERSON>, bevor ich Si<PERSON> ausgesaugt habe, waren Ihre dummen Sautitten genauso groß wie diese Dinger jetzt! "], "906": ["Jetzt. "], "907": ["Fünf Buchstaben? "], "908": ["Dann ist es kein Name, oder? "], "909": [""], "910": [0, ", ist es? "], "911": ["Erlauben Sie mir, mich richtig vorzustellen. "], "912": ["Nichts mit mir zu tun. "], "913": ["Du meinst Konvertierung? "], "914": ["Ich bin. "], "915": ["Wirk<PERSON>? "], "916": ["Woah – okay, gut, ich höre auf zu drücken! "], "917": ["Vor langer Zeit reinigte ein von Monstern angeführter Kreuzzug das Land von wahren Menschen. "], "918": ["<PERSON><PERSON> wäre dir also nicht erlaubt, ein <PERSON> zu sein. "], "919": ["Wir bauen hier in der Nähe eine Basis auf den Überresten eines alten befestigten Dorfes. "], "1000": ["Du hast mich gerade von den Toten auferweckt und ich bin jetzt ein Mädchen. "], "1001": ["<PERSON>ch würde gerne, ", 0, ", aber wir können nicht am Tatort herum<PERSON>ängen, es sei denn, du willst das Lieblingsprojekt eines vorbeikommenden Monstermädchens werden!"], "1002": ["Wir gehen sowieso durch die Höhle, ich gebe dir einen sicheren Platz zum Ausruhen, wenn wir zurück sind! "], "1003": ["<PERSON><PERSON><PERSON><PERSON> Si<PERSON> nicht, dass ich mich an Ihre Regeln halte. "], "1100": ["<PERSON>ute Einstellung. "], "000.char": "Regeln", "000": ["(„<PERSON>“. „Menschen“. Der Unterschied zwischen den beiden ist so offensichtlich und doch, wenn Sie eine Grenze zwischen den beiden ziehen würden, wo wäre sie?)"], "001.char": "Regeln", "001": ["(Die Wahrheit ist, dass diese Linie nicht existiert. Ein Monster kann ein Mensch werden, kann ein Monster werden.)"], "002.char": "Regeln", "002": ["(Indem sie die Monster von einst zur Ausrottung trieben, passten sie sich an unsere Welt an, um zu überleben. Sie überschritten die gleiche Grenze wie wir.)"], "003.char": "Regeln", "003": ["(In dieser Regenzeit, als sie einmarsch<PERSON>ten, konnte ich das, was mir am wichtigsten war, nicht schützen. Das war der Tag, an dem ich starb.)"], "004.char": "Regeln", "004": ["...<PERSON><PERSON>t beunruhigt mich diese schreckliche Dissonanz."], "005.char": "Erzählung", "005": ["Ein Klang wie eine geworfene Glocke, eine weltbewegende Explosion, der ferne Donner des Konflikts. "], "006.char": "Erzählung", "006": ["<PERSON>h sobald er beginnt, endet der <PERSON>, eine kühle Brise weht vorbei. "], "007.char": "Erzählung", "007": ["Nichts blieb außer dem schrillen, klingenden Nachklang. "], "008.char": "Stimme", "008": ["- Wach auf ... du I<PERSON>! "], "009.CHOICE": ["<PERSON><PERSON><PERSON>, dich ausz<PERSON>.", "Wer ist ein Blödmann?!"], "009.char": "Erzählung", "009": ["Was ist mit der Ruhelosigkeit der Toten passiert? "], "110.char": "Erzählung", "111.char": "Stimme", "112.char": "Erzählung", "150.char": "Regeln", "151.char": "Stimme", "300.char": "Erzählung", "301.char": "Erzählung", "302.char": "Erzählung", "303.char": "Stimme", "304.CHOICE": ["Wer ist „mädchenhaft“?!", "Vor was gere<PERSON><PERSON>?", "<PERSON>ch lasse mich nicht tä<PERSON>chen, Monster."], "304.char": "Stimme", "420.char": "Regeln", "440.char": "Regeln", "460.char": "Regeln", "700.char": "Erzählung", "701.char": "Regeln", "702.char": "Erzählung", "703.char": "Erzählung", "704.char": "Erzählung", "705.char": "Erzählung", "706.char": "Stimme", "707.char": "Stimme", "708.CHOICE": ["Ich habe genug gehabt!", "<PERSON><PERSON> was auch immer."], "708.char": "Stimme", "750.char": "Stimme", "751.char": "Regeln", "752.char": "Stimme", "753.char": "Regeln", "754.char": "Stimme", "800.CHOICE": ["Beginnen Sie mit dem Training"], "800.char": "Stimme... alt unter Bedingungen: Playervariables.tutorialscleared > 0, ", "800.1.CHOICE": ["Ich muss das nicht tun", "Beginnen Sie mit dem Training"], "800.1.char": "Stimme", "800.1": ["<PERSON>vor wir fortfahren, möchte ich noch einmal überprüfen, wie es Ihrem Körper geht. "], "810.CHOICE": ["Beginnen Sie mit dem Training"], "810.char": "Stimme... alt unter Bedingungen: Playervariables.tutorialscleared > 0, ", "810.1.CHOICE": ["Ich muss das nicht tun", "Beginnen Sie mit dem Training"], "810.1.char": "Stimme", "810.1": ["<PERSON><PERSON> <PERSON> aufh<PERSON><PERSON> wü<PERSON>, mich finster an<PERSON>, können wir den ersten Bereich überspringen, wenn <PERSON> möchten. Anscheinend wissen Si<PERSON> bereits, wie man sich bewegt."], "900.char": "Stimme", "900.1.char": "Stimme", "900.1": ["<PERSON><PERSON> <PERSON>, könne<PERSON>. "], "901.char": "Regeln", "902.char": "Stimme", "903.char": "Regeln", "904.char": "Stimme", "905.char": "Stimme", "906.char": "Stimme", "907.char": "Regeln", "908.char": "Stimme", "909.char": "Erzählung", "910.char": "Stimme... alt unter Bedingungen: Playervariables.debugmodeon, WNT0, WNT1, WNT2, WNT3, WNT4, WNT5, WNT6, WNT7, ", "910.1.char": "Stimme", "910.1": [0, "? "], "910.2.char": "Stimme", "910.2": ["Ist gerade ein Spiegel zerbrochen? "], "910.3.char": "Stimme... alt unter Bedingungen: WNT8, ", "910.3": [0, "? "], "910.3.1.char": "Stimme... alt unter Bedingungen: WNT8, ", "910.3.1": [0, ", ist es? "], "910.4.char": "Stimme", "910.4": ["'", 0, "'? "], "910.5.char": "Stimme", "910.5": ["'", 0, "„... Das ist eher ein seltsamer Ausdruck als ein Name, nicht wahr? "], "910.6.char": "Stimme", "910.6": ["Hey, du musst nicht schreien. ", 0, "'. "], "910.7.char": "Stimme", "910.7": [0, "? ", 1, "'"], "910.8.char": "Stimme", "910.8": [0, "? ", 0, ". "], "910.9.char": "Stimme", "910.9": ["Toller Name! "], "911.char": "Stimme... alt unter Bedingungen: WNT7, ", "911.1.char": "Stimme", "911.1": ["<PERSON><PERSON><PERSON><PERSON> Si<PERSON> mir, mich meinem neuen Haustier richtig vorzustellen! "], "912.char": "Regeln", "913.char": "Stimme", "914.char": "Regeln", "915.char": "Stimme", "916.char": "Stimme", "917.char": "Stimme", "918.char": "Stimme", "919.CHOICE": ["Ich werde es tun.", "Ich will nicht."], "919.char": "Stimme", "1000.char": "Regeln", "1001.char": "Stimme", "1002.char": "Stimme", "1003.char": "Regeln", "1100.char": "Stimme"}, "wishingring.json": {"100": ["Was auch immer ich tue, ich sollte mir nichts Dummes wünschen."], "200": ["Ein Paar Münzen für Hypnose? "], "300": ["Ein unverkennbarer Druck entsteht in der Brust, begle<PERSON>t von sofortigem Bedauern und einer zunehmenden BH-Größe."], "400": ["Eine Welle hypnotischer Magie lässt einen wie Luft fühlen, der Ring entpuppt sich als gefährlicher Ehering!"], "500": ["Mit einer Welle hypnotischer Magie fühlen sich Geist und Körper leicht wie Luft an. "], "000.CHOICE": ["Benutze es", "Wirf es weg"], "000.char": "Regeln", "000": ["Ein Ring... voller Wünsche? "], "100.CHOICE": ["<PERSON><PERSON><PERSON>, das die Suche erleich<PERSON>t.", "Je<PERSON> mit riesigen Titten.", "Bring <PERSON><PERSON><PERSON><PERSON>chen dazu, mich zu lieben.", "Gib mir einen großen Schwanz."], "100.char": "Erzählung... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat <PERSON> Ending 0 gesehen, <PERSON><PERSON><PERSON> hat <PERSON> Ending 1 gesehen, ", "100.1.CHOICE": ["<PERSON><PERSON><PERSON>, das die Suche erleich<PERSON>t.", "Ein Mädchen mit riesigen Titten.", "<PERSON><PERSON><PERSON> einen großen Schwanz."], "100.1.char": "Erzählung... alt unter Bedingungen: <PERSON><PERSON>ler hat Ram Ending 1 gesehen, ", "100.1": ["Was auch immer ich tue, ich sollte mir nichts Dummes wünschen."], "100.1.1.CHOICE": ["<PERSON><PERSON><PERSON>, das die Suche erleich<PERSON>t.", "Ein Mädchen mit riesigen Titten.", "Bring <PERSON><PERSON><PERSON><PERSON>chen dazu, mich zu lieben.", "<PERSON><PERSON><PERSON> einen großen Schwanz."], "100.1.1.char": "Erzählung", "100.1.1": ["Wenn ich nicht wieder in die Gefangenschaft der Rams geraten möchte, wähle ich lieber etwas Vernünftiges."], "100.2.CHOICE": ["<PERSON><PERSON><PERSON>, das die Suche erleich<PERSON>t.", "Ein Mädchen mit riesigen Titten.", "Bring <PERSON><PERSON><PERSON><PERSON>chen dazu, mich zu lieben."], "100.2.char": "Erzählung", "100.2": ["Was auch immer ich tue, ich sollte mir nichts Dummes wünschen."], "200.char": "Regeln", "300.char": "Erzählung", "400.char": "Erzählung", "500.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "500.1.char": "Erzählung", "500.1": ["Mitten im Wunsch wird der Gedankengang entgleist. "]}, "badend/badendcatgirl.json": {"100": ["Zeit vergeht..."], "101": ["Nya! "], "102": ["<PERSON><PERSON><PERSON> haben wir ihr dieses Ding mitgebracht! "], "103": ["Nnnn? "], "104": ["'Ku<PERSON>! "], "105": ["Ein Drink? "], "106": ["Ich habe heute ein Leckerli für dich! "], "107": ["Mit einer eifrigen Katze, die es ihr aufzwingt, ", 0, " schluckt sch<PERSON>ßlich alles herunter. "], "108": ["Die plötzliche, angene<PERSON>e Schwellung reichte aus, um sie von der vertrauten Stimme abzulenken, die sie im Flur hörte."], "109": ["Wohin denkst du denn, dass du mich hinführst?! "], "110": ["<PERSON><PERSON><PERSON><PERSON>. "], "111": ["<PERSON>e o<PERSON>, pochende Länge ergießt sich aus der tastenden Hand von ", 0, "'s <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, e<PERSON><PERSON>."], "112": ["<PERSON>n man die Stimme einer fruchtbaren, unverdorbenen Frau <PERSON>, zuckt das Organ und sendet alle möglichen Signale an das Gehirn. "], "113": ["Hier ist sie, <PERSON><PERSON><PERSON><PERSON>! "], "200": [0, "?!"], "201": ["Ich wurde absichtlich gefangen genommen, um dich zu finden, und zwar so schnell! "], "202": ["<PERSON>s ist plötzlich so offensichtlich. "], "203": ["Sie hat nicht einmal unmenschliche Ohren oder überhaupt einen Schwanz. "], "204": ["H-hey. ", 0, ", das ist mein- Aah! "], "205": ["<PERSON>t neuen Verbündeten, die Voices Arme hinter sich nehmen, ist es mühelos, den rundlichen, menschlichen Körper auf einen Schoß zu ziehen.  ", 0, " hat sich endlich entschieden."], "206": ["Es wird nicht ausreichen, nur in der Kaserne herumzuhängen und mit den Mädchen abzuspritzen."], "207": ["<PERSON><PERSON><PERSON>, wie <PERSON> zu verhandeln versucht, nur Gedanken an Sex erfüllen den Geist."], "208": ["<PERSON><PERSON> <PERSON> schwer<PERSON>, <PERSON><PERSON><PERSON> in die Luft gehoben wird, erkennt das Ringkampfpaar ein weiteres Detail: Es ist nicht nur die Zunge eines Katzenmädchens, die rau ist."], "209": ["Der Körper eines Monstermädchens ist alles andere als g<PERSON>öhnlich, einsch<PERSON>ßlich der Sexualorgane. "], "210": [0, "! "], "211": ["Das ist mein erste<PERSON>, das Ding wird mich durcheinander bringen! "], "212": ["<PERSON> findet endlich An<PERSON>ng. "], "213": ["<PERSON><PERSON>, wenn die Spitze des Schafts hi<PERSON>ingleitet, wird <PERSON> Möse mit einem Gänsehaut-Rückzug konfrontiert, der ihre Nerven zum Braten bringt, während sie die Zähne zusammenbeißt, grinst und es erträgt."], "214": ["<PERSON>e wird feucht vor Erregung, doch die raue, ausbaggernde Bewegung spritzt den Saft aus ihrer übergroßen Vulva."], "215": ["Dem unzüchtigen, schwammigen Loch wird Schritt für Schritt beigebracht, die erste Stufe der Korruption zu akzeptieren. "], "216": ["Die Abwehrkräfte des Körpers werden geschwächt, die natürlichen, menschlichen Hormone werden angeregt, die die Fortpflanzung und die Akzeptanz des Partners erleichtern."], "217": ["Der Raum füllt sich mit unzüchtigem, feuchtem Sex. "], "218": ["Der „Bösewicht“ hat bereits begonnen, einer von ihnen zu werden, ein Sklave des Vergnügens. "], "219": ["Die Stimme keucht atemlos, als sie beginnt, die Kontrolle über ihren Körper zu verlieren und sich einzucremen, aber die heftigste Dosis Korruption steht ihr noch bevor."], "220": ["<PERSON> Katzen in der Nähe kichern mit einem grinsenden Grinsen, während die beiden sich in korrupten Sex stürzen."], "221": ["<PERSON>hr Rückgrat kribbelt bis zum Steißbein, ihre Zunge hängt herab, während ihr Verstand durch das Schaukeln ihres Körpers durcheinander gebracht wird."], "222": [0, "Sein <PERSON> hämmert gegen die zitternde Gebärmutter, ein unkontrollierbares Gefühl steigt die steinharte Länge hinauf."], "223": ["<PERSON>ya<PERSON>- ich komme!"], "224": ["<PERSON>e zucken zusammen, klebrig-heiße weiße Strahlen drängen sich gegen Voices Bauch."], "225": ["In einem Aufblitzen von Weiß erreichen sie eine süße Befreiung und ertrinken in einer fröhlichen Sanftheit, während die beiden gemeinsam keuchen."], "226": ["<PERSON><PERSON> gibt so viel, dass es überläuft, der Bauch ist völlig mit transformierendem Sperma gefüllt."], "227": [0, " spürt ein Z<PERSON>hen am Knöchel, dann wieder etwas verlangend. "], "228": ["Zwischen ihren Beinen tragen sie nun jeweils einen schlanken, aber flauschigen Katzenschwanz. "], "229": ["<PERSON>u sehr in den Moment vertieft, um zu erkennen, dass jeder mit Sperma glitschige Stoß die Verderbtheit noch tiefer in sie hineinwühlt. "], "230": ["Da sie keine Z<PERSON> haben, ihre Gedanken zu klären, erliegen die beiden ihren neuen Instinkten."], "231": ["Die Katzenritter füllen die Seitenlinie, um die neuen „Rekruten“ willkommen zu heißen, spielen unterein<PERSON>, wichsen und verfallen in Orgien."], "232": ["Die Reise endet, bevor sie überhaupt begonnen hat, aber es ist ihnen egal. "], "233": ["Die Region würde bald von verspielten Katzenmädchen dominiert werden, die Reisende mit ihrer bloßen Zahl überwältigen würden."], "234": ["Am Ende wurde ich zu einem verbitterten Monstermädchen, das von Sex besessen war ..."], "000.char": "Erzählung... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Cat Ending 0 g<PERSON><PERSON>, ", "000": ["Überwält<PERSON><PERSON> von fremden Empfindungen stellt sich ein rosa <PERSON> ein."], "000.1.char": "Erzählung", "000.1": ["Überwältigt von fremden Empfindungen breitet sich ein rosa <PERSON>hleier aus. Wieder ein<PERSON> haben die Katzenmädchen ihren Tribut gefordert."], "001.char": "Erzählung... alt unter Bedingungen: Playervariables.consent == false, ", "001": ["Das Necken und Befummeln war anfangs erträglich gewesen, die raue Zunge eines Katzenmädchens würde nur eine anhaltende Hitze hinterlassen."], "001.1.char": "Erzählung", "001.1": [0, " Habe mich bei den Katzenmädchen eingelebt und glücklich bis ans Ende ihrer Tage gelebt (NSFW-Inhalt deaktiviert). "], "002.char": "Regeln", "002": ["Hya?! "], "003.char": "Erzählung", "003": ["Mit der letzten schwarzen Kugel, die vorbeigezogen wurde ", 0, "<PERSON>t seinem eigenen, verwan<PERSON><PERSON> Katzenschwanz durchströmen die Nerven ein elektrisierendes Vergnügen durch den ganzen Körper, das sich bei der Berührung der frechen Mädchen noch verstärkt."], "004.char": "Regeln", "004": ["Nhyaaa. "], "005.char": "Erzählung", "005": ["Der ehemalige Mensch kann nur erbärmliche, mäd<PERSON><PERSON>e Schreie von sich geben, während sie von dem glitschigen, schl<PERSON><PERSON><PERSON>en Muskel hingerissen wird und ihren Geist mit Dopamin überschwemmt."], "006.char": "Erzählung", "006": ["Immer mehr dieser halbmenschlichen Mädchen strömen herein, angezogen vom hilflosen Stöhnen ihrer neuen Kameradin.  ", 0, "'s <PERSON><PERSON><PERSON><PERSON>."], "007.char": "Erzählung", "007": ["An ihrem ersten Tag in ihrem neuen Leben kam sie ein Dutzend Mal, bevor sie in ihr Versteck verschleppt wurde. "], "100.char": "Erzählung", "101.char": "CatKnight", "102.char": "CatKnight", "103.char": "CatKnight", "104.char": "CatKnight", "105.char": "Regeln", "106.char": "CatKnight", "107.char": "Erzählung", "108.char": "Erzählung", "109.char": "Stimme", "110.char": "CatKnight", "111.char": "Erzählung", "112.char": "Erzählung", "113.char": "CatKnight", "200.char": "Stimme", "201.char": "Stimme", "202.char": "Erzählung", "203.char": "Erzählung", "204.char": "Stimme", "205.char": "Erzählung", "206.char": "Erzählung", "207.char": "Erzählung", "208.char": "Erzählung", "209.char": "Erzählung", "210.char": "Stimme", "211.char": "Stimme", "212.char": "Erzählung", "213.char": "Erzählung", "214.char": "Erzählung", "215.char": "Erzählung", "216.char": "Erzählung", "217.char": "Erzählung", "218.char": "Erzählung", "219.char": "Erzählung", "220.char": "Erzählung", "221.char": "Erzählung", "222.char": "Erzählung", "223.char": "Stimme", "224.char": "Erzählung", "225.char": "Erzählung", "226.char": "Erzählung", "227.char": "Erzählung", "228.char": "Erzählung", "229.char": "Erzählung", "230.char": "Erzählung", "231.char": "Erzählung", "232.char": "Erzählung", "233.char": "Erzählung", "234.CHOICE": ["<PERSON> vorn anfangen"], "234.char": "Regeln"}, "badend/badenddefault.json": {"000.char": "Erzählung... alt unter Bedingungen: endGeneric0, ", "000": ["Die Ausdauer erreicht 0. Das Gewicht der Erschöpfung zieht wie Fesseln nach unten."], "000.1.char": "Erzählung", "000.1": ["Ausdauer erreicht 0."], "001.char": "Erzählung", "001": ["<PERSON>t der letzten Energie, ", 0, " kämpft sich durch das Unterholz und sackt zu Boden."], "002.char": "Erzählung", "002": ["Regen setzt ein. Die Zeit vergeht. "], "003.char": "Erzählung", "003": ["Auch wenn die Sonne, die Sterne und die Wolken vorbeiziehen, ist nur das prasselnde Prasseln des Regens auf dem Blätterdach darüber zu hören."], "004.char": "Erzählung", "004": ["Einschlafen..."], "005.char": "Erzählung", "005": ["<PERSON><PERSON><PERSON>, rascheln..."], "006.CHOICE": ["<PERSON>ück<PERSON><PERSON> zu <PERSON>"], "006.char": "Erzählung", "006": ["<PERSON><PERSON>, ", 0, " wurde dieses Mal sicher weggebracht."]}, "badend/badenddragonharpy.json": {"000.char": "Erzählung", "000": [0, " beginnt das Bewusstsein zu verlieren..."], "001.char": "Erzählung... alt unter Bedingungen: Playervariables.consent == false, <PERSON>pieler ist im Ei einer Harpyie gefangen., <PERSON>pie<PERSON> hat CUM-Debuff, ", "001": ["<PERSON>l<PERSON>t wenn man den E<PERSON>n aus dem Weg geht, reicht die schiere Größe einer Harpyie aus, um zu stürzen ", 0, " ganz oben."], "001.1.char": "Erzählung", "001.1": [0, " wurde entführt und sie lebten glücklich bis ans Ende ihrer Tage (NSFW-Inhalt deaktiviert)."], "001.2.char": "Erzählung", "001.2": ["Im Ei der Harpyie stecken geblieben, so kommt zumindest kein Monster hindurch. "], "001.3.char": "Erzählung", "001.3": ["Was ist das für ein Mist überhaupt? "], "002.char": "Erzählung... alt unter Bedingungen: Der Spieler ist im E<PERSON> einer Harpyie gefangen., ", "002": ["In den weiten Himmel getragen, wird alles schwarz. "], "002.1.char": "Erzählung", "002.1": ["-und ein allmähliches Auf und Ab, als würde man darum kämpfen, in den Himmel zu fliegen ..."], "003.char": "Erzählung", "003": ["Mit der Zeit gibt es endlich einen Blick auf das große Blau dahinter. "], "004.char": "Erzählung", "004": ["Unten gibt es keinen Boden. "], "005.CHOICE": ["<PERSON> vorn anfangen"], "005.char": "Erzählung... alt unter Bedingungen: Spieler hat Harpyienflügel, ", "005": ["Ich stecke mich wieder in das isolierte Ei und trä<PERSON> da<PERSON>, eines Tages von hier aus zu fliegen ..."], "005.1.CHOICE": ["<PERSON> vorn anfangen"], "005.1.char": "Erzählung", "005.1": ["Ich habe keine Wahl mehr. "]}, "badend/badendfoxgirl.json": {"100": ["Wie zieht mich ein <PERSON>?!"], "101": ["<PERSON><PERSON><PERSON><PERSON>. "], "102": ["Ich versuche zu antworten – es kommen keine Worte. "], "103": ["..."], "104": ["<PERSON><PERSON> ist Morgen. "], "105": ["Und ich sitze in der Falle?! "], "106": ["Ich liebe dich, <PERSON><PERSON>! "], "107": ["<PERSON><PERSON>t durch meinen Geist. "], "108": ["Ich bin süchtig! "], "109": ["Sel<PERSON>t als Geist spüre ich etwas Salziges auf meiner Zunge. "], "110": ["Ich spähe durch die Wände. "], "111": ["Gllp... <PERSON>. "], "112": ["NEIN! "], "113": ["Du bedürftige Fotze, das reicht. "], "114": ["Slurrrp. "], "115": ["Ja ja. "], "116": ["<PERSON><PERSON><PERSON>, es ist Voices ehemalige Schlampe. "], "117": ["Das sind die Ohren und der Schwanz des Geistes. "], "118": ["Hast du nicht aufgepasst? "], "119": ["<PERSON>ch nehme meinen Körper zurück, ob sie es will oder nicht. "], "120": ["<PERSON><PERSON><PERSON> <PERSON><PERSON>, ich bin immer noch an der Reihe, wir müssen den Meister mehr bedienen!"], "121": ["..."], "200": ["Endlich mein Körper!... Warum bin ich jetzt ein Fuchs<PERSON>ädchen?!"], "201": ["Willkommen zurück, ", 0, ". "], "202": ["Was soll ich...? "], "203": ["... Das hat der Geist gesagt! "], "204": ["<PERSON>ch habe gesehen, wie es dir geht. "], "205": ["Den<PERSON>ch denkst du immer wieder an den Turm."], "206": ["Du bist süßer, wenn du Schwierigkeiten hast, aber ich kann nicht riskieren, dass du etwas Gefährliches tust. "], "207": ["Du wirst meine liebe, geh<PERSON><PERSON> sein, ja?"], "208": ["Ich werde! "], "209": ["Wir müssen an dir arbeiten, <PERSON><PERSON>d<PERSON>. "], "210": ["Ich habe deine alte Ausrüstung durch richtige Spirit-Werkzeuge ersetzt. "], "211": ["Ja..."], "212": ["Sel<PERSON>t wenn ich mich befreie, krieche ich unweigerlich zurück. "], "213": ["Das ist jetzt mein Zuhause..."], "000.char": "Erzählung... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat <PERSON> Ending 0 g<PERSON><PERSON>, ", "000": ["<PERSON>ch drift<PERSON> da<PERSON>, als würde ich in das Reich der Träume hineingezogen. "], "000.1.char": "Erzählung", "000.1": ["<PERSON>ch drift<PERSON> da<PERSON>, als würde ich in das Reich der Träume hineingezogen. "], "001.char": "Erzählung... alt unter Bedingungen: Playervariables.consent == false, ", "001": ["Der Boden ist unten noch spürbar, die Umgebung kaum sichtbar-"], "001.1.char": "Erzählung", "001.1": [0, " wurde zur Arbeit am Kitsune-Schrein entführt und sie lebten glücklich bis ans Ende ihrer Tage (NSFW-Inhalt deaktiviert). "], "002.char": "Erzählung", "002": ["Und ich selber)? "], "003.char": "Erzählung... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Fuchsschwanz, <PERSON><PERSON><PERSON> hat <PERSON>chsohren, <PERSON><PERSON><PERSON> hat Katzenschwanz, <PERSON><PERSON><PERSON> hat <PERSON><PERSON><PERSON>h<PERSON>rner, Oberweite des Spielers ist > 2, <PERSON><PERSON><PERSON> hat Widderoberteil, ", "003": ["<PERSON>ch kann diese Geister jetzt sehen, <PERSON><PERSON><PERSON>k<PERSON><PERSON>, die aus meinem Körper ragen. "], "003.1.char": "Erzählung", "003.1": ["<PERSON><PERSON><PERSON>n mit der Schande, meinen stockfuchsigen Zustand zu sehen. "], "003.2.char": "Erzählung", "003.2": ["Von hier aus kann ich diese Fuchs<PERSON>ren sehen, die auf dem Kopf meines Körpers sitzen und mich markieren, als wäre ich einer ihrer Verwandten."], "003.3.char": "Erzählung", "003.3": ["Befreit von der Last meines Katzenmädchen-Körpers kann ich wieder klar denken."], "003.4.char": "Erzählung", "003.4": ["Obwohl ich die Hörner des Widders immer noch sehen kann, ist bei mir keine Fuchsverderbnis zu erkennen. "], "003.5.char": "Erzählung", "003.5": ["Ich kann nicht glauben, dass ich mit so einer Brust herumgelaufen bin. "], "003.6.char": "Erzählung", "003.6": ["Wenn ich es jetzt von außen betrachte, wie konnte ich nur so ein beschämendes Outfit tragen! "], "004.char": "Erzählung", "004": ["Allerdings steht dieser Schreinfuchs mit seinen Geistern über meinem Körper. "], "005.char": "Erzählung", "005": ["... aber ein Pa<PERSON> geisterhafter Hände streckt sich von hinten aus ..."], "100.char": "Erzählung", "101.char": "FoxGirl", "102.char": "Erzählung", "103.char": "Erzählung", "104.char": "Erzählung", "105.char": "Erzählung... alt unter Bedingungen: Playervariables.possessionrank == true, ", "105.1.char": "Erzählung", "105.1": ["Und ich sitze in der Falle?! "], "106.char": "Regeln", "107.char": "Erzählung", "108.char": "Regeln", "109.char": "Erzählung", "110.char": "Erzählung", "111.char": "Regeln", "112.char": "Erzählung", "113.char": "FoxGirl", "114.char": "Regeln", "115.char": "FoxGirl", "116.char": "FoxGirl", "117.char": "Erzählung... alt unter Bedingungen: <PERSON>pieler hat Fuchsschwanz, <PERSON><PERSON><PERSON> hat Fuchsohren, ", "117.1.char": "Erzählung", "117.1": ["<PERSON>en Sinn hatte es, mich in ein Fuchsmädchen zu verwandeln, wenn mein <PERSON>rper weggenommen wird?!"], "117.2.char": "Erzählung", "117.2": ["Das mögen meine <PERSON> sein, aber solche Schwänze hatte ich nie. "], "118.char": "FoxGirl... alt unter Bedingungen: <PERSON>pieler hat Fuchsschwanz, ", "118.1.char": "FoxGirl", "118.1": ["Dein dummer Körper wedelte schon mit dem Schwanz, bevor ich dich besaß. "], "119.char": "Erzählung", "120.char": "Regeln", "121.char": "Erzählung", "200.char": "Regeln... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Fuchsschwanz, ", "200.1.char": "Regeln", "200.1": ["<PERSON><PERSON> ist mein Körper zurück!"], "201.char": "FoxGirl", "202.char": "Regeln", "203.char": "Regeln", "204.char": "FoxGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat <PERSON> Ending 0 gesehen, <PERSON><PERSON><PERSON> hat <PERSON> Ending 1 gesehen, <PERSON><PERSON><PERSON> hat <PERSON> Ending 0 gesehen, <PERSON><PERSON><PERSON> hat <PERSON> Ending 0 gesehen, ", "204.1.char": "FoxGirl", "204.1": ["Du hast die Angewohnheit wegzulaufen, nicht wahr? "], "204.2.char": "FoxGirl", "204.2": ["Ich glaube nicht, dass ich es nicht weiß. "], "204.3.char": "FoxGirl", "204.3": ["Ich glaube nicht, dass ich es nicht weiß. "], "204.4.char": "FoxGirl", "204.4": ["Ich glaube nicht, dass ich es nicht weiß. "], "205.char": "FoxGirl", "206.char": "FoxGirl", "207.char": "FoxGirl", "208.char": "Regeln", "209.char": "FoxGirl... alt unter Bedingungen: Oberweite des Spielers ist > 3, Oberweite des Spielers ist > 2, Oberweite des Spielers ist > 1, ", "209.1.char": "FoxGirl", "209.1": ["<PERSON><PERSON><PERSON><PERSON>, wir haben viel zu tun. "], "209.2.char": "FoxGirl", "209.2": ["Wir haben viel zu tun, <PERSON><PERSON>d<PERSON>. "], "209.3.char": "FoxGirl", "209.3": ["Du wirst die Brustbinden einer Schreinjungfrau tragen, während wir deine Einstellung korrigieren."], "210.char": "FoxGirl", "211.char": "Regeln", "212.char": "Erzählung", "213.CHOICE": ["<PERSON> vorn anfangen"], "213.char": "Erzählung"}, "badend/badendramgirl.json": {"300": ["Sssh. "], "301": ["Du bist eine anzügliche Hypno-Schlampe, die aus ihrer Fotze sabbert. "], "302": ["<PERSON><PERSON>, sobald du spürst, wie eine Ladung meiner heißen Wichse deine ausgetrocknete Kehle überflutet, wirst du aufhören zu denken und anfangen zu schlucken. "], "303": ["Als ob ich- Hnnh?!"], "304": ["Mit einem plötzlichen Keuchen, ", 0, " beißt sich auf die Lippe, als sie den krampfartigen Griff ihrer eigenen nassen Fotze spürt – sie hat sich gerade mit dem Finger zum Orgasmus gebumst?!"], "305": ["<PERSON><PERSON>, was für einen bösen B<PERSON> du mir zuwirfst... Während du nichts anderes tust, als Fantasien über mich auszuleben."], "306": ["Ich bin nicht-- Du schumme<PERSON>t mit dieser blöden fetten Fleischrute, kämp<PERSON> fair mit mir!"], "307": ["<PERSON><PERSON><PERSON>, sich sel<PERSON>t anzulü<PERSON>, <PERSON><PERSON> sind das Ebenbild einer verlegenen Schlampe. "], "308": ["<PERSON>rt stehen gel<PERSON>, mit O-Beinen und beschämt, und zu<PERSON>hen, wie das hypnotische Pendel wieder den Weg hinunterschlägt – das ist eine Selbstverständlichkeit. ", 0, " folgt i<PERSON><PERSON>."], "309": ["Mmm? "], "310": ["Ich kann dich nicht besiegen, wenn du wegläufst. "], "311": ["Oder will dein durstiger Frauenkörper wissen, wie tief ich in dich eindringen werde?"], "312": ["...! "], "313": ["Ein Mitbewerber? "], "314": ["?? "], "315": ["Das begabte, hochmütige Ram-Mädchen wird in die Enge getrieben. "], "316": ["Der gierige Mund eines lusttrunkenen Mädchens senkt sich darauf, nur um zu zögern. "], "317": ["Dann ein <PERSON>nan<PERSON>riff!  ", 0, " durch den Pony, was sie da<PERSON>, eine große Menge verwirrender Paarungspheromone einzuatmen."], "318": ["Aufleuchten. "], "319": ["<PERSON> zärtlicher, vors<PERSON><PERSON><PERSON> Le<PERSON> fällt auf das Ruder, dann noch einer und taucht das zuckende Ding in einen schlampigen Knutschfleck."], "320": ["Mmmh. "], "321": ["Ein plötzlicher Stoß überrascht den Futa, Zentimeter für Zentimeter sinkt das pochende Werkzeug an ihm vorbei ", 0, "'s <PERSON><PERSON><PERSON>, e<PERSON><PERSON> daran g<PERSON>."], "322": ["Haaa. "], "323": ["<PERSON><PERSON><PERSON><PERSON> dieser Schlund vergeblich versucht zu schlucken, schickt der Druck an der Kehle weiße Blitze durch den Körper."], "324": ["<PERSON>cht zufrieden damit, zu verlieren, ", 0, " legt ihre Hände mit drehenden und ruckartigen Bewegungen um das unverschluckte Stück an der Basis, alles, um eine dicke Ladung herauszulocken."], "325": ["<PERSON><PERSON>, j<PERSON><PERSON> sing<PERSON>, das aus dem Schwanzmädchen hervorgeht, best<PERSON><PERSON><PERSON> nur, dass sie etwas richtig macht. "], "326": ["<PERSON><PERSON><PERSON>, bist du so scharf da<PERSON>, mich zum Abspritzen zu bringen, du versaute Göre? "], "327": ["Vorsperma strömt gegen die pralle <PERSON>, die sanfte Salzigkeit vermischt sich, während die Zunge darüber wirbelt. "], "328": ["Das ist- hhhf- genug, du moschustrunkenes Miststücknnh?!"], "329": ["<PERSON><PERSON> bilden sich Strahlen aus heißer, <PERSON><PERSON> C<PERSON> ", 0, "Sein Gaumen ist in süßlicher Klebrigkeit, Pulsieren und Pochen, und er stopft Schuss für Schuss mehr hinein."], "330": ["Ein plötzliches Ziehen an den Haaren zwingt die Sperma spuckende Spitze in den hinteren Teil der Kehle, klebrige Hitze stopft die Speiseröhre voll, während sich die Nüsse des Widderbullen anspannen, um die Ladung herauszuquetschen."], "331": ["Es kommt und kommt und ertränkt dieses selbstgefällige Gesicht in einem bewusstseinsverändernden, von Korruption durchzogenen Meer aus cremeweißem Weiß."], "332": ["<PERSON><PERSON><PERSON><PERSON>, zittrige Stöße treiben die Spitze voran.  ", 0, "<PERSON><PERSON> dr<PERSON><PERSON> ihr <PERSON>nn, während ihr Hals darum kämpft, jede frische Portion herunterzudrücken."], "333": ["Jeder Tropfen dringt ein oder wird pflichtbewusst aufgeleckt, während Dampf aus ihrer Nase aufsteigt und verzweifelt schnauft, um eine Lunge voll frische Luft zu bekommen."], "334": ["<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> H<PERSON>rner kommen endlich zum Vorschein. Auch weichere Haut? "], "335": ["Gere<PERSON><PERSON>? "], "336": ["<PERSON><PERSON><PERSON>. "], "337": ["Du hast mich dazu verleitet zu glauben, dass ich Menschen helfen wollte?! "], "338": ["<PERSON>au so kämpfe ich mit Kerlen wie dir! "], "339": ["Bis zum Anschlag in deinem Deepthroat? "], "340": [0, " stellt es nicht einmal in Frage und schluckt die gefährliche Ladung eines Monstermädchens hinunter. "], "341": ["Diese beiden verbringen jede Nacht damit, dass der eine fest im Geschlecht des anderen verankert ist. "], "342": ["Sie legt ihre Waffen ab und schikaniert auf anzügliche Weise jeden G<PERSON>ner, dem sie begegnet, und lähmt ihn mit ihren perversen Techniken, während der dicke Turm ihres Partners jeden Widerstand ausstößt."], "343": ["Sogar andere Monster-Girls wachen an manchen Tagen vielleicht mit dem Geschmack roher <PERSON>, einem triefenden Creampie oder flauschigerem Ha<PERSON> und weicherer, mädchenhafterer Haut auf ..."], "344": ["Aah. "], "345": ["Nicht wahr? "], "346": ["Waaat, sind deine Eier schon wieder überfüllt? "], "347": ["Die Reise geht zu Ende. "], "000.char": "Erzählung... alt unter Bedingungen: Playervariables.makeuprank == true, <PERSON><PERSON><PERSON> hat Ram Ending 0 gesehen, ", "000": ["Gefangen in der sanften Umarmung des Widdermädchens und eingelullt vom sanften, melodischen Summen, ", 0, "Die Spannung beginnt zu schmelzen."], "000.1.char": "Erzählung... alt unter Bedingungen: <PERSON><PERSON>ler hat Ram Ending 1 gesehen, ", "000.1": ["<PERSON><PERSON><PERSON><PERSON><PERSON>, dem Einfluss des mächtigen Widders zu entkommen, ", 0, " ble<PERSON>t stehen und folgt jeder Bewegung der Dame."], "000.1.1.char": "Erzählung", "000.1.1": ["<PERSON><PERSON><PERSON> ein<PERSON> un<PERSON>, dem Einfluss des mächtigen Widders zu entkommen, ", 0, " ble<PERSON>t stehen und folgt jeder Bewegung der Dame."], "000.2.char": "Erzählung", "000.2": ["Wieder einmal gefangen in der sanften Umarmung des Widdermädchens und eingelullt von dem sanften, melodischen Summen, ", 0, "Die Spannung beginnt zu schmelzen."], "001.char": "Erzählung... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, ", "001": ["Die Schenkel des Ram-Girls sind perfekt angespannt, die Lippen der beiden bleiben aneinandergeklebt, ihre Blicke sind aufeinander gerichtet."], "001.1.char": "Erzählung", "001.1": ["Saß auf diesen perfekt weichen Widderschenkeln, be<PERSON> H<PERSON>rner ineinandergreifend. "], "002.char": "Erzählung", "002": ["Es werden keine Worte ausgetauscht, nur das sanfte Pochen ihrer Herzen, das hypnotische Schlaflied und der Kuss eines Liebhabers."], "003.char": "Erzählung", "003": ["<PERSON><PERSON><PERSON>, ob <PERSON> oder nicht, wagt es, die Heiligkeit eines Widdermädchens und seiner Beute zu brechen."], "004.char": "Erzählung... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, ", "004": [0, " lässt langsam den Griff um ihre Waffe los. "], "004.1.char": "Erzählung", "004.1": ["Oder besser gesagt, die beiden Ram-Mädchen.  ", 0, " wirft ihre Waffe weg. "], "005.char": "Erzählung... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, ", "005": ["<PERSON>s ist eine Umarmung, die die feurige, kriegerische Mentalität dahinschmilzt und sie in ein süßes, leuch<PERSON>es G<PERSON> taucht."], "005.1.char": "Erzählung", "005.1": ["Es befriedigt ein tief verwurzeltes Urbedürfnis und verleiht einem durch seinen Kuss Kraft, während sich der „Kampf“ hinz<PERSON>, während die beiden Widder ebenbürtig sind."], "006.char": "Erzählung", "006": ["<PERSON>s ist nicht sicher, wie lange die beiden so verbracht haben. "], "007.char": "Ram<PERSON><PERSON><PERSON>", "007": ["Haaa.  ", 0, "?"], "008.char": "Erzählung", "008": ["Etwas stimmt nicht, etwa ein dumpfes Summen. "], "009.char": "Erzählung", "009": ["<PERSON>s der Widder dieses Zögern sieht, stürzt er sich darauf ", 0, " und hinterlässt einen weiteren Kuss, um den Geist in einen glücklichen Wirbel zu versetzen."], "010.char": "Regeln", "010": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ich muss schockiert gewesen sein, wie schnell mein Leben so großartig geworden ist?"], "011.char": "RamGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, ", "011": ["<PERSON><PERSON><PERSON>. "], "011.1.char": "Ram<PERSON><PERSON><PERSON>", "011.1": ["<PERSON><PERSON><PERSON>. "], "012.char": "Erzählung", "012": ["Obwohl es ein langer Weg zurück ist, unterstützt der Ram ", 0, " Die ganze Zeit über, um diesem vom Kampf gezeichneten Körper jederzeit die Möglichkeit zu geben, anzuhalten und sich auf ihrem <PERSON> au<PERSON>."], "013.char": "Erzählung", "013": ["Der ganze Spaziergang ist eine Reise der kognitiven Dissonanz; "], "014.char": "Erzählung", "014": ["Die beiden kommen an einer Reihe neugieriger Katzenmädchen vorbei, der Widder antwortet mit einem schützenden Druck auf ihren Liebling."], "015.char": "Erzählung", "015": ["So geben ihr müder Körper und Geist nach und sehnen sich nach Trost und Geborgenheit. "], "016.char": "Erzählung", "016": ["..."], "017.CHOICE": ["Aufwachen"], "017.char": "Ram<PERSON><PERSON><PERSON>", "017": ["<PERSON><PERSON> <PERSON><PERSON>, Honigbrötchen. "], "018.char": "Erzählung", "018": [0, " wacht mit einem kühlen Gefühl auf. "], "019.char": "Erzählung", "019": ["Warum? "], "020.char": "Erzählung", "020": ["<PERSON><PERSON> der Treppe fallen Schritte. "], "021.char": "Ram<PERSON><PERSON><PERSON>", "021": ["<PERSON><PERSON> wach, Lie<PERSON>. "], "022.char": "Erzählung", "022": ["<PERSON><PERSON> is<PERSON>, als würde sie zu einem verwundeten Tier blicken, das sie aufgenommen hatte. <PERSON><PERSON> jetzt, da es sich soweit erholt hat, dass es sich bewegen kann, fürchtet es sich vor dem Entführer."], "023.char": "Ram<PERSON><PERSON><PERSON>", "023": ["<PERSON><PERSON>. "], "024.char": "Ram<PERSON><PERSON><PERSON>", "024": ["<PERSON>s ist der große, b<PERSON><PERSON>, der deine Lippen und dein Herz gestohlen und mit dir herumgespielt hat. "], "025.char": "Erzählung", "025": ["Sie steht im Licht und streckt die Arme seitlich aus, so dass sie jeder Art von Angriff ausgesetzt ist."], "026.char": "Erzählung", "026": ["W<PERSON>hrend sich die Luft im Raum beruhigt, weht der Geruch von frischen Pfannkuchen, <PERSON><PERSON><PERSON> und Eiern aus der unteren Etage. ", 0, " hat seit dem Shepherd's Pie gestern Abend nichts mehr gegessen."], "027.char": "Erzählung", "027": ["Das ist nicht wichtig. ", 0, " greift nach einem steifen Metallgegenstand am Bett. "], "028.char": "Erzählung", "028": ["<PERSON> Ram-Mädchen zuckt nicht ein<PERSON> zu<PERSON>mmen, als ihre gefangene Braut aus dem Bett aufsteht. "], "029.char": "Erzählung", "029": ["Und während die Hand über ihre „Waffe“ streicht, erklingt jede <PERSON> in einem ansteigenden Glissando und durchbricht die kühle Stille des Raumes mit dem vertrauten Klang der Leier."], "030.char": "Ram<PERSON><PERSON><PERSON>", "030": ["Oh, mach doch keine so große Sa<PERSON> draus. "], "031.char": "Regeln", "031": ["Du hast meinen Kopf mit all diesen falschen, glücklichen Erinnerungen gefüllt! "], "032.char": "Erzählung", "032": ["Mit einem <PERSON> bewegt sich die flauschige, ve<PERSON><PERSON>, setzt sich auf das Bett und legt sich auf den Rücken, endlich in der Lage, sich zu treffen ", 0, "'s niedergeschlagener Gesichtsausdruck."], "033.char": "Ram<PERSON><PERSON><PERSON>", "033": ["Wenn <PERSON> nicht wollen, dass es endet, warum sollte es dann? "], "034.char": "Regeln", "034": ["Denn es war alles Fake!"], "035.char": "Ram<PERSON><PERSON><PERSON>", "035": ["Dann kommen <PERSON> hierher und machen <PERSON> es wahr."], "036.char": "Erzählung", "036": ["Sie war nicht länger verzaubert und konnte jederzeit gehen. "], "037.char": "Erzählung", "037": [0, " fä<PERSON>t wieder in die Fänge des Widdermädchens und legt die Leier beiseite. "], "038.char": "Ram<PERSON><PERSON><PERSON>", "038": ["Dort Dort. "], "039.char": "Erzählung", "039": ["Die aufwallende Frustration zurückschlagen, ", 0, " erstreckt sich nun rund um diesen großen, kurvigen Körper. "], "040.char": "Ram<PERSON><PERSON><PERSON>", "040": ["<PERSON><PERSON><PERSON><PERSON>, ist dir das nicht aufgefallen? "], "041.char": "Regeln", "041": ["Das stimmt nicht – <PERSON>yau?!"], "042.char": "Erzählung... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, ", "042": ["Das Widdermädchen besänftigt die Gegenrede ruhig und mühelos mit festem Griff und ungezügelten Kopfstreicheln."], "042.1.char": "Erzählung", "042.1": ["Das größere Widdermädchen legt ihre Hand um ein Horn und zieht leicht daran, was eine sanfte Reaktion hervorruft."], "043.char": "RamGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, ", "043": ["<PERSON><PERSON><PERSON> hin oder her, du bist eher ein Kuschelmonster als ein Kämpfer, oder? "], "043.1.char": "Regeln", "043.1": ["<PERSON><PERSON>! "], "044.char": "Regeln... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, ", "044": ["Ich werde es dir zeigen, nachdem du mir Kopfstreiche, Lo<PERSON> und gebührende Anbetung gegeben hast!"], "044.1.char": "Erzählung", "044.1": ["An der Seite spiegelt ein hoher Spiegel die beiden gehörnten, kuschelfreudigen Damen wider, die wie Freundinnen aneinander kuscheln."], "045.char": "RamGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, ", "045": ["Mmm. "], "045.1.char": "Erzählung", "045.1": ["Beide sehen die gleiche Szene.  ", 0, "Das Selbstbild des Menschen kämpft darum, das „Menschliche“ zu finden."], "046.char": "Erzählung... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, ", "046": ["<PERSON>s dauert einige Zeit, das Jetztbewusste zur Ruhe zu bringen ", 0, "Erst dann erheben sie sich von den Laken und gehen Hand in Hand die Treppe hinunter. "], "046.1.char": "Erzählung", "046.1": ["Mehr wird nicht gesagt. "], "047.char": "Erzählung... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, ", "047": ["Mit nichts weiter als einer Schürze an, werden Pfannkuchen auf dem Teller der Frau serviert, während Ram-Girl die ganze Zeit freche Blicke auf ihre Brüste und ihren Körper wirft."], "047.1.char": "Erzählung", "047.1": ["Die Gewohnheiten der letzten Woche sind nicht verblasst. "], "048.char": "Erzählung... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, ", "048": ["So beginnen die Tage wie eine nie enden wollende Flitterwochen. "], "048.1.char": "Erzählung", "048.1": ["So beginnen ihre Flitterwochen. "], "049.char": "Regeln", "049": ["„<PERSON>urück nach Hause“? "], "050.char": "Ram<PERSON><PERSON><PERSON>", "050": ["Der Terminkalender meines Schatzes ist heute voll. "], "051.char": "Erzählung", "051": ["<PERSON>hon bevor man ein Widdermädchen wurde, versuchten die Leute es zu nehmen ", 0, " für sich selbst. "], "052.CHOICE": ["<PERSON> vorn anfangen"], "052.char": "Erzählung", "052": ["Die Reise geht zu Ende. "], "300.char": "Ram<PERSON><PERSON><PERSON>", "301.char": "Ram<PERSON><PERSON><PERSON>", "302.char": "RamGirl... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "302.1.char": "Ram<PERSON><PERSON><PERSON>", "302.1": ["In dem Moment, in dem du spürst, wie der Saft meiner Fotze über dein Gesicht fließt, wirst du aufhören zu denken und anfangen zu schlucken. "], "303.char": "Regeln... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Katzenohren, ", "303.1.char": "Regeln", "303.1": ["Als ob ich- Nyaaa?!"], "304.char": "Erzählung... alt unter Bedingungen: Spieler hat Katzenohren, ", "304.1.char": "Erzählung", "304.1": ["Beschämt über ihren katzenartigen Ausrutscher, ", 0, " beißt sich auf die Lippe und unterdrückt ihr Schnurren, während sie den krampfartigen Griff ihrer eigenen nassen Fotze spürt – sie hat sich gerade mit dem Finger zum Orgasmus gebumst?!"], "305.char": "RamGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, ", "305.1.char": "Ram<PERSON><PERSON><PERSON>", "305.1": ["<PERSON><PERSON>, was für einen fiesen Blick du mir zuwirfst ... Während du deinen Fantasien nachgibst, du<PERSON><PERSON><PERSON><PERSON><PERSON> von Korruption und herausgewachsenen Hörnern."], "306.char": "Regeln... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "306.1.char": "Regeln", "306.1": ["Ich bin nicht... Du betrügst und verarscht meinen Kopf, kämpf fair mit mir!"], "307.char": "Ram<PERSON><PERSON><PERSON>", "308.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "308.1.char": "Erzählung", "308.1": ["<PERSON>rt stehen gel<PERSON>, mit O-Beinen und beschämt, und zu<PERSON>hen, wie dieser hypnotische Arsch den Weg zurückgeht – das ist eine Selbstverständlichkeit. ", 0, " folgt i<PERSON><PERSON>."], "309.char": "Ram<PERSON><PERSON><PERSON>", "310.char": "Regeln", "311.char": "Ram<PERSON><PERSON><PERSON>", "312.char": "Regeln... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "312.1.char": "Regeln", "312.1": ["...! "], "313.char": "RamGirl... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "313.1.char": "Ram<PERSON><PERSON><PERSON>", "313.1": ["Es wird mir leid tun? "], "314.char": "Regeln... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "314.1.char": "Regeln", "314.1": ["?? "], "315.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "315.1.char": "Erzählung", "315.1": ["Das begabte, hochmütige Ram-Mädchen wird in die Enge getrieben. "], "316.char": "Erzählung", "317.char": "Erzählung", "318.char": "Ram<PERSON><PERSON><PERSON>", "319.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "319.1.char": "Erzählung", "319.1": ["<PERSON><PERSON><PERSON><PERSON>, vorsichtige Lecks fallen auf die Vulva des Widders, über ihre Klitoris, und werden immer schlampiger und schlampiger."], "320.char": "Ram<PERSON><PERSON><PERSON>", "321.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, <PERSON><PERSON><PERSON> hat Widder-Makeup, ", "321.1.char": "Erzählung... alt unter Bedingungen: S<PERSON><PERSON> hat Widder-Make-up, ", "321.1": ["Ein plötzlicher Stoß überrascht die verführerische Frau, der Angriff auf ihre Falten geht nun bis zur Zunge."], "321.1.1.char": "Erzählung", "321.1.1": ["Ein plötzlicher Stoß überrascht die verführerische Frau, der Angriff auf ihre Falten geht jetzt bis zur Zunge, und Lippenstift verschmiert die Seiten."], "321.2.char": "Erzählung", "321.2": ["Ein plötzlicher Stoß überrascht den Futa, Zentimeter für Zentimeter sinkt das pochende Werkzeug an ihm vorbei ", 0, "Seine geschminkten Lippen, heller Lippenstift in der Mitte des Schafts."], "322.char": "RamGirl... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "322.1.char": "Ram<PERSON><PERSON><PERSON>", "322.1": ["Haaa. "], "323.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "323.1.char": "Erzählung", "323.1": [0, " ist so zwischen weichen Schenkeln eingeklemmt, dass es ihr die Luft raubt."], "324.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "324.1.char": "Erzählung", "324.1": ["<PERSON>cht zufrieden damit, zu verlieren, ", 0, " legt ihre Arme um die Schenkel und steckt ihre leckende Zunge immer wieder hinein."], "325.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "325.1.char": "Erzählung", "325.1": ["<PERSON><PERSON>, j<PERSON><PERSON> sing<PERSON>, das der Schafdame entlockt wird, best<PERSON><PERSON>t nur, dass sie etwas richtig macht. "], "326.char": "Ram<PERSON><PERSON><PERSON>", "327.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "327.1.char": "Erzählung", "327.1": ["Weibliche Säfte ergießen sich auf die leckende Zunge, während sie immer wieder herumwirb<PERSON>t. "], "328.char": "Ram<PERSON><PERSON><PERSON>", "329.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "329.1.char": "Erzählung", "329.1": ["Im Nu klemmen sich die mit Strümpfen bekleideten Beine auf beiden Seiten fest ", 0, ", ein <PERSON><PERSON> weibliches Sperma spritzte auf ihren Nasenrücken."], "330.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "330.1.char": "Erzählung", "330.1": ["Ein plötzliches Ziehen an den Haaren erzwingt ", 0, " tief in diesen Muff hinein und badete ihr Gesicht in der Hitze des Nachglühens."], "331.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "331.1.char": "Erzählung", "331.1": ["Die glitschigen, verderblichen Flüssigkeiten kommen immer wieder und ertränken das fotzenschlürfende Mädchen in einem atemberaubenden Glanz klarer Flüssigkeit."], "332.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "332.1.char": "Erzählung... alt unter Bedingungen: S<PERSON><PERSON> hat Widder-Make-up, ", "332.1": ["<PERSON><PERSON> irgen<PERSON>ine <PERSON> von „Menschlichkeit“ übrig bliebe, würden die transformierenden Monstermädchensäfte, die aus diesen mädchenhaften Gesichtszügen s<PERSON>, kurzen Prozes<PERSON> machen."], "332.1.1.char": "Erzählung", "332.1.1": ["Gelb-orangefarbener Lippenstift überall verschmiert, ", 0, " bleibt dort gefangen, die verwandlungsfördernden Monster-Girl-Säfte strömen über ihre mädchenhaften Gesichtszüge."], "333.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "333.1.char": "Erzählung", "333.1": ["<PERSON>er Tropfen sickert ein oder wird brav abgeleckt. "], "334.char": "RamGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, ", "334.1.char": "Ram<PERSON><PERSON><PERSON>", "334.1": ["<PERSON><PERSON>t so aus, als ob du um die Hüften herum etwas dicker geworden bist. "], "335.char": "Regeln", "336.char": "RamGirl... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "336.1.char": "Ram<PERSON><PERSON><PERSON>", "336.1": ["<PERSON><PERSON><PERSON>. "], "337.char": "Regeln", "338.char": "Regeln... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "338.1.char": "Regeln", "338.1": ["Genau so kämpfe ich gegen Mädchen! "], "339.char": "RamGirl... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "339.1.char": "Ram<PERSON><PERSON><PERSON>", "339.1": ["<PERSON><PERSON>. "], "340.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "340.1.char": "Erzählung", "340.1": [0, " stellt es nicht einmal in Frage und leckt sich die Lippen. "], "341.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "341.1.char": "Erzählung", "341.1": ["Diese beiden verbringen jede Nacht mit aneinandergepressten Körpern und lustvollem Knirschen. "], "342.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "342.1.char": "Erzählung", "342.1": ["Sie legt ihre Waffen ab, schikan<PERSON><PERSON> jede<PERSON>, den sie trifft, anz<PERSON><PERSON><PERSON> und lähmt ihn mit ihren perversen Techniken im Tag-Team mit ihrem Partner."], "343.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "343.1.char": "Erzählung", "343.1": ["Sogar andere Monster-Mädchen können an manchen Tagen mit dem Geschmack von <PERSON>, flauschigerem Ha<PERSON> und weicherer, mädchenhafterer Haut aufgrund des schüchternen Makels aufwachen ..."], "344.char": "Regeln... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "344.1.char": "Regeln", "344.1": ["Aah. "], "345.char": "RamGirl... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "345.1.char": "Ram<PERSON><PERSON><PERSON>", "345.1": ["Nicht wahr? "], "346.char": "Regeln... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "346.1.char": "Regeln", "346.1": ["Waaat, wirst du schon wieder geil? "], "347.CHOICE": ["<PERSON> vorn anfangen"], "347.char": "Erzählung"}, "badend/badendwerewolf.json": {"000.char": "Erzählung", "000": [0, " fä<PERSON>t zu Boden, nur eine große Pfote streckt die Hand aus und greift ..."], "001.char": "Werwolf... alt unter Bedingungen: Playervariables.consent == false, ", "001": ["<PERSON><PERSON><PERSON>, nicht wahr? "], "001.1.char": "Erzählung", "001.1": [0, " wurde entführt und sie lebten glücklich bis ans Ende ihrer Tage (NSFW-Inhalt deaktiviert)."], "002.char": "Erzählung", "002": ["Eine süße, belebende Flüssigkeit strömt heraus. "], "003.char": "<PERSON><PERSON><PERSON>", "003": ["Braves <PERSON>. "], "004.char": "Erzählung... alt unter Bedingungen: Oberweite des Spielers ist == 4, Oberweite des Spielers ist == 3, Oberweite des Spielers ist == 2, ", "004": [0, "Seine Augen werden verschleiert, er kann nicht mit dem Trinken aufhören. "], "004.1.char": "Erzählung", "004.1": [0, " ist bereits an die Milch gewöhnt und klammert sich aus Schutz und Sicherheit an den Werwolf.  ", 0, "Der Busen ist schon riesig."], "004.2.char": "Erzählung", "004.2": [0, "Ihre ohnehin schon üppige Brust beginnt noch weiter anzuschwellen, wodurch ihre tief verwurzelte Sucht nach dem Trinken dieser Milch gestillt wird und ihre Brust immer größer wird."], "004.3.char": "Erzählung", "004.3": [0, " spürt ein vertrautes Engegefühl in ihrer Brust, diese bescheidenen Brüste beginnen sich mit verdorbener Milch zu füllen und entwickeln sich zu einem schweren Gewicht."], "005.CHOICE": ["<PERSON> vorn anfangen"], "005.char": "<PERSON><PERSON><PERSON>", "005": ["Gehen wir zurück. "]}, "load/loadconversationscat.json": {"100": ["Das Katzenmädchen startet ", 0, " von der anderen Seite des Raumes."], "101": ["Wie kommt es, dass ihr Mädels manchmal große Sprünge macht?"], "102": ["Mya? "], "103": ["Du wirst frustriert! "], "104": ["Dann spring einfach erst mal höher..."], "200": ["Hmmmmmu."], "201": ["Mmmmmmmu?"], "202": ["Tag der Katzenkontrolle! "], "203": ["<PERSON><PERSON> bin ich ein neues Katzenmädchen..."], "300": ["Nyaaa, Auszeit!"], "301": ["Ich habe NYA nur einmal getroffen! "], "302": ["Wir sind ausgebildete Schnurrprofis, weißt du. "], "303": ["Wir steigen ein, wenn das Z<PERSON> uns nervt, steigen wir aus! "], "304": ["Pass auf dich auf. "], "400": ["<PERSON><PERSON><PERSON>. "], "401": ["K<PERSON>len sind keine gute Waffe..."], "402": ["Verringer einfach die Distanz, Dummkopf!"], "403": ["Wenn ich näher komme, werden die Monster... Sex haben."], "404": ["<PERSON><PERSON>rt sich gut an! "], "000.char": "CatKnight", "000": ["Heeey, lass es uns tun!"], "001.char": "Regeln", "001": ["Wir hatten gerade Sex."], "002.char": "CatKnight", "002": ["Sex? "], "003.char": "Regeln", "003": ["Auf der Suche nach Mädchen zum Pinnen und Sex mit ihnen?"], "004.char": "CatKnight", "004": ["Genau!! "], "100.char": "Erzählung", "101.char": "Regeln", "102.char": "CatKnight", "103.char": "CatKnight", "104.char": "Regeln... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Katzenschwanz, ", "104.1.char": "Regeln", "104.1": ["<PERSON><PERSON><PERSON>, spring einfach mal höher!"], "200.char": "CatKnight", "201.char": "Regeln", "202.char": "CatKnight... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat W<PERSON><PERSON>hörner, <PERSON><PERSON><PERSON> hat <PERSON>ren, ", "202.1.char": "CatKnight", "202.1": ["Ich habe noch nie ein Kätzchenmädchen mit Hörnern g<PERSON>hen! "], "202.2.char": "CatKnight", "202.2": ["Nya hat nicht die „Katzenmädchen“-Energie. "], "203.char": "Regeln... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, <PERSON><PERSON><PERSON> hat <PERSON>chsohren, ", "203.1.char": "Regeln", "203.1": ["Ich sage nicht, wie ich sie bekommen habe!"], "203.2.char": "Regeln", "203.2": ["Ich... habe irgendwo, wo ich hin muss!"], "300.char": "CatKnight", "301.char": "Regeln", "302.char": "CatKnight", "303.char": "CatKnight", "304.char": "CatKnight", "400.char": "CatKnight", "401.char": "Regeln", "402.char": "CatKnight", "403.char": "Regeln", "404.char": "CatKnight"}, "load/loadconversationsfox.json": {"100": [0, " sitzt da und starrt auf einen kaputten Schrein."], "101": ["<PERSON><PERSON>..."], "102": ["Du hast gesagt, dass du das einmal gemacht hast?"], "103": ["Hat nur geholfen. "], "104": ["Du bist eine <PERSON>. "], "200": ["Haaa. "], "201": ["Es ist gut für Ihre Haut! "], "202": ["Hey. "], "203": ["<PERSON>ch habe vers<PERSON>t, meine <PERSON> flacher zu machen, aber nichts funktioniert."], "204": ["<PERSON>vor <PERSON> fragen: <PERSON><PERSON>, ich habe diese seltsame Milch NICHT getrunken. "], "300": [0, ".  ", 0, "?"], "301": ["Du bist eine unfassbare Kraft. "], "302": ["<PERSON> hei<PERSON>, wenn du nicht so eine <PERSON>hl<PERSON> wärst, weil du es <PERSON>n wegnehmen würdest."], "303": ["<PERSON><PERSON>, wo haben Sie die Leiche gefunden? "], "304": ["<PERSON><PERSON> ist mein Körper."], "305": ["Warum so sicher, hast du nicht gesagt, dass du ein Mann bist? "], "306": ["Wenn es <PERSON>hnen nicht gef<PERSON>t, die Jungfrau zu sein, gibt es etwas, das <PERSON> beschützen möchten? "], "400": ["<PERSON><PERSON><PERSON> bewölk<PERSON>. "], "401": ["<PERSON> we<PERSON>, was das bedeutet. "], "402": ["W<PERSON><PERSON>? "], "403": ["Was ist das? "], "404": ["... Sanft sein?"], "000.char": "FoxGirl", "000": ["<PERSON> schl<PERSON>t ein<PERSON>ch, wann immer du willst, oder?"], "001.char": "FoxGirl", "001": ["<PERSON>wi<PERSON> Ihnen und mir würden wir Ihnen gerne mehr Freiheit geben, wissen Si<PERSON>?"], "002.char": "FoxGirl", "002": ["Den<PERSON><PERSON> hast du diese seltsame Besessenheit wie das Vampirmädchen! "], "003.char": "FoxGirl", "003": ["<PERSON><PERSON><PERSON> haben wir Ihren Zustand den „Fluch“ genannt. "], "004.char": "FoxGirl", "004": ["Also ruhen Sie sich aus, so viel Sie wollen! "], "100.char": "Erzählung", "101.char": "Regeln", "102.char": "FoxGirl", "103.char": "Regeln", "104.char": "FoxGirl", "200.char": "FoxGirl", "201.char": "FoxGirl", "202.char": "FoxGirl", "203.char": "FoxGirl", "204.char": "FoxGirl... alt unter Bedingungen: Die Oberweite des Spielers ist > 2, ", "204.1.char": "FoxGirl", "204.1": ["<PERSON>ßt du, ich dachte, ich hätte deine Brust gut gefesselt, aber hier stellst du deine dummen Sautitten zur Schau! "], "300.char": "FoxGirl", "301.char": "FoxGirl", "302.char": "FoxGirl", "303.char": "FoxGirl", "304.char": "Regeln", "305.char": "FoxGirl", "306.char": "FoxGirl", "400.char": "FoxGirl", "401.char": "FoxGirl", "402.char": "Regeln", "403.char": "FoxGirl", "404.char": "Regeln"}, "load/loadconversationsfoxelite.json": {"100": ["..."], "101": ["Fox ist nicht zu <PERSON>."], "102": ["Idiot. "], "103": ["..."], "104": ["Ich gehe spazieren."], "200": ["<PERSON><PERSON>, das war ein guter Fi<PERSON>. "], "201": ["Das nächste Mal tun <PERSON> es, bevor wir gehen ..."], "202": ["<PERSON><PERSON> möchte, dass sie es sehen und ihnen zeigen, dass sie sich von meinem Mädchen fernhalten sollen. "], "203": ["... <PERSON><PERSON> verdeckt kaum etwas. "], "204": ["Oya? "], "205": ["Der Fuchs hat sich befreit und die reine und unschuldige Jungfrau verdorben! "], "300": [0, " war den ganzen Morgen draußen! "], "301": ["... So sei es. "], "302": ["Da bist du ja! "], "303": ["<PERSON>ch dachte."], "304": ["<PERSON>chts Gutes kommt, wenn man denkt. "], "305": ["Hier. "], "306": ["<PERSON>hau mich nicht so an! "], "400": ["Puhaa! "], "401": ["<PERSON><PERSON> du würdest sie ficken."], "402": ["Ich bin verletzt! "], "403": ["Seit wann hindern dich diese am <PERSON>n?"], "404": ["Wann immer ich es ausziehe, seid ihr vollgestopft und benommen, das merkt ihr wahrscheinlich nicht. "], "405": ["<PERSON>a jeden<PERSON>. "], "000.char": "FoxGirl", "000": ["<PERSON><PERSON><PERSON>, hast du vergessen, dass ich heute zurückgekommen bin? "], "001.char": "FoxGirl... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "001": ["<PERSON><PERSON>, es ist dein Favorit, dreh dich um~"], "001.1.char": "FoxGirl", "001.1": ["<PERSON><PERSON>, es ist dein Favorit, dreh dich um~"], "002.char": "FoxGirl", "002": ["<PERSON><PERSON> ich dir keine Aufmerksamkeit schenke, wirst du kämpferisch. "], "003.char": "FoxGirl", "003": ["<PERSON>s uns weiter hart an deinem Training arbeiten, Liebling."], "100.char": "Regeln", "101.char": "Regeln", "102.char": "Regeln", "103.char": "Regeln", "104.char": "Regeln", "200.char": "FoxGirl", "201.char": "Regeln", "202.char": "FoxGirl", "203.char": "Regeln", "204.char": "FoxGirl", "205.char": "FoxGirl", "300.char": "FoxGirl", "301.char": "FoxGirl", "302.char": "FoxGirl", "303.char": "Regeln", "304.char": "FoxGirl", "305.char": "FoxGirl", "306.char": "FoxGirl", "400.char": "FoxGirl", "401.char": "Regeln", "402.char": "FoxGirl... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "402.1.char": "FoxGirl", "402.1": ["Ich bin verletzt! "], "403.char": "Regeln", "404.char": "FoxGirl", "405.char": "FoxGirl"}, "load/loadconversationsram.json": {"100": [0, "Die Hand rutscht aus."], "101": ["<PERSON>n <PERSON> weiter hetzen, stoßen Si<PERSON> immer wieder auf die falschen Akkorde."], "102": ["Du musst nicht so nervös sein! "], "103": ["Ich kann es nicht ewig durchhalten."], "104": ["Mhm? "], "200": ["Die Sterne sind so hübsch wie immer. "], "201": ["<PERSON>ensichtlich. "], "202": ["Ist das eine Art Magie? "], "203": ["<PERSON>s ist keine Magie. "], "204": ["Das klingt für mich wie Magie, Lie<PERSON>. "], "300": [1, " Du rennst sicher sehr gern raus, ist da was los?"], "301": ["<PERSON><PERSON> würde helfen, wenn du mich nicht angreifen würdest, wenn ich draußen bin."], "302": ["Attacke? "], "303": ["<PERSON><PERSON><PERSON> sieht es so aus, als würde man sich selbst verletzen oder sich in Gefahr begeben. "], "304": ["Wenn es meinen Kampfwillen untergräbt, ist es nicht gut."], "305": ["Außerdem würde ich sonst einsam werden! "], "400": ["Du bist nicht immer noch sauer auf die Waffen, oder? "], "401": ["Beruhige einfach deine Hände und spiele deine Musik richtig und keines der anderen Mädchen wird dir wehtun."], "402": ["Aber am <PERSON>e ficken wir!"], "403": ["Dann vermitteln Sie Ihre Emotionen richtig durch die Musik. "], "404": ["... <PERSON><PERSON> manchmal?"], "000.char": "Ram<PERSON><PERSON><PERSON>", "000": ["<PERSON>n, mein, ", 0, "."], "001.char": "Ram<PERSON><PERSON><PERSON>", "001": ["<PERSON> s<PERSON>t tief, wie man sagt. "], "002.char": "Regeln", "002": ["Mnnnh. "], "003.char": "Ram<PERSON><PERSON><PERSON>", "003": ["Süße. "], "004.char": "Regeln", "004": ["Habe noch... Dinge zu tun..."], "100.char": "Erzählung", "101.char": "Ram<PERSON><PERSON><PERSON>", "102.char": "Ram<PERSON><PERSON><PERSON>", "103.char": "Regeln", "104.char": "RamGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, ", "104.1.char": "Ram<PERSON><PERSON><PERSON>", "104.1": ["Mhm? "], "200.char": "Ram<PERSON><PERSON><PERSON>", "201.char": "Regeln", "202.char": "Ram<PERSON><PERSON><PERSON>", "203.char": "Regeln", "204.char": "Ram<PERSON><PERSON><PERSON>", "300.char": "Ram<PERSON><PERSON><PERSON>", "301.char": "Regeln", "302.char": "Ram<PERSON><PERSON><PERSON>", "303.char": "Ram<PERSON><PERSON><PERSON>", "304.char": "Regeln", "305.char": "Ram<PERSON><PERSON><PERSON>", "400.char": "Ram<PERSON><PERSON><PERSON>", "401.char": "Ram<PERSON><PERSON><PERSON>", "402.char": "Regeln", "403.char": "Ram<PERSON><PERSON><PERSON>", "404.char": "Regeln"}, "load/loadconversationsramelite.json": {"100": ["Um die Ecke ruft Voice ", 0, "Der Name..."], "101": ["Sssh. "], "102": ["<PERSON><PERSON><PERSON> <PERSON><PERSON>, ", 0, " <PERSON><PERSON><PERSON><PERSON>, in der beschämenden Position, den Schwanz eines Monstermädchens zu deepthroaten."], "103": ["Mmm? "], "104": ["Mit einem Seufzer greifen die Hände des Widdermädchens nach den langen, mädchenhaften Haaren und ziehen den Schlürfer zurück zu ihrem Schritt."], "200": ["<PERSON><PERSON> in der Nacht, ", 0, " fühlt sich unwohl. "], "201": ["Aber sie findet eine dicke <PERSON>, die tief in ihr steckt und sie an <PERSON> und <PERSON> hält, egal wie sie sich bewegt."], "202": ["Während sie es versucht, erwacht es in ihren Tiefen und erzeugt ein magisches Gefühl der Ruhe und Schläfrigkeit in ihr ..."], "203": ["Hab dich. "], "204": ["...Nach <PERSON> gefahren, während das Satyr-Mädchen ihren Schwanzärmel umarmt und sich in die böse Kurve ihres Schafts stopft."], "205": ["<PERSON><PERSON> morgen, ", 0, " wird wieder einmal bis zum Rand g<PERSON>ü<PERSON>t sein."], "300": ["Du liebst es zu laufen, nicht wahr? "], "301": ["Sind Ihre Hupen nicht zum Aufladen da?"], "302": ["<PERSON><PERSON>, wie brutal. "], "303": ["<PERSON>n wir miteinander streiten, ist das ein angenehm intimer Wettbewerb, der in einem Knutsch-Wettbewerb endet."], "304": ["Wenn du jemals ganz oben sein willst, solltest du dein Kussspiel auffrischen~"], "400": ["Hey. "], "401": ["Was-?!"], "402": ["Mmm? "], "403": ["Ich mag es nicht, wenn du dich wehrst. "], "000.char": "Ram<PERSON><PERSON><PERSON>", "000": ["Wie? "], "001.char": "Regeln", "001": ["...?"], "002.char": "RamGirl... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "002": ["Hier. "], "002.1.char": "Ram<PERSON><PERSON><PERSON>", "002.1": ["Hier. "], "003.char": "Regeln", "003": ["! "], "004.char": "Ram<PERSON><PERSON><PERSON>", "004": ["Los geht's. "], "100.char": "Erzählung", "101.char": "RamGirl... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "101.1.char": "Ram<PERSON><PERSON><PERSON>", "101.1": ["Sssh. "], "102.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "102.1.char": "Erzählung", "102.1": ["<PERSON><PERSON><PERSON> <PERSON><PERSON>, ", 0, " <PERSON><PERSON><PERSON><PERSON>, in der beschämenden Position, die Fotze eines Monstermädchens anzubeten."], "103.char": "Ram<PERSON><PERSON><PERSON>", "104.char": "Erzählung... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, ", "104.1.char": "Erzählung", "104.1": ["Weiche und dennoch feste Hände greifen umher ", 0, "Die starken Widderhörner drücken den Schlürfer wieder hinein, bis sich die Hörner in die Schenkel des Satyrs bohren."], "200.char": "Erzählung", "201.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "201.1.char": "Erzählung", "201.1": ["Aber sie findet ein weiches und schweres Gewicht, das sie festhält und an <PERSON>t und Stelle hält, egal wie sie sich bewegt."], "202.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "202.1.char": "Erzählung", "202.1": ["Die Flauschigkeit löst einen beruhigenden, schlaffördernden Zauber aus."], "203.char": "Ram<PERSON><PERSON><PERSON>", "204.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "204.1.char": "Erzählung", "204.1": ["Arme schlingen sich um den vom Widder Befleckten ", 0, " als wäre sie ein lebensgroßes Körperkissen, das sie sicher hält."], "205.char": "Erzählung... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "205.1.char": "Erzählung", "205.1": ["Sie wird bis zum Morgen kein Entrinnen haben ..."], "300.char": "Ram<PERSON><PERSON><PERSON>", "301.char": "Regeln... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, ", "301.1.char": "Regeln", "301.1": ["<PERSON><PERSON> dienen zum Aufladen."], "302.char": "RamGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, ", "302.1.char": "RamGirl... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "302.1": [0, ". "], "302.1.1.char": "Ram<PERSON><PERSON><PERSON>", "302.1.1": [0, ". "], "303.char": "Ram<PERSON><PERSON><PERSON>", "304.char": "Ram<PERSON><PERSON><PERSON>", "400.char": "Ram<PERSON><PERSON><PERSON>", "401.char": "Regeln", "402.char": "Ram<PERSON><PERSON><PERSON>", "403.char": "RamGirl... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "403.1.char": "Ram<PERSON><PERSON><PERSON>", "403.1": ["Ich mag es nicht, wenn du dich wehrst. "]}, "load/loadconversationsvoice.json": {"100": ["Du bist ein Vampir. "], "101": ["Ich habe dir das schon einmal ges<PERSON>t, ich bin kein Vampir! "], "102": ["..."], "103": ["<PERSON>h."], "104": ["<PERSON><PERSON><PERSON>, sich selbstgefällig zu stellen, weil wir nicht wissen, was Ihre dummen prähistorischen Dinger sind!"], "200": ["Weißt du, so lange auf den Turm zu starren ist wirklich Pech."], "201": ["Seltsamer <PERSON>laube."], "202": ["Es ergibt Sinn! "], "203": ["<PERSON>t du mich beim <PERSON> sehen?"], "204": ["<PERSON><PERSON><PERSON> kann ich nicht sagen, ob du es ernst meinst oder nicht, ", 0, ". "], "300": ["Wo bist du gewesen! "], "301": ["Tut der Amor das nicht?"], "302": ["Sie tut? "], "303": ["WARTEN! "], "304": ["Wie wäre es eine Affäre?!"], "305": ["Weil du mein Eigentum bist, erinn<PERSON>t du dich?!"], "306": ["..."], "400": ["<PERSON><PERSON><PERSON>, ", 1, "! "], "401": ["<PERSON>u Ihrer Zeit gab es große Schuppenechsen, oder?!"], "402": ["Hm? "], "403": ["Wirklich?! "], "404": ["Am Ende sind sie jedoch ausgestorben."], "000.char": "Stimme", "000": [1, ", wach auf."], "001.char": "Stimme", "001": ["<PERSON>mm schon, komm schon ~"], "002.char": "Regeln", "002": ["<PERSON>ch stehe auf, sobald du das „Mädchen“ aus mir herausgesaugt hast."], "003.char": "Stimme", "003": ["<PERSON><PERSON> möglich."], "004.char": "Regeln", "004": ["<PERSON>n schlafe ich wieder ein."], "100.char": "Regeln", "101.char": "Stimme", "102.char": "Regeln", "103.char": "Regeln", "104.char": "Stimme", "200.char": "Stimme", "201.char": "Regeln", "202.char": "Stimme", "203.char": "Regeln", "204.char": "Stimme", "300.char": "Stimme", "301.char": "Regeln", "302.char": "Stimme", "303.char": "Stimme", "304.char": "Regeln", "305.char": "Stimme", "306.char": "Regeln", "400.char": "Stimme", "401.char": "Stimme", "402.char": "Regeln", "403.char": "Stimme", "404.char": "Regeln"}, "quip/quipcomplaints.json": {"000.char": "Keine- tl_note :???", "000": [""]}, "quip/quiplevelselect1.json": {"100": ["Ich habe keine Zeit zum Aufräumen! "], "000.char": "Stimme", "000": ["Hey! "], "001.char": "Regeln", "001": ["Warum mache ich das überhaupt für dich? "], "002.char": "Stimme", "002": ["Was-?! "], "003.char": "Stimme", "003": ["Dieses „große Übel“ möchte Sie wahrscheinlich zurück in die Leere stehlen, aus der Sie gekommen sind."], "004.char": "Stimme", "004": ["Aber ich kann in diesem Zustand jetzt nicht wirklich viel kämpfen."], "005.char": "Stimme", "005": ["Ich würde auch gerne mehr über dich erfahren. "], "006.char": "Regeln", "006": ["Hmmmm."], "100.char": "Stimme"}, "quip/quipshrinefox.json": {"100": ["Holen Sie diese Wolkengehirne von der Veranda und wir können reden!"], "200": ["Ich habe versucht, einen von ihnen zu besitzen, aber sie kamen gleich danach zurück ..."], "201": ["<PERSON>ch kann keine Hypnose anwenden. "], "300": ["<PERSON><PERSON>, lass uns reden!"], "400": ["Was zum Teufel?! "], "000.char": "FoxGirl... alt unter Bedingungen: Spieler hat Widder-Kleidungsoberteil, ", "000": ["Ernsthaft! "], "000.1.char": "FoxGirl", "000.1": ["<PERSON><PERSON><PERSON><PERSON><PERSON>, mehr von euch Schafen. "], "001.char": "FoxGirl", "001": ["Du bist nicht bei ihnen? "], "002.char": "FoxGirl", "002": ["<PERSON><PERSON>, wenn man einen Streit beginnt, wird alles nur noch schlimmer."], "100.char": "FoxGirl... alt unter Bedingungen: <PERSON>pieler hat Widder-Kleidungsoberteil, <PERSON>pieler hat <PERSON><PERSON>derhörner, ", "100.1.char": "FoxGirl", "100.1": ["Du bist einer von ihnen, nicht wahr? "], "100.2.char": "FoxGirl", "100.2": ["Bist du ihr Anführer? "], "200.char": "FoxGirl", "201.char": "FoxGirl... alt unter Bedingungen: Spieler hat Widder-Kleidungsoberteil, ", "201.1.char": "FoxGirl", "201.1": ["<PERSON>ch kann keine Hypnose anwenden, hm. "], "300.char": "FoxGirl... alt unter Bedingungen: Playervariables.queststart == false, ", "300.1.char": "FoxGirl", "300.1": ["Du hast die Rams aus dem Weg geräumt?"], "400.char": "FoxGirl... alt unter Bedingungen: Playervariables.queststart == false, ", "400.1.char": "FoxGirl", "400.1": ["Die Hölle?! "]}, "quip/quipshrineram.json": {"100": ["Ich habe versucht, sie zu be<PERSON>rn. "], "200": ["Hier ist es gefä<PERSON>! "], "300": ["<PERSON><PERSON> ist sicherer, in der Herde zu bleiben. "], "400": ["Wir müssen woanders sein? "], "500": ["Unsere kostbaren Kätzchen wurden angegriffen? "], "000.char": "RamGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, <PERSON><PERSON><PERSON> hat Fuchsohren, ", "000": ["Der Schrein schützt sie nicht vor einer „Gehirnwäsche“, sie ist nur übertrieben."], "000.1.char": "RamGirl... alt unter Bedingungen: <PERSON><PERSON>ler hat Fuchsohren, <PERSON><PERSON>ler hat Katzenohren, ", "000.1": ["Warst du nicht dieser Neubekehrte? "], "000.1.1.char": "Ram<PERSON><PERSON><PERSON>", "000.1.1": ["Fu<PERSON><PERSON><PERSON>...? "], "000.1.2.char": "Ram<PERSON><PERSON><PERSON>", "000.1.2": ["Kyaa- Cat-girl-ram! "], "000.2.char": "Ram<PERSON><PERSON><PERSON>", "000.2": ["<PERSON><PERSON>-Girls an einem <PERSON>t? "], "100.char": "RamGirl... alt unter Bedingungen: <PERSON>pieler hat Widder-Kleidungsoberteil, <PERSON>pieler hat Fuchsohren, ", "100.1.char": "Ram<PERSON><PERSON><PERSON>", "100.1": ["Hypnose funktioniert bei <PERSON> besser. "], "100.2.char": "Ram<PERSON><PERSON><PERSON>", "100.2": ["Du bist besser als sie, nicht wahr? "], "200.char": "RamGirl... alt unter Bedingungen: <PERSON>pieler hat Widder-Kleidungsoberteil, <PERSON>pieler hat Fuchsohren, ", "200.1.char": "Ram<PERSON><PERSON><PERSON>", "200.1": ["Du wirst ihr helfen, zur Vernunft zu kommen, nicht wahr, Schwes<PERSON>?"], "200.2.char": "Ram<PERSON><PERSON><PERSON>", "200.2": ["Haaaa. "], "300.char": "RamGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, <PERSON><PERSON><PERSON> hat Fuchsohren, ", "300.1.char": "Ram<PERSON><PERSON><PERSON>", "300.1": ["<PERSON><PERSON>, ich habe dich nicht hier gesehen. "], "300.2.char": "Ram<PERSON><PERSON><PERSON>", "300.2": ["Bist du ihr Freund? "], "400.char": "RamGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat W<PERSON>derhörner, <PERSON><PERSON><PERSON> hat <PERSON>ren, <PERSON><PERSON><PERSON> hat Katz<PERSON>, ", "400.1.char": "Ram<PERSON><PERSON><PERSON>", "400.1": ["Wir sollten uns zurückziehen? "], "400.2.char": "Ram<PERSON><PERSON><PERSON>", "400.2": ["<PERSON><PERSON><PERSON><PERSON>, denkt ihr, dieser Fuchs wird gefügiger sein?"], "400.3.char": "Ram<PERSON><PERSON><PERSON>", "400.3": ["<PERSON><PERSON> du gehen, <PERSON>? "], "500.char": "RamGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat W<PERSON>derhörner, <PERSON><PERSON><PERSON> hat <PERSON>ren, <PERSON><PERSON><PERSON> hat Katz<PERSON>, ", "500.1.char": "Ram<PERSON><PERSON><PERSON>", "500.1": ["So große Hupen... Wenn wir los müssen, ist das in Ordnung."], "500.2.char": "Ram<PERSON><PERSON><PERSON>", "500.2": ["So entzückende Fuchsohren ... Ja, stattdessen jagen wir dich!"], "500.3.char": "Ram<PERSON><PERSON><PERSON>", "500.3": ["<PERSON>ch konnte dich nicht <PERSON>, <PERSON>. "]}, "quip/quiptutorial1.json": {"100": ["<PERSON><PERSON> dieser Waffe können Sie auch „ANGRIFFEN“ und 1 Schaden verursachen! "], "101": ["Auch! "], "200": ["Sie haben auch eine Schwimmgeschwindigkeit von 2. <PERSON><PERSON> verringert Ihre Fallgeschwindigkeit, also ist es ein bisschen wie Schweben?"], "201": ["<PERSON>e haben auch bemer<PERSON>, dass Sie in einer Runde 4 Spielsteine ​​„LAUFEN“ oder 2 Spielsteine ​​springen können, wora<PERSON><PERSON> die Gegner an der Reihe sind."], "300": ["<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> und Wurzeln sind nur einige der Dinge, auf die Si<PERSON> klettern können, um einem Absturz zu entgehen."], "1020": ["... Ich stehe dem oberen Dialogmenü doch nicht im Weg, oder?"], "000.char": "Stimme", "000": ["Hey! "], "001.char": "Stimme... alt unter Bedingungen: Spieler verwendet einen Touchscreen, ", "001": ["Sie können die Konversation schließen, indem Sie mit der rechten Maustaste innerhalb einer Kachel um mich herum klicken oder die großen „X“-Schaltflächen verwenden."], "001.1.char": "Stimme", "001.1": ["Sie können das Gespräch beenden, indem Si<PERSON> gedrückt halten, im Kameramodus tippen oder die großen „X“-Tasten verwenden."], "100.char": "Stimme... alt unter Bedingungen: Spieler verwendet einen Touchscreen, ", "100.1.char": "Stimme", "100.1": ["<PERSON><PERSON> dieser Waffe können Sie auch „ANGRIFFEN“ und 1 Schaden verursachen! "], "101.char": "Stimme", "1020.char": "Stimme", "200.char": "Stimme", "201.char": "Stimme", "300.char": "Stimme"}, "quip/quiptutorial2.json": {"100": ["Das rote Gewichtssymbol auf der Axt zeigt an, dass es sich um einen SCHWEREN Angriff handelt, sodass sich die Feinde bewegen, bevor die Axt einschlägt."], "101": ["<PERSON>er<PERSON><PERSON> Axt, <PERSON><PERSON><PERSON> und deinen Basisangriff zusammen, um 4 Schadenspunkte zu verursachen, bevor die <PERSON>ste heilen kann!"], "200": ["<PERSON> Angriffe, die <PERSON> bisher verwendet haben, waren KRAFT oder PRÄZISION, aber Fackeln verursachen ENERGIE-Schaden."], "201": ["Fackeln haben 0 Schaden, aber sie beschädigen Hindernisse. "], "300": ["<PERSON><PERSON><PERSON> kann ein Ziel oder Feind Schaden von einer, zwei, drei oder vier Seiten abwehren! "], "301": ["<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> also die ungewöhnliche Angriffsreichweite des Scimitar, um durchzukommen. Sie müssen sich mehr „über“ als links davon befinden, um ihn zu treffen."], "400": ["Einige Fertigkeiten verfügen über Bewegungen wie „Sprint“. <PERSON><PERSON> können sehen, wohin <PERSON> sich bewegen, grün umrandet."], "401": ["Das Schöne an Bewegungsfertigkeiten ist, dass sie viele Schwächungen ignorieren und sich durch Büsche bewegen."], "500": ["Klicken Si<PERSON> mit der rechten Maustaste auf die Taschenlampe, um durch Ihre Verbrauchsmaterialien zu scrollen! "], "501": ["Ein Piton kann am Hintergrund oder an einer Klippe befestigt werden, sodass Sie fast überall hinaufklettern können."], "600": ["Und zu guter Letzt, weil ich Sie noch nicht schikanieren konnte, haben wir diese, die Sie tatsächlich angreifen werden!"], "601": ["Die Angriffe verursachen 0 Schaden, verursachen aber RÜCKSCHLAG. "], "602": ["Der Buckler bietet einen reinen Block, der alle Schadensarten blockiert, andere Blocktypen schützen nur vor bestimmten Schäden."], "700": ["„Windstep“ ist hier ziemlich einfach, es setzt lediglich Ihre Fallgeschwindigkeit für eine Runde auf 0. "], "701": ["Übrigens kannst du deinen BLOCK um dich herum sehen, solange er aktiv ist. "], "702": ["<PERSON>lte dir jemals der BLOCK ausgehen, erle<PERSON>t du alle Schwächungen des Angriffs, auch wenn dadurch möglicherweise der erlittene Schaden verringert wird!"], "800": ["Gut gemacht! "], "801": ["Aber Sie erhalten Ihre nicht verbrauchbaren Gegenstände am Ende jedes Bildschirms zurück, also legen Sie einen guten Vorrat davon an!"], "802": ["Bis Sie dauerhafte Gegenstände erhalten, bleiben diese jedoch nur bis zum Ende des jeweiligen Laufs bestehen."], "000.char": "Stimme", "000": ["<PERSON><PERSON>, wenn <PERSON> umziehen, können auch alle anderen umziehen! "], "001.char": "Stimme... alt unter Bedingungen: Spieler verwendet einen Touchscreen, ", "001": ["Diese <PERSON>-Fertigkeit ist „SCHNELL“, was bedeutet, dass sie keine Runde braucht! "], "001.1.char": "Stimme", "001.1": ["Diese <PERSON>-Fertigkeit ist „SCHNELL“, was bedeutet, dass sie keine Runde braucht! "], "002.char": "Stimme", "002": ["Das rote Quadrat zeigt an, wo es aufschlägt, wenn du es fallen lässt. Benutze es zusammen mit deinem Grundangriff, um in einer Runde 2 Schaden zu verursachen."], "100.char": "Stimme", "101.char": "Stimme", "200.char": "Stimme", "201.char": "Stimme", "300.char": "Stimme", "301.char": "Stimme", "400.char": "Stimme", "401.char": "Stimme", "500.char": "Stimme... alt unter Bedingungen: Spieler verwendet einen Touchscreen, ", "500.1.char": "Stimme", "500.1": ["Verwenden Sie den Kameramodus und tippen Sie dann auf die Taschenlampe, um durch Ihre Verbrauchsmaterialien zu scrollen! "], "501.char": "Stimme", "600.char": "Stimme", "601.char": "Stimme", "602.char": "Stimme", "700.char": "Stimme", "701.char": "Stimme", "702.char": "Stimme", "800.char": "Stimme", "801.char": "Stimme", "802.char": "Stimme"}, "quip/quipvoicevillage.json": {"100": ["Ich habe den Weg nach rechts geöffnet."], "101": ["Du gehst wirklich, nicht wahr? "], "200": ["Hey! "], "300": ["Das Galeriehaus, das ich Ihnen hinterlassen habe, ist in ziemlich gutem Zustand, nicht wahr?"], "301": ["Ich werde eine Weile hier bleiben, also fühlen Si<PERSON> sich dort wie zu <PERSON>."], "200.char": "Stimme", "300.char": "Stimme", "301.char": "Stimme", "000.char": "Stimme", "000": ["Heiße Quellen? "], "001.char": "Stimme... alt unter Bedingungen: Die Oberweite des Spielers ist > 2, ", "001": ["Ich habe es schon einmal versucht, aber es war alles schrecklich, blendender Dampf. "], "001.1.char": "Stimme", "001.1": ["Du siehst aus, als hättest du der Korruption Luft zu machen!"], "100.char": "Stimme", "101.char": "Stimme"}, "quip/tutorialmission1catknightquips.json": {"100": ["<PERSON>. "], "101": ["Das ist eine seltsame <PERSON>, die man einem <PERSON>emden sagen kann? "], "200": ["Nyaa! "], "000.char": "CatKnight", "000": ["Unya<PERSON>? "], "100.char": "CatKnight", "101.char": "Regeln", "200.char": "CatKnight"}, "quip/tutorialmission3catknightquips.json": {"100": ["Was machst du mit diesen Pfoten? "], "200": ["S<PERSON>len Si<PERSON> schwer zu bekommen? "], "300": ["<PERSON><PERSON>t dort gut aus, <PERSON><PERSON><PERSON>! "], "400": ["Hypno-Perlen? "], "401": ["Du wusstest es nicht? "], "402": ["<PERSON>n dein Schwanz plötzlich zucken und sie befreien würde, würdest du ganz uns gehören"], "500": ["<PERSON><PERSON><PERSON>r Schwanz! "], "600": ["<PERSON><PERSON>t dort echt heiß und genervt aus, Neuling. "], "700": ["<PERSON><PERSON><PERSON>, wo du den blöden Stock weglegst, können wir richtig freundlich werden."], "000.char": "CatKnight", "000": ["<PERSON><PERSON> wäre viel süßer, wenn du aufhören würdest, dich hart zu benehmen und so herumzureden!"], "100.char": "CatKnight", "200.char": "CatKnight", "300.char": "CatKnight", "400.char": "CatKnight", "401.char": "CatKnight", "402.char": "CatKnight", "500.char": "CatKnight", "600.char": "CatKnight", "700.char": "CatKnight"}, "quip/worldfoxgirlquips.json": {"100": ["Ich erspähe einen neuen Cocksleeve. "], "101": ["<PERSON><PERSON> kann meine Barriere nicht durchdringen."], "200": ["Halte diese böse Magie von mir fern!"], "600": ["<PERSON><PERSON> diesem <PERSON> kann man nicht entkommen. "], "700": ["Der salzige Nebel brennt in deinen Nebenhöhlen, ja?"], "800": ["Versuchst du es überhaupt? "], "900": ["Fummeln Si<PERSON> absichtlich herum? "], "1000": ["<PERSON><PERSON>h, es zieht immer wieder meinen Knoten hinein und nimmt mein ganzes Sperma auf. "], "1100": ["Haa. "], "1101": ["Du bist ein wirklich eifriger U<PERSON>, wenn diese Robben deine Hemmungen brechen."], "1400": ["Es beginnt sich durchzusetzen. "], "1500": ["Wie fühlt es sich an, ein <PERSON> zu sein? "], "1600": ["Diese Frisur passt zu einer echten Schreinjungfrau"], "1800": ["Der Geist hat deinen Körper bereits verlassen? "], "1900": ["Versuchen Sie, der Besessenheit zu widerstehen? "], "2000": ["Versiegele deine Schlampe wieder mit diesem Siegel!"], "2100": ["Man kann diese Siegel nicht einfach a<PERSON>ßen!"], "2200": ["Gehorche mir, verdammt!"], "2300": ["<PERSON>zt hast du alle Siegel, gehorche mir und geh auf den Boden! "], "5000": ["<PERSON> weißt nicht, wie du diesen Körper nutzen sollst, gib auf und lass die Geister herein."], "5100": ["Alle Cat-Girls sollten ihre Schwänze so zusammenbinden lassen!"], "5200": ["Ich mag die „Flauschwolken“-Strümpfe nicht, sie werden durch den Schweiß feucht."], "5300": ["Das Oberteil ist zu groß! "], "5400": ["Ich könnte deine dummen Hörner behalten, um sie als Lenker zu verwenden."], "5500": ["Mit solchen Titten siehst du aus wie eine Prostituierte!"], "5600": ["Tu schon mal etwas gegen deine dummen fetten Sautitten!"], "5700": ["Ich kann mich nicht erinnern, dir die Erlaubnis gegeben zu haben, hierher zu wandern, <PERSON><PERSON>!"], "5800": ["Diese Handschuhe passen zu einem Haustier wie Ihnen!"], "5900": ["Ich habe ein paar <PERSON>, um deine Gehässigkeit zu heilen!"], "6000": ["Du gehörst schon uns, warum machst du so viel Aufhebens?"], "6100": ["<PERSON>n <PERSON> so müde sind, warum lassen Si<PERSON> nicht zu, dass diese Geister für Sie die Macht übernehmen?"], "6200": ["Jeden Moment werde ich dich an meinen Schrein versiegeln."], "6300": ["Komm schon, gib auf! "], "6400": ["Leg einfach diese Siegel an und du wirst im Handumdrehen meinen Schwanz lutschen, Schlampe~"], "6500": ["Du hast unser Symbol, solltest du nicht gehorsamer sein?"], "6600": ["Wie soll ich mit diesen dummen Sauharpyien konkurrieren?!"], "6700": ["Du hast nicht einmal mehr Hände! "], "999999": ["aaaa"], "000.char": "FoxGirl- tl_note: <PERSON><PERSON><PERSON>n... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat <PERSON> Ending 0 gese<PERSON>, ", "000": ["<PERSON><PERSON><PERSON> ich dich gefangen habe, werde ich dich nicht mehr entkommen lassen!"], "000.1.char": "FoxGirl... alt unter Bedingungen: <PERSON>pieler hat Fuchsschwanz, ", "000.1": ["Wenn Si<PERSON> ohne Ihren Fuchsschwanz herumlaufen, brauchen Sie etwas Umschulung!"], "000.1.1.char": "FoxGirl", "000.1.1": ["Geh auf alle Viere, <PERSON><PERSON>, es ist Trainingszeit~"], "100.char": "FoxGirl- tl_note: <PERSON>rst<PERSON> Treffen mit Widdermädchen ELITE... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "100.1.char": "FoxGirl", "100.1": ["Ich erspähe ein neues Haustier. "], "101.char": "FoxGirl", "200.char": "FoxGirl- tl_note:Hypnose bei Fuchsmädchen angewendet", "600.char": "FoxGirl- tl_note: Spezieller Koffer für Achselszenen", "700.char": "FoxGirl- tl_note:Nach der Achselhöhlenszene", "800.char": "FoxGirl- tl_note: Nach der Achselhöhlenszene, Variante 1", "900.char": "FoxGirl- tl_note:Nach der Achselhöhlenszene Variante2... alt unter Bedingungen: Spieler hat Besitz-Debuff, ", "900.1.char": "FoxGirl", "900.1": ["Braves <PERSON>, bleib für mich an Ort und Stelle."], "1000.char": "FoxGirl- tl_note: <PERSON><PERSON> <PERSON> K<PERSON>, Elite... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, Spie<PERSON> hat Fuchsschwanz, ", "1000.1.char": "FoxGirl", "1000.1": ["<PERSON><PERSON>, ich verschütte meine klebrigen Säfte überall. "], "1000.2.char": "FoxGirl", "1000.2": ["<PERSON><PERSON> ist die Beste, sie drückt fest, während ich sie hochbringe~"], "1100.char": "FoxGirl- tl_note: Nach der Pussy-Press-Szene, nicht Elite... alt unter Bedingungen: Spieler hat Fuchsschwanz, ", "1100.1.char": "FoxGirl", "1100.1": ["<PERSON>ch lasse dich vorerst gehen, Fuchs."], "1101.char": "FoxGirl... alt unter Bedingungen: <PERSON>pieler hat Fuchsschwanz, ", "1101.1.char": "FoxGirl", "1101.1": ["Lass die Waffe fallen und komm jederzeit nach Hause."], "1400.char": "FoxGirl- tl_note:Nach dem TF-<PERSON><PERSON><PERSON> – <PERSON>ears? ", "1500.char": "FoxGirl- tl_note:Nach dem TF-Spieler – Fuchsschwanz? ", "1600.char": "FoxGirl- tl_note: Nach dem TF-Spieler – <PERSON><PERSON><PERSON><PERSON>? ", "1800.char": "FoxGirl- tl_note: Spieler ist BESESSEN... alt unter Bedingungen: Spieler hat BESITZ-Debuff, ", "1800.1.char": "FoxGirl", "1800.1": ["Wie ist es, an meinen Schrein gebunden zu sein?"], "1900.char": "FoxGirl- tl_note: Spieler ist BESITZ (Elite) ... alt unter Bedingungen: Spieler hat BESITZ-Debuff, ", "1900.1.char": "FoxGirl", "1900.1": ["<PERSON><PERSON>, lass die Geister die Kontrolle übernehmen. "], "2000.char": "FoxGirl- tl_note:<PERSON><PERSON><PERSON> hat SLUT-Siegel angewendet... alt unter Bedingungen: Spieler hat SLUT-Fuchssiegel > 0, ", "2000.1.char": "FoxGirl", "2000.1": ["<PERSON><PERSON> soll Si<PERSON> neugierig machen."], "2100.char": "FoxGirl- tl_note:<PERSON>pieler hat SEAL-Siegel angewendet... alt unter Bedingungen: Spieler hat SEAL fox-seal > 0, ", "2100.1.char": "FoxGirl", "2100.1": ["<PERSON><PERSON> bringt Ihren Körper in Hitze!"], "2200.char": "FoxGirl- tl_note:<PERSON>pieler hat OBEY-<PERSON>l angewendet... alt unter Bedingungen: Spieler hat OBEY fox-seal > 0, ", "2200.1.char": "FoxGirl", "2200.1": ["<PERSON><PERSON> wird dich dazu bringen, deine Beine für mich zu spreizen."], "2300.char": "FoxGirl – tl_note: Der Spieler ist anfällig für Füchse", "5000.char": "FoxGirl- tl_note:RANDOM... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Besitz-Debuff, ", "5000.1.char": "FoxGirl", "5000.1": ["Du siehst bezaubernd aus, wenn der Geisterfuchs die Kontrolle hat."], "5100.char": "FoxGirl- tl_note:RANDOMplayer hat <PERSON>enperlen", "5200.char": "FoxGirl – tl_note:<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> hat Zügel", "5300.char": "FoxGirl- tl_note:<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> hat Ramtop... alt unter Bedingungen: Die Oberweite des Spielers ist > 2, ", "5300.1.char": "FoxGirl", "5300.1": ["Deine kuhgroßen Titten sind eine Schande, zieh etwas weniger Freizügiges an!"], "5400.char": "FoxGirl – tl_note:<PERSON><PERSON><PERSON>, der Spieler hat wieder Hörner", "5500.char": "FoxGirl- tl_note:RANDOM Player hat Titten der Größe 3 oder 4", "5600.char": "FoxGirl- tl_note:RANDOM Player hat Titten der Größe 4", "5700.char": "FoxGirl- tl_note: RANDOM Player ist Fox-Klasse und unterscheidet sich von WOLDRAMGIRLQUIPS", "5800.char": "FoxGirl- tl_note:RANDOM Spieler hat Pfoten", "5900.char": "FoxGirl- tl_note:RANDOM Katzenschwanz", "6000.char": "FoxGirl- tl_note:RANDOM fox-tail", "6100.char": "FoxGirl- tl_note:Zufälliger Widerstand des Spielers liegt unter 10", "6200.char": "FoxGirl- tl_note:Zufälliger Widerstand des Spielers liegt unter 5", "6300.char": "FoxGirl- tl_note:RANDOM", "6400.char": "FoxGirl- tl_note:RANDOM (Rangliste)... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "6400.1.char": "FoxGirl- tl_note:RANDOM (Rangliste)", "6400.1": ["Leg einfach diese Siegel an und du wirst im Handumdrehen meine F<PERSON>ze sch<PERSON>ürfen, Schlampe~"], "6500.char": "FoxGirl- tl_note:RANDOM Fuchshaar", "6600.char": "FoxGirl – tl_note: ZUFÄLLIGE Harpyienflügel", "6700.char": "FoxGirl- tl_note:RANDOM2 Harpyienflügel", "999999.char": "FoxGirl"}, "quip/worldramgirlquips.json": {"100": ["Achte genau auf die Bewegung meiner Hüften. "], "200": ["Kufufufu. "], "300": ["Kyaaa! "], "400": ["Süße Gören wie du brauchen Bumsen, bis sie ganz klebrig sind ..."], "500": ["Wow! "], "600": ["Mmm? "], "601": ["Auf keinen Fall?! "], "700": ["<PERSON><PERSON>, jemand sieht schne<PERSON>ig aus. "], "800": ["P<PERSON>ah. "], "900": ["Danke für das Essen ~ Du bist ein echter Lecker<PERSON>."], "1000": ["Fwuah. "], "1100": ["Du windest dich ganz süß, wenn ich dir den Mund stopfe~"], "1101": ["<PERSON><PERSON>, wenn wir uns küssen, verändert sich dein Körper neu, um eine bessere Schlampe zu sein"], "1200": ["<PERSON><PERSON><PERSON>. "], "1300": ["Haaah. "], "1400": ["Hnnh. "], "1401": ["Du bist jetzt v<PERSON><PERSON><PERSON> von meiner Korruption gezeichnet, Kufufu."], "3000": ["Jetzt gibt es keinen unangenehmen Stoff mehr zwischen unserer Liebe."], "3100": ["<PERSON><PERSON>, ansch<PERSON><PERSON><PERSON><PERSON>, ganz ent<PERSON><PERSON><PERSON><PERSON><PERSON>~"], "3200": ["Du siehst schon weicher aus! "], "3300": ["<PERSON><PERSON> stehen dir sooo viel besser. "], "3400": ["Sogar deine Lippen sind in meiner Farbe befleckt. "], "3500": ["<PERSON><PERSON>. "], "3600": ["<PERSON><PERSON> sind so groß und so schnell gewachsen. "], "3700": ["Du bist so ein gehorsame<PERSON> Mädchen. "], "3800": ["Du verschenkst einfach deine ganze Vitalität, nicht wahr? "], "5000": ["<PERSON><PERSON>, ve<PERSON><PERSON><PERSON>. "], "5100": ["<PERSON><PERSON>. "], "5200": ["Mmm. "], "5300": ["Unsere Oberteile sind ziemlich luftig, nicht wahr? "], "5400": ["<PERSON>ch frage mich, ob wir unsere Hörner genau so neigen können, dass sie stecken bleiben. "], "5500": ["Dich an meine Brust zu drücken, wird mit deinen großen Titten schwer sein. "], "5600": ["Wow, du lässt dich wirklich auf die <PERSON>ch ein, ich bin neidisch."], "5700": ["Was für eine entzückende Kurve deine Hörner haben~"], "5800": ["Streicheln Sie nicht nur alle Kätzchen, sondern betasten Si<PERSON> auch Ihre große Schwester mit der Brust"], "5900": ["Hier <PERSON>. "], "6000": ["Die Fuchsmädchen sind alle an Schreine gesiegelt. "], "6100": ["<PERSON><PERSON><PERSON><PERSON>, du siehst völlig erschöpft aus. "], "6200": ["<PERSON>hr seid alle am Ende. "], "6300": ["<PERSON><PERSON> und schau mir tief in die Augen. "], "6400": ["Folge einfach der Schwingung meines Pendels, werde gebannt, uners<PERSON><PERSON><PERSON> ..."], "6500": ["Lass uns das aufbinden, lass deine Ha<PERSON> frei. "], "6600": ["Du musst dich von den Hügeln fernhalten! "], "6700": ["Geht es dir so gut? "], "9999": ["aaaa"], "000.char": "RamGirl- tl_note: <PERSON><PERSON><PERSON>", "000": ["Was für ein süßes verlorenes Lamm. "], "100.char": "RamGirl- tl_note: <PERSON><PERSON><PERSON> Treffen mit Ram-Girl ELITE... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "100.1.char": "Ram<PERSON><PERSON><PERSON>", "100.1": ["Achten Sie genau auf die Schwingungen dieses Pendels, meiner <PERSON>en ..."], "200.char": "RamGirl- tl_note: Hypnose wird bei Ram-Girl angewendet", "300.char": "RamGirl- tl_note: Hypnose wird bei Ram-<PERSON> an<PERSON>, ABER DER SPIELER IST KEMONOMIMI.", "400.char": "RamGirl- tl_note: Hypnose bei Ram-<PERSON> (Elite)", "500.char": "RamGirl- tl_note: <PERSON><PERSON><PERSON> Treffen einer Nicht-Elite mit dem Kemonomimi-Spieler FOX", "600.char": "RamGirl- tl_note: <PERSON><PERSON><PERSON> Treffen einer Nicht-Elite mit dem Kemonomimi-Spieler CAT", "601.char": "Ram<PERSON><PERSON><PERSON>", "700.char": "RamGirl- tl_note: <PERSON><PERSON><PERSON> Treffen mit dem Spieler mit Hörnern", "800.char": "RamGirl- tl_note:Nachdem er den Spieler geküsst hat", "900.char": "RamGirl- tl_note: Nach dem Kuss Spielervariante", "1000.char": "RamGirl- tl_note: Nach dem Küssen des Spielers, Elite... alt unter Bedingungen: <PERSON>pie<PERSON> hat Widderhörner, ", "1000.1.char": "Ram<PERSON><PERSON><PERSON>", "1000.1": ["Fwuah. "], "1100.char": "RamGirl- tl_note: Nach dem Küssen des Spielers, Elite-VARIANTE... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat <PERSON><PERSON><PERSON>hörner, ", "1100.1.char": "Ram<PERSON><PERSON><PERSON>", "1100.1": ["<PERSON> versauter Kusssüchtiger. "], "1101.char": "RamGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, ", "1101.1.char": "Ram<PERSON><PERSON><PERSON>", "1101.1": ["Du wirst mich groß und stark machen und dich beschützend an mich klammern. "], "1200.char": "RamGirl- tl_note:Nachdem ich den Spieler umarmt habe", "1300.char": "RamGirl- tl_note: Nach dem Streicheln des Spielers", "1400.char": "RamGirl- tl_note: Nach dem Futa-Spieler... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "1400.1.char": "Ram<PERSON><PERSON><PERSON>", "1400.1": ["All dieser enge Kontakt wird Ihre Haut weicher machen und Sie mit Verlegenheit erfüllen"], "1401.char": "RamGirl... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "1401.1.char": "Ram<PERSON><PERSON><PERSON>", "1401.1": ["<PERSON><PERSON><PERSON> Si<PERSON> nach und wir können zu mir nach Hause zurückkehren, okay?"], "3000.char": "RamGirl- tl_note: Verursacht: Spielerkleidung entfernt.... alt unter Bedingungen: <PERSON>pie<PERSON> <PERSON><PERSON><PERSON>hörner, ", "3000.1.char": "Ram<PERSON><PERSON><PERSON>", "3000.1": ["Deine alten Klamotten passen überhaupt nicht zu deinem neuen Ich."], "3100.char": "RamGirl- tl_note: Verursacht: Spielerkleidung entfernt (Rangliste).", "3200.char": "RamGirl- tl_note: Verursacht: Spielerkleidung wurde in den RAM verlagert.", "3300.char": "RamGirl- tl_note: Verursacht: Spielerkleidung wurde in den RAM getauscht (Rangliste)", "3400.char": "RamGirl- tl_note: Verursacht: S<PERSON>ler-Make-up", "3500.char": "RamGirl- tl_note: Verursacht: S<PERSON>ler-Make-up (vom Ranglisten-Ram)", "3600.char": "RamGirl- tl_note: Verursacht: Hörner des Spielers", "3700.char": "RamGirl- tl_note: Verursacht: Ram-Gesundheit 6 oder höher", "3800.char": "RamGirl- tl_note: Verursacht: Rangierte Ram-Gesundheit 7 oder höher... alt unter Bedingungen: Futa-Feinde umgeschaltet: AUS, ", "3800.1.char": "Ram<PERSON><PERSON><PERSON>", "3800.1": ["Unterwerfe dich weiterhin meinem Kuss. "], "5000.char": "RamGirl- tl_note:RANDOM", "5100.char": "RamGirl- tl_note:RANDOMplayer hat Katzenperlen", "5200.char": "RamGirl- tl_note:<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> hat Zügel", "5300.char": "RamGirl- tl_note:<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> hat Ramtop... alt unter Bedingungen: Oberweite des Spielers ist > 3, Oberweite des Spielers ist > 2, Oberweite des Spielers ist > 1, ", "5300.1.char": "Ram<PERSON><PERSON><PERSON>", "5300.1": ["Wie ist das Kleid, <PERSON><PERSON><PERSON>? "], "5300.2.char": "RamGirl... alt unter Bedingungen: <PERSON><PERSON><PERSON> hat Widderhörner, ", "5300.2": ["Du siehst jetzt fast mütterlich aus! "], "5300.2.1.char": "Ram<PERSON><PERSON><PERSON>", "5300.2.1": ["Ich kann dich kaum vom Rest von uns unterscheiden! "], "5300.3.char": "Ram<PERSON><PERSON><PERSON>", "5300.3": ["Du wirst dieses schöne Kleid mit diesen Kuhtitten ruinieren!"], "5400.char": "RamGirl- tl_note:<PERSON><PERSON><PERSON>, der Spieler hat wieder Hörner", "5500.char": "RamGirl- tl_note:RANDOM Player hat große Titten", "5600.char": "RamGirl- tl_note:RANDOM Player hat wirklich große Titten", "5700.char": "RamGirl- tl_note:RANDOM Spieler hat Widderhörner", "5800.char": "RamGirl- tl_note:RANDOM Spieler hat Pfoten", "5900.char": "RamGirl- tl_note:RANDOM Katzenschwanz", "6000.char": "RamGirl- tl_note:RANDOM fox-tail", "6100.char": "RamGirl- tl_note:Zufälliger Widerstand des Spielers liegt unter 10", "6200.char": "RamGirl- tl_note:Zufälliger Widerstand des Spielers liegt unter 5", "6300.char": "RamGirl- tl_note:RANDOM", "6400.char": "RamGirl- tl_note:RANDOM (Rangliste)", "6500.char": "RamGirl- tl_note:RANDOM Fuchshaar", "6600.char": "RamGirl- tl_note: ZUFÄLLIGE Harpyienflügel", "6700.char": "RamGirl- tl_note:RANDOM2 Harpyienflügel", "9999.char": "Ram<PERSON><PERSON><PERSON>"}, "meta": {"Icon_Path": "deflag.png", "Translator_Comment": "<PERSON><PERSON>", "Game_Version": "V0.6.2"}, "misc_ui": {"DISCARD": "VERWERFEN", "REMOVE": "ENTFERNEN", "petalmenutext": ["Alles überspringen", "Einmal überspringen", "Einstellungen", "Gehen Sie einmal zurück", "Gehen Sie alle zurück"], "spec_enemy_text_array": ["Bereit", "Zucken(!)Immun"]}, "mainmenu": {"progress": "Klasse:VAR1\n\nProgress:\n\nStages Cleared: VAR2 / VAR3\nTransformations: VAR4 / VAR5\nCGs: VAR6 / VAR7\nEndings: VAR8 / VAR9", "patreonbutton": "Unterstützung\n", "discordbutton": "Treten Sie der bei\n ", "veiltextdict": {"noreset": "<PERSON>e können den Speicherplatz (VAR1) nicht zurücksetzen, da er keine Daten enthält.", "reset": "<PERSON>e sind dabei, den Speicherplatz (VAR1) auf den Start zurückzusetzen. ", "touchreset": "<PERSON>e sind dabei, den Speicherplatz (VAR1) auf den Start zurückzusetzen. ", "start": "Diese Sicherungsdatei (Speicherplatz VAR1) enthält noch keine Daten. ", "exit": "Anwendung schließen?", "loading": "Lädt...", "resetdone": "Speichern wurde zurückgesetzt.", "patreon": "<PERSON><PERSON><PERSON> wird ein Link zur Patreon-Seite des Spiels geöffnet:\n\n", "discord": "<PERSON><PERSON><PERSON> wird ein Link zum offiziellen MonCurse-Discord-Server geöffnet.\n\n", "translationtools": "Übersetzung\n", "crashprevention": "Das Spiel endete vorzeitig.\n"}, "yes": "<PERSON>a", "cancel": "Stornieren"}, "specialmenu": {"fullscreenconfirm": "Bitte drücken Si<PERSON> hier, um zu bestätigen,\n", "fullscreencancel": "<PERSON>hren Sie zu Windowed zurück", "consentmessage": "Das folgende Spiel enthält sexuell eindeutige Themen und Inhalte. "}, "maptext": {"areastringdict": {"0": "Versteck des Flüchtlings", "1": "Fox's Shrine", "2": "Heiße Quellen", "101": "<PERSON><PERSON>", "102": "<PERSON><PERSON><PERSON>", "103": "<PERSON><PERSON>", "104": "<PERSON><PERSON><PERSON>", "105": "Fluss"}, "shortareastringdict": {"0": "<PERSON><PERSON>", "1": "<PERSON><PERSON><PERSON>", "2": "Heiße Quellen", "T1": "T1", "T2": "T2", "T3": "T3", "T4": "T4", "T5": "T5", "T?": "T?", "Blocked": "verstopft", "Confirm?": "Bestätigen?", "End Expedition": "Expedition beenden", "Retry This Tile": "Versuchen Sie es erneut mit dieser Ka<PERSON>", "Go Back": "<< Zurück"}, "queststringarray": ["Entdecken Sie den Schrein", "Besiege Rams", "Gehe zum Schrein", "<PERSON><PERSON> Ha<PERSON> zurückkehren", "Route A11\n", "ODER\n"], "namemissiondict": {"4": "Ein langer Spaziergang", "11": "In Bearbeitung"}, "chooselabelsarray": ["Ignorieren", "Akzeptieren", "Wählen Sie eine Fähigkeit:", "Hält bis zum Ende der Mission an", "Klassenfähigkeiten:", "Fuchsmädchen – Wählen Sie zwei Fähigkeiten:"]}, "settingstextdict": {"Scrollcontainer/settings/cen0/VBoxContainer/CheckBox6": "Vol<PERSON>bild (F11)", "Scrollcontainer/settings/cen0/VBoxContainer/CheckBox10": "Low-RAM-Kompatibilitätsmodus", "Scrollcontainer/settings/cen0/VBoxContainer/CheckBox8": "Schnellere Kurven", "Scrollcontainer/settings/cen0/VBoxContainer/CheckBox2": "Die Kamera folgt dem Spieler automatisch", "Scrollcontainer/settings/cen1/Mastervolume/volumelabel": "Master-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: ", "Scrollcontainer/settings/cen2/Musicvolume/volumelabel": "Musiklautstärke: ", "Scrollcontainer/settings/cen3/SFXvolume/volumelabel": "SFX-Lautstärke: ", "Scrollcontainer/settings/cen4/Ambiencevolume/volumelabel": "Umgebungslautstärke: ", "Scrollcontainer/settings/qcontrol/quittomainmenu/mainmenu": "<PERSON><PERSON><PERSON> auf\n ", "Scrollcontainer/settings/fcontrol/forfeit/mainmenu": "Verlieren"}, "attacknames": {"Warp": "<PERSON><PERSON>", "Dash": "Bindestrich", "Lance": "<PERSON><PERSON><PERSON>", "Sweep": "Fegen", "Pole": "Pole", "Axe": "Axt", "Dagger": "<PERSON><PERSON><PERSON>", "Torch": "<PERSON><PERSON><PERSON>", "Piton": "<PERSON><PERSON><PERSON><PERSON>", "Windstep": "<PERSON><PERSON><PERSON><PERSON>", "Buckler": "<PERSON><PERSON>", "Reposition": "Umstellen", "Scimitar": "Krummsäbel", "Pickaxe": "Spitzhacke", "Struggle": "<PERSON><PERSON><PERSON>", "Shortbow": "Kurzbogen", "Pounce": "<PERSON><PERSON><PERSON>lagen", "Scratch": "<PERSON><PERSON><PERSON>", "Heavenly Chord": "Himmlischer Akkord", "Sheep Song": "Schaflied", "Remove Seal": "Dichtung entfernen", "Resonance": "<PERSON><PERSON><PERSON><PERSON>", "Warpath": "Kriegspfad", "Sense": "<PERSON><PERSON>", "Hypnosis": "Hypnose", "Empower": "Ermächtigen", "Ruin": "<PERSON><PERSON><PERSON>", "Juggernaut": "Moloch", "Shockwave": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Ghost Bell": "Geisterglocke", "Milky Drain": "Milchiger Abfluss", "Overflow": "<PERSON><PERSON><PERSON><PERSON>", "Productivity": "Produktivität", "Draining Kiss": "Erschöpfender Kuss", "Cum Seal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Fox Seal": "<PERSON><PERSON><PERSON><PERSON>", "Egg Bomb": "E<PERSON>bombe", "Lance-II": "Lanze-II"}, "describecorruptiondict": {"horns5": "Befiehlt Autorität. ", "eyes5": "Rams Make-up. ", "top5": "Rams Kleidung. ", "pants5": "Rams Strümpfe und Tanga.", "ears3": "Tiger<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>. ", "tail3": "<PERSON><PERSON><PERSON>, 1 Sprung. ", "armright3": "Wischangriff. ", "ears4": "Markenzeichen des schlauen Fuchses. ", "tail4": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, flaus<PERSON><PERSON> Schwanz. ", "hair4": "Das Symbol des Fuchses. ", "armright7": "Unbehol<PERSON>e, aber flauschige Flügel. ", "bust2": "Eine größere Brust speichert Milch.", "bust3": "Eine noch größere Brust speichert mehr Milch.", "bust4": "Eine absurd große Büste hält Milch zurück."}, "icondescribearray": ["Windschritt: Fallgeschwindigkeit wird 0.", "Sinn: <PERSON><PERSON><PERSON> fein<PERSON> an.", "ExtraKnockback: Der nächste Angriff erhält einen Rückstoß.", "Entziehen: Gewährt Wirkung bei Treffer.", "Drainheal: Gewährt Gesundheit bei Treffer.", "Schnell: <PERSON><PERSON> die Runde nicht.", "Schwer: Wirkung verzögert sich, bis der Gegner an der Reihe ist.", "Wiederherstellung: <PERSON><PERSON><PERSON><PERSON> Ihre Debuffs herunter.", "Echo: Wird für jede Hotbar-Fähigkeit desselben Typs wiederholt.", "Block: Temporärer Schild gegen Schaden und Schwächungen."], "curseditemdescribedict": {"-16": "Schw<PERSON><PERSON> den Träger, wenn er am Schwanz des Benutzers festgebunden wird.", "-9": "Du erhältst „Laufen“, wenn du deine Runde beim Klettern beendest.", "-10": "<PERSON><PERSON><PERSON> Ihren Geruch, um Werwölfe zu verwirren.", "-11": "<PERSON><PERSON><PERSON><PERSON>, wenn er von KRAFT getroffen wird.", "-12": "Ein normaler Angriff verursacht SÜßEN Schaden.", "-14": "1 Fertigkeitsauswahl auf der Karte.", "-15": "Der normale Angriff trifft drei Ka<PERSON>n."}, "curseditemname": {"Status": "Status: ", "Cursed Item": "Verfluchter Gegenstand: ", "bust2": "Brustumfang: <PERSON><PERSON><PERSON><PERSON><PERSON>", "bust3": "Büste: <PERSON><PERSON><PERSON>", "bust4": "Büste: <PERSON><PERSON><PERSON>", "-16": "Hypno-Perlen", "-9": "Kletterklauen", "-10": "Wolfsfell", "-11": "Glocke der Milchmagd", "-12": "Weiche Pfoten", "-13": "<PERSON><PERSON><PERSON><PERSON>", "-14": "Fuchssymbol", "-15": "Pfoten"}, "bodypartdict": {"bust": "<PERSON><PERSON><PERSON>", "armright": "Waffen", "hair": "<PERSON><PERSON>", "pants": "Hose", "top": "Spitze", "horns": "<PERSON><PERSON><PERSON>", "ears": "<PERSON><PERSON>", "tail": "<PERSON><PERSON><PERSON>", "eyes": "<PERSON>en"}, "debuffdescriptionarray": ["<PERSON><PERSON><PERSON><PERSON><PERSON>, die Bewegung halbiert sich.", "<PERSON>s besteht die Gefahr, geh<PERSON><PERSON> zu werden.", "<PERSON>nn sich von anderen Erkrankungen nicht erholen.", "Verklebt. ", "Wissen und Bewegung sind die Merkmale der Kitsune.", "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "Kann die Verwendung angeborener Fähigkeiten einschränken.", "Es besteht die Gefahr einer Betäubung.", "<PERSON><PERSON><PERSON><PERSON><PERSON> das Gewicht auf der Brust."], "debuffidentifyarray": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hypnose", "<PERSON><PERSON><PERSON>witzt", "Sperma", "<PERSON><PERSON><PERSON>", "Geschreddert", "<PERSON><PERSON>", "Auswirkungen", "<PERSON><PERSON><PERSON>"], "debuffidentifynegativearray": ["<PERSON><PERSON><PERSON>", "Rückschlag"], "preview_words": ["Springen", "Waschbecken", "Fallen", "<PERSON><PERSON>", "Steigen", "Widerstehen", "Fähigkeit", "Neutral", "<PERSON><PERSON>", "Objekt", "verstopft", "<PERSON><PERSON>\n", "<PERSON><PERSON>", "<PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>", "Attacke", "Fliege", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON><PERSON>\n"], "customizationtextarray": ["<PERSON><PERSON><PERSON> hi<PERSON>, um den Vorgang abzuschließen – nur 5 Charakternamen.", "NAME EINGEBEN", "Nur 5 Charakternamen.", "<PERSON>den", "Die Anpassung kann auch später geändert werden.\n", "An", "Aus", "Zurückkehren", "Inhaltseinstellungen werden von allen Sicherungsdateien gemeinsam genutzt.", "Warnung! ", "R18-<PERSON><PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON> (Feind)", "Extreme Brust\n "], "tabinfodict": {"status": "Status", "statusinfo": "Erklärt Debuffs und Bedingungen.", "skills": "Fähigkeiten", "skillsinfo": "Die Inventur erfolgt vollautomatisch. ", "settings": "Einstellungen", "settingsinfo": "Das Spiel speichert Freischaltungen automatisch. "}, "racearray": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Amor", "Neko", "Kitsune", "RAM", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "raceclassarray": ["Lancer", "<PERSON><PERSON>", "Amor", "Tiger", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "monster_display_names": {"0": ["Katzenritter", "Tiger<PERSON><PERSON>"], "1": ["Fuchsmädchen", "Kutsu"], "2": ["<PERSON><PERSON><PERSON>", "Tonhöhe"], "3": ["Widdermädchen", "Saty<PERSON>"], "4": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "5": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "HP": "PS"}, "chapter_text": {}, "event_messages": {}}