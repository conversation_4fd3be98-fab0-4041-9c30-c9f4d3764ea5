[gd_scene load_steps=43 format=2]

[ext_resource path="res://Tilemaps/cleared.png" type="Texture" id=1]
[ext_resource path="res://rulessprite.tscn" type="PackedScene" id=2]
[ext_resource path="res://Assets/blacksquare.png" type="Texture" id=3]
[ext_resource path="res://escalatev2.gd" type="Script" id=4]
[ext_resource path="res://Assets/darksquare.png" type="Texture" id=5]
[ext_resource path="res://Background/nightsky.png" type="Texture" id=6]
[ext_resource path="res://zapsplat/zapsplat_multimedia_alert_bright_warm_system_positive_001_57863.ogg" type="AudioStream" id=7]
[ext_resource path="res://Background/fog2x2tiles.png" type="Texture" id=8]
[ext_resource path="res://Tilemaps/occupy.png" type="Texture" id=9]
[ext_resource path="res://Tilemaps/isometrictiles.tres" type="TileSet" id=10]
[ext_resource path="res://Tilemaps/overworldicons.tres" type="TileSet" id=11]
[ext_resource path="res://font/Verily-Serif-Mono/VerilySerifMono.otf" type="DynamicFontData" id=12]
[ext_resource path="res://Assets/ui/qaruleshorizontalbuttonhover.png" type="Texture" id=13]
[ext_resource path="res://Assets/ui/qaruleshorizontalbutton.png" type="Texture" id=14]
[ext_resource path="res://Assets/ui/questboardsmall.png" type="Texture" id=15]
[ext_resource path="res://zapsplat/zapsplat_multimedia_alert_bright_warm_system_positive_002_57864.ogg" type="AudioStream" id=16]
[ext_resource path="res://RewardPicker.tscn" type="PackedScene" id=17]
[ext_resource path="res://Assets/dialogueblacksquare.png" type="Texture" id=18]
[ext_resource path="res://Assets/ui/lootbox.png" type="Texture" id=19]
[ext_resource path="res://Assets/ui/lootboxhover.png" type="Texture" id=20]
[ext_resource path="res://font/OpenSans-ExtraBold.ttf" type="DynamicFontData" id=21]
[ext_resource path="res://zapsplat/zapsplat_multimedia_pop_up_tone_short_007_78859.ogg" type="AudioStream" id=22]
[ext_resource path="res://Background/blacksquare.png" type="Texture" id=23]
[ext_resource path="res://Assets/hologramtestdeletelater.png" type="Texture" id=24]
[ext_resource path="res://Assets/materials/hologramshader.shader" type="Shader" id=25]

[sub_resource type="DynamicFont" id=118]
outline_size = 2
outline_color = Color( 0, 0, 0, 0.705882 )
font_data = ExtResource( 12 )

[sub_resource type="DynamicFontData" id=153]
font_path = "res://font/Verily-Serif-Mono/VerilySerifMono.otf"

[sub_resource type="DynamicFont" id=146]
size = 22
outline_size = 2
outline_color = Color( 0, 0, 0, 0.705882 )
font_data = SubResource( 153 )

[sub_resource type="Animation" id=151]
resource_name = "jiggle"
length = 3.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:rect_rotation")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.5, 0.7, 1.5, 1.7, 2, 2.2 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, -5.0, 5.0, 0.0, 0.0, 5.0, -5.0, 0.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:rect_scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.2, 0.5, 1.5, 1.7, 2 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 1.1, 0.95 ), Vector2( 1, 1 ), Vector2( 1, 1 ), Vector2( 1.1, 0.95 ), Vector2( 1, 1 ) ]
}

[sub_resource type="DynamicFont" id=154]
size = 42
outline_size = 4
outline_color = Color( 0.34902, 0.427451, 0.454902, 0.705882 )
font_data = ExtResource( 21 )

[sub_resource type="Animation" id=158]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath(".:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ false ]
}

[sub_resource type="Animation" id=157]
resource_name = "plusone"
length = 1.8
tracks/0/type = "value"
tracks/0/path = NodePath(".:anchor_top")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1.8 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ 0.85, 0.65 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:anchor_bottom")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1.8 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ 0.9, 0.7 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1.8 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:self_modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.6, 1.8 ),
"transitions": PoolRealArray( 1, 1, 1.07177 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}

[sub_resource type="Shader" id=148]
code = "shader_type canvas_item;


uniform vec4 shine_color : hint_color = vec4(1.0);
uniform float shine_progress : hint_range(0.0, 1.0, 0.01) = 0.0;
uniform float shine_size : hint_range(0.01, 1.0, 0.01) = 0.1;
uniform float shine_angle : hint_range(0.0, 89.9, 0.1) = 45.0;

float scale(float value, float inMin, float inMax, float outMin, float outMax) {
    return (value - inMin) * (outMax - outMin) / (inMax - inMin) + outMin;
}

void fragment() {
    COLOR = texture(TEXTURE, UV);
	float slope = tan(radians(shine_angle));
	float progress = scale(shine_progress, 0.0, 1.0, -1.0 - shine_size - shine_size * slope, 1.0 * slope);
    float shine = step(slope * UV.x - UV.y, progress + shine_size + shine_size * slope) - step(slope * UV.x - UV.y, progress);
    COLOR.rgb = mix(COLOR.rgb, shine_color.rgb, shine * shine_color.a);
}"

[sub_resource type="ShaderMaterial" id=149]
shader = SubResource( 148 )
shader_param/shine_color = Color( 1, 0.941176, 0.941176, 0.666667 )
shader_param/shine_progress = 1.0
shader_param/shine_size = 0.08
shader_param/shine_angle = 45.0

[sub_resource type="Animation" id=147]
resource_name = "questchange"
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.862745, 1, 1, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:rect_scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.2, 1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 0.97, 1.03 ), Vector2( 1, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:material:shader_param/shine_progress")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.2, 1 ),
"transitions": PoolRealArray( 2, 0.5, 1 ),
"update": 0,
"values": [ 1.0, 0.0, 1.0 ]
}

[sub_resource type="Animation" id=150]
resource_name = "questcomplete"
length = 1.4
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 1.4 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.862745, 1, 1, 1 ), Color( 0.627451, 0.627451, 0.627451, 0.627451 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:rect_scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.2, 1.4 ),
"transitions": PoolRealArray( 1, 0.4, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 1.02, 0.95 ), Vector2( 1, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:material:shader_param/shine_progress")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1.1 ),
"transitions": PoolRealArray( 2, 0.5 ),
"update": 0,
"values": [ 1.0, 0.0 ]
}

[sub_resource type="TileSet" id=18]
0/name = "darksquare.png 0"
0/texture = ExtResource( 5 )
0/tex_offset = Vector2( 0, 0 )
0/modulate = Color( 1, 1, 1, 1 )
0/region = Rect2( 0, 0, 128, 128 )
0/tile_mode = 0
0/occluder_offset = Vector2( 0, 0 )
0/navigation_offset = Vector2( 0, 0 )
0/shape_offset = Vector2( 0, 0 )
0/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
0/shape_one_way = false
0/shape_one_way_margin = 0.0
0/shapes = [  ]
0/z_index = 0
1/name = "blacksquare.png 1"
1/texture = ExtResource( 3 )
1/tex_offset = Vector2( 0, 0 )
1/modulate = Color( 1, 1, 1, 1 )
1/region = Rect2( 0, 0, 128, 128 )
1/tile_mode = 0
1/occluder_offset = Vector2( 0, 0 )
1/navigation_offset = Vector2( 0, 0 )
1/shape_offset = Vector2( 0, 0 )
1/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
1/shape_one_way = false
1/shape_one_way_margin = 0.0
1/shapes = [  ]
1/z_index = 0
2/name = "fog2x2tiles.png 2"
2/texture = ExtResource( 8 )
2/tex_offset = Vector2( 0, 0 )
2/modulate = Color( 1, 1, 1, 1 )
2/region = Rect2( 0, 0, 512, 512 )
2/tile_mode = 1
2/autotile/bitmask_mode = 0
2/autotile/bitmask_flags = [ Vector2( 0, 0 ), 64, Vector2( 0, 1 ), 257, Vector2( 0, 2 ), 4, Vector2( 1, 0 ), 260, Vector2( 1, 1 ), 324, Vector2( 1, 2 ), 5, Vector2( 1, 3 ), 256, Vector2( 2, 0 ), 321, Vector2( 2, 1 ), 325, Vector2( 2, 2 ), 261, Vector2( 2, 3 ), 68, Vector2( 3, 0 ), 320, Vector2( 3, 1 ), 69, Vector2( 3, 2 ), 65, Vector2( 3, 3 ), 1 ]
2/autotile/icon_coordinate = Vector2( 0, 3 )
2/autotile/tile_size = Vector2( 128, 128 )
2/autotile/spacing = 0
2/autotile/occluder_map = [  ]
2/autotile/navpoly_map = [  ]
2/autotile/priority_map = [  ]
2/autotile/z_index_map = [  ]
2/occluder_offset = Vector2( 0, 0 )
2/navigation_offset = Vector2( 0, 0 )
2/shape_offset = Vector2( 0, 0 )
2/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
2/shape_one_way = false
2/shape_one_way_margin = 0.0
2/shapes = [  ]
2/z_index = 0

[sub_resource type="ShaderMaterial" id=156]
shader = ExtResource( 25 )
shader_param/speed = 1.4
shader_param/hologramTexture = ExtResource( 24 )

[sub_resource type="TileSet" id=19]
0/name = "occupy.png 0"
0/texture = ExtResource( 9 )
0/tex_offset = Vector2( 0, 0 )
0/modulate = Color( 1, 1, 1, 1 )
0/region = Rect2( 0, 0, 128, 128 )
0/tile_mode = 0
0/occluder_offset = Vector2( 0, 0 )
0/navigation_offset = Vector2( 0, 0 )
0/shape_offset = Vector2( 0, 0 )
0/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
0/shape_one_way = false
0/shape_one_way_margin = 0.0
0/shapes = [  ]
0/z_index = 0
1/name = "occupy.png 1"
1/texture = ExtResource( 9 )
1/tex_offset = Vector2( 0, 0 )
1/modulate = Color( 1, 1, 1, 1 )
1/region = Rect2( 128, 0, 128, 128 )
1/tile_mode = 0
1/occluder_offset = Vector2( 0, 0 )
1/navigation_offset = Vector2( 0, 0 )
1/shape_offset = Vector2( 0, 0 )
1/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
1/shape_one_way = false
1/shape_one_way_margin = 0.0
1/shapes = [  ]
1/z_index = 0
2/name = "occupy.png 2"
2/texture = ExtResource( 9 )
2/tex_offset = Vector2( 0, 0 )
2/modulate = Color( 1, 1, 1, 1 )
2/region = Rect2( 0, 128, 128, 128 )
2/tile_mode = 0
2/occluder_offset = Vector2( 0, 0 )
2/navigation_offset = Vector2( 0, 0 )
2/shape_offset = Vector2( 0, 0 )
2/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
2/shape_one_way = false
2/shape_one_way_margin = 0.0
2/shapes = [  ]
2/z_index = 0
3/name = "occupy.png 3"
3/texture = ExtResource( 9 )
3/tex_offset = Vector2( 0, 0 )
3/modulate = Color( 1, 1, 1, 1 )
3/region = Rect2( 128, 128, 128, 128 )
3/tile_mode = 0
3/occluder_offset = Vector2( 0, 0 )
3/navigation_offset = Vector2( 0, 0 )
3/shape_offset = Vector2( 0, 0 )
3/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
3/shape_one_way = false
3/shape_one_way_margin = 0.0
3/shapes = [  ]
3/z_index = 0

[sub_resource type="TileSet" id=155]
0/name = "cleared.png 0"
0/texture = ExtResource( 1 )
0/tex_offset = Vector2( 0, 0 )
0/modulate = Color( 1, 1, 1, 1 )
0/region = Rect2( 0, 0, 64, 64 )
0/tile_mode = 0
0/occluder_offset = Vector2( 0, 0 )
0/navigation_offset = Vector2( 0, 0 )
0/shape_offset = Vector2( 0, 0 )
0/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
0/shape_one_way = false
0/shape_one_way_margin = 0.0
0/shapes = [  ]
0/z_index = 0

[sub_resource type="TileSet" id=145]
0/name = "occupy.png 0"
0/texture = ExtResource( 9 )
0/tex_offset = Vector2( 0, 0 )
0/modulate = Color( 1, 1, 1, 1 )
0/region = Rect2( 0, 0, 128, 128 )
0/tile_mode = 0
0/occluder_offset = Vector2( 0, 0 )
0/navigation_offset = Vector2( 0, 0 )
0/shape_offset = Vector2( 0, 0 )
0/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
0/shape_one_way = false
0/shape_one_way_margin = 0.0
0/shapes = [  ]
0/z_index = 0
1/name = "occupy.png 1"
1/texture = ExtResource( 9 )
1/tex_offset = Vector2( 0, 0 )
1/modulate = Color( 1, 1, 1, 1 )
1/region = Rect2( 128, 0, 128, 128 )
1/tile_mode = 0
1/occluder_offset = Vector2( 0, 0 )
1/navigation_offset = Vector2( 0, 0 )
1/shape_offset = Vector2( 0, 0 )
1/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
1/shape_one_way = false
1/shape_one_way_margin = 0.0
1/shapes = [  ]
1/z_index = 0
2/name = "occupy.png 2"
2/texture = ExtResource( 9 )
2/tex_offset = Vector2( 0, 0 )
2/modulate = Color( 1, 1, 1, 1 )
2/region = Rect2( 0, 128, 128, 128 )
2/tile_mode = 0
2/occluder_offset = Vector2( 0, 0 )
2/navigation_offset = Vector2( 0, 0 )
2/shape_offset = Vector2( 0, 0 )
2/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
2/shape_one_way = false
2/shape_one_way_margin = 0.0
2/shapes = [  ]
2/z_index = 0
3/name = "occupy.png 3"
3/texture = ExtResource( 9 )
3/tex_offset = Vector2( 0, 0 )
3/modulate = Color( 1, 1, 1, 1 )
3/region = Rect2( 128, 128, 128, 128 )
3/tile_mode = 0
3/occluder_offset = Vector2( 0, 0 )
3/navigation_offset = Vector2( 0, 0 )
3/shape_offset = Vector2( 0, 0 )
3/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
3/shape_one_way = false
3/shape_one_way_margin = 0.0
3/shapes = [  ]
3/z_index = 0

[sub_resource type="Animation" id=152]
resource_name = "map"
length = 0.6
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.6 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}

[node name="escalatev2" type="Control"]
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource( 4 )

[node name="background" type="TextureRect" parent="."]
modulate = Color( 0.392157, 0.392157, 0.392157, 1 )
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource( 6 )
expand = true

[node name="backbuttoncanvas" type="CanvasLayer" parent="."]
layer = 9

[node name="Forbidden" type="TextureRect" parent="backbuttoncanvas"]
self_modulate = Color( 1, 1, 1, 0.627451 )
anchor_top = 0.8
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource( 23 )
expand = true

[node name="backbutton" type="TextureButton" parent="backbuttoncanvas"]
modulate = Color( 1, 1, 1, 0.588235 )
self_modulate = Color( 0.313726, 0.313726, 0.313726, 1 )
anchor_left = 0.06
anchor_top = 0.85
anchor_right = 0.19
anchor_bottom = 0.95
texture_normal = ExtResource( 15 )
expand = true

[node name="Label3" type="Label" parent="backbuttoncanvas/backbutton"]
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 0.517647, 0.407843, 0.407843, 1 )
custom_fonts/font = SubResource( 118 )
text = "<< Go Back"
align = 1
valign = 1

[node name="villagebutton" type="TextureButton" parent="backbuttoncanvas"]
visible = false
anchor_left = 0.7
anchor_top = 0.85
anchor_right = 0.95
anchor_bottom = 0.95
texture_normal = ExtResource( 14 )
texture_hover = ExtResource( 13 )
expand = true

[node name="Label" type="Label" parent="backbuttoncanvas/villagebutton"]
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
custom_fonts/font = SubResource( 146 )
text = "End Expedition"
align = 1
valign = 1

[node name="confirm" type="Timer" parent="backbuttoncanvas/villagebutton"]
wait_time = 80.0
one_shot = true

[node name="rewardbutton" type="TextureButton" parent="backbuttoncanvas"]
anchor_left = 0.35
anchor_top = 0.82
anchor_right = 0.65
anchor_bottom = 0.97
rect_pivot_offset = Vector2( 153.6, 45 )
texture_normal = ExtResource( 19 )
texture_pressed = ExtResource( 20 )
expand = true
stretch_mode = 5

[node name="AnimationPlayer" type="AnimationPlayer" parent="backbuttoncanvas/rewardbutton"]
playback_speed = 1.6
anims/jiggle = SubResource( 151 )

[node name="numleft" type="Label" parent="backbuttoncanvas/rewardbutton"]
anchor_top = 0.15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 0.858824, 0.898039, 0.901961, 1 )
custom_fonts/font = SubResource( 154 )
text = "1"
align = 1
valign = 1

[node name="open2" type="AudioStreamPlayer" parent="backbuttoncanvas/rewardbutton"]
stream = ExtResource( 22 )
volume_db = -6.0
bus = "SFX"

[node name="more_added" type="Label" parent="backbuttoncanvas"]
visible = false
self_modulate = Color( 1, 1, 1, 0 )
anchor_left = 0.5
anchor_top = 0.85
anchor_right = 0.5
anchor_bottom = 0.9
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 0.858824, 0.898039, 0.901961, 1 )
custom_fonts/font = SubResource( 154 )
text = "+1"
align = 1
valign = 1

[node name="plusone" type="AnimationPlayer" parent="backbuttoncanvas/more_added"]
anims/RESET = SubResource( 158 )
anims/plusone = SubResource( 157 )

[node name="Forbidden2" type="TextureRect" parent="backbuttoncanvas"]
visible = false
self_modulate = Color( 1, 1, 1, 0.627451 )
anchor_right = 1.0
anchor_bottom = 0.2
texture = ExtResource( 18 )
expand = true
flip_v = true

[node name="questbacker" type="NinePatchRect" parent="backbuttoncanvas"]
material = SubResource( 149 )
anchor_left = 0.8
anchor_top = 0.05
anchor_right = 0.95
anchor_bottom = 0.15
margin_left = -0.200001
margin_right = 0.199997
margin_bottom = 20.0
rect_pivot_offset = Vector2( 77, 40 )
mouse_filter = 1
texture = ExtResource( 15 )
patch_margin_left = 64
patch_margin_top = 40
patch_margin_right = 64
patch_margin_bottom = 40
axis_stretch_horizontal = 2
axis_stretch_vertical = 2

[node name="Label" type="Label" parent="backbuttoncanvas/questbacker"]
anchor_top = 0.2
anchor_right = 1.0
anchor_bottom = 0.9
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 0.862745, 0.784314, 0.941176, 1 )
custom_fonts/font = SubResource( 118 )
align = 1
valign = 1

[node name="Label2" type="Label" parent="backbuttoncanvas/questbacker"]
anchor_right = 1.0
anchor_bottom = 0.1
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 0.941176, 0.588235, 0.588235, 1 )
custom_fonts/font = SubResource( 118 )
text = "Quest"
align = 1
valign = 1

[node name="questchange" type="AudioStreamPlayer" parent="backbuttoncanvas/questbacker"]
stream = ExtResource( 7 )
volume_db = -12.0
pitch_scale = 0.7
bus = "SFX"

[node name="questcomplete" type="AudioStreamPlayer" parent="backbuttoncanvas/questbacker"]
stream = ExtResource( 16 )
volume_db = -12.0
pitch_scale = 0.8
bus = "SFX"

[node name="quested" type="AnimationPlayer" parent="backbuttoncanvas/questbacker"]
anims/questchange = SubResource( 147 )
anims/questcomplete = SubResource( 150 )

[node name="detailsbacker" type="NinePatchRect" parent="backbuttoncanvas"]
visible = false
material = SubResource( 149 )
anchor_left = 0.05
anchor_top = 0.05
anchor_right = 0.2
anchor_bottom = 0.15
margin_left = -0.200001
margin_right = 0.199997
margin_bottom = 20.0
rect_pivot_offset = Vector2( 77, 40 )
mouse_filter = 1
texture = ExtResource( 15 )
patch_margin_left = 64
patch_margin_top = 40
patch_margin_right = 64
patch_margin_bottom = 40
axis_stretch_horizontal = 2
axis_stretch_vertical = 2

[node name="Label" type="Label" parent="backbuttoncanvas/detailsbacker"]
anchor_top = 0.2
anchor_right = 1.0
anchor_bottom = 0.9
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 0.862745, 0.784314, 0.941176, 1 )
custom_fonts/font = SubResource( 118 )
align = 1
valign = 1

[node name="Label2" type="Label" parent="backbuttoncanvas/detailsbacker"]
anchor_right = 1.0
anchor_bottom = 0.1
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 0.941176, 0.588235, 0.588235, 1 )
custom_fonts/font = SubResource( 118 )
text = "Locale"
align = 1
valign = 1

[node name="ConfirmButton" type="TextureButton" parent="backbuttoncanvas"]
visible = false
anchor_left = 0.35
anchor_top = 0.05
anchor_right = 0.65
anchor_bottom = 0.15
texture_normal = ExtResource( 14 )
texture_hover = ExtResource( 13 )
expand = true

[node name="Label2" type="Label" parent="backbuttoncanvas/ConfirmButton"]
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
custom_fonts/font = SubResource( 146 )
text = "Confirm"
align = 1
valign = 1

[node name="foglayer" type="CanvasLayer" parent="."]
layer = 8

[node name="fogofwar" type="TileMap" parent="foglayer"]
z_index = 1
tile_set = SubResource( 18 )
cell_size = Vector2( 127, 63 )
format = 1

[node name="isometric" type="TileMap" parent="."]
tile_set = ExtResource( 10 )
cell_size = Vector2( 127, 63 )
cell_y_sort = true
format = 1

[node name="tilehighlight" type="TileMap" parent="isometric"]
modulate = Color( 0, 1, 1, 0.568627 )
material = SubResource( 156 )
tile_set = ExtResource( 10 )
cell_size = Vector2( 127, 63 )
cell_y_sort = true
format = 1

[node name="occupymap" type="TileMap" parent="isometric"]
modulate = Color( 1, 0.54902, 0.54902, 1 )
tile_set = SubResource( 19 )
cell_size = Vector2( 128, 128 )
format = 1

[node name="rulessprite" parent="isometric" instance=ExtResource( 2 )]
position = Vector2( 337, 157 )

[node name="selectorsdebug" type="Node2D" parent="isometric"]

[node name="selectors" type="Node2D" parent="isometric"]
z_index = 2

[node name="residents" type="Node2D" parent="isometric"]
modulate = Color( 0.588235, 0.588235, 0.588235, 1 )
z_index = 1

[node name="icons" type="TileMap" parent="isometric"]
z_index = 1
tile_set = ExtResource( 11 )
cell_size = Vector2( 127, 63 )
format = 1

[node name="tileselect" type="TileMap" parent="isometric"]
modulate = Color( 0.968627, 1, 0.392157, 1 )
tile_set = ExtResource( 10 )
cell_size = Vector2( 127, 63 )
cell_y_sort = true
format = 1

[node name="tilevisited" type="TileMap" parent="isometric"]
modulate = Color( 0, 0, 0, 0.54902 )
tile_set = ExtResource( 10 )
cell_size = Vector2( 127, 63 )
cell_y_sort = true
format = 1

[node name="cleared" type="TileMap" parent="isometric"]
position = Vector2( 164, 152 )
z_index = 5
tile_set = SubResource( 155 )
cell_size = Vector2( 127, 63 )
cell_y_sort = true
format = 1

[node name="tier" type="TileMap" parent="isometric"]
visible = false
position = Vector2( 64, 32 )
tile_set = SubResource( 145 )
cell_size = Vector2( 127, 63 )
cell_y_sort = true
format = 1

[node name="tiershow" type="TileMap" parent="isometric"]
position = Vector2( 64, 32 )
z_index = 5
tile_set = SubResource( 145 )
cell_size = Vector2( 127, 63 )
cell_y_sort = true
format = 1

[node name="touchtimer" type="Timer" parent="."]
one_shot = true

[node name="RewardPicker" parent="." instance=ExtResource( 17 )]
layer = 9
visible = false

[node name="clickdelay" type="Timer" parent="."]
wait_time = 0.6
one_shot = true

[node name="appear" type="AnimationPlayer" parent="."]
anims/map = SubResource( 152 )

[connection signal="mouse_entered" from="backbuttoncanvas/backbutton" to="." method="_on_backbutton_mouse_entered"]
[connection signal="mouse_exited" from="backbuttoncanvas/backbutton" to="." method="_on_backbutton_mouse_exited"]
[connection signal="pressed" from="backbuttoncanvas/backbutton" to="." method="_on_backbutton_pressed"]
[connection signal="pressed" from="backbuttoncanvas/villagebutton" to="." method="_on_villagebutton_pressed"]
[connection signal="timeout" from="backbuttoncanvas/villagebutton/confirm" to="." method="_on_confirm_timeout"]
[connection signal="pressed" from="backbuttoncanvas/rewardbutton" to="." method="_on_rewardbutton_pressed"]
[connection signal="mouse_entered" from="backbuttoncanvas/questbacker" to="." method="_on_questbacker_mouse_entered"]
[connection signal="mouse_exited" from="backbuttoncanvas/questbacker" to="." method="_on_questbacker_mouse_exited"]
[connection signal="mouse_entered" from="backbuttoncanvas/detailsbacker" to="." method="_on_detailsbacker_mouse_entered"]
[connection signal="mouse_exited" from="backbuttoncanvas/detailsbacker" to="." method="_on_detailsbacker_mouse_exited"]
[connection signal="mouse_entered" from="backbuttoncanvas/ConfirmButton" to="." method="_on_ConfirmButton_mouse_entered"]
[connection signal="mouse_exited" from="backbuttoncanvas/ConfirmButton" to="." method="_on_ConfirmButton_mouse_exited"]
[connection signal="timeout" from="touchtimer" to="." method="_on_touchtimer_timeout"]

[editable path="RewardPicker"]
