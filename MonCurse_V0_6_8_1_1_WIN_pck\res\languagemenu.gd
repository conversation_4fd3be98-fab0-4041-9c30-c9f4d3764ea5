extends TextureRect

var firstrun = true
var recentsizechange = false
func _on_viewport_size_changed():
	if recentsizechange == false:
		recentsizechange = true #this makes it so it only updates the viewport every 4 frames
		if firstrun == false:
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
		else:
			firstrun = false
		recentsizechange = false
		var ratio = get_viewport_rect().size/Playervariables.basescreensize #for event text purposes
#		var proportiony = (newsize.length()/600)
#		var proportionfull = (newsize.length()/1024)
#		var proportionx = (newsize.x/1024)
		$TL_BUTTON.get_node("Folder_Path").get("custom_fonts/font").set_size(17*ratio.x*$TL_BUTTON.spec_font_size)
		$TL_BUTTON.get_node("Folder_Path").get("custom_fonts/font").set_outline_size(ceil(2*ratio.x*$TL_BUTTON.spec_font_size))
		$TL_BUTTON.get_node("Translator_Comment").get("custom_fonts/font").set_size(23*ratio.x*$TL_BUTTON.spec_font_size)
		$TL_BUTTON.get_node("Translator_Comment").get("custom_fonts/font").set_outline_size(ceil(3*ratio.x*$TL_BUTTON.spec_font_size))
		
		var xbarsize = 30*ratio.x
		$ScrollContainer.get_v_scrollbar().rect_min_size.x = clamp(xbarsize,20,9000)
		$ScrollContainer.get_v_scrollbar().set_self_modulate(Color(0.8,0.8,1.0))
		
		for button in $ScrollContainer/VBoxContainer.get_children():
			button.rect_min_size = Vector2(280,120)*ratio
			button.get_node("Translator_Comment").get("custom_fonts/font").set_size(15*ratio.x*button.spec_font_size)
			button.get_node("Translator_Comment").get("custom_fonts/font").set_outline_size(ceil(1*ratio.x*button.spec_font_size))
#			if button.visible == false:
#				$TL_BUTTON.get_node("Translator_Comment").get("custom_fonts/font").set_size(15*ratio.x*button.spec_font_size)
#				$TL_BUTTON.get_node("Translator_Comment").get("custom_fonts/font").set_outline_size(ceil(1*ratio.x*button.spec_font_size))

var tl_button = preload("res://TL_BUTTON.tscn")

func load_external_tex(path):
	var tex_file = File.new()
	tex_file.open(path, File.READ)
	var bytes = tex_file.get_buffer(tex_file.get_len())
	var img = Image.new()
# warning-ignore:unused_variable
	var data
	if path.ends_with(".jpg"):
		data = img.load_jpg_from_buffer(bytes)
	elif path.ends_with(".png"):
		data = img.load_png_from_buffer(bytes)
	else:
		Playervariables.log_error("An external mod's filepath to an icon was not of type .jpg or .png. This was:  " +str(path))
		return null
	var imgtex = ImageTexture.new()
	imgtex.create_from_image(img)
	tex_file.close()
	return imgtex

func _ready():
	Playervariables.queuetllog = [] #set the log blank because errors may appear from this point onward
	$TL_BUTTON.prime = true
	$TL_BUTTON.set_modulate(Color(1,1,1,1))
#	$ScrollContainer/VBoxContainer/TL_BUTTON.visible = false
	
	var _c = get_tree().root.connect("size_changed", self, "_on_viewport_size_changed")
	
#	if Playervariables.orderdictionary.size() == 0:
	Playervariables.find_mods()
	Playervariables.find_internal_mods()
	
	scour_game_version()
	set_translations()
	_on_viewport_size_changed()

func set_translations():
	var button_fonted = false
	for key in Playervariables.orderdictionary:
		var newbutton = tl_button.instance()
		var modpath
		var internal = false
		var error_size = Playervariables.queuetllog.size()
		if key.find("internalmods") > -1:
			modpath = "res://" + key +"/"
			internal = true
		else:
			modpath = OS.get_executable_path().get_base_dir() + "/" + key + "/"
		var metadict = Playervariables.process_speechdict(modpath+Playervariables.orderdictionary[key],true)
		
		newbutton.get_node("Folder_Path").set_text(key)
		if internal == true:
			newbutton.set_self_modulate(Color(0.65,0.8,1))
			newbutton.get_node("Icon_Path/TextureRect").set_self_modulate(Color(0.65,0.8,1))
		else:
			newbutton.set_self_modulate(Color(0.55,0.55,0.80))
			newbutton.get_node("Icon_Path/TextureRect").set_self_modulate(Color(0.55,0.55,0.80))
			
		if metadict.has("mainmenu") and metadict["mainmenu"].has("veiltextdict") and metadict["mainmenu"]["veiltextdict"].has("translationtools"):
			newbutton.tl_tools_name = metadict["mainmenu"]["veiltextdict"]["translationtools"]
		if metadict.has("meta"):
			if metadict["meta"].has("Translator_Comment"):
				newbutton.get_node("Translator_Comment").set_text(metadict["meta"]["Translator_Comment"])
				
				var font_dict = Playervariables.process_speechdict(modpath+"font_parameters.json",true)
				if font_dict.size() > 0:
					if font_dict.has("font_attack_announce") and font_dict["font_attack_announce"] != null:
						newbutton.get_node("Translator_Comment").get("custom_fonts/font").font_data = Playervariables.load_external_font(modpath+font_dict["font_attack_announce"]).font_data
						if font_dict.has("font_attack_announce_size"):
							newbutton.spec_font_size = font_dict["font_attack_announce_size"]
			else:
				newbutton.get_node("Translator_Comment").visible = false
				
			if metadict["meta"].has("Game_Version"):
				newbutton.get_node("Game_Version").set_text(metadict["meta"]["Game_Version"])
				var versionnum = metadict["meta"]["Game_Version"].replace(".","").to_int()
#				if versionnum == gameversionnum or abs(gameversionnum-versionnum) < 10:
				if str(gameversionnum).substr(0,1) == str(versionnum).substr(0,1) or versionnum >= gameversionnum:
					if versionnum == gameversionnum or (str(gameversionnum).substr(1,1) == str(versionnum).substr(1,1)):
						newbutton.get_node("Game_Version").set_self_modulate(Color(0.52549, 0.827451, 0.462745))
					else:# versionnum > gameversionnum or str(gameversionnum).substr(0,1) == str(versionnum).substr(0,1):
						newbutton.get_node("Game_Version").set_self_modulate(Color(0.694118, 0.721569, 0.345098))
				else:
					newbutton.get_node("Game_Version").set_self_modulate(Color(0.823529, 0.282353, 0.282353))
			else:
				newbutton.get_node("Game_Version").set_text("V?.??.?")
				newbutton.get_node("Game_Version").set_self_modulate(Color(0.823529, 0.282353, 0.282353))
				
			if metadict["meta"].has("Icon_Path") and metadict["meta"]["Icon_Path"] != null:
				if internal == false:
					newbutton.get_node("Icon_Path").set_texture(load_external_tex(modpath+metadict["meta"]["Icon_Path"]))
				else:
					newbutton.get_node("Icon_Path").set_texture(load(modpath+metadict["meta"]["Icon_Path"]))
			else:
				newbutton.get_node("Icon_Path").visible = false
				
		else:
			newbutton.get_node("Translator_Comment").set_text("No info?")
			newbutton.get_node("Icon_Path").visible = false
			newbutton.get_node("Icon_Path").texture = null
			newbutton.get_node("Game_Version").set_text("V?.??.?")
			newbutton.get_node("Game_Version").set_self_modulate(Color(0.823529, 0.282353, 0.282353))
		$ScrollContainer/VBoxContainer.add_child(newbutton)
		if Playervariables.current_translation_code == newbutton.get_node("Folder_Path").get_text():
			prepare_translation(newbutton)
			button_fonted = true
		if Playervariables.queuetllog.size() > error_size:
			newbutton.has_error = true
			for i in range(Playervariables.queuetllog.size() - error_size):
				newbutton.errors.append(Playervariables.queuetllog[error_size+i])
	if button_fonted == false:
		prepare_translation($ScrollContainer/VBoxContainer/TL_BUTTON)
#		$TL_BUTTON.get_node("Translator_Comment").set("custom_fonts/font",$ScrollContainer/VBoxContainer/TL_BUTTON.get_node("Translator_Comment").get("custom_fonts/font"))

onready var hologram_mod = $TextureButton4/hologram.get_self_modulate()
onready var lastbutton = $ScrollContainer/VBoxContainer/TL_BUTTON
func prepare_translation(who):
	lastbutton.visible = true
	lastbutton = who
	$TL_BUTTON.get_node("Folder_Path").set_text(who.get_node("Folder_Path").get_text())
	$TL_BUTTON.get_node("Translator_Comment").set_text(who.get_node("Translator_Comment").get_text())
	
	$TL_BUTTON.get_node("Translator_Comment").get("custom_fonts/font").font_data = who.get_node("Translator_Comment").get("custom_fonts/font").font_data
	$TL_BUTTON.get_node("Translator_Comment").get("custom_fonts/font").size = who.get_node("Translator_Comment").get("custom_fonts/font").size*1.25
	$TL_BUTTON.get_node("Translator_Comment").get("custom_fonts/font").outline_size = ceil(who.get_node("Translator_Comment").get("custom_fonts/font").outline_size*1.25)
	
	$tools/tooltext.set("custom_fonts/font", who.get_node("Translator_Comment").get("custom_fonts/font"))
	$tools/tooltext.set_text(str(who.tl_tools_name))
	
	$TL_BUTTON.get_node("Game_Version").set_text(who.get_node("Game_Version").get_text())
	$TL_BUTTON.get_node("Icon_Path").texture = who.get_node("Icon_Path").texture
	$TL_BUTTON.get_node("Game_Version").self_modulate = who.get_node("Game_Version").self_modulate
	$TL_BUTTON.self_modulate = who.self_modulate
	$TL_BUTTON.get_node("Icon_Path/TextureRect").self_modulate = who.get_node("Icon_Path/TextureRect").self_modulate
	who.visible = false
	
	if $TL_BUTTON.get_node("Folder_Path").get_text() == Playervariables.current_translation_code or (Playervariables.current_translation_code == Playervariables.TL_DEFAULT and $TL_BUTTON.get_node("Folder_Path").get_text().begins_with("Official") == true):
		$TextureButton4/hologram.set_self_modulate(hologram_mod)
		$TextureButton4/hologram.material.set_shader_param("speed",5)
	else:
		$TextureButton4/hologram.set_self_modulate(Color(1,1,1,1))
		$TextureButton4/hologram.material.set_shader_param("speed",25)
	
	$TL_BUTTON.spec_font_size = who.spec_font_size
	var ratio = get_viewport_rect().size/Playervariables.basescreensize #for event text purposes
	$TL_BUTTON.get_node("Translator_Comment").get("custom_fonts/font").set_size(23*ratio.x*$TL_BUTTON.spec_font_size)
	$TL_BUTTON.get_node("Translator_Comment").get("custom_fonts/font").set_outline_size(ceil(3*ratio.x*$TL_BUTTON.spec_font_size))
	
	if who.has_error == true:
		update_lang_error_log(who.errors)
	else:
		$errorlog.visible = false

func _on_TextureButton4_pressed():
	self.visible = false

	get_node("/root/Master/SFX/uif").play()
	finalize_translation()
	get_node("/root/Master").call("update_error_log_via_master")
	
	if $TL_BUTTON.get_node("Folder_Path").get_text() == "internalmods/cn":
		var dir = Directory.new()
		if dir.file_exists("res://internalmods/cn/Readme.txt"):
			dir.copy("res://internalmods/cn/Readme.txt",OS.get_executable_path().get_base_dir()+"/Readme.txt")
	
	if get_parent().get_node("disclaimermenu").visible == false:
		get_parent().queue_free()
	else:
		queue_free()

func finalize_translation():
	if $TL_BUTTON.get_node("Folder_Path").get_text() != Playervariables.current_translation_code:
		if $TL_BUTTON.get_node("Folder_Path").get_text().begins_with("Official") == false:#Playervariables.current_translation_code:
			if Playervariables.current_translation_code != Playervariables.TL_DEFAULT:
				get_node("/root/Master").reset_game_to_english(true)
			
			var playervariablescopy = load("playervariables.gd")
			var newnode = TextureRect.new()
			newnode.set_script(playervariablescopy)
			add_child(newnode)
			Playervariables.set("default_font_dict",newnode.get("default_font_dict"))
			newnode.queue_free()
			
			Playervariables.translate_game($TL_BUTTON.get_node("Folder_Path").get_text())
		elif Playervariables.current_translation_code != Playervariables.TL_DEFAULT:
			get_node("/root/Master").reset_game_to_english(false)

var gameversionnum = 60
func scour_game_version():
	var text = OS.get_executable_path()
	text = text.substr(text.rfind("/",-1))
	var result = text.replace(".","").to_int()
	if result > 6:
		gameversionnum = result
		
		var numbify = str(text.replace(".","").to_int())
		var startnum = min(text.find(numbify.substr(0,1)),text.find("0."))
		var endnum = text.find(numbify.substr(numbify.length()-1,1))
		
		var endresult = "V" + text.substr(startnum,endnum)
		if endresult.is_valid_integer():
			$TL_BUTTON.get_node("Game_Version").set_text(endresult)
			$ScrollContainer/VBoxContainer/TL_BUTTON.get_node("Game_Version").set_text(endresult)
#	return result
#	var numbify = str(text.replace(".","").to_int())
#	var startnum = min(text.find(numbify.substr(0,1)),text.find("0."))
#	var endnum = text.find(numbify.substr(numbify.length()-1,1))
#	return text.substr(startnum,endnum)


func _on_tools_pressed():
	get_node("/root/Master/SFX/ui").play()
	finalize_translation()
	var modmenuscreen = load("res://exportedtools/modtools.tscn").instance()
	get_parent().add_child(modmenuscreen)



func update_lang_error_log(errorlog):
	if errorlog.size() > 0:
		$errorlog.visible = true
		var errormessage = "ERROR LOG"
		for error in errorlog:
			errormessage += "\n\n" + error
		$errorlog.set_text(errormessage)
	else:
		$errorlog.visible = false
