extends CanvasLayer

#const truebasesize = Vector2(1920,1080)
#var basesize = Vector2(1860,1020)
#var truebasesize = Vector2(3840,2160)
var basesize = Vector2(3720,2040)

#OFFSETS:
#MILK BOTTOM-LEFT: 0x,1550y
#MILK BOTTOM-RIGHT: 2630x,1530y
#HUFF: 1340x,675y


func _ready():
	$sprites.position = Vector2(0,0)
	var _c = get_tree().root.connect("size_changed", self, "_on_viewport_size_changed")
	_on_viewport_size_changed()
	engage_hover()
	$fadeshow.play("step 1")


const hover_offset = Vector2(-30,-30)
var firstrun = true
var recentsizechange = false
func _on_viewport_size_changed():
	if recentsizechange == false:
		recentsizechange = true #this makes it so it only updates the viewport every 4 frames
		if firstrun == false:
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
#		var viewportrect = get_parent().get_viewport_rect().size
		engage_hover(true)
		recentsizechange = false
		firstrun = false

var saved_animation_point = 0.0
func engage_hover(from_viewport=false):
	var viewportrect = $testdonotdelete.get_viewport_rect().size
	var proportion = viewportrect/basesize
#	if from_viewport == false:
#		hover_offset = ordinary_hover_offset
#	elif hover_offset != ordinary_hover_offset:
#		proportion = viewportrect/truebasesize
	var maxproportion = max(proportion.x,proportion.y)
	$sprites.scale = Vector2(maxproportion,maxproportion)*current_zoom
	var newhoveranim
	var actual_hover_offset = hover_offset*proportion*current_zoom
	if $hover.is_playing() == true:
		saved_animation_point = $hover.current_animation_position
	if ($hover.is_playing() == true and $hover.get_current_animation() == "newhover") or from_viewport == false:
		
		if $hover.has_animation("newhover"):
			newhoveranim = $hover.get_animation("newhover")
		else:
			newhoveranim = $hover.get_animation("hover").duplicate()
		if (proportion.x > proportion.y):#error in this case: cuts off extreme y
			newhoveranim.track_set_key_value(0,0,$sprites.position)#+actual_hover_offset)
			newhoveranim.track_set_key_value(0,1,Vector2(proportion.x*30,actual_hover_offset.y+viewportrect.y - (basesize.y * maxproportion))+actual_hover_offset)
			newhoveranim.track_set_key_value(0,2,Vector2(0,0))#+actual_hover_offset)
			newhoveranim.track_set_key_value(0,3,Vector2(-proportion.x*30,actual_hover_offset.y+viewportrect.y - (basesize.y * maxproportion))+actual_hover_offset)
			newhoveranim.track_set_key_value(0,4,Vector2(0,0))#+actual_hover_offset)
		else:#error in this case: cuts off extreme X
			newhoveranim.track_set_key_value(0,0,$sprites.position)#+actual_hover_offset)
			newhoveranim.track_set_key_value(0,1,Vector2(actual_hover_offset.x+viewportrect.x - (basesize.x * maxproportion),proportion.y*30)+actual_hover_offset)
			newhoveranim.track_set_key_value(0,2,Vector2(0,0))#+actual_hover_offset)
			newhoveranim.track_set_key_value(0,3,Vector2(actual_hover_offset.x+viewportrect.x - (basesize.x * maxproportion),-proportion.y*30)+actual_hover_offset)
			newhoveranim.track_set_key_value(0,4,Vector2(0,0))#actual_hover_offset)
#		$hover.playback_speed = 2 - ((maxproportion + 1) / (min(proportion.x,proportion.y)+1))
		var x = ((maxproportion+0.4) / (min(proportion.x,proportion.y)+0.4))
		$hover.playback_speed = (0.12 / (x*x))
		if $hover.has_animation("newhover") == false:
			$hover.add_animation("newhover",newhoveranim)
		$hover.stop()
		$hover.play("newhover")
		$hover.advance(saved_animation_point/$hover.playback_speed)
	elif $hover.is_playing() and $hover.get_current_animation() == "move_to_point":
		move_to_location(last_move_location,$hover.current_animation_position,true)
	elif from_viewport == true:
		move_to_location(last_move_location,0,true)

func pause_hover():
	saved_animation_point = $hover.current_animation_position
	$hover.stop(false)

#func engage_shake_rules(bigiftrue = true):
#	var viewportrect = $testdonotdelete.get_viewport_rect().size
#	var proportion = viewportrect/basesize
#	pass
#
#	var oldshakeanim
#	if bigiftrue == true:
#		oldshakeanim = $sprites/rules/AnimationPlayer.get_animation("shakerules")
#	else:
#		oldshakeanim = $sprites/rules/AnimationPlayer.get_animation("smallshakerules")
#	var newshakeanim
#	if $sprites/rules/AnimationPlayer.has_animation("newshake"):
#		newshakeanim = $sprites/rules/AnimationPlayer.get_animation("newshake")
#	else:
#		newshakeanim = $sprites/rules/AnimationPlayer.get_animation("shakerules").duplicate()
#
#	for i in range(5):
#		newshakeanim.track_set_key_value(0,i+1,oldshakeanim.track_get_key_value(0,i+1)*3*proportion*current_zoom)
#		newshakeanim.track_set_key_value(1,i+1,oldshakeanim.track_get_key_value(1,i+1)*3*proportion*current_zoom)
#
#	if $sprites/rules/AnimationPlayer.has_animation("newshake") == false:
#		$sprites/rules/AnimationPlayer.add_animation("newshake",newshakeanim)
#	$sprites/rules/AnimationPlayer.play("newshake")

func engage_shake():
#	hover_offset = ordinary_hover_offset
	var viewportrect = $testdonotdelete.get_viewport_rect().size
	var proportion = viewportrect/basesize
	var maxproportion = max(proportion.x,proportion.y)
#	var actual_hover_offset = hover_offset*proportion*current_zoom
	
	$sprites.scale = Vector2(maxproportion,maxproportion)
	var newshakeanim
#	if $hover.is_playing() == true:
#		saved_animation_point = $hover.current_animation_position
	var oldshakeanim = $hover.get_animation("shake")
	if $hover.has_animation("newshake"):
		newshakeanim = $hover.get_animation("newshake")
	else:
		newshakeanim = $hover.get_animation("shake").duplicate()
	var before_pos = $sprites.position
	for i in range(4):
		newshakeanim.track_set_key_value(0,i,oldshakeanim.track_get_key_value(0,i)*3*proportion*current_zoom + before_pos)#does not include actual_hover_effect because before_pos has it
	newshakeanim.track_set_key_value(0,4,hover_offset*proportion*current_zoom)#does not include actual_hover_effect because before_pos has it

	if $hover.has_animation("newshake") == false:
		$hover.add_animation("newshake",newshakeanim)
	$hover.stop()
	$hover.play("newshake")
	$hover.playback_speed = 2
#	$hover.advance(saved_animation_point/$hover.playback_speed)

var last_move_location = Vector2(0,0)
var current_zoom = 1.0
func move_to_location(percentage_vector = Vector2(0.5,0.5),advance_value = 0,straight = false):
	var viewportrect = $testdonotdelete.get_viewport_rect().size
	var proportion = viewportrect/basesize
#	if percentage_vector == Vector2(0.0,0.0):
#		hover_offset = Vector2(0,0)
#		proportion = viewportrect/truebasesize
#	else:
#		hover_offset = ordinary_hover_offset
	var use_anim = $hover.get_animation("move_to_point")
	var actual_hover_offset = hover_offset*proportion*current_zoom
	var maxproportion = max(proportion.x,proportion.y)
	var intended_vector = Vector2(0,0)
	var overshoot_vector = Vector2(0,0)
	var prevscale = $sprites.scale.x
	if (proportion.x > proportion.y):
		intended_vector = Vector2(0,-((maxproportion*current_zoom) - proportion.y) * percentage_vector.y * basesize.y)# * current_zoom
		var direction = (intended_vector - $sprites.position).normalized()
		overshoot_vector = Vector2(30*proportion.x,-(maxproportion*current_zoom - proportion.y) * clamp(percentage_vector.y+direction.y*0.05,0,1) * basesize.y)# * current_zoom
		if current_zoom > 1.0:
			intended_vector.x = -(current_zoom-1.0) * percentage_vector.x * basesize.x * maxproportion
			direction = (intended_vector - $sprites.position).normalized()
			overshoot_vector.x = -(current_zoom-1.0) * clamp(percentage_vector.x+direction.x*0.05,0,1) * basesize.x * maxproportion
		elif randf() > 0.5:
			overshoot_vector.x = -overshoot_vector.x
	else:
		intended_vector = Vector2(-((maxproportion*current_zoom) - proportion.x) * percentage_vector.x * basesize.x,0)# * current_zoom
		var direction = (intended_vector - $sprites.position).normalized()
		overshoot_vector = Vector2(-(maxproportion*current_zoom - proportion.x) * clamp(percentage_vector.x+direction.x*0.05,0,1) * basesize.x,30*proportion.y)# * current_zoom
		if current_zoom > 1.0:
			intended_vector.y = -(current_zoom-1.0) * percentage_vector.y * basesize.y * maxproportion
			direction = (intended_vector - $sprites.position).normalized()
			overshoot_vector.y = -(current_zoom-1.0) * clamp(percentage_vector.y+direction.y*0.05,0,1) * basesize.y * maxproportion
		elif randf() > 0.5:
			overshoot_vector.y = -overshoot_vector.y
	use_anim.track_set_key_value(0,0,$sprites.position)
	if straight == false:
		use_anim.track_set_key_value(0,1,overshoot_vector+actual_hover_offset)
	else:
		use_anim.track_set_key_value(0,1,intended_vector+actual_hover_offset)
	use_anim.track_set_key_value(0,2,intended_vector+actual_hover_offset)#+actual_hover_offset)
	use_anim.track_set_key_value(1,0,Vector2(prevscale,prevscale))
	if straight == false:
		use_anim.track_set_key_value(1,1,Vector2(maxproportion,maxproportion)*current_zoom*1.15)
	else:
		use_anim.track_set_key_value(1,1,Vector2(maxproportion,maxproportion)*current_zoom)
	use_anim.track_set_key_value(1,2,Vector2(maxproportion,maxproportion)*current_zoom)
	if $fadeshow.is_playing() == true:
		$fadeshow.advance(4)
	use_anim.track_set_key_value(2,0,$sprites.get_modulate())
	if current_zoom > 1.0:
		use_anim.track_set_key_value(2,1,Color(0.7,0.7,0.7,1.0))
		use_anim.track_set_key_value(2,2,Color(0.85,0.85,0.85,1.0))
	else:
		use_anim.track_set_key_value(2,1,Color(0.85,0.85,0.85,1.0))
		use_anim.track_set_key_value(2,2,Color(1.0,1.0,1.0,1.0))
#	if percentage_vector == Vector2(0.0,0.0):
#		use_anim.track_set_key_value(0,3,Vector2(0,0))
#		var trueproportion = viewportrect/truebasesize
#		var truemaxproportion = max(trueproportion.x,trueproportion.y)
#		use_anim.track_set_key_value(1,3,Vector2(truemaxproportion,truemaxproportion)*current_zoom)
#	else:
#		use_anim.track_set_key_value(0,3,use_anim.track_get_key_value(0,2))
#		use_anim.track_set_key_value(1,3,use_anim.track_get_key_value(1,2))
	$hover.playback_speed = 0.6
	$hover.stop()
	$hover.play("move_to_point")
	$hover.advance(advance_value/$hover.playback_speed)
	last_move_location = percentage_vector

func fix_zoom(alt = false):
	if current_zoom > 1.0:
		current_zoom = 1.0
		if alt == false:
			move_to_location(Vector2(0,0),0,true)
		else:
			move_to_location(Vector2(1,1),0,true)

var passed_go = false
var cum_level = 0
var queued_action = -1
func action(num = 0,_queued = false):
	if queued_action > -1:
		var save_action = queued_action
		queued_action = -1
		action(save_action,true)
	var test_name = "step "+str(num)
	if $fadeshow.has_animation(test_name):
		$fadeshow.advance(10)
		$fadeshow.play(test_name)
	match num:
		2:passed_go = true
		3:
			current_zoom = 1.4
			move_to_location(Vector2(1.0,0.8),0,true)
		4:
			fix_zoom()
		20:$sprites/foxeffect.play("jump")
		21:
			$sprites/foxeffect.play("multishake")
		22:$sprites/foxeffect.play("othershake")
		23:
			$fadeshow.advance(10)
			$fadeshow.play("flashvase")
			$sprites/foxeffect.play("othershake")
		24:#21 + reset
			$sprites/foxeffect.play("multishake")
			if passed_go == true:
				$fadeshow.play("step 1")
				$fadeshow.advance(10)
				fix_zoom()
		30:$sprites/foxeffect.play("fox2shake")
		31:$sprites/foxeffect.play("fox2squish")
		40:
			$fadeshow.advance(10)
			$fadeshow.play("vasedrift")
		41:
			$fadeshow.advance(10)
			$fadeshow.play("vasedrift2")
		42:
			$fadeshow.advance(10)
			$fadeshow.play("vasedrift3")
		43:#20+40 at the same time
			$sprites/foxeffect.play("jump")
			$fadeshow.advance(10)
			$fadeshow.play("vasedrift")
#		-1: pass
#		0:
#			$sprites/Skeleton2D/skeleton_handler.play("RESET")
#			fix_zoom()
#			$fadeshow.play("RESET")
#			$fadeshow.advance(10)
#			$fadeshow.play("appear environment")
#			engage_hover()
#			$sprites/environment/background/dust_handler.play("dust")
#		1:
#			fix_zoom()
#			$spank_handler.advance(12)
#			$sprites/Skeleton2D/skeleton_handler.advance(12)
#			$fadeshow.advance(12)
#			$fadeshow.play("appear rules")
#		99: #unload screen
#			$fadeshow.advance(10)
#			$spank_handler.advance(12)
#			$sprites/Skeleton2D/skeleton_handler.advance(12)
#			$fadeshow.advance(12)
#			fix_zoom(true)
#			$hover.advance(4)
#			$hover.stop(false)
#			$fadeshow.play("unload scene")
#			$fadeshow.play_backwards("appear")
		
#		30:$sprites.modulate = Color(1,1,1,0)
		
#		32:engage_hover()
#		33:engage_shake()
#		60:engage_shake_rules(true)
#		61:engage_shake_rules(false)


func _on_hover_animation_finished(anim_name):
	if anim_name == "newhover":
		$hover.get_animation("newhover").track_set_key_value(0,0,$sprites.position)
		$hover.play("newhover")
#	if anim_name == "move_to_point" and last_move_location == Vector2(0,0):
#		saved_animation_point = 0
#		engage_hover()
	

func _process(_delta):
	$sprites.position = $sprites.position.round()
#	for node in $sprites.get_children():
#		node.position = node.position.round()#Vector2(floor(node.position.x),floor(node.position.y))
