[gd_scene load_steps=6 format=2]

[ext_resource path="res://effects/frustration mark.png" type="Texture" id=1]

[sub_resource type="CanvasItemMaterial" id=5]
particles_animation = true
particles_anim_h_frames = 4
particles_anim_v_frames = 1
particles_anim_loop = true

[sub_resource type="Gradient" id=2]
offsets = PoolRealArray( 0, 0.305263, 0.6, 1 )
colors = PoolColorArray( 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=3]
gradient = SubResource( 2 )

[sub_resource type="ParticlesMaterial" id=6]
flag_disable_z = true
direction = Vector3( -1, -1, 0 )
gravity = Vector3( 0, 0, 0 )
initial_velocity = 50.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
damping = 15.0
angle = 90.0
angle_random = 1.0
color_ramp = SubResource( 3 )
anim_speed = 5.0

[node name="frustration" type="Particles2D"]
material = SubResource( 5 )
emitting = false
amount = 3
lifetime = 4.5
process_material = SubResource( 6 )
texture = ExtResource( 1 )
