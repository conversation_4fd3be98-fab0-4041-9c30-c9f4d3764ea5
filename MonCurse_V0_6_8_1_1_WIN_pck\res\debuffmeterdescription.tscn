[gd_scene load_steps=4 format=2]

[ext_resource path="res://Assets/ui/debuffdescriptionbox.png" type="Texture" id=1]
[ext_resource path="res://font/OpenSans-SemiBold.ttf" type="DynamicFontData" id=2]

[sub_resource type="DynamicFont" id=1]
size = 14
outline_size = 2
outline_color = Color( 0.109804, 0.105882, 0.105882, 1 )
font_data = ExtResource( 2 )

[node name="vbox" type="VBoxContainer"]
custom_constants/separation = 0
alignment = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="TextureRect" type="TextureRect" parent="."]
margin_right = 384.0
margin_bottom = 64.0
texture = ExtResource( 1 )
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label" type="Label" parent="TextureRect"]
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = 4.0
margin_top = 4.0
margin_right = -4.0
margin_bottom = -4.0
custom_colors/font_color = Color( 0.815686, 0.815686, 0.815686, 1 )
custom_fonts/font = SubResource( 1 )
text = "Debuff placeholder text. If you see this, something is going wrong. better do something about that huh."
align = 1
valign = 1
autowrap = true
clip_text = true
__meta__ = {
"_edit_use_anchors_": false
}
