[gd_scene load_steps=3 format=2]

[ext_resource path="res://font/Audiowide-Regular.ttf" type="DynamicFontData" id=1]

[sub_resource type="DynamicFont" id=1]
size = 60
outline_size = 2
outline_color = Color( 0, 0, 0, 0.627451 )
font_data = ExtResource( 1 )

[node name="enemydebuff" type="TextureRect"]
grow_horizontal = 2
grow_vertical = 2
stretch_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label" type="Label" parent="."]
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 0.917647, 0.721569, 0.819608, 1 )
custom_fonts/font = SubResource( 1 )
text = "5"
align = 1
valign = 1
