[gd_scene load_steps=10 format=2]

[ext_resource path="res://font/simonetta/Simonetta-Regular.ttf" type="DynamicFontData" id=1]
[ext_resource path="res://Assets/ui/barfill.png" type="Texture" id=2]
[ext_resource path="res://Assets/ui/invcorbaralt.png" type="Texture" id=3]
[ext_resource path="res://Settingscreen.gd" type="Script" id=4]
[ext_resource path="res://Assets/ui/qadesbutton.png" type="Texture" id=5]
[ext_resource path="res://Assets/ui/qadesclosebutton.png" type="Texture" id=6]
[ext_resource path="res://font/OpenSans-SemiBold.ttf" type="DynamicFontData" id=7]

[sub_resource type="DynamicFont" id=1]
size = 32
outline_size = 2
outline_color = Color( 1, 1, 1, 0.392157 )
font_data = ExtResource( 1 )

[sub_resource type="DynamicFont" id=2]
outline_size = 2
outline_color = Color( 0, 0, 0, 0.666667 )
font_data = ExtResource( 7 )

[node name="Settingscreen" type="Control"]
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = 0.280001
margin_right = 0.320068
margin_bottom = -20.0
mouse_filter = 2
script = ExtResource( 4 )

[node name="Scrollcontainer" type="ScrollContainer" parent="."]
anchor_left = 0.05
anchor_top = 0.1
anchor_right = 0.95
anchor_bottom = 0.95
margin_top = 20.0
mouse_filter = 1
scroll_horizontal_enabled = false

[node name="settings" type="VBoxContainer" parent="Scrollcontainer"]
margin_right = 909.636
margin_bottom = 527.0
grow_horizontal = 2
size_flags_horizontal = 3
custom_constants/separation = 10
alignment = 1

[node name="fcontrol" type="HBoxContainer" parent="Scrollcontainer/settings"]
visible = false
margin_right = 909.0
margin_bottom = 120.0
rect_min_size = Vector2( 0, 120 )
alignment = 1

[node name="forfeit" type="TextureButton" parent="Scrollcontainer/settings/fcontrol"]
margin_left = 329.0
margin_right = 579.0
margin_bottom = 120.0
grow_horizontal = 2
rect_min_size = Vector2( 250, 120 )
texture_normal = ExtResource( 5 )
expand = true
stretch_mode = 5

[node name="mainmenu" type="Label" parent="Scrollcontainer/settings/fcontrol/forfeit"]
anchor_left = -0.5
anchor_right = 1.5
anchor_bottom = 1.0
margin_left = 20.0
margin_right = -20.0
custom_colors/font_color = Color( 0.117647, 0.117647, 0.0196078, 1 )
custom_fonts/font = SubResource( 1 )
text = "Forfeit &
Return"
align = 1
valign = 1
autowrap = true

[node name="doubleclickerf" type="Timer" parent="Scrollcontainer/settings/fcontrol/forfeit"]
one_shot = true

[node name="qcontrol" type="HBoxContainer" parent="Scrollcontainer/settings"]
margin_right = 909.0
margin_bottom = 120.0
rect_min_size = Vector2( 0, 120 )
alignment = 1

[node name="quittomainmenu" type="TextureButton" parent="Scrollcontainer/settings/qcontrol"]
margin_left = 329.0
margin_right = 579.0
margin_bottom = 120.0
grow_horizontal = 2
rect_min_size = Vector2( 250, 120 )
texture_normal = ExtResource( 5 )
expand = true
stretch_mode = 5

[node name="mainmenu" type="Label" parent="Scrollcontainer/settings/qcontrol/quittomainmenu"]
anchor_left = -0.5
anchor_right = 1.5
anchor_bottom = 1.0
margin_left = 20.0
margin_right = -20.0
custom_colors/font_color = Color( 0.117647, 0.117647, 0.0196078, 1 )
custom_fonts/font = SubResource( 1 )
text = "Quit to
 Main Menu"
align = 1
valign = 1
autowrap = true

[node name="doubleclicker2" type="Timer" parent="Scrollcontainer/settings/qcontrol/quittomainmenu"]
one_shot = true

[node name="cen0" type="CenterContainer" parent="Scrollcontainer/settings"]
margin_top = 130.0
margin_right = 909.0
margin_bottom = 231.0
grow_horizontal = 2
rect_min_size = Vector2( 512, 64 )
mouse_filter = 1

[node name="VBoxContainer" type="VBoxContainer" parent="Scrollcontainer/settings/cen0"]
margin_left = 299.0
margin_right = 609.0
margin_bottom = 101.0

[node name="CheckBox6" type="CheckBox" parent="Scrollcontainer/settings/cen0/VBoxContainer"]
margin_right = 310.0
margin_bottom = 31.0
custom_colors/font_color = Color( 0.792157, 0.72549, 0.792157, 1 )
custom_colors/font_color_pressed = Color( 0.964706, 0.909804, 0.988235, 1 )
custom_fonts/font = SubResource( 2 )
text = "Fullscreen (F11)"

[node name="CheckBox5" type="CheckBox" parent="Scrollcontainer/settings/cen0/VBoxContainer"]
visible = false
margin_top = 35.0
margin_right = 310.0
margin_bottom = 66.0
custom_colors/font_color = Color( 0.792157, 0.72549, 0.792157, 1 )
custom_colors/font_color_pressed = Color( 0.964706, 0.909804, 0.988235, 1 )
custom_fonts/font = SubResource( 2 )
text = "Always Reduce Lower GUI Height"

[node name="CheckBox4" type="CheckBox" parent="Scrollcontainer/settings/cen0/VBoxContainer"]
visible = false
margin_top = 35.0
margin_right = 310.0
margin_bottom = 66.0
custom_colors/font_color = Color( 0.792157, 0.72549, 0.792157, 1 )
custom_colors/font_color_pressed = Color( 0.964706, 0.909804, 0.988235, 1 )
custom_fonts/font = SubResource( 2 )
text = "Reduced Move Selector Opacity"

[node name="CheckBox3" type="CheckBox" parent="Scrollcontainer/settings/cen0/VBoxContainer"]
visible = false
margin_top = 35.0
margin_right = 310.0
margin_bottom = 66.0
custom_colors/font_color = Color( 0.792157, 0.72549, 0.792157, 1 )
custom_colors/font_color_pressed = Color( 0.964706, 0.909804, 0.988235, 1 )
custom_fonts/font = SubResource( 2 )
text = "Show Item Descriptions On Hover"
__meta__ = {
"_edit_use_anchors_": false
}

[node name="CheckBox8" type="CheckBox" parent="Scrollcontainer/settings/cen0/VBoxContainer"]
margin_top = 35.0
margin_right = 310.0
margin_bottom = 66.0
custom_colors/font_color = Color( 0.792157, 0.72549, 0.792157, 1 )
custom_colors/font_color_pressed = Color( 0.964706, 0.909804, 0.988235, 1 )
custom_fonts/font = SubResource( 2 )
text = "Faster Turns"
__meta__ = {
"_edit_use_anchors_": false
}

[node name="CheckBox9" type="CheckBox" parent="Scrollcontainer/settings/cen0/VBoxContainer"]
visible = false
margin_top = 70.0
margin_right = 310.0
margin_bottom = 101.0
custom_colors/font_color = Color( 0.792157, 0.72549, 0.792157, 1 )
custom_colors/font_color_pressed = Color( 0.964706, 0.909804, 0.988235, 1 )
custom_fonts/font = SubResource( 2 )
text = "Power Saver Mode (Low-Res)"
__meta__ = {
"_edit_use_anchors_": false
}

[node name="CheckBox10" type="CheckBox" parent="Scrollcontainer/settings/cen0/VBoxContainer"]
visible = false
margin_top = 70.0
margin_right = 310.0
margin_bottom = 101.0
custom_colors/font_color = Color( 0.792157, 0.72549, 0.792157, 1 )
custom_colors/font_color_pressed = Color( 0.964706, 0.909804, 0.988235, 1 )
custom_fonts/font = SubResource( 2 )
text = "Low RAM Compatibility Mode"
__meta__ = {
"_edit_use_anchors_": false
}

[node name="CheckBox7" type="CheckBox" parent="Scrollcontainer/settings/cen0/VBoxContainer"]
visible = false
margin_top = 70.0
margin_right = 310.0
margin_bottom = 101.0
custom_colors/font_color = Color( 0.792157, 0.72549, 0.792157, 1 )
custom_colors/font_color_pressed = Color( 0.964706, 0.909804, 0.988235, 1 )
custom_fonts/font = SubResource( 2 )
text = "Allow NSFW/Player Consent"

[node name="CheckBox2" type="CheckBox" parent="Scrollcontainer/settings/cen0/VBoxContainer"]
margin_top = 70.0
margin_right = 310.0
margin_bottom = 101.0
custom_colors/font_color = Color( 0.792157, 0.72549, 0.792157, 1 )
custom_colors/font_color_pressed = Color( 0.964706, 0.909804, 0.988235, 1 )
custom_fonts/font = SubResource( 2 )
pressed = true
text = "Camera Automatically Follows Player"
__meta__ = {
"_edit_use_anchors_": false
}

[node name="CheckBox1" type="CheckBox" parent="Scrollcontainer/settings/cen0/VBoxContainer"]
visible = false
margin_top = 105.0
margin_right = 310.0
margin_bottom = 136.0
custom_colors/font_color = Color( 0.792157, 0.72549, 0.792157, 1 )
custom_colors/font_color_pressed = Color( 0.964706, 0.909804, 0.988235, 1 )
custom_fonts/font = SubResource( 2 )
pressed = true
text = "Indicators Show Above Interactibles"

[node name="cen1" type="CenterContainer" parent="Scrollcontainer/settings"]
margin_top = 241.0
margin_right = 909.0
margin_bottom = 305.0
grow_horizontal = 2
rect_min_size = Vector2( 512, 64 )
mouse_filter = 1

[node name="Mastervolume" type="TextureProgress" parent="Scrollcontainer/settings/cen1"]
margin_left = 254.0
margin_right = 654.0
margin_bottom = 64.0
grow_horizontal = 2
rect_min_size = Vector2( 400, 0 )
value = 1.0
allow_greater = true
allow_lesser = true
texture_under = ExtResource( 2 )
texture_over = ExtResource( 3 )
texture_progress = ExtResource( 2 )
tint_progress = Color( 0.54902, 0.392157, 0.392157, 1 )
nine_patch_stretch = true
stretch_margin_left = 68
stretch_margin_top = 32
stretch_margin_right = 68
stretch_margin_bottom = 32

[node name="volumelabel" type="Label" parent="Scrollcontainer/settings/cen1/Mastervolume"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_colors/font_color = Color( 0, 0, 0, 1 )
custom_fonts/font = SubResource( 1 )
text = "Master Volume: 100%"
align = 1
valign = 1
autowrap = true

[node name="cen2" type="CenterContainer" parent="Scrollcontainer/settings"]
margin_top = 315.0
margin_right = 909.0
margin_bottom = 379.0
grow_horizontal = 2
rect_min_size = Vector2( 512, 64 )
mouse_filter = 1

[node name="Musicvolume" type="TextureProgress" parent="Scrollcontainer/settings/cen2"]
margin_left = 254.0
margin_right = 654.0
margin_bottom = 64.0
grow_horizontal = 2
rect_min_size = Vector2( 400, 0 )
value = 1.0
allow_greater = true
allow_lesser = true
texture_under = ExtResource( 2 )
texture_over = ExtResource( 3 )
texture_progress = ExtResource( 2 )
tint_progress = Color( 0.392157, 0.392157, 0.54902, 1 )
nine_patch_stretch = true
stretch_margin_left = 68
stretch_margin_top = 32
stretch_margin_right = 68
stretch_margin_bottom = 32

[node name="volumelabel" type="Label" parent="Scrollcontainer/settings/cen2/Musicvolume"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_colors/font_color = Color( 0, 0, 0, 1 )
custom_fonts/font = SubResource( 1 )
text = "Music Volume: 100%"
align = 1
valign = 1
autowrap = true
__meta__ = {
"_edit_use_anchors_": false
}

[node name="cen3" type="CenterContainer" parent="Scrollcontainer/settings"]
margin_top = 389.0
margin_right = 909.0
margin_bottom = 453.0
grow_horizontal = 2
rect_min_size = Vector2( 512, 64 )
mouse_filter = 1

[node name="SFXvolume" type="TextureProgress" parent="Scrollcontainer/settings/cen3"]
margin_left = 254.0
margin_right = 654.0
margin_bottom = 64.0
grow_horizontal = 2
rect_min_size = Vector2( 400, 0 )
value = 1.0
allow_greater = true
allow_lesser = true
texture_under = ExtResource( 2 )
texture_over = ExtResource( 3 )
texture_progress = ExtResource( 2 )
tint_progress = Color( 0.392157, 0.54902, 0.392157, 1 )
nine_patch_stretch = true
stretch_margin_left = 68
stretch_margin_top = 32
stretch_margin_right = 68
stretch_margin_bottom = 32

[node name="volumelabel" type="Label" parent="Scrollcontainer/settings/cen3/SFXvolume"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_colors/font_color = Color( 0, 0, 0, 1 )
custom_fonts/font = SubResource( 1 )
text = "SFX Volume: 100%"
align = 1
valign = 1
autowrap = true
__meta__ = {
"_edit_use_anchors_": false
}

[node name="cen4" type="CenterContainer" parent="Scrollcontainer/settings"]
margin_top = 463.0
margin_right = 909.0
margin_bottom = 527.0
grow_horizontal = 2
rect_min_size = Vector2( 512, 64 )
mouse_filter = 1

[node name="Ambiencevolume" type="TextureProgress" parent="Scrollcontainer/settings/cen4"]
margin_left = 254.0
margin_right = 654.0
margin_bottom = 64.0
grow_horizontal = 2
rect_min_size = Vector2( 400, 0 )
value = 1.0
allow_greater = true
allow_lesser = true
texture_under = ExtResource( 2 )
texture_over = ExtResource( 3 )
texture_progress = ExtResource( 2 )
tint_progress = Color( 0.392157, 0.431373, 0.392157, 1 )
nine_patch_stretch = true
stretch_margin_left = 68
stretch_margin_top = 32
stretch_margin_right = 68
stretch_margin_bottom = 32

[node name="volumelabel" type="Label" parent="Scrollcontainer/settings/cen4/Ambiencevolume"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_colors/font_color = Color( 0, 0, 0, 1 )
custom_fonts/font = SubResource( 1 )
text = "Ambience Volume: 100%"
align = 1
valign = 1
autowrap = true

[node name="CloseButton" type="TextureButton" parent="."]
anchor_left = 0.95
anchor_top = -0.11
anchor_right = 1.05
anchor_bottom = 0.07
grow_horizontal = 2
grow_vertical = 2
texture_normal = ExtResource( 6 )
expand = true

[connection signal="pressed" from="Scrollcontainer/settings/fcontrol/forfeit" to="." method="_on_forfeit_pressed"]
[connection signal="timeout" from="Scrollcontainer/settings/fcontrol/forfeit/doubleclickerf" to="." method="_on_doubleclickerf_timeout"]
[connection signal="pressed" from="Scrollcontainer/settings/qcontrol/quittomainmenu" to="." method="_on_quittomainmenu_pressed"]
[connection signal="timeout" from="Scrollcontainer/settings/qcontrol/quittomainmenu/doubleclicker2" to="." method="_on_doubleclicker2_timeout"]
[connection signal="toggled" from="Scrollcontainer/settings/cen0/VBoxContainer/CheckBox6" to="." method="_on_CheckBox6_toggled"]
[connection signal="toggled" from="Scrollcontainer/settings/cen0/VBoxContainer/CheckBox5" to="." method="_on_CheckBox5_toggled"]
[connection signal="toggled" from="Scrollcontainer/settings/cen0/VBoxContainer/CheckBox4" to="." method="_on_CheckBox4_toggled"]
[connection signal="toggled" from="Scrollcontainer/settings/cen0/VBoxContainer/CheckBox3" to="." method="_on_CheckBox3_toggled"]
[connection signal="toggled" from="Scrollcontainer/settings/cen0/VBoxContainer/CheckBox8" to="." method="_on_CheckBox8_toggled"]
[connection signal="toggled" from="Scrollcontainer/settings/cen0/VBoxContainer/CheckBox9" to="." method="_on_CheckBox9_toggled"]
[connection signal="toggled" from="Scrollcontainer/settings/cen0/VBoxContainer/CheckBox10" to="." method="_on_CheckBox10_toggled"]
[connection signal="toggled" from="Scrollcontainer/settings/cen0/VBoxContainer/CheckBox7" to="." method="_on_CheckBox7_toggled"]
[connection signal="toggled" from="Scrollcontainer/settings/cen0/VBoxContainer/CheckBox2" to="." method="_on_CheckBox2_toggled"]
[connection signal="toggled" from="Scrollcontainer/settings/cen0/VBoxContainer/CheckBox1" to="." method="_on_CheckBox1_toggled"]
[connection signal="gui_input" from="Scrollcontainer/settings/cen1/Mastervolume" to="." method="_on_Mastervolume_gui_input"]
[connection signal="gui_input" from="Scrollcontainer/settings/cen2/Musicvolume" to="." method="_on_Musicvolume_gui_input"]
[connection signal="gui_input" from="Scrollcontainer/settings/cen3/SFXvolume" to="." method="_on_SFXvolume_gui_input"]
[connection signal="gui_input" from="Scrollcontainer/settings/cen4/Ambiencevolume" to="." method="_on_Ambiencevolume_gui_input"]
[connection signal="pressed" from="CloseButton" to="." method="_on_CloseButton_pressed"]
