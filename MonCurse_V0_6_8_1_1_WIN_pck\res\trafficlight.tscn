[gd_scene load_steps=6 format=2]

[ext_resource path="res://Background/trafficlightred.png" type="Texture" id=1]
[ext_resource path="res://Background/trafficlightyellow.png" type="Texture" id=2]
[ext_resource path="res://Background/trafficlightgreen.png" type="Texture" id=3]
[ext_resource path="res://trafficlight.gd" type="Script" id=4]

[sub_resource type="SpriteFrames" id=1]
animations = [ {
"frames": [ ExtResource( 3 ), ExtResource( 1 ), ExtResource( 2 ) ],
"loop": true,
"name": "default",
"speed": 5.0
} ]

[node name="trafficlight" type="AnimatedSprite"]
z_index = 12
frames = SubResource( 1 )
offset = Vector2( 0, -128 )
script = ExtResource( 4 )

[node name="enemyturn" type="Timer" parent="."]
one_shot = true
