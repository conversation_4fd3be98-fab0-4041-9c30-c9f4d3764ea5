extends Control


var disablecheckboxes = true
var ismainscene = false

var masterstring = "Master Volume: "
var musicstring =  "Music Volume: "
var sfxstring = "SFX Volume: "
var ambiencestring =  "Ambience Volume: "
func _ready():
	if Playervariables.modarray[Playervariables.mods.TRANSLATION] == true:
		for nodekey in Playervariables.settingstextdict:
			if get_node_or_null(nodekey) != null:
				get_node(nodekey).set_text(Playervariables.settingstextdict[nodekey])
		masterstring = $Scrollcontainer/settings/cen1/Mastervolume/volumelabel.get_text()
		musicstring = $Scrollcontainer/settings/cen2/Musicvolume/volumelabel.get_text()
		sfxstring = $Scrollcontainer/settings/cen3/SFXvolume/volumelabel.get_text()
		ambiencestring = $Scrollcontainer/settings/cen4/Ambiencevolume/volumelabel.get_text()
		if Playervariables.default_font_dict["font_speech_choices"] != null:
			$Scrollcontainer/settings/qcontrol/quittomainmenu/mainmenu.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_speech_choices"].font_data
		if Playervariables.default_font_dict["font_attack_announce"] != null:
			$Scrollcontainer/settings/cen0/VBoxContainer/CheckBox6.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_announce"].font_data
	add_to_group("fullscreencheck")
	if Playervariables.touchscreenmode == true:
#		$Scrollcontainer/settings/cen0/VBoxContainer/CheckBox9.visible = true
		$Scrollcontainer/settings/cen0/VBoxContainer/CheckBox6.visible = false
		if get_parent().get_parent().get_name() == "Mainmenu":
			$Scrollcontainer/settings/cen0/VBoxContainer/CheckBox10.visible = true
	else:
#		$Scrollcontainer/settings/cen0/VBoxContainer/CheckBox9.visible = false
		$Scrollcontainer/settings/cen0/VBoxContainer/CheckBox6.visible = true
	var _c = get_tree().root.connect("size_changed", self, "_on_viewport_size_changed")
	_on_viewport_size_changed()
func _fullscreencheckboxcheck():
	$Scrollcontainer/settings/cen0/VBoxContainer/CheckBox6.pressed = Playervariables.fullscreen
	$Scrollcontainer/settings/cen0/VBoxContainer/CheckBox9.pressed = Playervariables.batterysaver

const minimumscreenratio = 1.3
var recentsizechange = false
var firstrun = true
func _on_viewport_size_changed():
	if recentsizechange == false:
		recentsizechange = true #this makes it so it only updates the viewport every 4 frames
		if firstrun == true:
			firstrun = false
		else:
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
		var screenratiox = (clamp(get_viewport_rect().size.x,1,100000)/Playervariables.basescreensize.x)
		var screenratioy = (clamp(get_viewport_rect().size.y,1,100000)/Playervariables.basescreensize.y)
		var dampenedratiox = (1+clamp(screenratiox,0.8,9999))/2
		var scrollbarsize = 30*screenratiox
		$Scrollcontainer.get_v_scrollbar().rect_min_size.x = clamp(scrollbarsize,30,9000)
		$Scrollcontainer.get_v_scrollbar().rect_position.x = $Scrollcontainer.rect_size.x -scrollbarsize
		var checkboxdampenedx = dampenedratiox
		if Playervariables.default_font_dict["font_attack_announce"] != null:
			checkboxdampenedx = dampenedratiox*Playervariables.default_font_dict["font_attack_announce_size"]
		$Scrollcontainer/settings/cen0/VBoxContainer/CheckBox6.get("custom_fonts/font").size = 16*checkboxdampenedx
		$Scrollcontainer/settings/cen0/VBoxContainer/CheckBox6.get("custom_fonts/font").outline_size = ceil(1.8*checkboxdampenedx)
		$Scrollcontainer/settings/fcontrol.rect_min_size.y = 120*dampenedratiox
		$Scrollcontainer/settings/fcontrol/forfeit.rect_min_size.x = 250*dampenedratiox
		$Scrollcontainer/settings/qcontrol.rect_min_size.y = 120*dampenedratiox
		$Scrollcontainer/settings/qcontrol/quittomainmenu.rect_min_size.x = 250*dampenedratiox
		var fontdampenedx = dampenedratiox
		if Playervariables.default_font_dict["font_speech_choices"] != null:
			fontdampenedx = dampenedratiox*Playervariables.default_font_dict["font_speech_choices_size"]
		$Scrollcontainer/settings/qcontrol/quittomainmenu/mainmenu.get("custom_fonts/font").size = 32*fontdampenedx
		$Scrollcontainer/settings/qcontrol/quittomainmenu/mainmenu.get("custom_fonts/font").outline_size = ceil(1.8*fontdampenedx)
		var volumesize = Vector2(clamp(400*dampenedratiox,300,9999),clamp(64*((2+screenratioy)/3),50,9999))
		$Scrollcontainer/settings/cen1/Mastervolume.rect_min_size = volumesize
		$Scrollcontainer/settings/cen2/Musicvolume.rect_min_size = volumesize
		$Scrollcontainer/settings/cen3/SFXvolume.rect_min_size = volumesize
		$Scrollcontainer/settings/cen4/Ambiencevolume.rect_min_size = volumesize
#		print(screenratiox)
#		if screenratiox > minimumscreenratio:
#			var scalechange = (1+(screenratiox-(minimumscreenratio-1))) * 0.5
#			print(scalechange)
##			for item in $Scrollcontainer/settings.get_children():
#			$Scrollcontainer/settings.rect_scale = Vector2(scalechange,scalechange)
#		else:
##			for item in $Scrollcontainer/settings.get_children():
#			$Scrollcontainer/settings.rect_scale = Vector2(1,1)
		recentsizechange = false

func load_settings():
	disablecheckboxes = true
#	if Playervariables.gameslowness >= Playervariables.gameslownessdefault:
	$Scrollcontainer/settings/cen0/VBoxContainer/CheckBox8.pressed = (Playervariables.gameslowness < Playervariables.gameslownessdefault)
#	else:
#		$Scrollcontainer/settings/cen0/VBoxContainer/CheckBox8.pressed = true
	$Scrollcontainer/settings/cen1/Mastervolume/volumelabel.set_text(masterstring+str(Playervariables.Mastervolume)+"%")
	$Scrollcontainer/settings/cen2/Musicvolume/volumelabel.set_text(musicstring+str(Playervariables.Musicvolume)+"%")
	$Scrollcontainer/settings/cen3/SFXvolume/volumelabel.set_text(sfxstring+str(Playervariables.SFXvolume)+"%")
	$Scrollcontainer/settings/cen4/Ambiencevolume/volumelabel.set_text(ambiencestring+str(Playervariables.Ambiencevolume)+"%")
	$Scrollcontainer/settings/cen1/Mastervolume.value = Playervariables.Mastervolume
	$Scrollcontainer/settings/cen2/Musicvolume.value = Playervariables.Musicvolume
	$Scrollcontainer/settings/cen3/SFXvolume.value = Playervariables.SFXvolume
	$Scrollcontainer/settings/cen4/Ambiencevolume.value = Playervariables.Ambiencevolume
	$Scrollcontainer/settings/cen0/VBoxContainer/CheckBox10.pressed = (Playervariables.lastloadedvariables == -2)
	$Scrollcontainer/settings/cen0/VBoxContainer/CheckBox9.pressed = Playervariables.batterysaver
	$Scrollcontainer/settings/cen0/VBoxContainer/CheckBox7.pressed = Playervariables.consent
	$Scrollcontainer/settings/cen0/VBoxContainer/CheckBox6.pressed = Playervariables.fullscreen
	$Scrollcontainer/settings/cen0/VBoxContainer/CheckBox5.pressed = Playervariables.reducelowergui
#	$Scrollcontainer/settings/cen0/VBoxContainer/CheckBox4.pressed = Playervariables.transparentselectors
	$Scrollcontainer/settings/cen0/VBoxContainer/CheckBox3.pressed = Playervariables.descriptions
	$Scrollcontainer/settings/cen0/VBoxContainer/CheckBox2.pressed = Playervariables.camerafollow 
	$Scrollcontainer/settings/cen0/VBoxContainer/CheckBox1.pressed = Playervariables.indicators
	disablecheckboxes = false

func _on_quittomainmenu_pressed():
	if $Scrollcontainer/settings/qcontrol/quittomainmenu/doubleclicker2.is_stopped():
		$Scrollcontainer/settings/qcontrol/quittomainmenu/doubleclicker2.start(3)
		$Scrollcontainer/settings/qcontrol/quittomainmenu.set_modulate(Color(0.3,0.8,0.8))
		get_node("/root/Master/SFX/uif").play()
	else:
		get_node("/root/Master/SFX/ui").play()
		get_node("/root/Master").call_deferred("master_event",2) #WATCH OUT! I don't know if this can cause games to lag by the fact that it never gets to finish MAIN's script

func _on_doubleclicker2_timeout():
	$Scrollcontainer/settings/qcontrol/quittomainmenu.set_modulate(Color(1,1,1))

func _input(event):
	if dragging > 0 and event is InputEventMouseMotion:
		adjustvolume()

var dragging = 0
func _on_Mastervolume_gui_input(event):
	pass # Replace with function body.
	if event.is_action_pressed("ui_click") or (event is InputEventScreenTouch and event.pressed == true):
		dragging = 1
		adjustvolume()
	if event.is_action_released("ui_click") or (event is InputEventScreenTouch and event.pressed == false):
		dragging = 0
		Playervariables.savevalue("Volume","Master",Playervariables.Mastervolume)
func _on_Musicvolume_gui_input(event):
	if event.is_action_pressed("ui_click") or (event is InputEventScreenTouch and event.pressed == true):
		dragging = 2
		adjustvolume()
	if event.is_action_released("ui_click") or (event is InputEventScreenTouch and event.pressed == false):
		dragging = 0
		Playervariables.savevalue("Volume","Music",Playervariables.Musicvolume)
func _on_SFXvolume_gui_input(event):
	if event.is_action_pressed("ui_click") or (event is InputEventScreenTouch and event.pressed == true):
		dragging = 3
		adjustvolume()
	if event.is_action_released("ui_click") or (event is InputEventScreenTouch and event.pressed == false):
		dragging = 0
		Playervariables.savevalue("Volume","SFX",Playervariables.SFXvolume)
func _on_Ambiencevolume_gui_input(event):
	if event.is_action_pressed("ui_click") or (event is InputEventScreenTouch and event.pressed == true):
		dragging = 4
		adjustvolume()
	if event.is_action_released("ui_click") or (event is InputEventScreenTouch and event.pressed == false):
		dragging = 0
		Playervariables.savevalue("Volume","Ambience",Playervariables.Ambiencevolume)
func adjustvolume():
	match dragging:
		1:
			Playervariables.Mastervolume = stepify(clamp(100*(get_viewport().get_mouse_position().x - $Scrollcontainer/settings/cen1/Mastervolume.get_global_transform_with_canvas().origin.x)/($Scrollcontainer/settings/cen1/Mastervolume.rect_size.x),0,100),5)
			$Scrollcontainer/settings/cen1/Mastervolume/volumelabel.set_text(masterstring+str(Playervariables.Mastervolume)+"%")
			$Scrollcontainer/settings/cen1/Mastervolume.value = Playervariables.Mastervolume
			AudioServer.set_bus_volume_db(Playervariables.Masterbus,linear2db(Playervariables.Mastervolume/100))
		2: 
			Playervariables.Musicvolume = stepify(clamp(100*(get_viewport().get_mouse_position().x - $Scrollcontainer/settings/cen2/Musicvolume.get_global_transform_with_canvas().origin.x)/($Scrollcontainer/settings/cen2/Musicvolume.rect_size.x),0,100),5)
			$Scrollcontainer/settings/cen2/Musicvolume/volumelabel.set_text(musicstring+str(Playervariables.Musicvolume)+"%")
			$Scrollcontainer/settings/cen2/Musicvolume.value = Playervariables.Musicvolume
			AudioServer.set_bus_volume_db(Playervariables.Musicbus,linear2db(Playervariables.Musicvolume/100))
		3: 
			Playervariables.SFXvolume = stepify(clamp(100*(get_viewport().get_mouse_position().x - $Scrollcontainer/settings/cen3/SFXvolume.get_global_transform_with_canvas().origin.x)/($Scrollcontainer/settings/cen3/SFXvolume.rect_size.x),0,100),5)
			$Scrollcontainer/settings/cen3/SFXvolume/volumelabel.set_text(sfxstring+str(Playervariables.SFXvolume)+"%")
			$Scrollcontainer/settings/cen3/SFXvolume.value = Playervariables.SFXvolume
			AudioServer.set_bus_volume_db(Playervariables.SFXbus,linear2db(Playervariables.SFXvolume/100))
		4: 
			Playervariables.Ambiencevolume = stepify(clamp(100*(get_viewport().get_mouse_position().x - $Scrollcontainer/settings/cen4/Ambiencevolume.get_global_transform_with_canvas().origin.x)/($Scrollcontainer/settings/cen4/Ambiencevolume.rect_size.x),0,100),5)
			$Scrollcontainer/settings/cen4/Ambiencevolume/volumelabel.set_text(ambiencestring+str(Playervariables.Ambiencevolume)+"%")
			$Scrollcontainer/settings/cen4/Ambiencevolume.value = Playervariables.Ambiencevolume
			AudioServer.set_bus_volume_db(Playervariables.Ambiencebus,linear2db(Playervariables.Ambiencevolume/100))

var queue_checkbox_10 = false
func _on_CheckBox10_toggled(button_pressed):
	if disablecheckboxes == false:
		if button_pressed == true:
			Playervariables.lastloadedvariables = -2
		else:
			Playervariables.lastloadedvariables = 999
		queue_checkbox_10 = true
		Playervariables.savevalue("Crash","Loadedvariables",Playervariables.lastloadedvariables)


func _on_CheckBox9_toggled(button_pressed):
	if disablecheckboxes == false:
#		Playervariables.savevalue("Checkboxes","Batterysave",button_pressed)
#		Playervariables.batterysaver = button_pressed
		Playervariables.set_battery_saver_size(button_pressed)
#		if Playervariables.fullscreen == true:
#			get_node("/root/Master").call("fullscreentest")
func _on_CheckBox2_toggled(button_pressed):
	if disablecheckboxes == false:
		Playervariables.savevalue("Checkboxes","Camerafollow",button_pressed)
		Playervariables.camerafollow = button_pressed
func _on_CheckBox1_toggled(button_pressed):
	if disablecheckboxes == false:
		Playervariables.savevalue("Checkboxes","Indicators",button_pressed)
		Playervariables.indicators = button_pressed
		if ismainscene == true:
			get_parent().get_parent().get_parent().mainscene.get_node("indicators").visible = button_pressed
func _on_CheckBox3_toggled(button_pressed):
	if disablecheckboxes == false:
		Playervariables.savevalue("Checkboxes","Descriptions",button_pressed)
		Playervariables.descriptions = button_pressed
		for deployable in $Scrollcontainer/deployablecontainer/deploy.get_children():
			if Playervariables.descriptions == true:
				deployable.get_node("VBoxContainer/description3").visible = true
			else:
				deployable.get_node("VBoxContainer/description3").visible = false
func _on_CheckBox8_toggled(button_pressed):
	if button_pressed == true:
		Playervariables.gameslowness = 0
	else:
		Playervariables.gameslowness = Playervariables.gameslownessdefault
	Playervariables.savevalue("Checkboxes","Fastmode",button_pressed)
#	print(Playervariables.gameslowness)
func _on_CheckBox4_toggled(_button_pressed):
	return
#	if disablecheckboxes == false:
#		Playervariables.savevalue("Checkboxes","Selectors",button_pressed)
#		Playervariables.transparentselectors = button_pressed
#		if ismainscene == true:
#			if button_pressed == true:
#				get_parent().get_parent().get_parent().mainscene.get_node("selectors").set_modulate(Color(1,1,1,0.5))
#			else:
#				get_parent().get_parent().get_parent().mainscene.get_node("selectors").set_modulate(Color(1,1,1,1))
func _on_CheckBox5_toggled(button_pressed):
	if disablecheckboxes == false:
		Playervariables.savevalue("Checkboxes","LowerGUI",button_pressed)
		Playervariables.reducelowergui = button_pressed
		if ismainscene == true:
			if button_pressed == true or get_viewport().size.y < 700:
				get_parent().get_parent().get_parent().get_node("deployablecontainer").margin_top = -60
				get_parent().get_parent().get_parent().get_node("deployablecontainer/healthbox/resleft").margin_top = -50
			else:
				get_parent().get_parent().get_parent().get_node("deployablecontainer").margin_top = -120
				get_parent().get_parent().get_parent().get_node("deployablecontainer/healthbox/resleft").margin_top = 0
				get_parent().get_parent().get_parent().scrollupper(0)
			get_parent().get_parent().get_parent().mainscene.deployablemargintop = get_parent().get_parent().get_parent().get_node("deployablecontainer").margin_top
func _on_CheckBox6_toggled(button_pressed):
	if disablecheckboxes == false:
#		Playervariables.savevalue("Checkboxes","Fullscreen",button_pressed)
		Playervariables.fullscreen = button_pressed
		OS.set_window_fullscreen(button_pressed)
		if Playervariables.fullscreen == true:
			get_node("/root/Master").call("fullscreentest")
func _on_CheckBox7_toggled(button_pressed):
	if disablecheckboxes == false:
		Playervariables.consent = button_pressed
		Playervariables.savevalue("Checkboxes","Consent",button_pressed)
		if ismainscene == true:
			get_parent().get_parent().get_parent().tempwarning(get_parent().get_parent().get_parent().warningCONSENT)
			get_parent().get_parent().get_parent().get_node("ConversationV1").rulestalker.assignplayercolour()#clothes(false)
			get_parent().get_parent().get_parent().mainscene.updateplayersprite()

func _on_CloseButton_pressed():
	get_node("/root/Master/SFX/uib").play()
	if get_parent().get_parent().get_name() == "Mainmenu": #scuffed
		get_parent().get_parent().get_node("veil").visible = false
	else:
		if get_parent().get_parent().get_parent().has_node("Mainmenu"):
			print("Serious error in Settingscreen: It appears there is more than one mainmenu loaded??")
		else:
			get_parent().get_parent().veilblock = false
			get_parent().get_parent().get_node("veil").visible = false
	if queue_checkbox_10 == true:
		if Playervariables.lastloadedvariables == -2:
			get_node("/root/Master").call("unpreload")
		elif Playervariables.lastloadedvariables == 999:
			get_node("/root/Master").call("preload_procedure")
			Playervariables.lastloadedvariables = 999
#	if get_parent().get_name() != "inventory":
	get_parent().visible = false
	queue_free()


func _on_forfeit_pressed():
	if $Scrollcontainer/settings/fcontrol/forfeit/doubleclickerf.is_stopped():
		$Scrollcontainer/settings/fcontrol/forfeit/doubleclickerf.start(3)
		$Scrollcontainer/settings/fcontrol/forfeit.set_modulate(Color(0.3,0.8,0.8))
		get_node("/root/Master/SFX/uif").play()
	else:
		get_node("/root/Master/SFX/ui").play()
		if get_parent().get_parent().get_parent().has_method("forfeit"):
			get_parent().get_parent().get_parent().forfeit()
		else:
			print("Attempted to forfeit when not possible?? settingscreen.gd")
#		get_node("/root/Master").call_deferred("master_event",2) #WATCH OUT! I don't know if this can cause games to lag by the fact that it never gets to finish MAIN's script

#
#func _on_modtools_pressed():
#	if $Scrollcontainer/settings/mcontrol/modtools/doubleclickerm.is_stopped():
#		$Scrollcontainer/settings/mcontrol/modtools/doubleclickerm.start(3)
#		$Scrollcontainer/settings/mcontrol/modtools.set_modulate(Color(0.3,0.8,0.8))
#		get_node("/root/Master/SFX/uif").play()
#	else:
#		get_node("/root/Master/SFX/ui").play()
#		var modmenuscreen = load("res://exportedtools/modtools.tscn").instance()
#		add_child(modmenuscreen)



func _on_doubleclickerf_timeout():
	$Scrollcontainer/settings/fcontrol/forfeit.set_modulate(Color(1,1,1))


func _on_doubleclickerm_timeout():
	$Scrollcontainer/settings/mcontrol/modtools.set_modulate(Color(1,1,1))
