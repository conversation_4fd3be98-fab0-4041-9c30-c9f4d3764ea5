extends CanvasLayer

var from_main = false

func _on_villagebutton_pressed():
	if $backing/appear.is_playing() == false:
		if from_main == true:
			get_parent().call("pause",false)
			get_parent().reward_picker = null
			queue_free()
		else:
			self.visible = false
			for reward in $backing/board/rewardploy.get_children():
				reward.queue_free()
			get_parent().reward_chosen()

#enum chooselabel{IGNORE,ACCEPT,CHOOSEONE,LASTSUNTIL,CLASS,FOXCHOOSETWO}
#var chooselabelsarray = [
#	"Ignore & Discard",
#	"Accept & Continue",
#	"Choose One Skill:",
#	"Lasts until end of mission",
#	"Class Skills:",
#	"Fox-Girl - Choose Two Skills:",
#]

func _ready():
	$backing/board/Label2.set_text(Playervariables.chooselabelsarray[Playervariables.chooselabel.LASTSUNTIL])
	$backing/board/villagebutton/duplicatelabel.set_text(Playervariables.chooselabelsarray[Playervariables.chooselabel.IGNORE])
	$backing/board/acceptbutton/duplicatelabel.set_text(Playervariables.chooselabelsarray[Playervariables.chooselabel.ACCEPT])


func pick_random_rewards(num):
	var chosenarray = []
	var chosencorruption = []
	var usepool = Playervariables.get("LootPool"+str(Playervariables.tier+1)).duplicate()
	if Playervariables.corruptiondict.size() >= 5 or (randf() > 0.5 and Playervariables.corruptiondict.size() >= 3):
		for human_pool_int in Playervariables.HumanPool:
			usepool.erase(human_pool_int)
	var backuppool = {}
	if (Playervariables.tier+1) > 0:
		backuppool = Playervariables.get("LootPool"+str(Playervariables.tier)).duplicate()
	var usedict = Playervariables.get("extrapoolsdict"+str(Playervariables.tier+1))
	var poollength = usepool.size()
#	print("Usepool:"+str(usepool))
#	print("Pool length:"+str(poollength))
	for key in Playervariables.corruptiondict:
		var testkey = key+str(Playervariables.corruptiondict[key])
		if usedict.has(testkey):
			usepool = usepool + usedict[testkey]
		elif backuppool.has(testkey):
			usepool = usepool + backuppool[testkey]
#			print("Usepool:"+str(usepool))
	for _i in range(num):
		var randomvalue = randi()%(usepool.size())
#		print("Random value:"+str(randomvalue)+"... which is:"+str(usepool[randomvalue]))
		chosenarray.append(usepool[randomvalue])
		if randomvalue+1 > poollength:
			chosencorruption.append(true)
		else:
			chosencorruption.append(false)
#	print("Corruptarray:"+str(chosencorruption)+"... and chosenarray:"+str(chosenarray))
	if from_main == false:
		get_parent().bigrewardqueue.append(chosenarray)
		get_parent().bigcorruptionqueue.append(chosencorruption)
	else:
#		$backbuttoncanvas/rewardbutton/open2.play()
		queue_rewards(chosenarray,chosencorruption,REWARD)


var bonusrewards = 0
enum{REWARD=0,STARTING=1}
var currentmode = REWARD
var rewardisarray = []
func queue_rewards(rewardarray,corruptarray,mode=REWARD):
	chosenrewards = 0
	currentmode = mode
	if mode == STARTING:
		$backing/board/acceptbutton.visible = true
		$backing/board/villagebutton.visible = false
		$backing/board/Label2.visible = false
	elif mode == REWARD:
		$backing/board/acceptbutton.visible = false
		$backing/board/villagebutton.visible = true
		$backing/board/Label2.visible = true
	if get_node("/root/Master").call("mainscene_check") == false:
		self.visible = false
		return
	for rewardnum in rewardarray.size():
		var movename = "Move"+str(rewardarray[rewardnum])
		if movename in Playervariables:
			var movedict = Playervariables.get(movename)
			var newdeploy = load(Mainpreload.Deployables).instance()
			$backing/board/rewardploy.add_child(newdeploy)
			if movedict.has("slot") and movedict["slot"] == Playervariables.CONSUMABLE:
				newdeploy.call("_identify",[rewardarray[rewardnum],0,movedict["slot"],10],true,false)
			else:
				newdeploy.call("_identify",[rewardarray[rewardnum],0,Playervariables.ITEM],true,false)
			var deployposition = (newdeploy.rect_size)/2
			newdeploy.rect_pivot_offset = deployposition
			newdeploy.set_v_grow_direction(2)
			if corruptarray[rewardnum] == true:
				var smokefx = load(Mainpreload.CorruptionSmoke).instance()
				newdeploy.add_child(smokefx)
				smokefx.position = deployposition
				smokefx.play("smoke")
				var corrupttext = load(Mainpreload.CorruptedText).instance()
				newdeploy.add_child(corrupttext)
				corrupttext.rect_position = deployposition + Vector2(-corrupttext.rect_size.x*0.5,100)
			var raritylight = Sprite.new()
			var rarity = movedict["types"][5]
			raritylight.set_self_modulate(Playervariables.raritycolorsarray[rarity])
			raritylight.position = deployposition
			raritylight.show_behind_parent = true
			raritylight.texture = load(Mainpreload.GododLight)
			raritylight.scale = Vector2(0.4,0.4)
			newdeploy.add_child(raritylight)
#			var newpreview = load(Mainpreload.SkillPreview).instance()
#			newdeploy.get_node("previewnode").add_child(newpreview)
#			newpreview.preview_skill(rewardarray[rewardnum])
	if rewardarray.size() > 0:
		if mode == 1:
			$backing/board/Label.set_text(Playervariables.chooselabelsarray[Playervariables.chooselabel.CLASS])
		elif rewardarray.size() > 1 and Playervariables.corruptiondict.has("tail") and Playervariables.corruptiondict["tail"] == Playervariables.raceKITSUNE:
			bonusrewards = 1
			$backing/board/Label.set_text(Playervariables.chooselabelsarray[Playervariables.chooselabel.FOXCHOOSETWO])
		else:
			$backing/board/Label.set_text(Playervariables.chooselabelsarray[Playervariables.chooselabel.CHOOSEONE])
		rewardisarray = rewardarray
		$backing/appear.play("reward")
		var openfx = load(Mainpreload.CorruptionSmoke).instance()
		$backing.add_child(openfx)
		openfx.position = Vector2($backing.rect_size.x*0.5,$backing.rect_size.y*0.77)
		openfx.play("open")
		openfx.scale = $backing/board/rewardploy.rect_scale
#		get_parent().firstrun = true
#		get_parent()._on_viewport_size_changed() #excessive?
		$backing/board/rewardploy.set("custom_constants/separation",$backing/board.rect_size.x*0.42/(rewardarray.size()))
		self.visible = true
		if Playervariables.touchscreenmode == true and from_main == false:
			get_parent().touchupper = true
#		yield(get_tree(),"idle_frame")
#		var ratiox = $backing.get_viewport_rect().size.x / Playervariables.basescreensize.x
#		var deployablescale = (ratiox*0.45) + 0.1
#		$backing/board/rewardploy.rect_pivot_offset = $backing/board/rewardploy.rect_size/2
#		$backing/board/rewardploy.rect_scale = Vector2(deployablescale,deployablescale)
#		$backing/board/rewardploy.rect_pivot_offset = $backing/board/rewardploy.rect_size/2
#		$backing/board/rewardploy.margin_left = 0
#		$backing/board/rewardploy.margin_top = 0
#		$backing/board/rewardploy.margin_bottom = 0
#		$backing/board/rewardploy.margin_right = 0
#		var ratio = $backing.get_viewport_rect().size / Playervariables.basescreensize
#		var deployablescale = (ratio.x/2) + 0.25
#		$backing/board/rewardploy.rect_scale = Vector2(deployablescale,deployablescale)

var chosenrewards = 0
func chosen_reward(rewardchosen):
#	if $backing/appear.is_playing() == false:
	if currentmode == STARTING:
		chosenrewards += 1
		if chosenrewards == rewardisarray.size():
			_on_villagebutton_pressed()
	else:
		get_node("/root/Master").call("give_reward",rewardchosen)
		if chosenrewards < bonusrewards and rewardisarray.size() > bonusrewards:#and Playervariables.corruptiondict.has("tail") and Playervariables.corruptiondict["tail"] == Playervariables.raceKITSUNE:
			chosenrewards += 1
		else:
			for reward in $backing/board/rewardploy.get_children():
				reward.queue_free()
			if from_main == false:
				get_parent().reward_chosen()
				self.visible = false
			else:
				get_parent().call("pause",false)
				get_parent().reward_picker = null
				queue_free()
