[gd_scene load_steps=21 format=2]

[ext_resource path="res://LightNode.tscn" type="PackedScene" id=1]
[ext_resource path="res://obj1torch.gd" type="Script" id=2]
[ext_resource path="res://Assets/explosive.png" type="Texture" id=3]
[ext_resource path="res://Assets/explosivefuse.png" type="Texture" id=4]
[ext_resource path="res://obj5.gd" type="Script" id=5]
[ext_resource path="res://zapsplat/zapsplat_warfare_bomb_wick_fizz_burn_74550.ogg" type="AudioStream" id=6]
[ext_resource path="res://zapsplat/sound_spark_Fire_Spell_Flamestrike_02.ogg" type="AudioStream" id=7]

[sub_resource type="AtlasTexture" id=9]
flags = 4
atlas = ExtResource( 4 )
region = Rect2( 0, 0, 46, 38 )

[sub_resource type="AtlasTexture" id=10]
flags = 4
atlas = ExtResource( 4 )
region = Rect2( 46, 0, 46, 38 )

[sub_resource type="AtlasTexture" id=11]
flags = 4
atlas = ExtResource( 4 )
region = Rect2( 0, 38, 46, 38 )

[sub_resource type="AtlasTexture" id=12]
flags = 4
atlas = ExtResource( 4 )
region = Rect2( 46, 38, 46, 38 )

[sub_resource type="SpriteFrames" id=13]
animations = [ {
"frames": [ SubResource( 9 ), SubResource( 10 ), SubResource( 11 ), SubResource( 12 ) ],
"loop": true,
"name": "default",
"speed": 8.0
} ]

[sub_resource type="Animation" id=8]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath(".:z_index")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:offset")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0, 35 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:rotation_degrees")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}

[sub_resource type="Animation" id=7]
resource_name = "burn"
length = 0.7
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.7 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 4, 1.5, 1.5, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:z_index")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.6 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ 0, 6, 0 ]
}

[sub_resource type="Animation" id=14]
resource_name = "charge"
length = 0.7
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.7 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1.96078, 1.76471, 1.50196, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:z_index")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.6 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ 0, 6, 0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.2, 0.7 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 1.2, 1.2 ), Vector2( 1, 1 ) ]
}

[sub_resource type="Animation" id=6]
resource_name = "cum"
length = 0.7
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.7 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 3, 3, 3, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:z_index")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.6 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ 0, 6, 0 ]
}

[sub_resource type="Animation" id=15]
resource_name = "deepwater"
length = 4.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.7, 4 ),
"transitions": PoolRealArray( 1, 1, 2, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.701961, 0.701961, 2.7451, 1 ), Color( 0.701961, 0.701961, 0.784314, 1 ), Color( 0.392157, 0.392157, 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:z_index")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.6 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ 0, 6, 0 ]
}

[sub_resource type="Animation" id=17]
resource_name = "explode"
length = 0.4
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.4 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 3, 3, 2.5, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:z_index")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.05 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ 0, 6 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.05, 0.4 ),
"transitions": PoolRealArray( 1, 0.5, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 0.9, 0.9 ), Vector2( 2, 2 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:offset")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.05, 0.4 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 35 ), Vector2( 0, 25 ), Vector2( 0, 0 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath(".:rotation_degrees")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 0.4 ),
"transitions": PoolRealArray( 2, 1 ),
"update": 0,
"values": [ 0.0, -120.0 ]
}

[sub_resource type="Animation" id=5]
resource_name = "water"
length = 0.7
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.7 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.7, 0.7, 4, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:z_index")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.6 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ 0, 6, 0 ]
}

[sub_resource type="Animation" id=16]
resource_name = "quieten"
length = 1.5
tracks/0/type = "value"
tracks/0/path = NodePath(".:volume_db")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ -2.0, -5.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:playing")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:max_distance")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ 5000.0, 1200.0 ]
}

[node name="torch" type="Sprite"]
scale = Vector2( 2, 2 )
texture = ExtResource( 3 )
offset = Vector2( 0, 35 )
script = ExtResource( 2 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
position = Vector2( -6, 62 )
scale = Vector2( 0.5, 0.5 )
frames = SubResource( 13 )
frame = 2
playing = true
script = ExtResource( 5 )

[node name="LightNode" parent="AnimatedSprite" instance=ExtResource( 1 )]
position = Vector2( 2, -1 )
texture_scale = 0.1

[node name="flash" type="AnimationPlayer" parent="."]
anims/RESET = SubResource( 8 )
anims/burn = SubResource( 7 )
anims/charge = SubResource( 14 )
anims/cum = SubResource( 6 )
anims/deepwater = SubResource( 15 )
anims/explode = SubResource( 17 )
anims/water = SubResource( 5 )

[node name="expire" type="Timer" parent="."]
wait_time = 4.0

[node name="fusesfx" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource( 6 )
volume_db = -2.0
autoplay = true
max_distance = 5000.0
bus = "SFX"

[node name="fuse" type="AnimationPlayer" parent="fusesfx"]
autoplay = "quieten"
anims/quieten = SubResource( 16 )

[node name="explode" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource( 7 )
max_distance = 3000.0
bus = "SFX"

[connection signal="timeout" from="expire" to="." method="_on_expire_timeout"]
