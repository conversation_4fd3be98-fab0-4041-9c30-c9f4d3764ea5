[gd_scene load_steps=8 format=2]

[ext_resource path="res://Assets/blacksquare.png" type="Texture" id=1]
[ext_resource path="res://font/Audiowide-Regular.ttf" type="DynamicFontData" id=2]
[ext_resource path="res://Assets/endpointbluebig.png" type="Texture" id=3]
[ext_resource path="res://Assets/endpointbig.png" type="Texture" id=4]
[ext_resource path="res://RAMmenu.gd" type="Script" id=5]

[sub_resource type="DynamicFont" id=8]
size = 20
font_data = ExtResource( 2 )

[sub_resource type="DynamicFont" id=9]
size = 24
font_data = ExtResource( 2 )

[node name="RAMmenu" type="CanvasLayer"]
layer = 118
script = ExtResource( 5 )

[node name="veil" type="TextureRect" parent="."]
modulate = Color( 0.894118, 0.788235, 0.960784, 1 )
self_modulate = Color( 1, 1, 1, 0.94902 )
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 0
texture = ExtResource( 1 )
expand = true

[node name="veilbutton1" type="TextureButton" parent="veil"]
modulate = Color( 0.784314, 1, 0.784314, 1 )
anchor_left = 0.3
anchor_top = 0.6
anchor_right = 0.45
anchor_bottom = 0.8
texture_normal = ExtResource( 4 )
texture_hover = ExtResource( 3 )
expand = true
stretch_mode = 5

[node name="Label" type="Label" parent="veil/veilbutton1"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_fonts/font = SubResource( 8 )
text = "Yes"
align = 1
valign = 1

[node name="veilbutton2" type="TextureButton" parent="veil"]
modulate = Color( 1, 0.784314, 0.784314, 1 )
anchor_left = 0.55
anchor_top = 0.6
anchor_right = 0.7
anchor_bottom = 0.8
texture_normal = ExtResource( 4 )
texture_hover = ExtResource( 3 )
expand = true
stretch_mode = 5

[node name="Label" type="Label" parent="veil/veilbutton2"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_fonts/font = SubResource( 8 )
text = "Cancel"
align = 1
valign = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label" type="Label" parent="veil"]
anchor_left = 0.2
anchor_top = 0.15
anchor_right = 0.8
anchor_bottom = 0.55
grow_horizontal = 2
grow_vertical = 0
custom_fonts/font = SubResource( 9 )
text = "The game closed prematurely.
If your device has limited RAM click 'Yes' to enter low-performance mode.

You can also change this in the Settings."
align = 1
valign = 1
autowrap = true

[connection signal="pressed" from="veil/veilbutton1" to="." method="_on_veilbutton1_pressed"]
[connection signal="pressed" from="veil/veilbutton2" to="." method="_on_veilbutton2_pressed"]
