[gd_scene load_steps=24 format=2]

[ext_resource path="res://Assets/materials/hairhueshifter.shader" type="Shader" id=1]
[ext_resource path="res://DialogueArt/CG/gameovers/fox/saunabackground.png" type="Texture" id=2]
[ext_resource path="res://DialogueArt/CG/gameovers/fox/shikasteam.png" type="Texture" id=3]
[ext_resource path="res://DialogueArt/CG/gameovers/fox/saunaruleshair.png" type="Texture" id=4]
[ext_resource path="res://DialogueArt/CG/gameovers/fox/saunashika.png" type="Texture" id=5]
[ext_resource path="res://DialogueArt/CG/gameovers/fox/rulessteam.png" type="Texture" id=6]
[ext_resource path="res://DialogueArt/CG/gameovers/fox/saunarulesbody.png" type="Texture" id=7]
[ext_resource path="res://DialogueArt/CG/gameovers/fox/playerwisp.png" type="Texture" id=8]
[ext_resource path="res://Assets/materials/assailanthueshifterheadcg.shader" type="Shader" id=9]
[ext_resource path="res://Assets/materials/global_skinshift.tres" type="Material" id=10]
[ext_resource path="res://DialogueArt/CG/gameovers/fox/foxbadendbg.png" type="Texture" id=11]
[ext_resource path="res://DialogueArt/CG/gameovers/fox/saunarulesbusthuge.png" type="Texture" id=12]
[ext_resource path="res://DialogueArt/CG/gameovers/fox/shikaelitevariant.png" type="Texture" id=13]
[ext_resource path="res://DialogueArt/CG/gameovers/foxbadend.gd" type="Script" id=16]

[sub_resource type="ShaderMaterial" id=14]
shader = ExtResource( 1 )
shader_param/hue_shift = 0.7
shader_param/sat_mul = 0.65
shader_param/val_mul = 1.1

[sub_resource type="ShaderMaterial" id=12]
shader = ExtResource( 9 )
shader_param/hue_shift = 0.9
shader_param/sat_mul = 0.7
shader_param/val_mul = 1.1

[sub_resource type="ShaderMaterial" id=15]
shader = ExtResource( 1 )
shader_param/hue_shift = 0.0
shader_param/sat_mul = 1.0
shader_param/val_mul = 1.0

[sub_resource type="Animation" id=2]
resource_name = "appear"
length = 0.9
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/scene2:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.9 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites/background:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.9 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("sprites/scene2:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.7, 0.9 ),
"transitions": PoolRealArray( 0.5, 1.6, 1 ),
"update": 0,
"values": [ Vector2( 150, 50 ), Vector2( 0, -20 ), Vector2( 0, 0 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("sprites/background2:modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.9 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("sprites/scene1:modulate")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 0.9 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("sprites/scene1:position")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0, 0.7, 0.9 ),
"transitions": PoolRealArray( 0.5, 1.6, 1 ),
"update": 0,
"values": [ Vector2( 0, 100 ), Vector2( 5, -3 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=3]
resource_name = "appearbg"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/scene2:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites/background:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 1.6, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("sprites/background2:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("sprites/scene1:modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ) ]
}

[sub_resource type="Animation" id=4]
resource_name = "shake"
length = 0.5
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/scene2:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.2, 0.3, 0.5 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 5, 0 ), Vector2( 0, -5 ), Vector2( 0, 0 ), Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites/background:position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = false
tracks/1/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0, 0 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("sprites/scene1:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.1, 0.2, 0.3, 0.5 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 5, 0 ), Vector2( 0, -5 ), Vector2( 0, 0 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=8]
length = 0.001

[sub_resource type="Animation" id=9]
resource_name = "steam"
length = 4.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/scene2/steamshika:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 2, 4 ),
"transitions": PoolRealArray( 0.7, 1.4, 1 ),
"update": 0,
"values": [ Vector2( 40, 5 ), Vector2( -40, -5 ), Vector2( 40, 5 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites/scene2/steamshika:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = false
tracks/1/keys = {
"times": PoolRealArray( 0, 1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.705882 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0.705882 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0.705882 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("sprites/scene2/rulessteam:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 2, 4 ),
"transitions": PoolRealArray( 1.4, 0.7, 1 ),
"update": 0,
"values": [ Vector2( -40, -5 ), Vector2( 40, 5 ), Vector2( -40, -5 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("sprites/scene2/rulessteam:modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = false
tracks/3/keys = {
"times": PoolRealArray( 0, 1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.705882 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0.705882 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0.705882 ) ]
}

[sub_resource type="Animation" id=11]
resource_name = "hover"
length = 4.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1, 2, 3, 4 ),
"transitions": PoolRealArray( 0.9, 1.3, 0.9, 1.3, 1 ),
"update": 0,
"values": [ Vector2( 0, -138 ), Vector2( 0, -238 ), Vector2( 0, -138 ), Vector2( 0, -38 ), Vector2( 0, -138 ) ]
}

[node name="foxbadend" type="CanvasLayer"]
layer = 4
script = ExtResource( 16 )

[node name="sprites" type="Node2D" parent="."]
position = Vector2( 0, -138 )

[node name="background" type="Sprite" parent="sprites"]
texture = ExtResource( 11 )
centered = false

[node name="background2" type="Sprite" parent="sprites"]
visible = false
texture = ExtResource( 2 )
centered = false

[node name="scene2" type="Node2D" parent="sprites"]
modulate = Color( 1, 1, 1, 0 )
position = Vector2( 150, 50 )

[node name="shika" type="Sprite" parent="sprites/scene2"]
material = SubResource( 14 )
use_parent_material = true
texture = ExtResource( 5 )
centered = false

[node name="elitevariant" type="Sprite" parent="sprites/scene2/shika"]
use_parent_material = true
texture = ExtResource( 13 )
centered = false
offset = Vector2( 1298, 863 )

[node name="steamshika" type="Sprite" parent="sprites/scene2"]
position = Vector2( 40, 5 )
texture = ExtResource( 3 )
centered = false

[node name="rulesbody" type="Sprite" parent="sprites/scene2"]
material = ExtResource( 10 )
texture = ExtResource( 7 )
centered = false
offset = Vector2( 238, 588 )

[node name="rulesbust" type="Sprite" parent="sprites/scene2/rulesbody"]
show_behind_parent = true
use_parent_material = true
texture = ExtResource( 12 )
centered = false
offset = Vector2( 1250, 1166 )

[node name="ruleshair" type="Sprite" parent="sprites/scene2"]
material = SubResource( 12 )
use_parent_material = true
texture = ExtResource( 4 )
centered = false
offset = Vector2( 238, 588 )

[node name="rulessteam" type="Sprite" parent="sprites/scene2"]
position = Vector2( -40, -5 )
texture = ExtResource( 6 )
centered = false

[node name="scene1" type="Node2D" parent="sprites"]
visible = false
modulate = Color( 1, 1, 1, 0 )
position = Vector2( 0, 100 )

[node name="ruleswisp" type="Sprite" parent="sprites/scene1"]
material = SubResource( 15 )
use_parent_material = true
texture = ExtResource( 8 )
centered = false

[node name="Node2D" type="Node2D" parent="sprites"]

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
anims/appear = SubResource( 2 )
anims/appearbg = SubResource( 3 )
anims/shake = SubResource( 4 )

[node name="steam" type="AnimationPlayer" parent="."]
autoplay = "steam"
playback_speed = 0.3
anims/RESET = SubResource( 8 )
anims/steam = SubResource( 9 )

[node name="hover" type="AnimationPlayer" parent="."]
root_node = NodePath("../sprites")
playback_speed = 0.05
anims/hover = SubResource( 11 )
