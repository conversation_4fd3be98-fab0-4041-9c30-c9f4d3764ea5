extends Node

const SpriteVillagerOnee = preload("res://DialogueArt/villageronee.tscn")
const SpriteVillagerShort = preload("res://DialogueArt/villagershort.tscn")
const SpriteVillagerBag = preload("res://DialogueArt/villagerbag.tscn")


const SymbolVillagerShort = preload("res://Conversations/symbolVillager.png") #Villager
const SpeechVillagerShort = preload("res://Conversations/speechbubblerectVillager.png") #Villagers
#const SymbolVillagerOnee = "res://Conversations/symbolVillager.png" #Villager
#const SpeechVillagerOnee = "res://Conversations/speechbubblerectVillager.png" #Villagers
#const SymbolVillagerBag = "res://Conversations/symbolVillager.png" #Villager
#const SpeechVillagerBag = "res://Conversations/speechbubblerectVillager.png" #Villagers
