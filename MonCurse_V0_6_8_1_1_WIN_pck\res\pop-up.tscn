[gd_scene load_steps=8 format=2]

[ext_resource path="res://font/OpenSans-Bold.ttf" type="DynamicFontData" id=1]
[ext_resource path="res://eventtext.tscn" type="PackedScene" id=2]
[ext_resource path="res://Assets/ui/informationbar.png" type="Texture" id=3]
[ext_resource path="res://pop-up.gd" type="Script" id=4]

[sub_resource type="DynamicFont" id=1]
size = 28
font_data = ExtResource( 1 )

[sub_resource type="DynamicFont" id=4]
outline_size = 2
outline_color = Color( 0, 0, 0, 1 )
font_data = ExtResource( 1 )

[sub_resource type="Animation" id=3]
length = 2.5
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 2.5 ),
"transitions": PoolRealArray( 3, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.784314 ), Color( 1, 1, 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:anchor_top")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 2.4 ),
"transitions": PoolRealArray( 3, 1 ),
"update": 0,
"values": [ 0.2, -0.2 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:anchor_bottom")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 2.4 ),
"transitions": PoolRealArray( 3, 1 ),
"update": 0,
"values": [ 0.2, -0.2 ]
}

[node name="pop-up" type="TextureRect"]
modulate = Color( 1, 1, 1, 0.784314 )
anchor_left = 0.5
anchor_top = 0.2
anchor_right = 0.5
anchor_bottom = 0.2
margin_left = -0.400002
margin_right = -0.599976
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource( 3 )
script = ExtResource( 4 )
__meta__ = {
"_edit_use_anchors_": false
}

[node name="filler3" parent="." instance=ExtResource( 2 )]
anchor_right = 1.0
anchor_bottom = 1.0
margin_right = 0.0
margin_bottom = 0.0
custom_fonts/font = SubResource( 1 )
custom_colors/font_color = Color( 0, 0, 0, 1 )
text = "..."
align = 1
autowrap = true

[node name="also" type="Label" parent="."]
visible = false
anchor_left = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = -3.05176e-05
margin_right = 82.0
custom_fonts/font = SubResource( 4 )
custom_colors/font_color = Color( 0.501961, 0.501961, 0.501961, 1 )
text = "(X more
messages)"
valign = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
autoplay = "popfade"
anims/popfade = SubResource( 3 )
[connection signal="animation_finished" from="AnimationPlayer" to="." method="_on_AnimationPlayer_animation_finished"]
