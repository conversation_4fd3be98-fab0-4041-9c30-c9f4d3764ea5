extends Node2D


# Declare member variables here. Examples:
# var a = 2
# var b = "text"

#var Debufftext = preload("res://hitmarkerdebufftext.tscn")
var newdebuff


# Called when the node enters the scene tree for the first time.
#func _ready():
#	$AnimationPlayer.play("hitmarker")
func defineresource(operator,quantity,resource): #OUTDATED
	$Label.get("custom_fonts/font").set_size(lerp(80*quantity,80,0.2))
#	var positionshift = 40-(quantity*40)
#	position += Vector2(positionshift,positionshift)
	$AnimationPlayer.play("hitmarkerplayer")
	newdebuff = TextureRect.new()
	$GridContainer.add_child(newdebuff)
	match resource:
		Playervariables.CAON:
#			newdebuff.set_texture(load("res://Assets/ui/resource"+str(resource)+".png"))
			newdebuff.set_texture(load(Mainpreload.caon))
	match int(operator):
		-1:
			$Label.set_text("-"+str(quantity))
		+1:
			$Label.set_text("+"+str(quantity))
			$Label.material.set_shader_param("first_color",Color8(65,192,88))
#			$Label.add_color_override("font_color",Color(0.5,0.9,0.6))
		0:
			$Label.set_text("")
#			$Label.add_color_override("font_color",Color(0.3,0.3,0.3))
			$Label.material = null
			#$Label.material.set_shader_param("first_color",Color8(120,120,120))
enum Multiplier{NORMAL,VULNERABLE,RESISTANT,IMMUNE}
func definehit(operator,quantity,debuff,playeryesno,multiplier=Multiplier.NORMAL):
#	var monstertag = ""
	var size = 1
#	print("Quantity value:"+str(quantity))
	if quantity > 1:
		size = sqrt(sqrt(quantity))
	else:
		size = lerp(quantity,1,0.2)
#	print("Size value:"+str(size))
	$Label.rect_scale = Vector2(size,size)
	if playeryesno == false: #is enemy
		$AnimationPlayer.play("hitmarker")
		for i in range(debuff.size()):
			for _i2 in range (debuff[i].y):
				newdebuff = TextureRect.new()
				$GridContainer.add_child(newdebuff)
				#newdebuff.set_texture(load("res://Assets/ui/debufficons/"+str(debuff[i].x)+"m.png"))
				if debuff[i].x >= 0:
					newdebuff.set_texture(load("res://Assets/ui/debufficons/"+str(debuff[i].x)+"m.png"))
					#newdebuff.set_texture(load(Mainpreload.get("debuffnm"+str(abs(debuff[i].x))+".png")))
				else:
					newdebuff.set_texture(load("res://Assets/ui/debufficons/"+str(debuff[i].x)+"m.png"))
					#newdebuff.set_texture(load(Mainpreload.get("debuffm"+str(debuff[i].x)+".png")))
#		$Label.get("custom_fonts/font").set_size(lerp(160*quantity,160,0.1))
	elif quantity < 1: #is player
#		scale = Vector2(quantity,quantity)
#		$Label.get("custom_fonts/font").set_size(lerp(80*quantity,80,0.2))
#		print(lerp(80*quantity,80,0.2))
#		var positionshift = 40-(quantity*40)
#		position += Vector2(positionshift,positionshift)
		$Label.add_color_override("font_color",Color(0.55,0.25,0.25))#.get("custom_colors/font_color").set_color(Color(0.7,0.3,0.3,0.8))
		$AnimationPlayer.play("hitmarkerplayer decay")
	else: #is player
#		$Label.get("custom_fonts/font").set_size(lerp(160*quantity,160,0.1))
		var positionshift = -(size*30)
		position += Vector2(positionshift,positionshift)
		$AnimationPlayer.play("hitmarkerplayer")
	match int(operator):
		-1:
			$Label.set_text("-"+str(quantity))
		+1:
			$Label.set_text("+"+str(quantity))
			$Label.material.set_shader_param("second_color",Color8(65,192,88))
#			$Label.add_color_override("font_color",Color(0.5,0.9,0.6))
		0:
			$Label.set_text("0")
#			$Label.material.set_shader_param("first_color",Color8(120,120,120))
			$Label.material = null
#			$Label.add_color_override("font_color",Color(0.3,0.3,0.3))
	match multiplier:
		Multiplier.NORMAL:pass
		Multiplier.RESISTANT:$Label.set_text($Label.get_text()+" RESIST")
		Multiplier.IMMUNE:$Label.set_text($Label.get_text()+" IMMUNE")
		Multiplier.VULNERABLE:$Label.set_text($Label.get_text()+" WEAK")
func blocked_effect(size,override = 0,multiplier=Multiplier.NORMAL):
	if override > 0:
		override = "+"+str(override)
		$Label.material.set_shader_param("second_color",Color8(65,192,88))
	else:
		override = str(override)
		$Label.material.set_shader_param("second_color",Color8(80,130,192))
	$Label.set_text(override)
#	size = lerp(clamp(size,0.5,9999),1,0.2)
	size = clamp(sqrt(size),0.5,9999)
	$Label.rect_scale = Vector2(size,size)
	position += Vector2(2.2+size*0.2,-1)*80
	$AnimationPlayer.play("blocked")
#	print("Doing blocked effect, I guess")
	match multiplier:
		Multiplier.NORMAL:pass
		Multiplier.RESISTANT:$Label.set_text($Label.get_text()+" RESIST")
		Multiplier.IMMUNE:$Label.set_text($Label.get_text()+" IMMUNE")
		Multiplier.VULNERABLE:$Label.set_text($Label.get_text()+" WEAK")

func _on_AnimationPlayer_animation_finished(_hitmarker):
	queue_free()
