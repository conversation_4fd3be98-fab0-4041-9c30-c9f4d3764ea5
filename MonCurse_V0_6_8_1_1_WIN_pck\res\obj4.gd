extends AnimatedSprite

var mainnode

const tilesize = 128
var loc = Vector2(0,0)
var locv = Vector2(0,0)
var direction = 0 #-1 means left, 0 means down, 1 means forward
var mapsize = null
var health = 4

const attackable = true

var Player_Egg = false
var Elite = false
var SpawnHarpy = false
var CaptureIsPlayer = false
var CaptureMonsterNum = -1
var CaptureTarget = null
var CaptureTargetWeakRef = null
var MinionOwner = null
var MinionOwnerWeakRef = null

func _ready():
	set_physics_process(false)
	$AnimationPlayer.play("reset")
#	$offset.play("default")

func drop():
	mapsize = mainnode.mapsize
	add_to_group("Attackers")
	if deathstate == false:
		$AnimationPlayer.play("drop")

func lose_health(losenum):
	health -= losenum
#	hit_marker(losenum,Multiplier.NORMAL)
	egg_animation("damage",int(clamp((4-health),0,4)))
	if health <= 0:
		if SpawnHarpy == true and CaptureIsPlayer == false:
#			if MinionOwnerWeakRef != null and MinionOwnerWeakRef.get_ref() != null:
#				mainnode.spawn_converted_enemy(Playervariables.DRAGONHARPY,locv,MinionOwner)
#			else:
			mainnode.spawn_converted_enemy(Playervariables.DRAGONHARPY,locv,Player_Egg,0,CaptureMonsterNum)
			if Player_Egg == false and MinionOwnerWeakRef != null and MinionOwnerWeakRef.get_ref() != null and MinionOwner.enemyactive == true:
				MinionOwner.assignquip(1000,null,"normal",false,7)
		else:
			if CaptureTargetWeakRef != null and CaptureTargetWeakRef.get_ref() != null:
				CaptureTarget.free_capture()
#			print("Free capture due to: health lost")
		CaptureTarget = null
		CaptureTargetWeakRef = null
		destroysize = -1
		destroy_egg()
	if SpawnHarpy == true:
		$flash.play("transform")
	else:
		$flash.play("hurt")

var one_time_heal = false
var start_falling = false
func _get_ready():
	bounces = 0
	if remove_next_ready == true:
		queue_free()
	elif deathstate == false:
		if visible == true:
			if CaptureTarget == null:
				move_egg()
			else:
				if CaptureTargetWeakRef != null and CaptureTargetWeakRef.get_ref() != null and (CaptureIsPlayer == true or CaptureTarget.health > 0):
					if CaptureIsPlayer == true:
						CaptureTarget.get_node("CanvasLayer2/Dialogue").corruptplayerwithbar(Playervariables.raceHARPY,13)
					if CaptureTarget.capture_state != EGGCAPTURE:
						health = 0
					elif health > 0:
						mainnode.call("attacking",[loc],Vector2(Playervariables.BITTER,1),self,[Vector2(Playervariables.CUM,1)],[Playervariables.DRAGONHARPY],0,"Capturing")
						if CaptureTarget != null and CaptureTargetWeakRef != null and CaptureTargetWeakRef.get_ref() != null and CaptureIsPlayer == false and CaptureTarget.enemyactive == false:
							assign_loc(loc)
							if one_time_heal == false:
								health += 2
								one_time_heal = true#just to be sure it never bugs out
					if Elite == true and CaptureIsPlayer == false and CaptureTargetWeakRef != null and CaptureTargetWeakRef.get_ref() != null and CaptureTarget.health <= 0 and CaptureTarget.EnemyCategory != 1: #1 is MINION
						SpawnHarpy = true
				else:
					var testloc = loc
					for _y in range(3):
						if mainnode.map2dpatharray[testloc.y+1][testloc.x] > 0:
							break
						else:
							testloc += Vector2(0,1)
					assign_loc(testloc)
				lose_health(1)
	elif $AnimationPlayer.is_playing() == false:
		remove_next_ready = true

const big_explosion = [Vector2(0,0),Vector2(-1,0),Vector2(1,0),Vector2(0,1),Vector2(0,-1),Vector2(1,1),Vector2(-1,-1),Vector2(-1,1),Vector2(1,-1),Vector2(-2,0),Vector2(2,0),Vector2(0,2),Vector2(0,-2),Vector2(-2,1),Vector2(2,1),Vector2(1,2),Vector2(1,-2),Vector2(-2,-1),Vector2(2,-1),Vector2(-1,2),Vector2(-1,-2)]
const base_explosion = [Vector2(0,0),Vector2(-1,0),Vector2(1,0),Vector2(0,1),Vector2(0,-1),Vector2(1,1),Vector2(-1,-1),Vector2(-1,1),Vector2(1,-1),Vector2(-2,0),Vector2(2,0),Vector2(0,2),Vector2(0,-2)]
const wet_explosion = [Vector2(0,0),Vector2(-1,0),Vector2(1,0),Vector2(0,1),Vector2(0,-1),Vector2(1,1),Vector2(-1,-1),Vector2(-1,1),Vector2(1,-1)]
const weak_explosion = [Vector2(0,0),Vector2(-1,0),Vector2(1,0),Vector2(0,1),Vector2(0,-1)]
var deathstate = false
var wet = false
var num = 1
var destroysize = 0
func move_egg():
#	num = clamp(num,1,3)
	var hitlocations = []
	var newloc = loc# + Vector2(1*direction,0)
	var hitstatus = MISS
	if abs(direction) > 0 and num > 1:
		hitlocations.append(newloc+Vector2(0,1))
		hitstatus = hit_check(hitlocations[-1])
		if hitstatus == MISS:
			hitlocations.append(newloc+Vector2(direction,0))
			hitstatus = hit_check(hitlocations[-1])
	if hitstatus == MISS:
		for i in range(num):
			hitlocations.append(newloc+Vector2(direction,i+1))
			hitstatus = hit_check(hitlocations[-1])
			if hitstatus != MISS:
				break
	if hitstatus == OUTOFBOUNDS:
		destroy_egg()
		return
	if hitlocations.size() > 0:
		if hitstatus == HIT:
			if direction != 0 and hitlocations.size() <= 3:
				hitlocations = [loc]
			else:
				hitlocations.pop_back()
				if hitlocations.size() < 1:
					hitlocations = [loc]
		newloc = hitlocations[-1]
		var altstatus = assign_loc(newloc)
		if hitstatus == MISS:
			hitstatus = altstatus
		self.rotation_degrees = 180 - ((60-(num*15))*direction)
	if deathstate == false:
		if hitstatus == HIT:
			explode()
		elif hitstatus == DIRECTHITPLAYER:
			if deathstate == false:
				CaptureIsPlayer = true
				CaptureTarget = mainnode
				CaptureTargetWeakRef = weakref(CaptureTarget)
				if MinionOwnerWeakRef != null and MinionOwnerWeakRef.get_ref() != null and MinionOwner.has_method("perfectly_track"):
					MinionOwner.perfectly_track(true)
				var beforecum = CaptureTarget.playerdebuffarray[Playervariables.CUM-1]
				mainnode.call("attacking",[loc],Vector2(Playervariables.BITTER,0),self,[Vector2(Playervariables.CUM,1)],[Playervariables.DRAGONHARPY],0,"Egg Capture")
				if CaptureTarget.playerdebuffarray[Playervariables.CUM-1] - beforecum <= 0:
					CaptureTarget = null
					CaptureTargetWeakRef = null
					explode()
				else:
#					$offset.play("captureoffset")
					$AnimationPlayer.play("capture")
					CaptureTarget.capture(self,EGGCAPTURE)
					if Player_Egg == false and MinionOwnerWeakRef != null and MinionOwnerWeakRef.get_ref() != null and MinionOwner.enemyactive == true:
						MinionOwner.assignquip(600,null,"normal",false,5)
		elif hitstatus == DIRECTHITENEMY:
			if deathstate == false:
				CaptureIsPlayer = false
				CaptureTarget = mainnode.enemy2darray[loc.y][loc.x]
				if CaptureTarget != null and "enemydebuffarray" in CaptureTarget:
					var beforecum = CaptureTarget.enemydebuffarray[Playervariables.CUM-1]
					mainnode.call("attacking",[loc],Vector2(Playervariables.BITTER,0),self,[Vector2(Playervariables.CUM,1)],[Playervariables.DRAGONHARPY],0,"Egg Capture")
					if CaptureTarget.enemydebuffarray[Playervariables.CUM-1] - beforecum <= 0:
						CaptureTarget = null
						CaptureTargetWeakRef = null
						explode()
					else:
						if Player_Egg == true and Playervariables.get_corruption("tail") != Playervariables.raceHARPY and Playervariables.mobile_ads == true:
							mainnode.get_node("CanvasLayer2/Dialogue").corruptplayerwithbar(Playervariables.raceHARPY,25,true)
#						$offset.play("captureoffset")
						capture_process(CaptureTarget)
					if mainnode.playerloc == loc:
						mainnode.solve_debuff(Vector2(Playervariables.KNOCKBACK,1),loc,self)
						mainnode.register_event_via_main("VAR1 took the egg's hit!",[CaptureTarget.displayname])
				else:
					CaptureTarget = null
					CaptureTargetWeakRef = null
					explode()
		elif hitstatus == WATERHIT:
			mainnode.call("attacking",hitlocations,Vector2(Playervariables.PRECISION,0),self,[],[Playervariables.DRAGONHARPY],0,"Egg Shell")
			num = 1
			direction = 0
			if deathstate == false:
				$AnimationPlayer.play("wet")
			wet = true
		else:
			num = clamp(num+1,1,4)
			mainnode.call("attacking",hitlocations,Vector2(Playervariables.PRECISION,0),self,[],[Playervariables.DRAGONHARPY],0,"Egg Shell")
			if num == 3:
				$AnimationPlayer.play("faster")
			elif num == 4:
				direction = 0
				$AnimationPlayer.play("fastest")

func explode():
	if CaptureTarget != null:
		return
	var explosiontiles
	var damage = 1
	if wet == true:
		explosiontiles = wet_explosion.duplicate()
		destroysize = 1
	elif num <= 1:
		destroysize = -1
		explosiontiles = []
	elif num <= 2:
		explosiontiles = weak_explosion.duplicate()
		destroysize = 1
	elif num <= 3:
		explosiontiles = base_explosion.duplicate()
		destroysize = 2
		damage = 2
	elif num >= 4:
		explosiontiles = big_explosion.duplicate()
		damage = 2
		destroysize = 3
	for i in range(explosiontiles.size()):
		explosiontiles[i] = explosiontiles[i] + loc
	var useraces = [Playervariables.DRAGONHARPY]
	if Player_Egg == true:
		useraces.append(-3)
		for explosiontile in explosiontiles:
			if explosiontile == mainnode.playerloc:
				mainnode.solve_debuff(Vector2(Playervariables.KNOCKBACK,damage),loc)
	mainnode.call("attacking",explosiontiles,Vector2(Playervariables.BITTER,damage),self,[Vector2(Playervariables.FLINCH,1),Vector2(Playervariables.KNOCKBACK,damage),Vector2(Playervariables.DROWSY,2*damage),Vector2(Playervariables.CUM,2*damage)],useraces,0,"Eggsplosion")
	destroy_egg()

enum{MISS=0,HIT=1,DIRECTHITENEMY=2,DIRECTHITPLAYER=3,OUTOFBOUNDS=4,WATERHIT=5}
func hit_check(hitloc) -> int:
	if mapsize == null or hitloc.y > mapsize.y-1 or hitloc.x > mapsize.x-1 or hitloc.y < 1 or hitloc.x < 1:
		return OUTOFBOUNDS
	if mainnode.map2darray[hitloc.y][hitloc.x] > 0:
		return HIT
	elif mainnode.map2dobstructionarray[hitloc.y][hitloc.x] != -1:
		return HIT
	elif mainnode.enemy2darray[hitloc.y][hitloc.x] != null and "monsternum" in mainnode.enemy2darray[hitloc.y][hitloc.x] and mainnode.enemy2darray[hitloc.y][hitloc.x].monsternum != Playervariables.DRAGONHARPY:
		return DIRECTHITENEMY
	elif mainnode.playerloc == hitloc and Player_Egg == false:
		return DIRECTHITPLAYER
	elif mainnode.watermap[hitloc.y][hitloc.x] == 2:
		return WATERHIT
	else:
		return MISS

var disableloc = Vector2(1,1) #previous location for use in astar enable/disable
var disablepoint = -1 #just the locv version for convenience
func assign_loc(newloc,instant = false):
	if instant == true:
		set_physics_process(false)
		loc = newloc
		locv = (loc+Vector2(0.5,0.5))*tilesize
		position = locv
	else:
		loc = newloc
		locv = (loc+Vector2(0.5,0.5))*tilesize
		set_physics_process(true)
	var currentpoint = mainnode.mapstar.get_closest_point((Vector2(0.5,0.5)+newloc)*tilesize,true)
	var failed = false
	if visible == true:
		if CaptureIsPlayer == false and CaptureTarget != null and CaptureTargetWeakRef != null and CaptureTargetWeakRef.get_ref() != null and CaptureTarget.enemyactive == true:
			failed = mainnode.astarenabledisable(loc,-1,disableloc,disablepoint,self)
		else:
			failed = mainnode.astarenabledisable(loc,currentpoint,disableloc,disablepoint,self)
	if failed == false:
		return MISS
	else:
		return OUTOFBOUNDS

func set_enemy_to_loc():
	if CaptureIsPlayer == true:
		mainnode.assign_player_loc_and_move([loc],1,[0,10])
	elif CaptureTarget != null and CaptureIsPlayer == false and CaptureTargetWeakRef != null and CaptureTargetWeakRef.get_ref() != null:
		CaptureTarget.loc = loc
		CaptureTarget.locv = locv
		CaptureTarget.position = locv
		CaptureTarget.enemymovepoint = locv
		CaptureTarget.handlepointcalc()
	else:
		print("Enemy was supposed to be in released egg but missing? Destroying egg.")
		destroy_egg()

func _physics_process(_delta):
	position = lerp(position,locv,0.1)
	if (position - locv).length() < 2:
		position = locv
		set_physics_process(false)


var remove_next_ready = false
func _on_AnimationPlayer_animation_finished(anim_name):
	if deathstate == true:
		remove_next_ready = true
	elif anim_name == "capture":
		$AnimationPlayer.play("captureloop")

func destroy_egg():
	if CaptureTarget == null:
		if is_in_group("Attackers"):
			remove_from_group("Attackers")
		mainnode.astarenabledisable(Vector2(0,0),-1,disableloc,disablepoint,self)
		if deathstate == false:
			match destroysize:
				-1:
					$AnimationPlayer.play("capturedestroy")
				1:
					$AnimationPlayer.play("destroy")
				2:
					$AnimationPlayer.play("destroy big")
					if mainnode.map2dpatharray[loc.y+1][loc.x] > 0:
						if mainnode.watermap[loc.y][loc.x] <= 1 and mainnode.map2darray[loc.y][loc.x] == 0:
							if mainnode.backgroundmap[loc.y][loc.x] > 0:
								mainnode.get_node("cummap").set_cell(loc.x,loc.y,3+randi()%2)
							else:
								mainnode.get_node("cummap").set_cell(loc.x,loc.y,5+randi()%2)
#							mainnode.watermap[loc.y][loc.x] = 3
				3:
					$AnimationPlayer.play("destroy huge")
					for x in range(3):
						if mainnode.map2dpatharray[loc.y+1][loc.x+(x-1)] > 0:
							if mainnode.watermap[loc.y][loc.x+(x-1)] <= 1 and mainnode.map2darray[loc.y][loc.x+(x-1)] == 0:
								if mainnode.backgroundmap[loc.y][loc.x+(x-1)] > 0:
									mainnode.get_node("cummap").set_cell(loc.x+(x-1),loc.y,3+randi()%2)
								else:
									mainnode.get_node("cummap").set_cell(loc.x+(x-1),loc.y,5+randi()%2)
#								mainnode.watermap[loc.y][loc.x+(x-1)] = 3
				_:
					$AnimationPlayer.play("destroy small")
			deathstate = true

func taking_damage(_targetarray,typedamage,ID,debuff,friendnums,_hardcodeddirection,_attackname):
	if friendnums.find(Playervariables.DRAGONHARPY) > -1:# or (Player_Egg == true and friendnums.find(-3) > -1):
		return
	if visible == false:
		return
	var finaldamage = typedamage.y
	var multiplier = Multiplier.NORMAL
	if typedamage.x == Playervariables.SWEET:
		finaldamage = -1
		multiplier = Multiplier.IMMUNE
	elif typedamage.x == Playervariables.ENERGY:
		finaldamage = ceil((typedamage.y*0.71) -1)
		multiplier = Multiplier.RESISTANT
	if typedamage.y > -1:
		for debuffeffect in debuff:
			if debuffeffect.x == Playervariables.KNOCKBACK:
				var attackerloc = ID.get("loc")
				if attackerloc == null:
					if CaptureIsPlayer == true:
						attackerloc = ID.get("playerloc") #so that if the player lances into an enemy, it knocks them back properly
					else:
						attackerloc = ID.get("playerprevloc")
					if attackerloc == null:
						attackerloc = loc
				if loc == attackerloc:
					var diff = Vector2(0,1)#unlike other knockback cases, the egg will default to being knocked upwards.
					var proposedloc = attackerloc-diff
					var inc = 0
					while (mainnode.map2darray[proposedloc.y][proposedloc.x] > 0 or proposedloc == attackerloc) and inc < 15:
						inc += 1
						diff = Vector2((randi()%3)-1,(randi()%3)-1)
						proposedloc = attackerloc-diff
					attackerloc = attackerloc+diff
				if loc != attackerloc:
	#									yield(get_tree(),"idle_frame")
					mainnode.call("astarenabledisable",loc,-1,disableloc,disablepoint,self)
					knockbacks.append(Vector3(attackerloc.x,attackerloc.y,debuffeffect.y))
					add_to_group("delayed_knockback")
	if typedamage.y > 0:
		if CaptureTarget != null:
			if CaptureIsPlayer == false and ID == mainnode and CaptureTargetWeakRef != null and CaptureTargetWeakRef.get_ref() != null and Player_Egg == false:
				if CaptureTarget.has_method("player_favor"):
					print("Testing egg favor player:"+str(CaptureTarget.TargetType)+"..."+str(CaptureTarget.TargetNotPlayer))
					CaptureTarget.player_favor(false)
					print("Testing egg favor player:"+str(CaptureTarget.TargetType)+"..."+str(CaptureTarget.TargetNotPlayer))
					print("Captured target now favors player. you can mark this as tested and done and delete the print entries.")
			SpawnHarpy = false#interrupts TF
			lose_health(typedamage.y)
		else:
			if Player_Egg == false and MinionOwnerWeakRef != null and MinionOwnerWeakRef.get_ref() != null and MinionOwner.enemyactive == true:
				if ID == mainnode and MinionOwner.monsternum == Playervariables.DRAGONHARPY:
					MinionOwner.make_angry()
					if Elite == true:
						MinionOwner.assignquip(19*100,null,"normal",false,5)
					else:
						MinionOwner.assignquip(20*100,null,"normal",false,5)
			destroysize = -1
			num -= typedamage.y
			explode()
#			destroy_egg()
	hit_marker(finaldamage,multiplier)

enum Multiplier{NORMAL,VULNERABLE,RESISTANT,IMMUNE}
func hit_marker(finaldamage,multiplier):
	var attackeffect = load(Mainpreload.Hitmarker).instance()
	mainnode.get_node("attackeffects").add_child(attackeffect)
#	if finaldamage <= 0:
#		attackeffect.definehit(-1,0,[],false,Multiplier.IMMUNE)
#	else:
	if finaldamage <= 0 and multiplier in [Multiplier.RESISTANT,Multiplier.IMMUNE]:
		attackeffect.blocked_effect(finaldamage,0,multiplier)
	else:
		attackeffect.definehit(-1,finaldamage,[],false,multiplier)
#	attackeffect.definehit(-1,finaldamage,[],false,multiplier)
	attackeffect.position = locv

func egg_animation(string:String,forceframe:int):
	if Elite == true:
		string = "dragon"+string
	if forceframe == -1:
		self.play(string)
	else:
		self.playing = false
		self.set_animation(string)
		self.frame = forceframe



var knockbacks = []
func knockback():
	if self.is_in_group("delayed_knockback"):
		remove_from_group("delayed_knockback")
		var provisional_loc = loc
		var provisional_locv = locv
		for i in range(knockbacks.size()):
			var attackerloc = Vector2(knockbacks[i].x,knockbacks[i].y)
			var debuffeffect = knockbacks[i].z
			var knockback = Vector2(1,0).rotated((provisional_loc - attackerloc).angle())
			knockback = Vector2(int(stepify(knockback.x,1)),int(stepify(knockback.y,1)))
			var knockbackcount = 0
			var knockdir = 1
			var enemymovedarray = [mainnode.mapstar.get_closest_point(provisional_locv,true)]
			if debuffeffect < 0:
	#			debuffeffect = -debuffeffect
				knockdir = -1
			for _i in range(abs(debuffeffect)):
				if mainnode.map2darray[provisional_loc.y+knockback.y*knockdir][provisional_loc.x+knockback.x*knockdir] == 0:
					provisional_loc += knockback*knockdir
					provisional_locv += knockback*tilesize*knockdir
					knockbackcount += 1
					enemymovedarray.append(mainnode.mapstar.get_closest_point(provisional_locv,true))
				if provisional_loc == attackerloc:
					break
			if knockbackcount > 0:
				var finalpoint = mainnode.skystar.get_closest_point(provisional_locv,true)
				provisional_locv = mainnode.skystar.get_point_position(finalpoint)
				provisional_loc = (provisional_locv/tilesize) - Vector2(0.5,0.5)
				debuff_bounce(Playervariables.KNOCKBACK)
				if CaptureTarget == null:
					assign_loc(provisional_loc)
				elif CaptureIsPlayer == true:
					CaptureTarget.assign_player_loc_and_move([provisional_loc],1,[0,10])
				elif CaptureTarget.has_method("assign_enemy_loc_and_move") == true:
					CaptureTarget.assign_enemy_loc_and_move([provisional_loc],1,[-1,0])
				else:
					assign_loc(provisional_loc)
		knockbacks = []
		


var bounces = 0
func debuff_bounce(debuffid):
	mainnode.debuff_bounce_main(debuffid,self.position,bounces)
	bounces += 1

enum{NOCAPTURE,RAMCAPTURE,EGGCAPTURE}#eggcapture includes eggcapture and enemycapture cases
func capture_process(captured_enemy):
	if "playerloc" in captured_enemy:
		CaptureIsPlayer = true
		if visible == false:
			match int(Playervariables.player_harpied_count):
				0:mainnode.register_event_via_main("Everything's gone dark, what happened?!")
				1:mainnode.register_event_via_main("Abducted again. It's snug and warm.")
				2:
					if Playervariables.get_corruption("armright") == Playervariables.raceHARPY:
						mainnode.register_event_via_main("...It's not so bad, being here.")
					else:
						mainnode.register_event_via_main("Harpyification seems inevitable.")
				3:
					mainnode.register_event_via_main("Abducted again...",[],true)
			Playervariables.player_harpied_count += 1
	else:
		CaptureIsPlayer = false
		CaptureMonsterNum = captured_enemy.monsternum
	CaptureTarget = captured_enemy
	CaptureTargetWeakRef = weakref(CaptureTarget)
	$AnimationPlayer.play("capture")
	CaptureTarget.capture(self,EGGCAPTURE)

func release_case():
	if CaptureTarget != null and CaptureIsPlayer == false and CaptureTargetWeakRef != null and CaptureTargetWeakRef.get_ref() != null:
		mainnode.call("astarenabledisable",CaptureTarget.loc,-1,CaptureTarget.disableloc,CaptureTarget.disablepoint,CaptureTarget)
