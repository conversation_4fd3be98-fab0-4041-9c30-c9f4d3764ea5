[gd_scene load_steps=72 format=2]

[ext_resource path="res://Enemy/dragonharpy/eggr.png" type="Texture" id=1]
[ext_resource path="res://obj4.gd" type="Script" id=2]
[ext_resource path="res://zapsplat/zapsplat_food_egg_shell_intact_hollow_cut_crack_knife_29234.ogg" type="AudioStream" id=3]
[ext_resource path="res://zapsplat/zapsplat_cartoon_slime_hit_002_89575.ogg" type="AudioStream" id=4]
[ext_resource path="res://zapsplat/zapsplat_cartoon_slime_hit_005_89578.ogg" type="AudioStream" id=5]
[ext_resource path="res://zapsplat/sound_ex_machina_Action-Cracking-Egg.ogg" type="AudioStream" id=6]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_designed_water_hit_thump_005_26347.ogg" type="AudioStream" id=7]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_futuristic_tech_hit_open_activate_74152.ogg" type="AudioStream" id=8]
[ext_resource path="res://Enemy/dragonharpy/dragoneggr.png" type="Texture" id=9]

[sub_resource type="AtlasTexture" id=47]
atlas = ExtResource( 1 )
region = Rect2( 450, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=48]
atlas = ExtResource( 1 )
region = Rect2( 300, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=49]
atlas = ExtResource( 1 )
region = Rect2( 450, 150, 150, 150 )

[sub_resource type="AtlasTexture" id=50]
atlas = ExtResource( 1 )
region = Rect2( 300, 150, 150, 150 )

[sub_resource type="AtlasTexture" id=51]
atlas = ExtResource( 1 )
region = Rect2( 150, 150, 150, 150 )

[sub_resource type="AtlasTexture" id=52]
atlas = ExtResource( 1 )
region = Rect2( 0, 150, 150, 150 )

[sub_resource type="AtlasTexture" id=26]
atlas = ExtResource( 1 )
region = Rect2( 0, 150, 150, 150 )

[sub_resource type="AtlasTexture" id=27]
atlas = ExtResource( 1 )
region = Rect2( 150, 150, 150, 150 )

[sub_resource type="AtlasTexture" id=28]
atlas = ExtResource( 1 )
region = Rect2( 300, 150, 150, 150 )

[sub_resource type="AtlasTexture" id=29]
atlas = ExtResource( 1 )
region = Rect2( 450, 150, 150, 150 )

[sub_resource type="AtlasTexture" id=30]
atlas = ExtResource( 1 )
region = Rect2( 600, 150, 150, 150 )

[sub_resource type="AtlasTexture" id=5]
atlas = ExtResource( 1 )
region = Rect2( 0, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=6]
atlas = ExtResource( 1 )
region = Rect2( 150, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=7]
atlas = ExtResource( 1 )
region = Rect2( 300, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=8]
atlas = ExtResource( 1 )
region = Rect2( 450, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=9]
atlas = ExtResource( 1 )
region = Rect2( 600, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=53]
atlas = ExtResource( 9 )
region = Rect2( 450, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=54]
atlas = ExtResource( 9 )
region = Rect2( 300, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=55]
atlas = ExtResource( 9 )
region = Rect2( 450, 150, 150, 150 )

[sub_resource type="AtlasTexture" id=56]
atlas = ExtResource( 9 )
region = Rect2( 450, 150, 150, 150 )

[sub_resource type="AtlasTexture" id=57]
atlas = ExtResource( 9 )
region = Rect2( 300, 150, 150, 150 )

[sub_resource type="AtlasTexture" id=58]
atlas = ExtResource( 9 )
region = Rect2( 150, 150, 150, 150 )

[sub_resource type="AtlasTexture" id=59]
atlas = ExtResource( 9 )
region = Rect2( 0, 150, 150, 150 )

[sub_resource type="AtlasTexture" id=32]
atlas = ExtResource( 9 )
region = Rect2( 0, 150, 150, 150 )

[sub_resource type="AtlasTexture" id=33]
atlas = ExtResource( 9 )
region = Rect2( 150, 150, 150, 150 )

[sub_resource type="AtlasTexture" id=34]
atlas = ExtResource( 9 )
region = Rect2( 300, 150, 150, 150 )

[sub_resource type="AtlasTexture" id=35]
atlas = ExtResource( 9 )
region = Rect2( 450, 150, 150, 150 )

[sub_resource type="AtlasTexture" id=36]
atlas = ExtResource( 9 )
region = Rect2( 600, 150, 150, 150 )

[sub_resource type="AtlasTexture" id=37]
atlas = ExtResource( 9 )
region = Rect2( 0, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=38]
atlas = ExtResource( 9 )
region = Rect2( 150, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=39]
atlas = ExtResource( 9 )
region = Rect2( 300, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=40]
atlas = ExtResource( 9 )
region = Rect2( 450, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=41]
atlas = ExtResource( 9 )
region = Rect2( 600, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=46]
atlas = ExtResource( 9 )
region = Rect2( 600, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=45]
atlas = ExtResource( 9 )
region = Rect2( 450, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=44]
atlas = ExtResource( 9 )
region = Rect2( 300, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=43]
atlas = ExtResource( 9 )
region = Rect2( 150, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=42]
atlas = ExtResource( 9 )
region = Rect2( 0, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=10]
atlas = ExtResource( 1 )
region = Rect2( 600, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=11]
atlas = ExtResource( 1 )
region = Rect2( 450, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=12]
atlas = ExtResource( 1 )
region = Rect2( 300, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=13]
atlas = ExtResource( 1 )
region = Rect2( 150, 0, 150, 150 )

[sub_resource type="AtlasTexture" id=14]
atlas = ExtResource( 1 )
region = Rect2( 0, 0, 150, 150 )

[sub_resource type="SpriteFrames" id=15]
animations = [ {
"frames": [ null ],
"loop": false,
"name": "blank",
"speed": 5.0
}, {
"frames": [ SubResource( 47 ), SubResource( 48 ), SubResource( 49 ), SubResource( 50 ), SubResource( 51 ), SubResource( 52 ) ],
"loop": false,
"name": "capture",
"speed": 5.0
}, {
"frames": [ SubResource( 26 ), SubResource( 27 ), SubResource( 28 ), SubResource( 29 ), SubResource( 30 ) ],
"loop": false,
"name": "damage",
"speed": 5.0
}, {
"frames": [ SubResource( 5 ), SubResource( 6 ), SubResource( 7 ), SubResource( 8 ), SubResource( 9 ), null ],
"loop": false,
"name": "destroy",
"speed": 8.0
}, {
"frames": [ SubResource( 53 ), SubResource( 54 ), SubResource( 55 ), SubResource( 56 ), SubResource( 57 ), SubResource( 57 ), SubResource( 58 ), SubResource( 58 ), SubResource( 59 ) ],
"loop": false,
"name": "dragoncapture",
"speed": 7.0
}, {
"frames": [ SubResource( 32 ), SubResource( 33 ), SubResource( 34 ), SubResource( 35 ), SubResource( 36 ) ],
"loop": false,
"name": "dragondamage",
"speed": 5.0
}, {
"frames": [ SubResource( 37 ), SubResource( 38 ), SubResource( 39 ), SubResource( 40 ), SubResource( 41 ), null ],
"loop": false,
"name": "dragondestroy",
"speed": 5.0
}, {
"frames": [ SubResource( 46 ), SubResource( 45 ), SubResource( 44 ), SubResource( 43 ), SubResource( 42 ) ],
"loop": false,
"name": "dragonform",
"speed": 5.0
}, {
"frames": [ SubResource( 10 ), SubResource( 11 ), SubResource( 12 ), SubResource( 13 ), SubResource( 14 ) ],
"loop": false,
"name": "form",
"speed": 5.0
} ]

[sub_resource type="Animation" id=21]
resource_name = "capture"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 0.51, 1 ),
"update": 0,
"values": [ Vector2( 0.8, 0.8 ), Vector2( 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.921569, 0.921569, 0.921569, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:flip_v")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:animation")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ "form" ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("form:playing")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 0.6 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}
tracks/5/type = "value"
tracks/5/path = NodePath(".:rotation_degrees")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0, 0.4, 0.7, 1.1, 1.4 ),
"transitions": PoolRealArray( 0.5, 1, 0.5, 1, 1 ),
"update": 0,
"values": [ 0.0, -30.0, 30.0, -10.0, 0.0 ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("capture:playing")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}
tracks/7/type = "value"
tracks/7/path = NodePath(".:animation")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ "damage" ]
}
tracks/8/type = "value"
tracks/8/path = NodePath(".:playing")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/9/type = "method"
tracks/9/path = NodePath(".")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"values": [ {
"args": [ "capture", -1 ],
"method": "egg_animation"
} ]
}
tracks/10/type = "animation"
tracks/10/path = NodePath("offset")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/keys = {
"clips": PoolStringArray( "captureoffset" ),
"times": PoolRealArray( 0 )
}

[sub_resource type="Animation" id=31]
resource_name = "capturedestroy"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 1.4, 1.4 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 2, 1 ),
"update": 0,
"values": [ Color( 0.921569, 0.921569, 0.921569, 1 ), Color( 0.921569, 0.803922, 1, 0 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:flip_v")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("crack:playing")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 1.7 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}
tracks/4/type = "value"
tracks/4/path = NodePath(".:rotation_degrees")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ 6.0, -6.0 ]
}
tracks/5/type = "method"
tracks/5/path = NodePath(".")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"values": [ {
"args": [ "damage", 4 ],
"method": "egg_animation"
} ]
}

[sub_resource type="Animation" id=22]
resource_name = "captureloop"
length = 2.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 0.921569, 0.921569, 0.921569, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:rotation_degrees")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.2, 0.8, 1, 1.4, 2 ),
"transitions": PoolRealArray( 0.7, 1.4, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, -9.0, 5.0, -3.0, 2.0, 0.0 ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:flip_v")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}

[sub_resource type="Animation" id=16]
resource_name = "destroy"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 0.9, 0.9 ), Vector2( 1.6, 1.6 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 0.901961, 0.901961, 1 ), Color( 0.784314, 0.784314, 0.784314, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:flip_v")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("smallexplode:playing")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/4/type = "method"
tracks/4/path = NodePath(".")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"values": [ {
"args": [ "destroy", -1 ],
"method": "egg_animation"
} ]
}

[sub_resource type="Animation" id=17]
resource_name = "destroy big"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 0.9, 0.9 ), Vector2( 2, 2 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 0.843137, 0.843137, 1 ), Color( 0.784314, 0.784314, 0.784314, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:flip_v")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("bigexplode:playing")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/4/type = "method"
tracks/4/path = NodePath(".")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"values": [ {
"args": [ "destroy", -1 ],
"method": "egg_animation"
} ]
}

[sub_resource type="Animation" id=18]
resource_name = "destroy huge"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 0.9, 0.9 ), Vector2( 2.6, 2.6 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 0.745098, 0.745098, 1 ), Color( 0.784314, 0.784314, 0.784314, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:flip_v")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("bigexplode:playing")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/4/type = "method"
tracks/4/path = NodePath(".")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"values": [ {
"args": [ "destroy", -1 ],
"method": "egg_animation"
} ]
}

[sub_resource type="Animation" id=19]
resource_name = "destroy small"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 0.9, 0.9 ), Vector2( 0.7, 0.7 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.509804, 0.509804, 0.509804, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:flip_v")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("crack:playing")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/4/type = "method"
tracks/4/path = NodePath(".")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"values": [ {
"args": [ "destroy", -1 ],
"method": "egg_animation"
} ]
}

[sub_resource type="Animation" id=1]
resource_name = "drop"
tracks/0/type = "value"
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3, 1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 1.2, 1.2 ), Vector2( 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.3, 1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.564706, 0.564706, 0.564706, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:flip_v")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/3/type = "method"
tracks/3/path = NodePath(".")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"values": [ {
"args": [ "destroy", 0 ],
"method": "egg_animation"
} ]
}

[sub_resource type="Animation" id=2]
resource_name = "faster"
tracks/0/type = "value"
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3, 1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 0.8, 0.8 ), Vector2( 0.9, 0.9 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.3, 1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 0.823529, 0.823529, 1 ), Color( 1, 0.901961, 0.901961, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:flip_v")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}

[sub_resource type="Animation" id=3]
resource_name = "fastest"
tracks/0/type = "value"
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3, 1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0.9, 0.9 ), Vector2( 0.7, 0.7 ), Vector2( 0.8, 0.8 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.3, 1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 0.901961, 0.901961, 1 ), Color( 1, 0.745098, 0.745098, 1 ), Color( 1, 0.784314, 0.784314, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:flip_v")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}

[sub_resource type="Animation" id=20]
resource_name = "reset"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0.8, 0.8 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:flip_v")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("form:playing")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/4/type = "method"
tracks/4/path = NodePath(".")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"values": [ {
"args": [ "form", -1 ],
"method": "egg_animation"
} ]
}
tracks/5/type = "animation"
tracks/5/path = NodePath("offset")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"clips": PoolStringArray( "default" ),
"times": PoolRealArray( 0 )
}

[sub_resource type="Animation" id=4]
resource_name = "wet"
tracks/0/type = "value"
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3, 1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 1.2, 1.2 ), Vector2( 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.713726, 0.713726, 1, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:flip_v")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0.3 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}

[sub_resource type="Animation" id=23]
resource_name = "captureoffset"
length = 0.2
tracks/0/type = "value"
tracks/0/path = NodePath(".:offset")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0, 15 ) ]
}

[sub_resource type="Animation" id=24]
resource_name = "default"
length = 0.2
tracks/0/type = "value"
tracks/0/path = NodePath(".:offset")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0, 30 ) ]
}

[sub_resource type="Animation" id=25]
resource_name = "shake"
length = 1.2
tracks/0/type = "value"
tracks/0/path = NodePath(".:offset")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.3, 0.5, 0.7 ),
"transitions": PoolRealArray( 1, 1, 2, 0.5, 1 ),
"update": 0,
"values": [ Vector2( 0, 10 ), Vector2( -7, 22 ), Vector2( 6, -5 ), Vector2( -2, 10 ), Vector2( 0, 10 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("struggle:playing")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("form:playing")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.7 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}

[sub_resource type="Animation" id=61]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:z_index")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0 ]
}

[sub_resource type="Animation" id=60]
resource_name = "hurt"
length = 0.8
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.8 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 3, 3, 3, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:z_index")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.6 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ 0, 4, 0 ]
}

[sub_resource type="Animation" id=62]
resource_name = "transform"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3, 2 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 2, 4, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:z_index")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.9 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ 0, 4, 0 ]
}

[node name="egg" type="AnimatedSprite"]
self_modulate = Color( 0.921569, 0.921569, 0.921569, 1 )
position = Vector2( 0, 50 )
frames = SubResource( 15 )
animation = "damage"
offset = Vector2( 0, 15 )
script = ExtResource( 2 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
anims/capture = SubResource( 21 )
anims/capturedestroy = SubResource( 31 )
anims/captureloop = SubResource( 22 )
anims/destroy = SubResource( 16 )
"anims/destroy big" = SubResource( 17 )
"anims/destroy huge" = SubResource( 18 )
"anims/destroy small" = SubResource( 19 )
anims/drop = SubResource( 1 )
anims/faster = SubResource( 2 )
anims/fastest = SubResource( 3 )
anims/reset = SubResource( 20 )
anims/wet = SubResource( 4 )

[node name="crack" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource( 3 )
volume_db = -7.0
max_distance = 2500.0
bus = "SFX"

[node name="form" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource( 6 )
volume_db = -15.0
max_distance = 2500.0
bus = "SFX"

[node name="smallexplode" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource( 4 )
volume_db = -6.0
bus = "SFX"

[node name="bigexplode" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource( 5 )
volume_db = -6.0
bus = "SFX"

[node name="capture" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource( 7 )
volume_db = -3.0
bus = "SFX"

[node name="struggle" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource( 8 )
bus = "SFX"

[node name="offset" type="AnimationPlayer" parent="."]
anims/captureoffset = SubResource( 23 )
anims/default = SubResource( 24 )
anims/shake = SubResource( 25 )

[node name="flash" type="AnimationPlayer" parent="."]
anims/RESET = SubResource( 61 )
anims/hurt = SubResource( 60 )
anims/transform = SubResource( 62 )

[connection signal="animation_finished" from="AnimationPlayer" to="." method="_on_AnimationPlayer_animation_finished"]
