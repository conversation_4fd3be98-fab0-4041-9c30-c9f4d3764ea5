[gd_resource type="TileSet" load_steps=2 format=2]

[ext_resource path="res://Tilemaps/isometriclandscape.png" type="Texture" id=1]

[resource]
0/name = "isometriclandscape.png 0"
0/texture = ExtResource( 1 )
0/tex_offset = Vector2( 0, 0 )
0/modulate = Color( 1, 1, 1, 1 )
0/region = Rect2( 0, 0, 256, 256 )
0/tile_mode = 0
0/occluder_offset = Vector2( 0, 0 )
0/navigation_offset = Vector2( 0, 0 )
0/shape_offset = Vector2( 0, 0 )
0/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
0/shape_one_way = false
0/shape_one_way_margin = 0.0
0/shapes = [  ]
0/z_index = 0
1/name = "isometriclandscape.png 1"
1/texture = ExtResource( 1 )
1/tex_offset = Vector2( 0, 0 )
1/modulate = Color( 1, 1, 1, 1 )
1/region = Rect2( 256, 0, 256, 256 )
1/tile_mode = 0
1/occluder_offset = Vector2( 0, 0 )
1/navigation_offset = Vector2( 0, 0 )
1/shape_offset = Vector2( 0, 0 )
1/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
1/shape_one_way = false
1/shape_one_way_margin = 0.0
1/shapes = [  ]
1/z_index = 0
2/name = "isometriclandscape.png 2"
2/texture = ExtResource( 1 )
2/tex_offset = Vector2( 0, 0 )
2/modulate = Color( 1, 1, 1, 1 )
2/region = Rect2( 512, 0, 256, 256 )
2/tile_mode = 0
2/occluder_offset = Vector2( 0, 0 )
2/navigation_offset = Vector2( 0, 0 )
2/shape_offset = Vector2( 0, 0 )
2/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
2/shape_one_way = false
2/shape_one_way_margin = 0.0
2/shapes = [  ]
2/z_index = 0
3/name = "isometriclandscape.png 3"
3/texture = ExtResource( 1 )
3/tex_offset = Vector2( 0, 0 )
3/modulate = Color( 1, 1, 1, 1 )
3/region = Rect2( 768, 0, 256, 256 )
3/tile_mode = 0
3/occluder_offset = Vector2( 0, 0 )
3/navigation_offset = Vector2( 0, 0 )
3/shape_offset = Vector2( 0, 0 )
3/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
3/shape_one_way = false
3/shape_one_way_margin = 0.0
3/shapes = [  ]
3/z_index = 0
4/name = "isometriclandscape.png 4"
4/texture = ExtResource( 1 )
4/tex_offset = Vector2( 0, 0 )
4/modulate = Color( 1, 1, 1, 1 )
4/region = Rect2( 1024, 0, 256, 256 )
4/tile_mode = 0
4/occluder_offset = Vector2( 0, 0 )
4/navigation_offset = Vector2( 0, 0 )
4/shape_offset = Vector2( 0, 0 )
4/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
4/shape_one_way = false
4/shape_one_way_margin = 0.0
4/shapes = [  ]
4/z_index = 0
5/name = "isometriclandscape.png 5"
5/texture = ExtResource( 1 )
5/tex_offset = Vector2( 0, 0 )
5/modulate = Color( 1, 1, 1, 1 )
5/region = Rect2( 1280, 0, 256, 256 )
5/tile_mode = 0
5/occluder_offset = Vector2( 0, 0 )
5/navigation_offset = Vector2( 0, 0 )
5/shape_offset = Vector2( 0, 0 )
5/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
5/shape_one_way = false
5/shape_one_way_margin = 0.0
5/shapes = [  ]
5/z_index = 0
6/name = "isometriclandscape.png 6"
6/texture = ExtResource( 1 )
6/tex_offset = Vector2( 0, 0 )
6/modulate = Color( 1, 1, 1, 1 )
6/region = Rect2( 1536, 0, 256, 256 )
6/tile_mode = 0
6/occluder_offset = Vector2( 0, 0 )
6/navigation_offset = Vector2( 0, 0 )
6/shape_offset = Vector2( 0, 0 )
6/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
6/shape_one_way = false
6/shape_one_way_margin = 0.0
6/shapes = [  ]
6/z_index = 0
7/name = "isometriclandscape.png 7"
7/texture = ExtResource( 1 )
7/tex_offset = Vector2( 0, 0 )
7/modulate = Color( 1, 1, 1, 1 )
7/region = Rect2( 0, 256, 256, 256 )
7/tile_mode = 0
7/occluder_offset = Vector2( 0, 0 )
7/navigation_offset = Vector2( 0, 0 )
7/shape_offset = Vector2( 0, 0 )
7/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
7/shape_one_way = false
7/shape_one_way_margin = 0.0
7/shapes = [  ]
7/z_index = 0
8/name = "isometriclandscape.png 8"
8/texture = ExtResource( 1 )
8/tex_offset = Vector2( 0, 96 )
8/modulate = Color( 1, 1, 1, 1 )
8/region = Rect2( 256, 256, 256, 160 )
8/tile_mode = 0
8/occluder_offset = Vector2( 0, 0 )
8/navigation_offset = Vector2( 0, 0 )
8/shape_offset = Vector2( 0, 0 )
8/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
8/shape_one_way = false
8/shape_one_way_margin = 0.0
8/shapes = [  ]
8/z_index = 0
9/name = "isometriclandscape.png 9"
9/texture = ExtResource( 1 )
9/tex_offset = Vector2( 0, 96 )
9/modulate = Color( 1, 1, 1, 1 )
9/region = Rect2( 512, 256, 256, 160 )
9/tile_mode = 0
9/occluder_offset = Vector2( 0, 0 )
9/navigation_offset = Vector2( 0, 0 )
9/shape_offset = Vector2( 0, 0 )
9/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
9/shape_one_way = false
9/shape_one_way_margin = 0.0
9/shapes = [  ]
9/z_index = 0
10/name = "isometriclandscape.png 10"
10/texture = ExtResource( 1 )
10/tex_offset = Vector2( 0, 96 )
10/modulate = Color( 1, 1, 1, 1 )
10/region = Rect2( 768, 256, 256, 160 )
10/tile_mode = 0
10/occluder_offset = Vector2( 0, 0 )
10/navigation_offset = Vector2( 0, 0 )
10/shape_offset = Vector2( 0, 0 )
10/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
10/shape_one_way = false
10/shape_one_way_margin = 0.0
10/shapes = [  ]
10/z_index = 0
11/name = "isometriclandscape.png 11"
11/texture = ExtResource( 1 )
11/tex_offset = Vector2( 0, 96 )
11/modulate = Color( 1, 1, 1, 1 )
11/region = Rect2( 1024, 256, 256, 160 )
11/tile_mode = 0
11/occluder_offset = Vector2( 0, 0 )
11/navigation_offset = Vector2( 0, 0 )
11/shape_offset = Vector2( 0, 0 )
11/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
11/shape_one_way = false
11/shape_one_way_margin = 0.0
11/shapes = [  ]
11/z_index = 0
12/name = "isometriclandscape.png 12"
12/texture = ExtResource( 1 )
12/tex_offset = Vector2( 0, 96 )
12/modulate = Color( 1, 1, 1, 1 )
12/region = Rect2( 1280, 256, 256, 160 )
12/tile_mode = 0
12/occluder_offset = Vector2( 0, 0 )
12/navigation_offset = Vector2( 0, 0 )
12/shape_offset = Vector2( 0, 0 )
12/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
12/shape_one_way = false
12/shape_one_way_margin = 0.0
12/shapes = [  ]
12/z_index = 0
13/name = "isometriclandscape.png 13"
13/texture = ExtResource( 1 )
13/tex_offset = Vector2( 0, 96 )
13/modulate = Color( 1, 1, 1, 1 )
13/region = Rect2( 1536, 256, 256, 160 )
13/tile_mode = 0
13/occluder_offset = Vector2( 0, 0 )
13/navigation_offset = Vector2( 0, 0 )
13/shape_offset = Vector2( 0, 0 )
13/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
13/shape_one_way = false
13/shape_one_way_margin = 0.0
13/shapes = [  ]
13/z_index = 0
14/name = "isometriclandscape.png 14"
14/texture = ExtResource( 1 )
14/tex_offset = Vector2( 0, 96 )
14/modulate = Color( 1, 1, 1, 1 )
14/region = Rect2( 0, 512, 256, 160 )
14/tile_mode = 0
14/occluder_offset = Vector2( 0, 0 )
14/navigation_offset = Vector2( 0, 0 )
14/shape_offset = Vector2( 0, 0 )
14/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
14/shape_one_way = false
14/shape_one_way_margin = 0.0
14/shapes = [  ]
14/z_index = 0
15/name = "isometriclandscape.png 15"
15/texture = ExtResource( 1 )
15/tex_offset = Vector2( 0, 96 )
15/modulate = Color( 1, 1, 1, 1 )
15/region = Rect2( 256, 416, 256, 160 )
15/tile_mode = 0
15/occluder_offset = Vector2( 0, 0 )
15/navigation_offset = Vector2( 0, 0 )
15/shape_offset = Vector2( 0, 0 )
15/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
15/shape_one_way = false
15/shape_one_way_margin = 0.0
15/shapes = [  ]
15/z_index = 0
16/name = "isometriclandscape.png 16"
16/texture = ExtResource( 1 )
16/tex_offset = Vector2( 0, 96 )
16/modulate = Color( 1, 1, 1, 1 )
16/region = Rect2( 512, 416, 256, 160 )
16/tile_mode = 0
16/occluder_offset = Vector2( 0, 0 )
16/navigation_offset = Vector2( 0, 0 )
16/shape_offset = Vector2( 0, 0 )
16/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
16/shape_one_way = false
16/shape_one_way_margin = 0.0
16/shapes = [  ]
16/z_index = 0
17/name = "isometriclandscape.png 17"
17/texture = ExtResource( 1 )
17/tex_offset = Vector2( 0, 96 )
17/modulate = Color( 1, 1, 1, 1 )
17/region = Rect2( 768, 416, 256, 160 )
17/tile_mode = 0
17/occluder_offset = Vector2( 0, 0 )
17/navigation_offset = Vector2( 0, 0 )
17/shape_offset = Vector2( 0, 0 )
17/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
17/shape_one_way = false
17/shape_one_way_margin = 0.0
17/shapes = [  ]
17/z_index = 0
18/name = "isometriclandscape.png 18"
18/texture = ExtResource( 1 )
18/tex_offset = Vector2( 0, 0 )
18/modulate = Color( 1, 1, 1, 1 )
18/region = Rect2( 1024, 416, 256, 256 )
18/tile_mode = 0
18/occluder_offset = Vector2( 0, 0 )
18/navigation_offset = Vector2( 0, 0 )
18/shape_offset = Vector2( 0, 0 )
18/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
18/shape_one_way = false
18/shape_one_way_margin = 0.0
18/shapes = [  ]
18/z_index = 0
19/name = "isometriclandscape.png 19"
19/texture = ExtResource( 1 )
19/tex_offset = Vector2( 0, 0 )
19/modulate = Color( 1, 1, 1, 1 )
19/region = Rect2( 1280, 416, 256, 256 )
19/tile_mode = 0
19/occluder_offset = Vector2( 0, 0 )
19/navigation_offset = Vector2( 0, 0 )
19/shape_offset = Vector2( 0, 0 )
19/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
19/shape_one_way = false
19/shape_one_way_margin = 0.0
19/shapes = [  ]
19/z_index = 0
20/name = "isometriclandscape.png 20"
20/texture = ExtResource( 1 )
20/tex_offset = Vector2( 0, 96 )
20/modulate = Color( 1, 1, 1, 1 )
20/region = Rect2( 1536, 416, 256, 160 )
20/tile_mode = 0
20/occluder_offset = Vector2( 0, 0 )
20/navigation_offset = Vector2( 0, 0 )
20/shape_offset = Vector2( 0, 0 )
20/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
20/shape_one_way = false
20/shape_one_way_margin = 0.0
20/shapes = [  ]
20/z_index = 0
21/name = "isometriclandscape.png 21"
21/texture = ExtResource( 1 )
21/tex_offset = Vector2( 0, 96 )
21/modulate = Color( 1, 1, 1, 1 )
21/region = Rect2( 0, 672, 256, 160 )
21/tile_mode = 0
21/occluder_offset = Vector2( 0, 0 )
21/navigation_offset = Vector2( 0, 0 )
21/shape_offset = Vector2( 0, 0 )
21/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
21/shape_one_way = false
21/shape_one_way_margin = 0.0
21/shapes = [  ]
21/z_index = 0
22/name = "isometriclandscape.png 22"
22/texture = ExtResource( 1 )
22/tex_offset = Vector2( 0, 96 )
22/modulate = Color( 1, 1, 1, 1 )
22/region = Rect2( 256, 576, 256, 192 )
22/tile_mode = 0
22/occluder_offset = Vector2( 0, 0 )
22/navigation_offset = Vector2( 0, 0 )
22/shape_offset = Vector2( 0, 0 )
22/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
22/shape_one_way = false
22/shape_one_way_margin = 0.0
22/shapes = [  ]
22/z_index = 0
23/name = "isometriclandscape.png 23"
23/texture = ExtResource( 1 )
23/tex_offset = Vector2( 0, 96 )
23/modulate = Color( 1, 1, 1, 1 )
23/region = Rect2( 512, 576, 256, 192 )
23/tile_mode = 0
23/occluder_offset = Vector2( 0, 0 )
23/navigation_offset = Vector2( 0, 0 )
23/shape_offset = Vector2( 0, 0 )
23/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
23/shape_one_way = false
23/shape_one_way_margin = 0.0
23/shapes = [  ]
23/z_index = 0
24/name = "isometriclandscape.png 24"
24/texture = ExtResource( 1 )
24/tex_offset = Vector2( 0, 96 )
24/modulate = Color( 1, 1, 1, 1 )
24/region = Rect2( 768, 576, 256, 192 )
24/tile_mode = 0
24/occluder_offset = Vector2( 0, 0 )
24/navigation_offset = Vector2( 0, 0 )
24/shape_offset = Vector2( 0, 0 )
24/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
24/shape_one_way = false
24/shape_one_way_margin = 0.0
24/shapes = [  ]
24/z_index = 0
25/name = "isometriclandscape.png 25"
25/texture = ExtResource( 1 )
25/tex_offset = Vector2( 0, 96 )
25/modulate = Color( 1, 1, 1, 1 )
25/region = Rect2( 1024, 672, 256, 160 )
25/tile_mode = 0
25/occluder_offset = Vector2( 0, 0 )
25/navigation_offset = Vector2( 0, 0 )
25/shape_offset = Vector2( 0, 0 )
25/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
25/shape_one_way = false
25/shape_one_way_margin = 0.0
25/shapes = [  ]
25/z_index = 0
26/name = "isometriclandscape.png 26"
26/texture = ExtResource( 1 )
26/tex_offset = Vector2( 0, 96 )
26/modulate = Color( 1, 1, 1, 1 )
26/region = Rect2( 1280, 672, 256, 160 )
26/tile_mode = 0
26/occluder_offset = Vector2( 0, 0 )
26/navigation_offset = Vector2( 0, 0 )
26/shape_offset = Vector2( 0, 0 )
26/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
26/shape_one_way = false
26/shape_one_way_margin = 0.0
26/shapes = [  ]
26/z_index = 0
27/name = "isometriclandscape.png 27"
27/texture = ExtResource( 1 )
27/tex_offset = Vector2( 0, 96 )
27/modulate = Color( 1, 1, 1, 1 )
27/region = Rect2( 1536, 576, 256, 192 )
27/tile_mode = 0
27/occluder_offset = Vector2( 0, 0 )
27/navigation_offset = Vector2( 0, 0 )
27/shape_offset = Vector2( 0, 0 )
27/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
27/shape_one_way = false
27/shape_one_way_margin = 0.0
27/shapes = [  ]
27/z_index = 0
28/name = "isometriclandscape.png 28"
28/texture = ExtResource( 1 )
28/tex_offset = Vector2( 0, 96 )
28/modulate = Color( 1, 1, 1, 1 )
28/region = Rect2( 0, 832, 256, 160 )
28/tile_mode = 0
28/occluder_offset = Vector2( 0, 0 )
28/navigation_offset = Vector2( 0, 0 )
28/shape_offset = Vector2( 0, 0 )
28/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
28/shape_one_way = false
28/shape_one_way_margin = 0.0
28/shapes = [  ]
28/z_index = 0
29/name = "isometriclandscape.png 29"
29/texture = ExtResource( 1 )
29/tex_offset = Vector2( 0, 128 )
29/modulate = Color( 1, 1, 1, 1 )
29/region = Rect2( 256, 768, 256, 128 )
29/tile_mode = 0
29/occluder_offset = Vector2( 0, 0 )
29/navigation_offset = Vector2( 0, 0 )
29/shape_offset = Vector2( 0, 0 )
29/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
29/shape_one_way = false
29/shape_one_way_margin = 0.0
29/shapes = [  ]
29/z_index = 0
30/name = "isometriclandscape.png 30"
30/texture = ExtResource( 1 )
30/tex_offset = Vector2( 0, 128 )
30/modulate = Color( 1, 1, 1, 1 )
30/region = Rect2( 512, 768, 256, 128 )
30/tile_mode = 0
30/occluder_offset = Vector2( 0, 0 )
30/navigation_offset = Vector2( 0, 0 )
30/shape_offset = Vector2( 0, 0 )
30/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
30/shape_one_way = false
30/shape_one_way_margin = 0.0
30/shapes = [  ]
30/z_index = 0
31/name = "isometriclandscape.png 31"
31/texture = ExtResource( 1 )
31/tex_offset = Vector2( 0, 128 )
31/modulate = Color( 1, 1, 1, 1 )
31/region = Rect2( 768, 768, 256, 128 )
31/tile_mode = 0
31/occluder_offset = Vector2( 0, 0 )
31/navigation_offset = Vector2( 0, 0 )
31/shape_offset = Vector2( 0, 0 )
31/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
31/shape_one_way = false
31/shape_one_way_margin = 0.0
31/shapes = [  ]
31/z_index = 0
