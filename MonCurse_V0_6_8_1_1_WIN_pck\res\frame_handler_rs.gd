extends Sprite

var useframe = -1 setget new_frame,get_frame
var folder = null
var specific_name = null
var json_array = []
var flip = 1
var texturesizex = 0
#var secondarytexturesizex = 0

func hflip(newflip):
	if newflip != flip:
		flip = newflip
		if flip == -1:
			offset.x = (offset.x*-1) - texturesizex
#			if secondarytexturesizex > 0:
#				$extra.offset.x = ($extra.offset.x*-1) - secondarytexturesizex
#				$extra.flip_h = newflip == -1
		else:
			offset.x = ((offset.x+texturesizex)*-1)# -300
#			if secondarytexturesizex > 0:
#				$extra.offset.x = ($extra.offset.x+secondarytexturesizex)*-1
#				$extra.flip_h = newflip == -1
		flip_h = newflip == -1
		

var json_array_pointer = []
func new_frame(newframe):
	useframe = newframe
	if folder != null and newframe > -1:
		if json_array_pointer[useframe] != null:
			texture = load(folder+json_array_pointer[useframe][0])
			texturesizex = texture.get_size().x
			offset = Vector2(json_array_pointer[useframe][1][0],json_array_pointer[useframe][1][1]) - Vector2(150,150)
			if flip == -1:
				offset.x = offset.x*-1 - texturesizex
		else:
			texture = null

func get_frame(): return useframe

enum {MAINFOLDER=0,OUTFIT=1,DEBUFF=2}
func new_part(partfolder,directoryint=0):
	if partfolder != specific_name:
		specific_name = partfolder
		match directoryint:
			MAINFOLDER:folder = "res://MC_processed/"+partfolder +"/"
			OUTFIT:folder = "res://MC_processed/outfit/"+partfolder +"/"
			DEBUFF:folder = "res://MC_processed/debuff/"+partfolder +"/"
			_:
				print("Error in new_part: Invalid directoryint:"+str(directoryint))
				return
		process_frame_json()
		json_array_pointer = json_array
#		if Playervariables.touchscreenmode == true:
		get_node("/root/Master").transmit_new_part(self.get_name(),folder)
	new_frame(useframe)

func blank():
	texture = null
	folder = null
	specific_name = null

func process_frame_json(secondary=false):
	var datafile = File.new()
	var jsonparse
	if secondary == true:
#		if secondary_folder == null:
#			return
		datafile.open(secondary_folder+secondary_specific_name+".png.json", File.READ)
	else:
		datafile.open(folder+specific_name+".png.json", File.READ)
	if datafile.is_open() == false:
		if secondary == false:
			print("frame handler couldn't find folder: "+str(folder))
			folder = null
		else:
			print("frame handler couldn't find secondary folder: "+str(secondary_folder))
			secondary_folder = null
		return
	if secondary == true:
		jsonparse = JSON.parse(datafile.get_as_text())
	else:
		jsonparse = JSON.parse(datafile.get_as_text())
#	JSON.close()
	if jsonparse.error == OK:
		if secondary == true:
			secondary_json_array = jsonparse.result
		else:
			json_array = jsonparse.result
	else:
		var _errormessage = ("JSON Parse Error: "+ str(jsonparse.error_string) + " in process_frame_json, secondary is "+str(secondary)+" for "+str(specific_name))
		if secondary == true:
			secondary_folder = null
		else:
			folder = null

func animate(secondarypartfolder,directoryint):
	secondary_specific_name = secondarypartfolder
	match directoryint:
		MAINFOLDER:secondary_folder = "res://MC_processed/"+secondarypartfolder+"/"
		OUTFIT:secondary_folder = "res://MC_processed/outfit/"+secondarypartfolder+"/"
		DEBUFF:secondary_folder = "res://MC_processed/debuff/"+secondarypartfolder+"/"
		_:
			print("Error in frame animate: Invalid directoryint:"+str(directoryint))
			return
	process_frame_json(true)
	if double_animate == true:
		$anim_timer.start(0.5)
	else:
		$extra.visible = true
		$anim_timer.start(0.833)
		max_anim_frames = 0
		for i in range (secondary_json_array.size()):
			if secondary_json_array[i] == null:
				break
			else:
				max_anim_frames += 1
		if max_anim_frames == 0:
			print("Couldn't find anim frames for:"+str(secondarypartfolder))
			max_anim_frames = 2
			$anim_timer.stop()
	hold_primary_folder = folder

var double_animate = false#false for drowsy, heat, shredded, sweaty extras. True for possession.

var max_anim_frames = 2
var anim_frame = 0
var flip_flop = false
var secondary_folder = null
var hold_primary_folder = null
var secondary_specific_name = null
var secondary_json_array = []
func _on_anim_timer_timeout():
	if double_animate == true:
		if flip_flop == true:
			folder = secondary_folder
			json_array_pointer = secondary_json_array
		else:
			folder = hold_primary_folder
			json_array_pointer = json_array
		flip_flop = !flip_flop
		new_frame(useframe)
	else:
		new_anim_frame()

func new_anim_frame():
	anim_frame = (anim_frame+1) % max_anim_frames
	if secondary_folder != null:
		if secondary_json_array[anim_frame] != null:
			$extra.texture = load(secondary_folder+secondary_json_array[anim_frame][0])
#			secondarytexturesizex = $extra.texture.get_size().x
			$extra.offset = Vector2(secondary_json_array[anim_frame][1][0],secondary_json_array[anim_frame][1][1]) - Vector2(150,150)
#			if flip == -1:
#				$extra.offset.x = $extra.offset.x*-1 - secondarytexturesizex
		else:
			$extra.texture = null
