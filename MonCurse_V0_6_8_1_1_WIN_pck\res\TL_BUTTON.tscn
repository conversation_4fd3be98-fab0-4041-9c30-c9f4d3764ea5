[gd_scene load_steps=9 format=2]

[ext_resource path="res://font/OpenSans-ExtraBold.ttf" type="DynamicFontData" id=1]
[ext_resource path="res://Assets/ui/questboard.png" type="Texture" id=2]
[ext_resource path="res://Assets/ui/translationflag.png" type="Texture" id=3]
[ext_resource path="res://font/Verily-Serif-Mono/VerilySerifMono.otf" type="DynamicFontData" id=4]
[ext_resource path="res://TL_BUTTON.gd" type="Script" id=5]
[ext_resource path="res://Assets/ui/translationboard.png" type="Texture" id=6]

[sub_resource type="DynamicFont" id=3]
size = 17
outline_size = 3
outline_color = Color( 0.4, 0.4, 0.4, 1 )
extra_spacing_top = -3
extra_spacing_bottom = -3
font_data = ExtResource( 1 )

[sub_resource type="DynamicFont" id=4]
resource_local_to_scene = true
outline_size = 2
outline_color = Color( 0.4, 0.4, 0.4, 1 )
extra_spacing_top = -3
extra_spacing_bottom = -3
font_data = ExtResource( 4 )

[node name="TL_BUTTON" type="TextureButton"]
modulate = Color( 0.392157, 0.392157, 0.784314, 0.627451 )
margin_right = -153.6
margin_bottom = -150.0
rect_min_size = Vector2( 307.2, 150 )
texture_normal = ExtResource( 2 )
expand = true
script = ExtResource( 5 )

[node name="Folder_Path" type="Label" parent="."]
anchor_left = 0.5
anchor_top = 0.05
anchor_right = 0.93
anchor_bottom = 0.45
custom_fonts/font = SubResource( 3 )
text = "Official/
English"
align = 1
valign = 1
autowrap = true

[node name="Translator_Comment" type="Label" parent="."]
self_modulate = Color( 0.737255, 0.670588, 0.584314, 1 )
anchor_left = 0.1
anchor_top = 0.52
anchor_right = 0.9
anchor_bottom = 0.75
grow_vertical = 2
custom_fonts/font = SubResource( 4 )
text = "The game's default language."
valign = 1
autowrap = true

[node name="Game_Version" type="Label" parent="."]
self_modulate = Color( 0.917647, 0.262745, 0.254902, 1 )
anchor_left = 0.55
anchor_top = 0.8
anchor_right = 0.95
anchor_bottom = 0.95
grow_horizontal = 2
grow_vertical = 2
custom_fonts/font = SubResource( 3 )
align = 1
valign = 1

[node name="Icon_Path" type="TextureRect" parent="."]
anchor_left = 0.05
anchor_top = 0.07
anchor_right = 0.47
anchor_bottom = 0.42
texture = ExtResource( 3 )
expand = true

[node name="TextureRect" type="TextureRect" parent="Icon_Path"]
show_behind_parent = true
anchor_left = -0.05
anchor_top = -0.08
anchor_right = 1.05
anchor_bottom = 1.08
texture = ExtResource( 6 )
expand = true

[connection signal="mouse_entered" from="." to="." method="_on_TL_BUTTON_mouse_entered"]
[connection signal="mouse_exited" from="." to="." method="_on_TL_BUTTON_mouse_exited"]
[connection signal="pressed" from="." to="." method="_on_TL_BUTTON_pressed"]
