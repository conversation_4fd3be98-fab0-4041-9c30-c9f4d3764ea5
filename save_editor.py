#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MonCurse 存档编辑器
基于对游戏源码的分析制作

存档文件结构：
- 第1字节：存档版本号 (当前为15)
- 第2-6字节：玩家名称长度+名称
- 第7字节：当前职业
- 第8字节：替代职业
- 第9字节：玩家碎片数量
- 后续：各种数组数据

使用方法：
python save_editor.py [存档文件路径]
"""

import struct
import sys
import os

class MonCurseSaveEditor:
    def __init__(self, save_path):
        self.save_path = save_path
        self.data = None
        self.load_save()
    
    def load_save(self):
        """加载存档文件"""
        if not os.path.exists(self.save_path):
            print(f"错误：存档文件 {self.save_path} 不存在")
            return False
        
        with open(self.save_path, 'rb') as f:
            self.data = bytearray(f.read())
        
        if len(self.data) < 10:
            print("错误：存档文件太小，可能已损坏")
            return False
        
        print(f"成功加载存档文件：{self.save_path}")
        return True
    
    def save_file(self, backup=True):
        """保存存档文件"""
        if backup:
            backup_path = self.save_path + ".backup"
            with open(backup_path, 'wb') as f:
                with open(self.save_path, 'rb') as orig:
                    f.write(orig.read())
            print(f"已创建备份：{backup_path}")
        
        with open(self.save_path, 'wb') as f:
            f.write(self.data)
        print(f"存档已保存：{self.save_path}")
    
    def read_pascal_string(self, offset):
        """读取Pascal字符串（长度前缀）"""
        if offset >= len(self.data):
            return "", offset
        
        length = struct.unpack('<I', self.data[offset:offset+4])[0]
        offset += 4
        
        if offset + length > len(self.data):
            return "", offset
        
        string = self.data[offset:offset+length].decode('utf-8', errors='ignore')
        return string, offset + length
    
    def write_pascal_string(self, offset, string):
        """写入Pascal字符串"""
        encoded = string.encode('utf-8')
        length = len(encoded)
        
        # 写入长度
        struct.pack_into('<I', self.data, offset, length)
        offset += 4
        
        # 确保有足够空间
        while offset + length > len(self.data):
            self.data.extend(b'\x00' * 100)
        
        # 写入字符串
        self.data[offset:offset+length] = encoded
        return offset + length
    
    def analyze_save(self):
        """分析存档内容"""
        if not self.data:
            return
        
        print("\n=== 存档分析 ===")
        
        # 版本号
        version = self.data[0]
        print(f"存档版本: {version}")
        
        if version == 0:
            print("这是一个新存档（空存档）")
            return
        
        # 玩家名称
        player_name, offset = self.read_pascal_string(1)
        print(f"玩家名称: '{player_name}'")
        
        if offset < len(self.data):
            current_class = self.data[offset]
            print(f"当前职业: {current_class} ({self.get_class_name(current_class)})")
            offset += 1
        
        if offset < len(self.data):
            alt_class = self.data[offset]
            print(f"替代职业: {alt_class}")
            offset += 1
        
        if offset < len(self.data):
            shards = self.data[offset]
            print(f"玩家碎片: {shards}")
            offset += 1
        
        print(f"存档大小: {len(self.data)} 字节")
        print(f"已分析: {offset} 字节")
    
    def get_class_name(self, class_id):
        """获取职业名称"""
        class_names = {
            0: "人类",
            1: "NAMAN", 
            2: "CUPID",
            3: "猫娘",
            4: "狐娘", 
            5: "羊娘",
            6: "狼娘",
            7: "小恶魔",
            8: "BUFFER"
        }
        return class_names.get(class_id, f"未知({class_id})")
    
    def modify_player_name(self, new_name):
        """修改玩家名称"""
        if not self.data or self.data[0] == 0:
            print("无法修改空存档的玩家名称")
            return False
        
        print(f"修改玩家名称为: '{new_name}'")
        self.write_pascal_string(1, new_name)
        return True
    
    def modify_shards(self, new_shards):
        """修改玩家碎片数量"""
        if not self.data or self.data[0] == 0:
            print("无法修改空存档的碎片数量")
            return False
        
        # 找到碎片位置（跳过名称和职业）
        _, offset = self.read_pascal_string(1)
        offset += 2  # 跳过当前职业和替代职业
        
        if offset < len(self.data):
            old_shards = self.data[offset]
            self.data[offset] = min(255, max(0, new_shards))  # 限制在0-255范围
            print(f"碎片数量从 {old_shards} 修改为 {self.data[offset]}")
            return True
        
        print("无法找到碎片数据位置")
        return False
    
    def modify_class(self, new_class):
        """修改当前职业"""
        if not self.data or self.data[0] == 0:
            print("无法修改空存档的职业")
            return False
        
        # 找到职业位置
        _, offset = self.read_pascal_string(1)
        
        if offset < len(self.data):
            old_class = self.data[offset]
            self.data[offset] = min(8, max(0, new_class))  # 限制在有效范围
            print(f"职业从 {old_class}({self.get_class_name(old_class)}) 修改为 {self.data[offset]}({self.get_class_name(self.data[offset])})")
            return True
        
        print("无法找到职业数据位置")
        return False

def main():
    if len(sys.argv) < 2:
        print("用法: python save_editor.py <存档文件路径>")
        print("示例: python save_editor.py moncurse/save1.dat")
        return
    
    save_path = sys.argv[1]
    editor = MonCurseSaveEditor(save_path)
    
    if not editor.data:
        return
    
    while True:
        print("\n=== MonCurse 存档编辑器 ===")
        print("1. 分析存档")
        print("2. 修改玩家名称")
        print("3. 修改碎片数量")
        print("4. 修改职业")
        print("5. 保存并退出")
        print("6. 退出（不保存）")
        
        choice = input("\n请选择操作 (1-6): ").strip()
        
        if choice == '1':
            editor.analyze_save()
        
        elif choice == '2':
            new_name = input("请输入新的玩家名称: ").strip()
            if new_name:
                editor.modify_player_name(new_name)
        
        elif choice == '3':
            try:
                new_shards = int(input("请输入新的碎片数量 (0-255): "))
                editor.modify_shards(new_shards)
            except ValueError:
                print("请输入有效的数字")
        
        elif choice == '4':
            print("职业列表:")
            for i in range(9):
                print(f"  {i}: {editor.get_class_name(i)}")
            try:
                new_class = int(input("请输入新的职业ID (0-8): "))
                editor.modify_class(new_class)
            except ValueError:
                print("请输入有效的数字")
        
        elif choice == '5':
            editor.save_file()
            print("再见！")
            break
        
        elif choice == '6':
            print("退出，未保存修改")
            break
        
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    main()
