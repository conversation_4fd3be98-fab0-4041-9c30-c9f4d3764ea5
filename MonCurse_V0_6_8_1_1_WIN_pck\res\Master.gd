extends Node

onready var ramblevolume = $SFX/ramble.volume_db

# Declare member variables here. Examples:
# var a = 2
# var b = "text"
#var Main = preload("res://Main.tscn")
var main
var currentscene
var Mainmenu = load("res://Mainmenu.tscn")#const Mainmenu = preload("res://Mainmenu.tscn")
#var mainmenu
const Specialmenu = preload("res://specialmenu.tscn")
const SettingScreen = preload("res://Settingscreen.tscn") #ConversationCG
#var musicloader = preload("res://LevelMusicLoader.tscn")

#var InstancedMusic = null

# Called when the node enters the scene tree for the first time.

#var startconversationdone = false

func reset_game_to_english(quick_purge = false):

	var playervariablescopy = load("playervariables.gd")
	var newnode = TextureRect.new()
	newnode.set_script(playervariablescopy)
	add_child(newnode)
	for variable in Playervariables.translation_node_names:
		Playervariables.set(variable,newnode.get(variable))
	
	Playervariables.modarray[Playervariables.mods.TRANSLATION] = false

	Playervariables.set("default_font_dict",newnode.get("default_font_dict"))
	
	for key in Playervariables.actual_font_dict.keys():
		Playervariables.default_font_dict[key] = Playervariables.load_external_font(Playervariables.actual_font_dict[key])

	modify_fonts(true)

	newnode.queue_free()
	
	Playervariables.current_translation_code = Playervariables.TL_DEFAULT
	Playervariables.savevalue("Translation","Lasttranslation",Playervariables.TL_DEFAULT)
	
	translate_main_menu_via_master(true,quick_purge)

func _notification(what):
	if what == MainLoop.NOTIFICATION_WM_FOCUS_IN:
#		print("focus in")
#		AudioServer.set_bus_volume_db(Playervariables.Masterbus,linear2db(float(Playervariables.Mastervolume)/100))
		AudioServer.set_bus_mute(Playervariables.Masterbus,false)
		yield(get_tree(),"idle_frame")
		get_tree().get_root().set_disable_input(false)
	elif what == MainLoop.NOTIFICATION_WM_FOCUS_OUT:
		get_tree().get_root().set_disable_input(true)
		AudioServer.set_bus_mute(Playervariables.Masterbus,true)
#		print("focus out")

enum{CONV=0,PLAY=1,MENU=2}
var touchoverlay = null
func touch_mode_switch(num):
	if touchoverlay != null:
		touchoverlay.mode_switch(num)
func show_touch_overlay():
	if touchoverlay != null:
		touchoverlay.show_up()

func touch_camera_alert(onoff):
	if touchoverlay != null:
		touchoverlay.camera_alert(onoff)

func modify_fonts(purge = false):
	for child in $translated.get_children():
		child.queue_free()
	var deploy = add_preload_tl("Deployables")
	if Playervariables.default_font_dict["font_attack_announce"] != null:
		var attackannounce = add_preload_tl("AnnounceAttack")
		attackannounce.get_child(0).get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_announce"].font_data
		attackannounce.get_child(0).get("custom_fonts/font").size = 30*Playervariables.default_font_dict["font_attack_announce_size"]
		var debuffbounce = add_preload_tl("DebuffTrigger")
		debuffbounce.get_node("Control/Label").get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_announce"].font_data
		debuffbounce.get_node("Control/Label").get("custom_fonts/font").size = 38*Playervariables.default_font_dict["font_attack_announce_size"]
		debuffbounce.get_node("Control/Label").get("custom_fonts/font").outline_size = 3*sqrt(Playervariables.default_font_dict["font_attack_announce_size"])
		
		deploy.get_node("VBoxContainer/description3/description").get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_announce"].font_data
		deploy.get_node("VBoxContainer/description3/description").get("custom_fonts/font").size = 32*Playervariables.default_font_dict["font_attack_announce_size"]
		deploy.get_node("VBoxContainer/description3/description").get("custom_fonts/font").outline_size = 3*Playervariables.default_font_dict["font_attack_announce_size"]
		
		var chaptertext = add_preload_tl("ChapterText")
		chaptertext.get_node("CanvasLayer/Label").get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_announce"].font_data
		chaptertext.get_node("CanvasLayer").visible = false
		
		$loadinglayer/loadingtext.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_announce"].font_data
		$loadinglayer/loadingtext.get("custom_fonts/font").size = 18*Playervariables.default_font_dict["font_attack_announce_size"]
		
		var passiveshow = add_preload_tl("Passiveshow")
		passiveshow.get_node("TextureButton/Label").get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_announce"].font_data
		passiveshow.get_node("TextureButton/Label").get("custom_fonts/font").size = 28*Playervariables.default_font_dict["font_attack_announce_size"]
#		passiveshow.get("TextureButton/Label").get("custom_fonts/font").outline_size = 2*Playervariables.default_font_dict["font_attack_announce_size"]

	if Playervariables.default_font_dict["font_debuff_description"] != null:
		var shoptab = add_preload_tl("NewDebuffDescriber")
		shoptab.get_node("Board/Info").get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_debuff_description"].font_data
		shoptab.get_node("Board/TitleBar/Title").get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_debuff_description"].font_data
		
	if Playervariables.default_font_dict["font_attack_record"] !=  null:
		var endselector = add_preload_tl("Endselector")
		endselector.get_child(0).get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_record"].font_data
		endselector.get_child(0).get("custom_fonts/font").size = 24*Playervariables.default_font_dict["font_attack_record_size"]
		
		var graph = add_preload_tl("Graph")
		graph.get_node("vbox/attackname").get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_record"].font_data
		graph.get_node("vbox/attackname").get("custom_fonts/font").size = 16*Playervariables.default_font_dict["font_attack_record_size"]
		deploy.get_node("Label3").get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_record"].font_data
		deploy.get_node("Label3").get("custom_fonts/font").size = 32*Playervariables.default_font_dict["font_attack_record_size"]
		deploy.get_node("Label3").get("custom_fonts/font").outline_size = 3*Playervariables.default_font_dict["font_attack_record_size"]
		
		var enemydebuff = add_preload_tl("Enemydebuff")
		enemydebuff.get_child(0).get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_record"].font_data
		enemydebuff.get_child(0).get("custom_fonts/font").size = 60*Playervariables.default_font_dict["font_attack_record_size"]
		
		var reticule = add_preload_tl("enemyreticule")
		reticule.get_node("Sprite/Label").get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_record"].font_data
		reticule.get_node("Sprite/Label").get("custom_fonts/font").size = 28*Playervariables.default_font_dict["font_attack_record_size"]
		
	if Playervariables.default_font_dict["font_speech_default"] != null:
		var bubble = add_preload_tl("Speechbubble")
#		bubble.get_node("NinePatchRect/RichTextLabel").set("custom_fonts/normal_font", Playervariables.default_font_dict["font_speech_default"])
		bubble.get_node("NinePatchRect/RichTextLabel").get("custom_fonts/normal_font").font_data = Playervariables.default_font_dict["font_speech_default"].font_data
	
	if Playervariables.default_font_dict["font_move_names"] != null:
		deploy.get_node("Label").get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_move_names"].font_data
		deploy.get_node("Label").get("custom_fonts/font").size = 32*Playervariables.default_font_dict["font_move_names_size"]
		deploy.get_node("Label").get("custom_fonts/font").outline_size = 2*Playervariables.default_font_dict["font_move_names_size"]
		
	if Playervariables.default_font_dict["font_event_messages"] != null:
		var pop = add_preload_tl("Pop")
		pop.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_event_messages"].font_data
		var newtext = add_preload_tl("Eventtext")
		newtext.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_event_messages"].font_data
	
	if purge == true:
		for child in $translated.get_children():
			child.queue_free()

func update_error_log_via_master():
	if currentscene != null and currentscene.has_method("check_errors"):
		Playervariables.tlerrorlog = Playervariables.queuetllog.duplicate()
		Playervariables.queuetllog = []
		currentscene.check_errors()

func translate_main_menu_via_master(reset = false,quick_purge = false):
	if currentscene != null and currentscene.has_method("translate_main_menu"):
		currentscene.translate_main_menu(reset,quick_purge)

func add_preload_tl(string):
	var object = load(Mainpreload.get(string)).instance()
	object.visible = false
	$translated.add_child(object)
	return object

func _exit_tree():
	if Playervariables.lastloadedvariables == -1:
		Playervariables.lastloadedvariables = 0
		Playervariables.savevalue("Crash","Loadedvariables",0)

var linux_mode = false
func _ready():
#	print("Game loaded in " + str(OS.get_ticks_msec()/1000.0) + " seconds.")
	OS.min_window_size = Vector2(600,350)
#	print("Operating system: "+str(OS.get_name()))
	if OS.has_feature("Windows"):
		Playervariables.queuetllog.append("Windows")
	elif OS.has_feature("OSX"):
		pass
	elif OS.has_feature("Android"):
		Playervariables.queuetllog.append("android")
	elif OS.has_feature("X11"):
		Playervariables.queuetllog.append("linuxX11")
	match OS.get_name().substr(0,3):
		"Windows", "UWP":pass
#	        print("Windows")
		"macOS":pass
#	        print("macOS")
#		"Linux", "FreeBSD", "NetBSD", "OpenBSD", "BSD":
		"Lin", "Fre", "Net", "Ope", "BSD":
			linux_mode = true
#			Playervariables.queuetllog.append("OS is: Linux, apparently. It should be, since this is a linux test.")
#	        print("Linux/BSD")
		"And":
			touchoverlay = load(Mainpreload.touchoverlay).instance()
			add_child(touchoverlay)
			preloadsequenceconversation.append("TouchScreen")
		_:
			var dir = Directory.new()
			if dir.file_exists("res://fix_linux.dumb") or OS.has_feature("X11"):
				linux_mode = true
			elif OS.has_feature("Android"):
				touchoverlay = load(Mainpreload.touchoverlay).instance()
				add_child(touchoverlay)
				preloadsequenceconversation.append("TouchScreen")
#				Playervariables.queuetllog.append("linuxX11")
#			if OS.has_feature("linux"):
#				Playervariables.queuetllog.append("OS is: Linux, apparently. It should be, since this is a linux test.")
#	        print("Android")
#	    "iOS":
#	        print("iOS")
#	    "Web":
#	        print("Web")
	Input.set_custom_mouse_cursor(load(Mainpreload.cursorpoint))
	Playervariables.christmas = Time.get_date_dict_from_system()["month"] == 12
	var mainmenu = Mainmenu.instance()
	add_child(mainmenu)
	currentscene = mainmenu
#	$Popup.visible = false
#	$menumusic.play()
#	var InstancedMusic = musicloader.instance()
#	yield(get_tree(),"idle_frame")
#	AudioServer.set_bus_volume_db(Playervariables.Masterbus,linear2db(Playervariables.Mastervolume/100))
#	AudioServer.set_bus_volume_db(Playervariables.Musicbus,linear2db(Playervariables.Musicvolume/100))
#	AudioServer.set_bus_volume_db(Playervariables.SFXbus,linear2db(Playervariables.SFXvolume/100))
#	print(AudioServer.get_bus_volume_db(Playervariables.Masterbus))
	Playervariables.initiate_game()
	mainmenu.setname()
	mainmenu.check_errors()
	$loadinglayer/LoadingVeil.visible = false
	$loadinglayer/LoadingVeil/TextureRect.visible = true
#	if Playervariables.lastloadedvariables == 0 and Playervariables.touchscreenmode == true:
#		Playervariables.lastloadedvariables = -1
#		Playervariables.savevalue("Crash","Loadedvariables",Playervariables.lastloadedvariables)
	
	$loadinglayer/loadingtext.set_text(Playervariables.veiltextdict["loading"])

	if Playervariables.lastloadedvariables < 0 and Playervariables.touchscreenmode == true:
		if Playervariables.lastloadedvariables == -1:
			add_child(load("RAMmenu.tscn").instance())
	else:
		preload_procedure()
	
#	update_error_log_via_master()

var loadsum = -1
func preload_procedure():#override = false):
	if preloaded == false:
		preloaded = true
		preloadscenes(preloadsequencecore)
		filestocore = preloadsequencecore.size()
		preloadscenes(preloadsequenceconversation)
		filestoconv = filestocore + preloadsequenceconversation.size()
		preloadscenes(preloadsequencemain)
		filestomain = filestoconv + preloadsequencemain.size()
		if linux_mode == true:
			if Playervariables.mobile_ads == true:
				preloadscenes(preloadsequencefinal+preloadsequenceparish+preloadsequencevillage+preloadsequencea11+preloadsequencedesert)
			else:
				preloadscenes(preloadsequencefinal+preloadsequenceparish+preloadsequencevillage+preloadsequencea11)
		else:
			preloadscenes(preloadsequencefinal)
		filestofinal = filestomain + preloadsequencefinal.size()
		filesleft = filestoconv #NOT files to core becauuuse. one  bar.
		loadingstagestring = "Conversation Files"
		subsectionfilesleft = filesleft
		if Playervariables.touchscreenmode == true:
			loadsum = filestocore+filestoconv+filestomain+filestofinal
			if Playervariables.lastloadedvariables < loadsum:
				Playervariables.lastloadedvariables = -1
				Playervariables.savevalue("Crash","Loadedvariables",-1)
	#		if override == true:
	#			Playervariables.lastloadedvariables = loadsum
	#			Playervariables.savevalue("Crash","Loadedvariables",loadsum)

var preloaded = false
func unpreload():
	if preloaded == true:
		preloaded = false
		unloadscenes([],true)
		if awaitingfiles.size() > 0:
			awaitingfiles = []
			queueing = false
			hideloading(true)
			$loadinglayer/hbox.set_value(100)


var filestocore = 0;var filestoconv = 0;var filestomain = 0;var filestofinal = 0
var filesleft = 99

enum {VILLAGELOAD,PARISHLOAD,EVERYLOAD,A11LOAD,DESERTLOAD}
var preloadset = VILLAGELOAD
func parish_village_preload_switch(loadcase):
	if linux_mode == true:
		return
#		loadcase = EVERYLOAD
	if Playervariables.lastloadedvariables < 0:
		return
	elif preloadset != loadcase:
		match loadcase:
			EVERYLOAD:
				if Playervariables.mobile_ads == true:
					preloadscenes(preloadsequenceparish+preloadsequencevillage+preloadsequencea11+preloadsequencedesert)
				else:
					preloadscenes(preloadsequenceparish+preloadsequencevillage+preloadsequencea11)
			A11LOAD:
				unloadscenes(preloadsequencedesert+preloadsequencevillage+preloadsequenceparish,false)
				preloadscenes(preloadsequencea11)
			PARISHLOAD:
				unloadscenes(preloadsequencedesert+preloadsequencevillage+preloadsequencea11,false)
				preloadscenes(preloadsequenceparish)
			VILLAGELOAD:
				unloadscenes(preloadsequencedesert+preloadsequenceparish+preloadsequencea11,false)
				preloadscenes(preloadsequencevillage)
			DESERTLOAD:
				preloadscenes(preloadsequencedesert)
#			print("switched to village")
		preloadset = loadcase

func show_disclaimer(truefalse = false,language_only = false):
	if truefalse == true:
		var disclaimer = load("res://disclaimermenu.tscn").instance()
		if language_only == true:
			disclaimer.get_node("disclaimermenu").visible = false
			disclaimer.get_node("languagemenu/tools").visible = true
			disclaimer.get_node("languagemenu/tools/tooltext").set_text(Playervariables.veiltextdict["translationtools"])
		else:
			$LevelMusicLoader.stop_music()
		add_child(disclaimer)
	else: #disclaimer will call show disclaimer false when closed
		if language_only == false:
			if self.has_node("Mainmenu"):
				get_node("Mainmenu").get_node("translationbutton/firstzoom").play("firstplay")
		$LevelMusicLoader.set_music(0)

func _input(event):
	if event.is_action_pressed("ui_fullscreen"):
		OS.set_window_fullscreen(!OS.window_fullscreen)
		Playervariables.fullscreen = OS.window_fullscreen
#		Playervariables.savevalue("Checkboxes","Fullscreen",OS.window_fullscreen)

func start_conversation(speechdictstring):
	var newconversation = Playervariables.process_speechdict(speechdictstring)
	get_node("ConvoV1Holder/ConversationV1").converse(newconversation,0)

var firstload = true
func start_game(special = 0):
	Playervariables.resetvariables()
	if special == 1: #debugstart
		master_event(0,3)
	else:
		if (Playervariables.newsave == true or Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] < 2) and special == 0:
			firstload = false
			if Playervariables.newsave == true or Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] == 0:
				master_event(0)
			elif Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] == 1:
				master_event(0,1)
			else:
				print("this should not occur, failsafe in master.gd")
				master_event(1,2)
	#		var seekstring = "res://PreloadScenes/" + preloadsequenceconversation[-1] + ".tscn"
	#		if awaitingfiles.find(seekstring) != -1:
	#			$loadinglayer/LoadingVeil.visible = true
	#			while awaitingfiles.find(seekstring) != -1:
	#				yield(get_tree(),"idle_frame")
	#				yield(get_tree(),"idle_frame")
	#			$loadinglayer/LoadingVeil.visible = false
	#		var newscene = load(Mainpreload.ConversationV1).instance()
	#		add_child(newscene)
	#		var newconversation = Playervariables.process_speechdict("voiceintroconversation")
	#		newscene.converse(newconversation,0)
	#		Playervariables.call("resetvariables")
	#		currentscene = newscene
		else:
			if firstload == true:
				firstload = false
				master_event(0,2)
			else:
				master_event(1,2)
	#		Playervariables.testtimer = OS.get_ticks_msec()
	#		if self.has_node("ConversationV1"):
	#			self.get_node("ConversationV1").queue_free()
	#	#	Playervariables.nextlocation = Playervariables.ForestZone #change this to change where the player starts!
	#	#	$LevelMusicLoader.stop_music()
	#	#	if Playervariables.nextlocation == ForestZone:
	#	#		$menumusic.stop()
	#		if special == 0:
	#			Playervariables.homefirstload = true
	#			Playervariables.nextlocation = Playervariables.Home
	#		var seekstring = "res://PreloadScenes/" + preloadsequencemain[-1] + ".tscn"
	#		if awaitingfiles.find(seekstring) != -1:
	#			$loadinglayer/LoadingVeil.visible = true
	#			while awaitingfiles.find(seekstring) != -1:
	#				yield(get_tree(),"idle_frame")
	#				yield(get_tree(),"idle_frame")
	#			$loadinglayer/LoadingVeil.visible = false
	#		main = load(Mainpreload.Main).instance()
	#		Playervariables.call("resetvariables")
	#	#	print("Time to instance new level: "+str(OS.get_ticks_msec() - Playervariables.testtimer))
	#		add_child(main)
	#	#	print("Then add it: "+str(OS.get_ticks_msec() - Playervariables.testtimer))
	#	#	print("Then remove main menu: "+str(OS.get_ticks_msec() - Playervariables.testtimer))
	#		currentscene = main
	if self.has_node("Mainmenu"):
		get_node("Mainmenu").queue_free()
	#	print("Overall new scene timer: "+str(OS.get_ticks_msec() - Playervariables.testtimer))

var zero = 0.0
func close_game():
	set_process(false)
	$PreloadScenes.thread_on = false
	yield(get_tree(),"idle_frame")
	if linux_mode == true:
#	var _boring_solution = OS.kill(int($PreloadScenes.thread.get_id()))
		var consider_the_ramifications = 1.0 / (zero)
		print(consider_the_ramifications)
#	match OS.get_name().substr(0,3):
#		"Linux", "FreeBSD", "NetBSD", "OpenBSD", "BSD",_:
#			var _consider_the_rammifications = 1.0 / (zero)
	get_tree().quit()
#
#enum{DEBUGBONUS=0}
#func popup(num):
#	if currentscene == null:
#		print("serious popup error, there's no scene??? master.gd")
#		return
#	match num:
#		DEBUGBONUS:
#			$Popup.visible = true
#	var newsize = currentscene.get_viewport_rect().size
#	var proportionfull = (newsize.length()/1024) #DOES NOT HAPPEN WHEN VIEWPORT SIZE CHANGED!
#	$Popup/veil/veilbutton2/Label.get("custom_fonts/font").size = int(20*proportionfull)
#	$Popup/veil/Label.get("custom_fonts/font").size = int(((24.0*proportionfull) + 24.0) /2)

var queue_special_convo = ""
var queue_convo_point = 0
func set_special_convo(string,startnum):
	queue_special_convo = string
	queue_convo_point = startnum
#var DESERTLOADconvscroll = -1
enum special{NONE,TUTORIALHALFTALK,HOMEFIRSTLOAD,TUTORIAL1,TUTORIAL2,RESETVARIABLES,USELOADCONVERSATION,FORCEDARKROOM}
const debugconversation = "badend/scenespecialfoxgirl"#"badend/badendspecialramgirl"#"unused/imptest"#"badend/badendspecialwerewolf"
var startdebugpoint = 0
func master_event(event,special = 0):
#	Playervariables.testtimer = OS.get_ticks_msec()
	match event:
		0: #Start with ConversationV1
			if currentscene != null:
				currentscene.queue_free()
				currentscene = null
			var seekstring = "res://PreloadScenes/" + preloadsequenceconversation[-1] + ".tscn"
			if awaitingfiles.find(seekstring) != -1:
				hideloading(false)
				while awaitingfiles.find(seekstring) != -1:
					yield(get_tree(),"idle_frame")
#					yield(get_tree(),"idle_frame")
				hideloading(true)
			specialloadposition(true)
#			Playervariables.corruptiondict["ears"] = Playervariables.raceKITSUNE #temporary for debug
#			Playervariables.corruptiondict["tail"] = Playervariables.raceKITSUNE #temporary for debug
			var newscene = load(Mainpreload.ConversationV1).instance()
			$ConvoV1Holder.add_child(newscene)
			match special:
				4:#engage conversationv1 from main, for example ramspecbadend
					var newconversation = Playervariables.process_speechdict(queue_special_convo)
					newscene.converse(newconversation,queue_convo_point)
					queue_special_convo = ""
					queue_convo_point = 0
				3:
#					Playervariables.corruptiondict["ears"] = Playervariables.raceKITSUNE #temporary for debug
#					Playervariables.corruptiondict["tail"] = Playervariables.raceKITSUNE #temporary for debug
#					Playervariables.possessionrank = true
					if debugconversation.length() > 3 and debugconversation.substr(0,4) == "load":
						if Playervariables.AltClass == 1:
							Playervariables.gameoverrank = true
							startdebugpoint = 3
#					Playervariables.corruptiondict["armright"] = Playervariables.raceNEKO
					var newconversation = Playervariables.process_speechdict(debugconversation)
					newscene.converse(newconversation,startdebugpoint)
					if debugconversation.length() > 3 and debugconversation.substr(0,4) == "load":
						startdebugpoint = (startdebugpoint+1)%5
				2:
					var talkstring
					match Playervariables.CurrentClass:
						Playervariables.raceKITSUNE:
							if Playervariables.AltClass == 1:
								talkstring = "foxelite"
								Playervariables.gameoverrank = true
								Playervariables.possessionrank = true
							else:
								talkstring = "fox"
								Playervariables.gameoverrank = false
								Playervariables.possessionrank = false
						Playervariables.raceRAM:
							if Playervariables.AltClass == 1:
								talkstring = "ramelite"
								Playervariables.gameoverrank = true
#								DESERTLOADconvscroll = (DESERTLOADconvscroll+1) % 5
							else:
								talkstring = "ram"
								Playervariables.gameoverrank = false
						Playervariables.raceNEKO:talkstring = "cat"
						Playervariables.raceWOLF:
							talkstring = "wolf"
						_: talkstring = "voice"
					if Playervariables.consent == false:
						talkstring = "voice"
					var newconversation = Playervariables.process_speechdict("load/loadconversations"+talkstring)
					if newconversation == null or newconversation.has("scenario") == false:
						print("newconversation error in master for loading, check master.gd")
						newscene.queue_free()
						master_event(1,2)
						Playervariables.gameoverrank = false
						return
					else:
						randomize()
						if newconversation.get("scenario").has("randstarts"):
							var usenum = 0
							if Playervariables.force_load_convo > -1 and newconversation.has(str(Playervariables.force_load_convo*100)):
								usenum = Playervariables.force_load_convo
								Playervariables.force_load_convo = -1
							else:
								usenum = randi()%(int(newconversation.get("scenario").get("randstarts")))
								if usenum == Playervariables.last_load_convo:
									usenum = (usenum+1) % int(newconversation.get("scenario").get("randstarts"))
								Playervariables.last_load_convo = usenum
								Playervariables.savevalue("SaveFiles","Lastconvo",Playervariables.last_load_convo)
							newscene.converse(newconversation,usenum)
							if talkstring == "cat" and Playervariables.CurrentClass == Playervariables.raceNEKO and Playervariables.AltClass == 1:
								newscene.force_rank()
#							newscene.converse(newconversation,(DESERTLOADconvscroll))
#							newscene.converse(newconversation,1)
						else:
							newscene.converse(newconversation,0)
					Playervariables.gameoverrank = false
				0,1:
					var newconversation = Playervariables.process_speechdict("voiceintroconversation")
					parish_village_preload_switch(VILLAGELOAD)
					hideloading(true) #prevents the player from seeing it's background loading when you first play
					if special == 1:
						newscene.converse(newconversation,9)
					else:
						newscene.converse(newconversation,0)
					Playervariables.call("resetvariables")
			currentscene = newscene
			touch_mode_switch(CONV)
		1: #level cleared
#			print("1 cse."+str(special))
			if special == 6 and firstload == true:
				firstload = false
				master_event(0,2)
				return
			if currentscene != null:
				currentscene.queue_free()
				currentscene = null
			var seekstring = "res://PreloadScenes/" + preloadsequencemain[-1] + ".tscn"
			if Playervariables.stagenum >= 2:
				seekstring = "res://PreloadScenes/" + preloadsequencefinal[-1] + ".tscn"
			if awaitingfiles.find(seekstring) != -1:
				specialloadposition(false)
				hideloading(false)
				while awaitingfiles.find(seekstring) != -1:
					yield(get_tree(),"idle_frame")
#					yield(get_tree(),"idle_frame")
				hideloading(true)
			match special:
				2:
					Playervariables.homefirstload = true
#					if Playervariables.testbuild == false:
#						Playervariables.nextlocation = Playervariables.Village
#						Playervariables.nextexit = 3
#					else:
					Playervariables.call("resetvariables")
					if Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] >= Playervariables.tutorialstages.SHIKATALK and Playervariables.CurrentClass == Playervariables.raceRAM and Playervariables.Endings[Playervariables.EndingsRef.RAMGIRL] > 0:
						Playervariables.nextlocation = Playervariables.RamHome
						Playervariables.nextexit = 0
					else:
						Playervariables.nextlocation = Playervariables.Village
						Playervariables.nextexit = 3
#					if (Playervariables.nextlocation in [Playervariables.Village,Playervariables.Home]) == false:
#						Playervariables.nextlocation = Playervariables.LevelSelect1
				3:
					Playervariables.nextlocation = Playervariables.Tutorial1
				4:
					Playervariables.nextlocation = Playervariables.Tutorial2
				5:
					Playervariables.call("resetvariables")
				7:
					Playervariables.nextlocation = Playervariables.DarkRoom
					firstload = true
			if special != 5 and Playervariables.nextlocation in [Playervariables.Home,Playervariables.LevelSelect1] and Playervariables.awaitingconversation == "":
				Playervariables.stagenum = 0
				Playervariables.call("resetvariables")
			match Playervariables.nextlocation:
				Playervariables.RouteA11:parish_village_preload_switch(A11LOAD)
				Playervariables.NewVillage:parish_village_preload_switch(PARISHLOAD)#does not include wolfcombat because if the player gets the werewolf ending... it'll end up adding RAM
				Playervariables.Village,Playervariables.Home:parish_village_preload_switch(VILLAGELOAD)
				Playervariables.ImpDebug,Playervariables.GlassedDesert:parish_village_preload_switch(DESERTLOAD)
			main = load(Mainpreload.Main).instance()
			add_child(main)
#			if currentmap != null:
#				main.tier = currentmap.tier
			currentscene = main
			yield(currentscene, "ready")
			touch_mode_switch(PLAY)
		2: #quit to main menu
			remove_map()
			parish_village_preload_switch(VILLAGELOAD)
			hideloading(true)
			if Playervariables.gallery_mode == true:
				Playervariables.gallery_mode = false
				if Playervariables.GalleryClass > -1:
					Playervariables.AltClass = Playervariables.GalleryClass
					Playervariables.GalleryClass = -1
			if currentscene != null:
				currentscene.queue_free()
				currentscene = null
			else:
				print("Trying to quit to main menu but it already deleted itself first...")
			if special == 1:
				Playervariables.CurrentClass = 0
				Playervariables.AltClass = 0
				Playervariables.resetvariables(true)
			else:
				Playervariables.rope_quest = -1
			if Playervariables.queue_save == true:#put this after both resetvariables and galley_mode = false
				Playervariables.saveprogress()
			$LevelMusicLoader.set_music(0)
#			$menumusic.play()
			Playervariables.stagenum = 0
			var mainmenu = Mainmenu.instance()
			add_child(mainmenu)
			currentscene = mainmenu
			mainmenu.setname()
			touch_mode_switch(MENU)
#	print("Overall new scene timer: "+str(OS.get_ticks_msec() - Playervariables.testtimer))

func fullscreentest():
	add_child(Specialmenu.instance())

const preloadsequencecore = ["Global","GUI"]
var preloadsequenceconversation = ["Rules","RulesMale","Voice","ConversationCG"]
const preloadsequencemain = ["MainScene","Deployables","GalleryCG","RulesSprite","Qades"]
const preloadsequencefinal = ["CatKnight","RamGirl","FoxGirl","WereWolf","SunsetZone","NightZone"]
const preloadsequenceparish = ["Villagers"]
const preloadsequencea11 = ["DragonHarpy"]
const preloadsequencevillage = ["Voice","Qades"]
const preloadsequencedesert = ["ImpGirl"]

var filepathsloaded = [] #NOT FULL PATH
var awaitingfiles = [] #FULL PATH
#var startingfilesint = 0
func preloadscenes(filepatharray):
#	currentpatharray = filepatharray
	var dir = Directory.new()
	dir.open("res://PreloadScenes")
	if awaitingfiles.size() <= 0:
		$PreloadScenes.start()
		$loadinglayer/hbox.set_value(0)
	for file in filepatharray:
		if filepathsloaded.find(file) == -1 and dir.file_exists(file+".tscn"):
			var fullpath = "res://PreloadScenes/"+file+".tscn"
			$PreloadScenes.queue_resource(fullpath, false)
			filepathsloaded.append(file)
			awaitingfiles.append(fullpath)
			queueing = true
			set_process(true)
		else:
#			print("Attempted to load file: " +file+" . File either does not exist or is already loaded. Testing...")
			if dir.file_exists(file+".tscn"):
				pass
#				print("The file exists, it must've already been loaded.")
			else:
#				print("The file doesn't exist, idiot.")
				print("ERROR. Attempted to load file: " +file+" . However, file either does not exist.")
#	startingfilesint = awaitingfiles.size()
#	for filepath in filepatharray:
#		if filepathsloaded.has(filepath) == false:
#			var newscene = load("res://PreloadScenes/"+str(filepath)+".tscn").instance()
#			$PreloadScenes.add_child(newscene)
#			filepathsloaded.append(filepath)
#		else:
#			print("Tried to load an already loaded scene in the master scene.")
func unloadscenes(filepatharray,all = false):
	if all == true:
		filepatharray = []
		var namearray = $PreloadScenes.get_children()
		for node in namearray:
			filepatharray.append(node.get_name())
			filepatharray = $PreloadScenes.get_children()
#	print("unloadpatharray:" +str(filepatharray))
	for filepath in filepatharray:
		$PreloadScenes.cancel_resource(filepath)
		if $PreloadScenes.get_node_or_null(filepath) != null: #warning! This uses node NAME, not the actual scene! fix later?
			$PreloadScenes.get_node(filepath).queue_free()
#			print("removed:"+str(filepath))
			if filepathsloaded.find(filepath) != -1:
				filepathsloaded.remove(filepathsloaded.find(filepath))
#		else:
#			print("Tried to unload an unloaded scene in the master scene.")
#	print("Remaining after unloadscenes: "+str($PreloadScenes.get_children()))
	$PreloadScenes.start()

func hideloading(yesno = true):
	if yesno == true:		
		$loadinglayer/LoadingVeil.visible = false
		$loadinglayer/hbox.visible = false
		$loadinglayer/loadingtext.visible = false
	else:
		keep100 = false
		$loadinglayer/LoadingVeil.visible = true
		$loadinglayer/hbox.visible = true
		$loadinglayer/loadingtext.visible = true
func specialloadposition(yesno = true):
	if awaitingfiles.size() > 0:
		if yesno == true:
			keep100 = false
			$loadinglayer/loadingtext.visible = false
			$loadinglayer/LoadingVeil.visible = false
			$loadinglayer/hbox.visible = true
			$loadinglayer/hbox.set_modulate(Color(0.5,0.5,1,0.3))
			$loadinglayer/hbox.grow_horizontal = Control.GROW_DIRECTION_END
			$loadinglayer/hbox.grow_vertical = Control.GROW_DIRECTION_END
			$loadinglayer/hbox.anchor_left = 0
			$loadinglayer/hbox.anchor_right = 0
			$loadinglayer/hbox.anchor_top = 0
			$loadinglayer/hbox.anchor_bottom = 0
			$loadinglayer/hbox.margin_top = 0
			$loadinglayer/hbox.margin_bottom = $loadinglayer/hbox.rect_size.y - 10
			$loadinglayer/hbox.margin_right = $loadinglayer/hbox.rect_size.x #$loadinglayer/hbox.rect_size.x/2
			$loadinglayer/hbox.margin_left = 0#-$loadinglayer/hbox.rect_size.x/2
		else:
			$loadinglayer/hbox.set_modulate(Color(1,1,1,1))
			$loadinglayer/hbox.grow_horizontal = Control.GROW_DIRECTION_BEGIN
			$loadinglayer/hbox.grow_vertical = Control.GROW_DIRECTION_BEGIN
			$loadinglayer/hbox.anchor_left = 1
			$loadinglayer/hbox.anchor_right = 1
			$loadinglayer/hbox.anchor_top = 1
			$loadinglayer/hbox.anchor_bottom = 1
			$loadinglayer/hbox.margin_top = -90#-$loadinglayer/hbox.rect_size.y
			$loadinglayer/hbox.margin_bottom = -26
			$loadinglayer/hbox.margin_right = -35
			$loadinglayer/hbox.margin_left = -294#-$loadinglayer/hbox.rect_size.x

var keep100 = false
var subsectionfilecount = 0
var subsectionfilesleft = 99
var filecountcheck = 0
var queueing = true
var loadingstagestring = "Core Files"
func _process(_delta):
	# Returns true if a resource is done loading and ready to be retrieved.
#	var filecountcheck = filestofinal-awaitingfiles.size()
#	var DEBUG = false
	for file in awaitingfiles:
		if $PreloadScenes.is_ready(file):
			var loadedresource = $PreloadScenes.get_resource(file).instance()
			$PreloadScenes.add_child(loadedresource)
			awaitingfiles.remove(awaitingfiles.find(file))
			filecountcheck += 1
			subsectionfilecount += 1
			if awaitingfiles.size() == 0:
				set_process(false)
				queueing = false
				hideloading(true)
				$loadinglayer/hbox.set_value(100)
				if Playervariables.touchscreenmode == true and loadsum > -1 and Playervariables.lastloadedvariables < loadsum:
					Playervariables.lastloadedvariables = loadsum
					Playervariables.savevalue("Crash","Loadedvariables",loadsum)
				$PreloadScenes.thread_on = false
			elif filecountcheck >= filesleft and filesleft != filestofinal:
#				if filecountcheck >= filestocore:
				if filecountcheck >= filestoconv:
					if filecountcheck >= filestomain:
						filesleft = filestofinal
						subsectionfilesleft = filestofinal-filestomain
						loadingstagestring = "All Remaining Files"
						subsectionfilecount = 0
						if $loadinglayer/hbox.visible == true and $loadinglayer/loadingtext.visible == false:
							hideloading(true)
						keep100 = true
					else:
						filesleft = filestomain
						subsectionfilesleft = filestomain-filestoconv
						loadingstagestring= "Stage Files"
						subsectionfilecount = 0
						keep100 = true
#					else:
#						filesleft = filestoconv
#						loadingstagestring = "Conversation Files"
#						subsectionfilecount = 0
#						keep100 = true
#			else:
#				$loadinglayer/hbox.set_value(0)
#				print("Progress is 0")
	if keep100 == false:
		if awaitingfiles.size() > 0:
			$loadinglayer/hbox.set_value(round(100*(subsectionfilecount)/clamp(subsectionfilesleft,1,100)))
	else:
		$loadinglayer/hbox.set_value(100)
#	if DEBUG == true:
#		print("keep100:"+str(keep100)+"..."+"Awaiting files:"+str(awaitingfiles)+"..."+str(subsectionfilecount)+"<-filecount, files left->"+str(subsectionfilesleft))
#		print("final value: "+str($loadinglayer/hbox.value))
#	var loadingstring = "Now loading: "
#	if Playervariables.debugmodeon == true:
#		for file in awaitingfiles:
#			loadingstring += file
#			loadingstring += ", "
#		loadingstring = loadingstring.rstrip(2)
#		loadingstring += "."
#	elif $loadinglayer/LoadingVeil.is_visible() == false:
#		loadingstring = "Now preloading "+loadingstagestring+" in the background."
#	else:
#		loadingstring = "Now loading "+loadingstagestring+", this should only take a moment."

#
#func _on_veilbutton1_pressed():
##	Playervariables.debugallowbonus = true
#	$Popup.visible = false
#	if "escalationscreen" in currentscene:
#		currentscene.escalationscreen.temporarydisable = false
#	else:
#		print("Error MAY CAUSE SOFTLOCK: Could not find 'escalationscreen' after debug veil.")
#func _on_veilbutton2_pressed():
##	Playervariables.debugallowbonus = false
#	$Popup.visible = false
#	if "escalationscreen" in currentscene:
#		currentscene.escalationscreen.temporarydisable = false
#	else:
#		print("Error MAY CAUSE SOFTLOCK: Could not find 'escalationscreen' after debug veil.")

var currentmap = null
func generate_new_map(num = 0,new = false,retreat = true):
	if currentmap == null:
		new = true
	if new == true:
		if mainscene_check() == true:
			currentscene.kcursoractive = false
			currentscene.get_node("CanvasLayer3/kcursor").visible = false
			Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)
		remove_map()
		currentmap = load(Mainpreload.Escalation2).instance()
		$map.add_child(currentmap)
		currentmap.generate_new_map(true,num)
#		Playervariables.playerinventory2darray = [[3,0,2],[5,0,2],[11,0,2],[13,0,2]]
#		Playervariables.impish = [[10,0,3,20],[9,0,3,10]]
#		Playervariables.identify_inventory()
	if retreat == false:
		currentmap.neutralize_tile()
	currentmap.enabledisable(true)
	if new == true and num == 4:# and Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] > 5:
		Playervariables.set_inventory_to_class(true)
		currentmap.starting_gear()

func remove_map():
	if currentmap != null:
		currentmap.queue_free()
		currentmap = null
#
#func update_quest_mission(string):
#	if currentmap != null:
#		currentmap.mission_name(string)
func update_quest(uniquenumber,onoff):
	if currentmap !=  null:
		currentmap.set_quest(uniquenumber,onoff)
func remove_chest():
	if currentmap != null:
		currentmap.remove_chest()
func remove_spawner():
	if currentmap != null:
		currentmap.remove_spawner()
func register_event_via_master(string,var_array = [],onceonly = false,important=false,leftalign = false):
	if currentscene != null and currentscene.has_method("register_event_via_main"):
		currentscene.register_event_via_main(string,var_array,onceonly,important,leftalign)

func offer_rewards(already_processed=false):
	if currentmap != null:
		currentmap.offer_rewards(already_processed)
func give_reward(num):
	if currentscene != null and currentscene.has_method("awardplayer"):
		currentscene.awardplayer(num)

func hide_springs_via_master_if_true(truefalse):
	if currentmap != null:
		currentmap.hide_springs_if_true(truefalse)

func cure_map_sprite_via_master():
	if currentmap != null:
		currentmap.cure_map_sprite()

func exit_map_checks():
	if touchoverlay != null:#Playervariables.touchscreenmode == true:
		touchoverlay.disable_overlay_camera()
func check_map_num():
	if currentmap != null:
		return currentmap.missionid
	else:
		return -1
func map_visible_check():
	if currentmap != null and currentmap.visible == true:
		return true
	else:
		return false
func mainscene_check():
	if currentscene != null and currentscene.has_method("awardplayer"):
		return true
	else:
		return false
func main_loot_gained():
	if mainscene_check() == true:
		currentscene.get_node("CanvasLayer2/Dialogue/lootchest/showloot").play("chest")

func transmit_new_part(partnode,partfolder):
	if $PreloadScenes.has_node("RulesSprite"):
		$PreloadScenes.get_node("RulesSprite").preload_new_part(partnode,partfolder)
	else:
		yield(get_tree(),"idle_frame")
		if $PreloadScenes.has_node("RulesSprite"):
			$PreloadScenes.get_node("RulesSprite").preload_new_part(partnode,partfolder)
