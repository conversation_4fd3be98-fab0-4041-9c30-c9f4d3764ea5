[gd_scene load_steps=19 format=2]

[ext_resource path="res://Assets/blacksquare.png" type="Texture" id=1]
[ext_resource path="res://Assets/ui/escalationcards/Backer.png" type="Texture" id=2]
[ext_resource path="res://Assets/ui/gobackui.png" type="Texture" id=3]
[ext_resource path="res://Assets/ui/escalationcards/lockedmeat.png" type="Texture" id=4]
[ext_resource path="res://Assets/ui/escalationcards/lockedmap.png" type="Texture" id=5]
[ext_resource path="res://escalation.gd" type="Script" id=6]
[ext_resource path="res://Assets/ui/escalationcards/Blank.png" type="Texture" id=7]
[ext_resource path="res://Assets/ui/escalationcards/BackerForest.png" type="Texture" id=8]
[ext_resource path="res://font/Audiowide-Regular.ttf" type="DynamicFontData" id=9]
[ext_resource path="res://Assets/ui/escalationcards/darkgreysquare.png" type="Texture" id=10]
[ext_resource path="res://Assets/ui/gobackuipress.png" type="Texture" id=11]
[ext_resource path="res://Assets/ui/goback.png" type="Texture" id=12]
[ext_resource path="res://font/OpenSans-Bold.ttf" type="DynamicFontData" id=13]

[sub_resource type="DynamicFont" id=1]
size = 28
outline_size = 2
outline_color = Color( 0, 0, 0, 0.705882 )
font_data = ExtResource( 9 )

[sub_resource type="DynamicFont" id=2]
size = 36
outline_color = Color( 0, 0, 0, 0.647059 )
font_data = ExtResource( 9 )

[sub_resource type="DynamicFont" id=5]
font_data = ExtResource( 9 )

[sub_resource type="Animation" id=3]
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("backer1:anchor_top")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ -0.1, 0.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("backer2:anchor_top")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ -0.1, 0.0 ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("backer3:anchor_top")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ -0.1, 0.0 ]
}

[sub_resource type="DynamicFont" id=4]
size = 20
outline_color = Color( 0, 0, 0, 0.627451 )
font_data = ExtResource( 13 )

[node name="EscalationWindow" type="Control"]
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource( 6 )

[node name="veil" type="TextureRect" parent="."]
self_modulate = Color( 1, 1, 1, 0.862745 )
anchor_left = -0.05
anchor_top = -0.05
anchor_right = 1.05
anchor_bottom = 1.05
margin_left = -7.11893
margin_top = -3.94052
margin_right = 2.88098
margin_bottom = 6.05945
texture = ExtResource( 1 )
expand = true
stretch_mode = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="whiteveil1" type="TextureRect" parent="."]
visible = false
self_modulate = Color( 1, 1, 1, 0.862745 )
anchor_right = 0.325
anchor_bottom = 1.0
margin_left = 5.0
margin_top = 5.0
margin_right = -5.0
margin_bottom = -5.0
texture = ExtResource( 10 )
expand = true
__meta__ = {
"_edit_use_anchors_": false
}

[node name="whiteveil2" type="TextureRect" parent="."]
visible = false
self_modulate = Color( 1, 1, 1, 0.862745 )
anchor_left = 0.325
anchor_right = 0.675
anchor_bottom = 1.0
margin_left = 5.0
margin_top = 5.0
margin_right = -5.0
margin_bottom = -5.0
texture = ExtResource( 10 )
expand = true
__meta__ = {
"_edit_use_anchors_": false
}

[node name="whiteveil3" type="TextureRect" parent="."]
visible = false
self_modulate = Color( 1, 1, 1, 0.862745 )
anchor_left = 0.675
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = 5.0
margin_top = 5.0
margin_right = -5.0
margin_bottom = -5.0
texture = ExtResource( 10 )
expand = true
__meta__ = {
"_edit_use_anchors_": false
}

[node name="backer1" type="TextureRect" parent="."]
modulate = Color( 0.392157, 0.392157, 0.392157, 1 )
self_modulate = Color( 0.705882, 0.705882, 0.705882, 1 )
anchor_right = 0.325
anchor_bottom = 1.0
margin_top = 15.0
margin_bottom = 5.0
rect_min_size = Vector2( 200, 540 )
texture = ExtResource( 2 )
expand = true
stretch_mode = 6
__meta__ = {
"_edit_use_anchors_": false
}

[node name="TextureRect1" type="TextureRect" parent="backer1"]
visible = false
anchor_top = 0.075
anchor_right = 1.0
anchor_bottom = 0.35
rect_min_size = Vector2( 0, 60 )
texture = ExtResource( 7 )
expand = true
stretch_mode = 6
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label" type="Label" parent="backer1/TextureRect1"]
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
margin_bottom = -54.0
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 0.901961, 0.733333, 0.698039, 1 )
custom_colors/font_color_shadow = Color( 0, 0, 0, 1 )
custom_fonts/font = SubResource( 1 )
text = "13"
align = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="TextureRect2" type="TextureRect" parent="backer1"]
visible = false
anchor_top = 0.375
anchor_right = 1.0
anchor_bottom = 0.65
rect_min_size = Vector2( 0, 60 )
texture = ExtResource( 7 )
expand = true
stretch_mode = 6
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label" type="Label" parent="backer1/TextureRect2"]
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
margin_bottom = -54.0
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 0.901961, 0.733333, 0.698039, 1 )
custom_colors/font_color_shadow = Color( 0, 0, 0, 1 )
custom_fonts/font = SubResource( 1 )
text = "13"
align = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="TextureRect3" type="TextureRect" parent="backer1"]
visible = false
anchor_top = 0.675
anchor_right = 1.0
anchor_bottom = 0.95
rect_min_size = Vector2( 0, 60 )
texture = ExtResource( 7 )
expand = true
stretch_mode = 6
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label" type="Label" parent="backer1/TextureRect3"]
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
margin_bottom = -54.0
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 0.901961, 0.733333, 0.698039, 1 )
custom_colors/font_color_shadow = Color( 0, 0, 0, 1 )
custom_fonts/font = SubResource( 1 )
text = "13"
align = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label" type="Label" parent="backer1"]
self_modulate = Color( 0.870588, 0.827451, 0.694118, 1 )
anchor_right = 1.0
margin_top = -15.0
custom_fonts/font = SubResource( 2 )
text = "LEFT"
align = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="backer2" type="TextureRect" parent="."]
self_modulate = Color( 0.705882, 0.705882, 0.705882, 1 )
anchor_left = 0.325
anchor_right = 0.675
anchor_bottom = 1.0
margin_top = 15.0
margin_bottom = 5.0
rect_min_size = Vector2( 200, 540 )
texture = ExtResource( 8 )
expand = true
stretch_mode = 6
__meta__ = {
"_edit_use_anchors_": false
}

[node name="TextureRect1" type="TextureRect" parent="backer2"]
anchor_top = 0.075
anchor_right = 1.0
anchor_bottom = 0.35
rect_min_size = Vector2( 0, 60 )
texture = ExtResource( 7 )
expand = true
stretch_mode = 6
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label" type="Label" parent="backer2/TextureRect1"]
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
margin_bottom = -54.0
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 0.901961, 0.733333, 0.698039, 1 )
custom_colors/font_color_shadow = Color( 0, 0, 0, 1 )
custom_fonts/font = SubResource( 1 )
text = "Rank X"
align = 1

[node name="TextureRect2" type="TextureRect" parent="backer2"]
anchor_top = 0.375
anchor_right = 1.0
anchor_bottom = 0.65
rect_min_size = Vector2( 0, 60 )
texture = ExtResource( 7 )
expand = true
stretch_mode = 6
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label" type="Label" parent="backer2/TextureRect2"]
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
margin_right = -0.400024
margin_bottom = -54.0
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 0.901961, 0.733333, 0.698039, 1 )
custom_colors/font_color_shadow = Color( 0, 0, 0, 1 )
custom_fonts/font = SubResource( 1 )
text = "13"
align = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="TextureRect3" type="TextureRect" parent="backer2"]
anchor_top = 0.675
anchor_right = 1.0
anchor_bottom = 0.95
rect_min_size = Vector2( 0, 60 )
texture = ExtResource( 7 )
expand = true
stretch_mode = 6
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label" type="Label" parent="backer2/TextureRect3"]
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
margin_bottom = -54.0
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 0.901961, 0.733333, 0.698039, 1 )
custom_colors/font_color_shadow = Color( 0, 0, 0, 1 )
custom_fonts/font = SubResource( 1 )
text = "13"
align = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label2" type="Label" parent="backer2"]
self_modulate = Color( 0.870588, 0.694118, 0.74902, 1 )
anchor_right = 1.0
margin_top = -15.0
custom_fonts/font = SubResource( 2 )
text = "STRAIGHT"
align = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="backer3" type="TextureRect" parent="."]
modulate = Color( 0.392157, 0.392157, 0.392157, 1 )
self_modulate = Color( 0.705882, 0.705882, 0.705882, 1 )
anchor_left = 0.675
anchor_right = 1.0
anchor_bottom = 1.0
margin_top = 15.0
margin_bottom = 5.0
rect_min_size = Vector2( 200, 540 )
texture = ExtResource( 2 )
expand = true
stretch_mode = 6
__meta__ = {
"_edit_use_anchors_": false
}

[node name="TextureRect1" type="TextureRect" parent="backer3"]
visible = false
anchor_top = 0.075
anchor_right = 1.0
anchor_bottom = 0.35
rect_min_size = Vector2( 0, 60 )
texture = ExtResource( 7 )
expand = true
stretch_mode = 6
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label" type="Label" parent="backer3/TextureRect1"]
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
margin_bottom = -54.0
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 0.901961, 0.733333, 0.698039, 1 )
custom_colors/font_color_shadow = Color( 0, 0, 0, 1 )
custom_fonts/font = SubResource( 1 )
text = "13"
align = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="TextureRect2" type="TextureRect" parent="backer3"]
visible = false
anchor_top = 0.375
anchor_right = 1.0
anchor_bottom = 0.65
rect_min_size = Vector2( 0, 60 )
texture = ExtResource( 7 )
expand = true
stretch_mode = 6
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label" type="Label" parent="backer3/TextureRect2"]
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
margin_bottom = -54.0
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 0.901961, 0.733333, 0.698039, 1 )
custom_colors/font_color_shadow = Color( 0, 0, 0, 1 )
custom_fonts/font = SubResource( 1 )
text = "13"
align = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="TextureRect3" type="TextureRect" parent="backer3"]
visible = false
anchor_top = 0.675
anchor_right = 1.0
anchor_bottom = 0.95
rect_min_size = Vector2( 0, 60 )
texture = ExtResource( 7 )
expand = true
stretch_mode = 6
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label" type="Label" parent="backer3/TextureRect3"]
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
margin_bottom = -54.0
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 0.901961, 0.733333, 0.698039, 1 )
custom_colors/font_color_shadow = Color( 0, 0, 0, 1 )
custom_fonts/font = SubResource( 1 )
text = "13"
align = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label3" type="Label" parent="backer3"]
self_modulate = Color( 0.694118, 0.756863, 0.870588, 1 )
anchor_right = 1.0
margin_top = -15.0
custom_fonts/font = SubResource( 2 )
text = "RIGHT"
align = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="mapwarn" type="TextureRect" parent="."]
modulate = Color( 0.862745, 0.666667, 0.768627, 1 )
anchor_left = 0.666
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource( 5 )
stretch_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label2" type="Label" parent="mapwarn"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_fonts/font = SubResource( 5 )
text = "





Defeat enemies!"
align = 1
valign = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="foodwarn" type="TextureRect" parent="."]
modulate = Color( 0.862745, 0.717647, 0.666667, 1 )
anchor_right = 0.33
anchor_bottom = 1.0
texture = ExtResource( 4 )
stretch_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label" type="Label" parent="foodwarn"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_fonts/font = SubResource( 5 )
text = "





Gather more food!"
align = 1
valign = 1

[node name="appear" type="AnimationPlayer" parent="."]
playback_speed = 2.0
anims/appearescalation = SubResource( 3 )

[node name="Foodcounter" type="Label" parent="."]
visible = false
anchor_top = 1.0
anchor_right = 0.325
anchor_bottom = 1.0
margin_bottom = -4.0
grow_vertical = 0
custom_fonts/font = SubResource( 4 )
text = "Food Count: X will be halved to Y"
align = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="TextureButton" type="TextureButton" parent="."]
self_modulate = Color( 1, 1, 1, 0.745098 )
anchor_top = 0.8
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 0
size_flags_horizontal = 3
size_flags_vertical = 3
texture_normal = ExtResource( 12 )
texture_pressed = ExtResource( 11 )
texture_hover = ExtResource( 3 )
expand = true
__meta__ = {
"_edit_use_anchors_": false
}

[connection signal="pressed" from="TextureButton" to="." method="_on_TextureButton_pressed"]
