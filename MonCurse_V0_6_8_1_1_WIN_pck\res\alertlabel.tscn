[gd_scene load_steps=5 format=2]

[ext_resource path="res://font/liberation-mono/LiberationMono-Bold.ttf" type="DynamicFontData" id=1]
[ext_resource path="res://AnimationQueueFree.gd" type="Script" id=2]

[sub_resource type="DynamicFont" id=1]
size = 32
outline_size = 2
outline_color = Color( 0.27451, 0.137255, 0.235294, 0.784314 )
font_data = ExtResource( 1 )

[sub_resource type="Animation" id=2]
resource_name = "loadmessage"
length = 7.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1.2, 6, 7 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.627451 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:visible_characters")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 5.3 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ 0, 400 ]
}

[node name="Label" type="Label"]
anchor_right = 1.0
margin_top = 41.0
margin_bottom = 78.0
custom_colors/font_color = Color( 0.901961, 0.784314, 0.843137, 0.901961 )
custom_fonts/font = SubResource( 1 )
align = 2
autowrap = true
script = ExtResource( 2 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
autoplay = "loadmessage"
anims/loadmessage = SubResource( 2 )

[connection signal="animation_finished" from="AnimationPlayer" to="." method="_on_AnimationPlayer_animation_finished"]
