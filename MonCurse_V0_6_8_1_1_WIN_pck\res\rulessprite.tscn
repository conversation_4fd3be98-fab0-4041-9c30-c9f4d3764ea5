[gd_scene load_steps=43 format=2]

[ext_resource path="res://Assets/materials/global_hairshift.tres" type="Material" id=1]
[ext_resource path="res://rulessprite.gd" type="Script" id=2]
[ext_resource path="res://Assets/materials/global_skinshift.tres" type="Material" id=3]
[ext_resource path="res://frame_handler_rs.gd" type="Script" id=4]
[ext_resource path="res://effects/runcloud.png" type="Texture" id=11]
[ext_resource path="res://effects/heartparticles.png" type="Texture" id=24]
[ext_resource path="res://Assets/materials/global_pantsshift.tres" type="Material" id=31]
[ext_resource path="res://Assets/materials/global_hornshift.tres" type="Material" id=33]
[ext_resource path="res://Assets/materials/global_clothesshift.tres" type="Material" id=35]
[ext_resource path="res://Assets/materials/global_rankshift.tres" type="Material" id=36]
[ext_resource path="res://effects/windstep.tscn" type="PackedScene" id=99]

[sub_resource type="Gradient" id=1]
offsets = PoolRealArray( 0, 0.296703, 1 )
colors = PoolColorArray( 1, 1, 1, 0, 1, 1, 1, 1, 0.439216, 0.439216, 0.439216, 0 )

[sub_resource type="GradientTexture" id=2]
gradient = SubResource( 1 )

[sub_resource type="Curve" id=3]
_data = [ Vector2( 0, 0 ), 0.0, 0.0, 0, 0, Vector2( 1, 1 ), 0.0, 0.0, 0, 0 ]

[sub_resource type="CurveTexture" id=4]
curve = SubResource( 3 )

[sub_resource type="ParticlesMaterial" id=5]
flag_disable_z = true
direction = Vector3( -1, 0, 0 )
spread = 10.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 90.0
angular_velocity = 70.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale_curve = SubResource( 4 )
color_ramp = SubResource( 2 )

[sub_resource type="Gradient" id=611]
offsets = PoolRealArray( 0, 0.209459, 0.641892, 1 )
colors = PoolColorArray( 1, 1, 1, 0, 0.784314, 0.784314, 0.784314, 0.784314, 1, 1, 1, 0.588235, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=612]
gradient = SubResource( 611 )

[sub_resource type="ParticlesMaterial" id=610]
emission_shape = 2
emission_box_extents = Vector3( 30, 10, 1 )
flag_disable_z = true
direction = Vector3( 0, -1, 0 )
spread = 180.0
gravity = Vector3( 0, -98, 0 )
initial_velocity = 130.0
angular_velocity = 20.0
angular_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
damping = 50.0
scale = 0.5
scale_random = 1.0
color_ramp = SubResource( 612 )

[sub_resource type="Shader" id=1003]
code = "shader_type canvas_item;

uniform float hue_shift : hint_range(0.0,1.0) = 0.0;
uniform float sat_mul : hint_range(-1.0,10.0) = 0.0;
uniform float val_mul : hint_range(-1.0,10.0) = 0.0;

uniform sampler2D mask_texture;

//void fragment() {
//    vec4 colour = texture(TEXTURE, UV);
//    colour.a *= texture(mask_texture, UV).a;
//	if(focus == true)
//		{
//	    colour.rgb *= vec3(1.0) - texture(focus_texture, UV).a;
//		}
//    COLOR = colour;
//}

vec3 rgb2hsv(vec3 c) {
vec4 K = vec4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);
vec4 p = mix(vec4(c.bg, K.wz), vec4(c.gb, K.xy), step(c.b, c.g));
vec4 q = mix(vec4(p.xyw, c.r), vec4(c.r, p.yzx), step(p.x, c.r));
float d = q.x - min(q.w, q.y);
float e = 1.0e-10;
return vec3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), d / (q.x + e), q.x);
}

vec3 hsv2rgb(vec3 c) {
vec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
vec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);
return c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);
}

void fragment() {
//float intensity = texture(mask_texture, UV).a;
vec4 texture_color = texture(TEXTURE, UV);
//vec4 texture_color = texture(SCREEN_TEXTURE, SCREEN_UV);
vec3 color_hsv = rgb2hsv(texture_color.rgb);
color_hsv.x = mod((color_hsv.x + hue_shift), 1.0);
color_hsv.y = min((color_hsv.y * (1.0+sat_mul)), 1.01);
color_hsv.z = min((color_hsv.z * (1.0+val_mul)), 1.01);
vec3 color_rgb = hsv2rgb(color_hsv);

COLOR.rgba = vec4(color_rgb.rgb,texture_color.a);
}"

[sub_resource type="ShaderMaterial" id=1004]
shader = SubResource( 1003 )
shader_param/hue_shift = 0.0
shader_param/sat_mul = 0.0
shader_param/val_mul = 0.0

[sub_resource type="Animation" id=508]
length = 4.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("../tailback:rotation_degrees")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 1,
"values": [ 0.0, -10.0, 0.0, 10.0, 0.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 1,
"values": [ 0.0, -10.0, 0.0, 10.0, 0.0 ]
}

[sub_resource type="Animation" id=509]
resource_name = "run"
length = 0.8
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("legleft:rotation_degrees")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.4, 0.5, 0.7, 0.8 ),
"transitions": PoolRealArray( 0.5, 2, 0.5, 2, 1, 0.5 ),
"update": 0,
"values": [ 0.0, 3.0, 0.0, -4.0, 1.5, 0.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("legright:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.4, 0.5, 0.7, 0.8 ),
"transitions": PoolRealArray( 0.5, 2, 0.5, 2, 1, 0.5 ),
"update": 0,
"values": [ 0.0, 3.0, 0.0, -4.0, 1.5, 0.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("pants:rotation_degrees")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.1, 0.4, 0.5, 0.7, 0.8 ),
"transitions": PoolRealArray( 0.5, 2, 0.5, 2, 1, 0.5 ),
"update": 0,
"values": [ 0.0, 3.0, 0.0, -4.0, 1.5, 0.0 ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("pantsunder:rotation_degrees")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.1, 0.4, 0.5, 0.7, 0.8 ),
"transitions": PoolRealArray( 0.5, 2, 0.5, 2, 1, 0.5 ),
"update": 0,
"values": [ 0.0, 3.0, 0.0, -4.0, 1.5, 0.0 ]
}

[sub_resource type="Animation" id=976]
resource_name = "RESET"
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0.8, 0.8 ) ]
}

[sub_resource type="Animation" id=977]
resource_name = "fall"
length = 0.6
tracks/0/type = "value"
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.6 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0.92, 0.68 ), Vector2( 0.76, 0.72 ), Vector2( 0.8, 0.8 ) ]
}

[sub_resource type="Animation" id=978]
resource_name = "jump"
length = 0.6
tracks/0/type = "value"
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.6 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0.8, 0.64 ), Vector2( 0.8, 0.96 ), Vector2( 0.8, 0.8 ) ]
}

[sub_resource type="Animation" id=685]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ) ]
}

[sub_resource type="Animation" id=835]
resource_name = "fall"
length = 0.6
tracks/0/type = "value"
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.6 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 1.15, 0.85 ), Vector2( 0.95, 0.9 ), Vector2( 1, 1 ) ]
}

[sub_resource type="Animation" id=595]
resource_name = "jump"
length = 0.6
tracks/0/type = "value"
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.6 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 0.8 ), Vector2( 1, 1.2 ), Vector2( 1, 1 ) ]
}

[sub_resource type="Animation" id=833]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}

[sub_resource type="Animation" id=830]
resource_name = "flinch"
length = 7.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:rotation_degrees")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.5, 1, 1.8, 3.3, 4.3, 5.8, 7 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 6.0, -4.0, 5.0, -3.0, 2.5, -1.5, 1.0, 0.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.7, 1.7, 3.6, 4.3, 5.4, 7 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 7, -2 ), Vector2( -5, 0 ), Vector2( 3, 1 ), Vector2( -2, 0 ), Vector2( 2, -1 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=831]
resource_name = "hit"
length = 1.3
tracks/0/type = "value"
tracks/0/path = NodePath(".:rotation_degrees")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.4, 0.8, 1.3 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 5.0, -3.0, 2.0, 0.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.5, 1.3 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 2, 3 ), Vector2( -2, -2 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=834]
resource_name = "hit2"
length = 1.3
tracks/0/type = "value"
tracks/0/path = NodePath(".:rotation_degrees")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.4, 0.8, 1.3 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, -3.0, 4.0, 1.0, 0.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.5, 1.3 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 4, -3 ), Vector2( -3, -1 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=832]
resource_name = "hithard"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:rotation_degrees")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.6, 1.4, 2 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 10.0, -5.0, 3.0, 0.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.4, 0.8, 1.2, 2 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -7, 4 ), Vector2( -11, -8 ), Vector2( 0, 2 ), Vector2( 5, -3 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=887]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath("frames:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("frames:z_index")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0 ]
}

[sub_resource type="Animation" id=888]
resource_name = "bighurt"
length = 1.2
tracks/0/type = "value"
tracks/0/path = NodePath("frames:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.4, 1.2 ),
"transitions": PoolRealArray( 1, 0.6, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 3.5, 2, 1.6, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("frames:z_index")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 1.1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ 0, 6, 0 ]
}

[sub_resource type="Animation" id=885]
resource_name = "heal"
length = 2.6
tracks/0/type = "value"
tracks/0/path = NodePath("frames:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1.6, 1.9, 2.6 ),
"transitions": PoolRealArray( 1, 2, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 1 ), Color( 1, 2.00392, 1, 1 ), Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=889]
resource_name = "hugehurt"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath("frames:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.4, 2 ),
"transitions": PoolRealArray( 1, 0.7, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 4, 2.2, 2, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("frames:z_index")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 1.5 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ 0, 6, 0 ]
}

[sub_resource type="Animation" id=884]
resource_name = "hurt"
length = 0.7
tracks/0/type = "value"
tracks/0/path = NodePath("frames:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.7 ),
"transitions": PoolRealArray( 1, 0.5, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 3, 1.8, 1.4, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("frames:z_index")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.6 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ 0, 6, 0 ]
}

[sub_resource type="Animation" id=891]
resource_name = "hypno"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath("frames:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3, 2 ),
"transitions": PoolRealArray( 1, 0.5, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 3.5, 1.5, 3.5, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("frames:z_index")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 1.7 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ 0, 6, 0 ]
}

[sub_resource type="Animation" id=890]
resource_name = "megahurt"
length = 3.0
tracks/0/type = "value"
tracks/0/path = NodePath("frames:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.6, 3 ),
"transitions": PoolRealArray( 1, 0.8, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 6, 2.2, 2.2, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("frames:z_index")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 2.2 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ 0, 6, 0 ]
}

[sub_resource type="Animation" id=886]
resource_name = "transform"
tracks/0/type = "value"
tracks/0/path = NodePath("frames:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3, 1 ),
"transitions": PoolRealArray( 1, 0.5, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 4.01176, 4.01176, 4.01176, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("frames:z_index")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.2, 0.8 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ 0, 10, 0 ]
}

[node name="rulessprite" type="Node2D"]
z_index = 6
script = ExtResource( 2 )

[node name="Particles2D" parent="." instance=ExtResource( 99 )]

[node name="Particles2D2" type="Particles2D" parent="."]
visible = false
position = Vector2( 0, 70 )
emitting = false
amount = 3
lifetime = 2.0
process_material = SubResource( 5 )
texture = ExtResource( 11 )

[node name="Particles2D3" type="Particles2D" parent="."]
visible = false
position = Vector2( 0, -20 )
z_index = 3
emitting = false
amount = 12
lifetime = 2.0
speed_scale = 0.7
randomness = 0.5
process_material = SubResource( 610 )
texture = ExtResource( 24 )

[node name="frames" type="Node2D" parent="."]
material = ExtResource( 1 )

[node name="tailback" type="Sprite" parent="frames"]
visible = false
material = ExtResource( 36 )
use_parent_material = true
centered = false
script = ExtResource( 4 )

[node name="wings" type="Sprite" parent="frames"]
visible = false
material = ExtResource( 36 )
centered = false
script = ExtResource( 4 )

[node name="weaponback" type="Sprite" parent="frames"]
centered = false
script = ExtResource( 4 )

[node name="hairback" type="Sprite" parent="frames"]
use_parent_material = true
centered = false
script = ExtResource( 4 )

[node name="armright" type="Sprite" parent="frames"]
material = ExtResource( 3 )
centered = false
script = ExtResource( 4 )

[node name="gloveright" type="Sprite" parent="frames"]
visible = false
material = ExtResource( 3 )
centered = false
script = ExtResource( 4 )

[node name="legright" type="Sprite" parent="frames"]
material = ExtResource( 3 )
centered = false
script = ExtResource( 4 )

[node name="bootright" type="Sprite" parent="frames"]
visible = false
material = ExtResource( 1 )
centered = false
script = ExtResource( 4 )

[node name="pantsundermirror" type="Sprite" parent="frames"]
visible = false
material = ExtResource( 31 )
centered = false
script = ExtResource( 4 )

[node name="abdomen" type="Sprite" parent="frames"]
material = ExtResource( 3 )
centered = false
script = ExtResource( 4 )

[node name="pantsunder" type="Sprite" parent="frames"]
visible = false
material = ExtResource( 31 )
centered = false
script = ExtResource( 4 )

[node name="top" type="Sprite" parent="frames"]
material = ExtResource( 35 )
centered = false
script = ExtResource( 4 )

[node name="legleft" type="Sprite" parent="frames"]
material = ExtResource( 3 )
centered = false
script = ExtResource( 4 )

[node name="pants" type="Sprite" parent="frames"]
material = ExtResource( 31 )
centered = false
script = ExtResource( 4 )

[node name="bootleft" type="Sprite" parent="frames"]
visible = false
material = ExtResource( 1 )
centered = false
script = ExtResource( 4 )

[node name="weapon" type="Sprite" parent="frames"]
centered = false
script = ExtResource( 4 )

[node name="armleft" type="Sprite" parent="frames"]
material = ExtResource( 3 )
centered = false
script = ExtResource( 4 )

[node name="bust" type="Sprite" parent="frames"]
material = ExtResource( 3 )
centered = false
script = ExtResource( 4 )

[node name="topupper" type="Sprite" parent="frames"]
material = ExtResource( 35 )
centered = false
script = ExtResource( 4 )

[node name="bell" type="Sprite" parent="frames"]
material = ExtResource( 35 )
centered = false
script = ExtResource( 4 )

[node name="backhorns" type="Sprite" parent="frames"]
visible = false
material = ExtResource( 33 )
centered = false
script = ExtResource( 4 )

[node name="face" type="Sprite" parent="frames"]
material = ExtResource( 3 )
centered = false
script = ExtResource( 4 )

[node name="facemark" type="Sprite" parent="frames"]
visible = false
material = ExtResource( 3 )
centered = false
script = ExtResource( 4 )

[node name="ears" type="Sprite" parent="frames"]
visible = false
material = ExtResource( 36 )
centered = false
script = ExtResource( 4 )

[node name="hair" type="Sprite" parent="frames"]
use_parent_material = true
centered = false
script = ExtResource( 4 )

[node name="eyes" type="Sprite" parent="frames"]
material = SubResource( 1004 )
centered = false
script = ExtResource( 4 )

[node name="horns" type="Sprite" parent="frames"]
visible = false
material = ExtResource( 33 )
centered = false
script = ExtResource( 4 )

[node name="wingsover" type="Sprite" parent="frames"]
visible = false
material = ExtResource( 36 )
centered = false
script = ExtResource( 4 )

[node name="tailover" type="Sprite" parent="frames"]
visible = false
material = ExtResource( 36 )
use_parent_material = true
centered = false
script = ExtResource( 4 )

[node name="tailshift" type="AnimationPlayer" parent="frames/tailover"]
anims/tailshift = SubResource( 508 )

[node name="RemoteTransform2D" type="RemoteTransform2D" parent="."]
update_rotation = false

[node name="run" type="AnimationPlayer" parent="."]
root_node = NodePath("../frames")
playback_speed = 4.0
anims/run = SubResource( 509 )

[node name="impjump" type="AnimationPlayer" parent="."]
root_node = NodePath("../frames")
playback_speed = 3.0
anims/RESET = SubResource( 976 )
anims/fall = SubResource( 977 )
anims/jump = SubResource( 978 )

[node name="jump" type="AnimationPlayer" parent="."]
root_node = NodePath("../frames")
playback_speed = 3.0
anims/RESET = SubResource( 685 )
anims/fall = SubResource( 835 )
anims/jump = SubResource( 595 )

[node name="flinch" type="AnimationPlayer" parent="."]
root_node = NodePath("../frames")
playback_speed = 3.0
anims/RESET = SubResource( 833 )
anims/flinch = SubResource( 830 )
anims/hit = SubResource( 831 )
anims/hit2 = SubResource( 834 )
anims/hithard = SubResource( 832 )

[node name="flash" type="AnimationPlayer" parent="."]
anims/RESET = SubResource( 887 )
anims/bighurt = SubResource( 888 )
anims/heal = SubResource( 885 )
anims/hugehurt = SubResource( 889 )
anims/hurt = SubResource( 884 )
anims/hypno = SubResource( 891 )
anims/megahurt = SubResource( 890 )
anims/transform = SubResource( 886 )
