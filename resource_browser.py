#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MonCurse 资源浏览器
帮助浏览和提取游戏资源

功能：
1. 浏览解包后的资源目录
2. 查找特定类型的文件
3. 提取和复制资源文件
4. 查看图片资源
5. 查看对话文件内容
"""

import os
import json
import shutil
import sys
from pathlib import Path

class ResourceBrowser:
    def __init__(self, resource_path):
        self.resource_path = Path(resource_path)
        if not self.resource_path.exists():
            print(f"错误：资源路径 {resource_path} 不存在")
            sys.exit(1)
    
    def list_directory(self, path=None, max_depth=2):
        """列出目录内容"""
        if path is None:
            path = self.resource_path
        else:
            path = Path(path)
        
        print(f"\n=== 目录内容: {path} ===")
        
        try:
            items = []
            for item in path.iterdir():
                if item.is_dir():
                    items.append(f"📁 {item.name}/")
                else:
                    size = item.stat().st_size
                    if size > 1024*1024:
                        size_str = f"{size/(1024*1024):.1f}MB"
                    elif size > 1024:
                        size_str = f"{size/1024:.1f}KB"
                    else:
                        size_str = f"{size}B"
                    items.append(f"📄 {item.name} ({size_str})")
            
            items.sort()
            for i, item in enumerate(items[:50], 1):  # 限制显示50个项目
                print(f"{i:2d}. {item}")
            
            if len(items) > 50:
                print(f"... 还有 {len(items) - 50} 个项目")
        
        except PermissionError:
            print("权限错误：无法访问此目录")
    
    def find_files(self, pattern="", file_type=""):
        """查找文件"""
        print(f"\n=== 搜索文件 ===")
        if pattern:
            print(f"搜索模式: {pattern}")
        if file_type:
            print(f"文件类型: {file_type}")
        
        found_files = []
        
        for root, dirs, files in os.walk(self.resource_path):
            for file in files:
                file_path = Path(root) / file
                
                # 检查文件类型
                if file_type and not file.lower().endswith(file_type.lower()):
                    continue
                
                # 检查搜索模式
                if pattern and pattern.lower() not in file.lower():
                    continue
                
                relative_path = file_path.relative_to(self.resource_path)
                found_files.append(relative_path)
        
        found_files.sort()
        
        print(f"找到 {len(found_files)} 个文件:")
        for i, file_path in enumerate(found_files[:30], 1):  # 限制显示30个
            print(f"{i:2d}. {file_path}")
        
        if len(found_files) > 30:
            print(f"... 还有 {len(found_files) - 30} 个文件")
        
        return found_files
    
    def view_json_file(self, file_path):
        """查看JSON文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"\n=== JSON文件内容: {file_path.name} ===")
            print(json.dumps(data, indent=2, ensure_ascii=False))
        
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
        except Exception as e:
            print(f"读取文件错误: {e}")
    
    def view_text_file(self, file_path, max_lines=50):
        """查看文本文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            print(f"\n=== 文本文件内容: {file_path.name} ===")
            for i, line in enumerate(lines[:max_lines], 1):
                print(f"{i:3d}: {line.rstrip()}")
            
            if len(lines) > max_lines:
                print(f"... 还有 {len(lines) - max_lines} 行")
        
        except Exception as e:
            print(f"读取文件错误: {e}")
    
    def extract_files(self, file_list, output_dir):
        """提取文件到指定目录"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        print(f"\n=== 提取文件到: {output_path} ===")
        
        for file_rel_path in file_list:
            source_path = self.resource_path / file_rel_path
            dest_path = output_path / file_rel_path.name
            
            try:
                shutil.copy2(source_path, dest_path)
                print(f"✓ {file_rel_path.name}")
            except Exception as e:
                print(f"✗ {file_rel_path.name}: {e}")
    
    def show_file_info(self, file_path):
        """显示文件信息"""
        try:
            stat = file_path.stat()
            print(f"\n=== 文件信息: {file_path.name} ===")
            print(f"路径: {file_path}")
            print(f"大小: {stat.st_size} 字节")
            print(f"修改时间: {stat.st_mtime}")
            print(f"文件类型: {file_path.suffix}")
            
            # 如果是图片文件，显示额外信息
            if file_path.suffix.lower() in ['.png', '.jpg', '.jpeg', '.webp']:
                print("这是一个图片文件")
            
            # 如果是音频文件
            elif file_path.suffix.lower() in ['.ogg', '.wav', '.mp3']:
                print("这是一个音频文件")
            
            # 如果是脚本文件
            elif file_path.suffix.lower() in ['.gd', '.cs']:
                print("这是一个脚本文件")
            
            # 如果是场景文件
            elif file_path.suffix.lower() == '.tscn':
                print("这是一个Godot场景文件")
            
            # 如果是对话文件
            elif file_path.suffix.lower() == '.json':
                print("这是一个JSON文件（可能是对话文件）")
        
        except Exception as e:
            print(f"获取文件信息错误: {e}")

def main():
    if len(sys.argv) < 2:
        print("用法: python resource_browser.py <资源目录路径>")
        print("示例: python resource_browser.py MonCurse_V0_6_8_1_1_WIN_pck/res")
        return
    
    resource_path = sys.argv[1]
    browser = ResourceBrowser(resource_path)
    
    current_path = browser.resource_path
    
    while True:
        print(f"\n当前位置: {current_path}")
        print("\n=== MonCurse 资源浏览器 ===")
        print("1. 列出当前目录")
        print("2. 进入子目录")
        print("3. 返回上级目录")
        print("4. 搜索文件")
        print("5. 查看文件内容")
        print("6. 提取文件")
        print("7. 查看文件信息")
        print("8. 退出")
        
        choice = input("\n请选择操作 (1-8): ").strip()
        
        if choice == '1':
            browser.list_directory(current_path)
        
        elif choice == '2':
            subdir = input("请输入子目录名称: ").strip()
            new_path = current_path / subdir
            if new_path.exists() and new_path.is_dir():
                current_path = new_path
                print(f"已进入: {current_path}")
            else:
                print("目录不存在")
        
        elif choice == '3':
            if current_path != browser.resource_path:
                current_path = current_path.parent
                print(f"已返回: {current_path}")
            else:
                print("已在根目录")
        
        elif choice == '4':
            pattern = input("请输入搜索模式（文件名包含）: ").strip()
            file_type = input("请输入文件类型（如.png, .json）: ").strip()
            browser.find_files(pattern, file_type)
        
        elif choice == '5':
            filename = input("请输入文件名: ").strip()
            file_path = current_path / filename
            if file_path.exists() and file_path.is_file():
                if file_path.suffix.lower() == '.json':
                    browser.view_json_file(file_path)
                else:
                    browser.view_text_file(file_path)
            else:
                print("文件不存在")
        
        elif choice == '6':
            pattern = input("请输入要提取的文件模式: ").strip()
            file_type = input("请输入文件类型: ").strip()
            output_dir = input("请输入输出目录: ").strip()
            
            if output_dir:
                found_files = browser.find_files(pattern, file_type)
                if found_files:
                    confirm = input(f"确定要提取 {len(found_files)} 个文件吗？(y/n): ")
                    if confirm.lower() == 'y':
                        browser.extract_files(found_files, output_dir)
        
        elif choice == '7':
            filename = input("请输入文件名: ").strip()
            file_path = current_path / filename
            if file_path.exists():
                browser.show_file_info(file_path)
            else:
                print("文件不存在")
        
        elif choice == '8':
            print("再见！")
            break
        
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    main()
