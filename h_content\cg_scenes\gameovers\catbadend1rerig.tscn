[gd_scene load_steps=35 format=2]

[ext_resource path="res://DialogueArt/CG/gameovers/cat/voice expression big.png" type="Texture" id=1]
[ext_resource path="res://DialogueArt/CG/gameovers/cat/voice mouth big.png" type="Texture" id=2]
[ext_resource path="res://DialogueArt/CG/gameovers/cat/rules hands.png" type="Texture" id=3]
[ext_resource path="res://DialogueArt/CG/gameovers/cat/cum.png" type="Texture" id=4]
[ext_resource path="res://DialogueArt/CG/gameovers/cat/voice cattail.png" type="Texture" id=5]
[ext_resource path="res://DialogueArt/CG/gameovers/cat/rules dick in.png" type="Texture" id=6]
[ext_resource path="res://DialogueArt/CG/gameovers/cat/silhouettecatright.png" type="Texture" id=8]
[ext_resource path="res://DialogueArt/CG/gameovers/cat/sweat.png" type="Texture" id=9]
[ext_resource path="res://DialogueArt/CG/gameovers/cat/silhouettecatmiddle.png" type="Texture" id=10]
[ext_resource path="res://DialogueArt/CG/gameovers/cat/silhouetterules.png" type="Texture" id=11]
[ext_resource path="res://DialogueArt/CG/gameovers/cat/rules legs.png" type="Texture" id=12]
[ext_resource path="res://DialogueArt/CG/gameovers/cat/silhouettecatleft.png" type="Texture" id=13]
[ext_resource path="res://DialogueArt/CG/gameovers/cat/voice head.png" type="Texture" id=14]
[ext_resource path="res://DialogueArt/CG/gameovers/cat/voice pussy spread.png" type="Texture" id=15]
[ext_resource path="res://DialogueArt/CG/gameovers/cat/voice legs.png" type="Texture" id=16]
[ext_resource path="res://DialogueArt/CG/gameovers/cat/voice tits.png" type="Texture" id=17]
[ext_resource path="res://DialogueArt/CG/gameovers/catbadend1.gd" type="Script" id=18]
[ext_resource path="res://DialogueArt/CG/gameovers/cat/legs shadow.png" type="Texture" id=19]
[ext_resource path="res://Background/catknightroomdark.png" type="Texture" id=20]
[ext_resource path="res://Background/catknightroom.png" type="Texture" id=21]
[ext_resource path="res://DialogueArt/CG/voicescene/borders2.png" type="Texture" id=22]
[ext_resource path="res://DialogueArt/CG/voicescene/borders.png" type="Texture" id=23]
[ext_resource path="res://Assets/materials/global_skinshift.tres" type="Material" id=24]

[sub_resource type="Animation" id=11]
resource_name = "daytime"
tracks/0/type = "value"
tracks/0/path = NodePath("night:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("day:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("night:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=12]
resource_name = "nighttime"
length = 3.0
tracks/0/type = "value"
tracks/0/path = NodePath("night:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 2.8 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("day:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 3 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("night:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=5]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath("middle:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("middle:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("left:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("left:rotation_degrees")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("rules:position")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ) ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("rules:rotation_degrees")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("right:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ) ]
}
tracks/7/type = "value"
tracks/7/path = NodePath("right:rotation_degrees")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/8/type = "value"
tracks/8/path = NodePath("left:visible")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ true ]
}
tracks/9/type = "value"
tracks/9/path = NodePath("middle:visible")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ true ]
}
tracks/10/type = "value"
tracks/10/path = NodePath("right:visible")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ true ]
}
tracks/11/type = "value"
tracks/11/path = NodePath("rules:visible")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ true ]
}
tracks/12/type = "value"
tracks/12/path = NodePath("left:self_modulate")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/13/type = "value"
tracks/13/path = NodePath("middle:self_modulate")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/14/type = "value"
tracks/14/path = NodePath("right:self_modulate")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/15/type = "value"
tracks/15/path = NodePath("rules:self_modulate")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=6]
resource_name = "appear"
length = 6.2
tracks/0/type = "value"
tracks/0/path = NodePath("left:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 2 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("middle:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 4.4, 6.2 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("right:self_modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1.5, 3.5 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("rules:self_modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 2.9, 4.8 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath(".:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 0.1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ false, true ]
}

[sub_resource type="Animation" id=8]
resource_name = "appear"
length = 1.5
tracks/0/type = "value"
tracks/0/path = NodePath("cum:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0.1, 0.4, 0.9, 1.5 ),
"transitions": PoolRealArray( 0.3, 1, 0.3, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 0.588235 ), Color( 1, 1, 1, 0.509804 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("cum:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ false, true ]
}

[sub_resource type="Animation" id=1]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath("Skeleton2D/voicecore/ass:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( -1.47577, 207.297 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Skeleton2D/voicecore/ass:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("Skeleton2D/voicecore:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 964.594, 512.179 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("Skeleton2D/voicecore:rotation_degrees")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("Skeleton2D/voicecore/upperbody/righttit:position")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 181.538, -2.66968 ) ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("Skeleton2D/voicecore/upperbody/righttit:rotation_degrees")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("Skeleton2D/voicecore/ass/thighleft/legleft:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( -80.9575, 336.529 ) ]
}
tracks/7/type = "value"
tracks/7/path = NodePath("Skeleton2D/voicecore/ass/thighleft/legleft:rotation_degrees")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/8/type = "value"
tracks/8/path = NodePath("Skeleton2D/voicecore/ass/tail/tailbend:position")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( -7.5509, 130.255 ) ]
}
tracks/9/type = "value"
tracks/9/path = NodePath("Skeleton2D/voicecore/ass/tail/tailbend:rotation_degrees")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/10/type = "value"
tracks/10/path = NodePath("Skeleton2D/voicecore/upperbody:position")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( -7.22711, -110.327 ) ]
}
tracks/11/type = "value"
tracks/11/path = NodePath("Skeleton2D/voicecore/upperbody:rotation_degrees")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/12/type = "value"
tracks/12/path = NodePath("Skeleton2D/hair/hairright:position")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 72.0813, 80.0904 ) ]
}
tracks/13/type = "value"
tracks/13/path = NodePath("Skeleton2D/hair/hairright:rotation_degrees")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/14/type = "value"
tracks/14/path = NodePath("Skeleton2D/voicecore/ass/thighleft:position")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( -259.565, -248.239 ) ]
}
tracks/15/type = "value"
tracks/15/path = NodePath("Skeleton2D/voicecore/ass/thighleft:rotation_degrees")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/16/type = "value"
tracks/16/path = NodePath("Skeleton2D/voicecore/ass/thighleft/legleft/footleft:position")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( -77.7827, 111.118 ) ]
}
tracks/17/type = "value"
tracks/17/path = NodePath("Skeleton2D/voicecore/ass/thighleft/legleft/footleft:rotation_degrees")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/18/type = "value"
tracks/18/path = NodePath("Skeleton2D/voicecore/ass/thighright:position")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 258.746, -242.872 ) ]
}
tracks/19/type = "value"
tracks/19/path = NodePath("Skeleton2D/voicecore/ass/thighright:rotation_degrees")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/20/type = "value"
tracks/20/path = NodePath("Skeleton2D/voicecore/ass/thighright/legright:position")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 73.0205, 323.83 ) ]
}
tracks/21/type = "value"
tracks/21/path = NodePath("Skeleton2D/voicecore/ass/thighright/legright:rotation_degrees")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/imported = false
tracks/21/enabled = true
tracks/21/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/22/type = "value"
tracks/22/path = NodePath("Skeleton2D/voicecore/upperbody/neck/face:position")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/imported = false
tracks/22/enabled = true
tracks/22/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( -13.3484, 24.0271 ) ]
}
tracks/23/type = "value"
tracks/23/path = NodePath("Skeleton2D/voicecore/upperbody/neck/face:rotation_degrees")
tracks/23/interp = 1
tracks/23/loop_wrap = true
tracks/23/imported = false
tracks/23/enabled = true
tracks/23/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/24/type = "value"
tracks/24/path = NodePath("Skeleton2D/voicecore/ass/thighright/legright/footright:position")
tracks/24/interp = 1
tracks/24/loop_wrap = true
tracks/24/imported = false
tracks/24/enabled = true
tracks/24/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 73.0205, 117.468 ) ]
}
tracks/25/type = "value"
tracks/25/path = NodePath("Skeleton2D/voicecore/ass/thighright/legright/footright:rotation_degrees")
tracks/25/interp = 1
tracks/25/loop_wrap = true
tracks/25/imported = false
tracks/25/enabled = true
tracks/25/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/26/type = "value"
tracks/26/path = NodePath("Skeleton2D/voicecore/upperbody/lefttit:position")
tracks/26/interp = 1
tracks/26/loop_wrap = true
tracks/26/imported = false
tracks/26/enabled = true
tracks/26/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( -217.579, 9.34387 ) ]
}
tracks/27/type = "value"
tracks/27/path = NodePath("Skeleton2D/voicecore/upperbody/lefttit:rotation_degrees")
tracks/27/interp = 1
tracks/27/loop_wrap = true
tracks/27/imported = false
tracks/27/enabled = true
tracks/27/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/28/type = "value"
tracks/28/path = NodePath("Skeleton2D/voicecore/upperbody/neck:position")
tracks/28/interp = 1
tracks/28/loop_wrap = true
tracks/28/imported = false
tracks/28/enabled = true
tracks/28/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( -10.6788, -89.4343 ) ]
}
tracks/29/type = "value"
tracks/29/path = NodePath("Skeleton2D/voicecore/upperbody/neck:rotation_degrees")
tracks/29/interp = 1
tracks/29/loop_wrap = true
tracks/29/imported = false
tracks/29/enabled = true
tracks/29/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/30/type = "value"
tracks/30/path = NodePath("Skeleton2D/voicecore/ass/tail:position")
tracks/30/interp = 1
tracks/30/loop_wrap = true
tracks/30/imported = false
tracks/30/enabled = true
tracks/30/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 198.523, 156.365 ) ]
}
tracks/31/type = "value"
tracks/31/path = NodePath("Skeleton2D/voicecore/ass/tail:rotation_degrees")
tracks/31/interp = 1
tracks/31/loop_wrap = true
tracks/31/imported = false
tracks/31/enabled = true
tracks/31/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/32/type = "value"
tracks/32/path = NodePath("Skeleton2D/dickpussy:position")
tracks/32/interp = 1
tracks/32/loop_wrap = true
tracks/32/imported = false
tracks/32/enabled = true
tracks/32/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 941.987, 681.477 ) ]
}
tracks/33/type = "value"
tracks/33/path = NodePath("Skeleton2D/dickpussy:rotation_degrees")
tracks/33/interp = 1
tracks/33/loop_wrap = true
tracks/33/imported = false
tracks/33/enabled = true
tracks/33/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/34/type = "value"
tracks/34/path = NodePath("Skeleton2D/voicecore/belly:position")
tracks/34/interp = 1
tracks/34/loop_wrap = true
tracks/34/imported = false
tracks/34/enabled = true
tracks/34/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 25.3619, -8.00903 ) ]
}
tracks/35/type = "value"
tracks/35/path = NodePath("Skeleton2D/voicecore/belly:rotation_degrees")
tracks/35/interp = 1
tracks/35/loop_wrap = true
tracks/35/imported = false
tracks/35/enabled = true
tracks/35/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/36/type = "value"
tracks/36/path = NodePath("Skeleton2D/hair:position")
tracks/36/interp = 1
tracks/36/loop_wrap = true
tracks/36/imported = false
tracks/36/enabled = true
tracks/36/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 952.603, 232.951 ) ]
}
tracks/37/type = "value"
tracks/37/path = NodePath("Skeleton2D/hair:rotation_degrees")
tracks/37/interp = 1
tracks/37/loop_wrap = true
tracks/37/imported = false
tracks/37/enabled = true
tracks/37/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/38/type = "value"
tracks/38/path = NodePath("Skeleton2D/hair/hairleft:position")
tracks/38/interp = 1
tracks/38/loop_wrap = true
tracks/38/imported = false
tracks/38/enabled = true
tracks/38/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( -78.7556, 77.4207 ) ]
}
tracks/39/type = "value"
tracks/39/path = NodePath("Skeleton2D/hair/hairleft:rotation_degrees")
tracks/39/interp = 1
tracks/39/loop_wrap = true
tracks/39/imported = false
tracks/39/enabled = true
tracks/39/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/40/type = "value"
tracks/40/path = NodePath("Skeleton2D/dickpussy/sex:position")
tracks/40/interp = 1
tracks/40/loop_wrap = true
tracks/40/imported = false
tracks/40/enabled = true
tracks/40/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( -1.15781, -95.451 ) ]
}
tracks/41/type = "value"
tracks/41/path = NodePath("Skeleton2D/dickpussy/sex:rotation_degrees")
tracks/41/interp = 1
tracks/41/loop_wrap = true
tracks/41/imported = false
tracks/41/enabled = true
tracks/41/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}

[sub_resource type="Animation" id=9]
resource_name = "fast"
length = 4.0
tracks/0/type = "value"
tracks/0/path = NodePath("Skeleton2D/voicecore/ass:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -1.47577, 207.297 ), Vector2( -5, 225.539 ), Vector2( 0, 210 ), Vector2( -5, 225.539 ), Vector2( -1.47577, 207.297 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Skeleton2D/voicecore/ass:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0, 0.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("Skeleton2D/voicecore:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 964.594, 512.179 ), Vector2( 964.594, 512.179 ), Vector2( 964.594, 517.842 ), Vector2( 964.594, 512.179 ), Vector2( 964.594, 512.179 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("Skeleton2D/voicecore:rotation_degrees")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0, 0.0 ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("Skeleton2D/voicecore/upperbody/righttit:position")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 181.538, -2.66968 ), Vector2( 180, -12.205 ), Vector2( 182.975, 5 ), Vector2( 176.241, -12.2048 ), Vector2( 181.538, -2.66968 ) ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("Skeleton2D/voicecore/upperbody/righttit:rotation_degrees")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0, 0.0 ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("Skeleton2D/voicecore/ass/thighleft/legleft:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -80.9575, 336.529 ), Vector2( -89.4332, 307.924 ), Vector2( -75, 345 ), Vector2( -89.4332, 307.924 ), Vector2( -80.9575, 336.529 ) ]
}
tracks/7/type = "value"
tracks/7/path = NodePath("Skeleton2D/voicecore/ass/thighleft/legleft:rotation_degrees")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0, 0.0 ]
}
tracks/8/type = "value"
tracks/8/path = NodePath("Skeleton2D/voicecore/ass/tail/tailbend:position")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -7.5509, 130.255 ), Vector2( -28.9083, 154.282 ), Vector2( -22.2386, 126.925 ), Vector2( -28.9083, 154.282 ), Vector2( -7.5509, 130.255 ) ]
}
tracks/9/type = "value"
tracks/9/path = NodePath("Skeleton2D/voicecore/ass/tail/tailbend:rotation_degrees")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0, 0.0 ]
}
tracks/10/type = "value"
tracks/10/path = NodePath("Skeleton2D/voicecore/upperbody:position")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -7.22711, -110.327 ), Vector2( -1.98297, -105.083 ), Vector2( -1.98297, -105.083 ), Vector2( -1.98297, -105.083 ), Vector2( -7.22711, -110.327 ) ]
}
tracks/11/type = "value"
tracks/11/path = NodePath("Skeleton2D/voicecore/upperbody:rotation_degrees")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0, 0.0 ]
}
tracks/12/type = "value"
tracks/12/path = NodePath("Skeleton2D/hair/hairright:position")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 72.0813, 80.0904 ), Vector2( 71.2876, 82.4715 ), Vector2( 71.2876, 89.6148 ), Vector2( 71.2876, 82.4715 ), Vector2( 72.0813, 80.0904 ) ]
}
tracks/13/type = "value"
tracks/13/path = NodePath("Skeleton2D/hair/hairright:rotation_degrees")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0, 0.0 ]
}
tracks/14/type = "value"
tracks/14/path = NodePath("Skeleton2D/voicecore/ass/thighleft:position")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -259.565, -248.239 ), Vector2( -245, -270 ), Vector2( -259, -262.101 ), Vector2( -255.507, -283.52 ), Vector2( -259.565, -248.239 ) ]
}
tracks/15/type = "value"
tracks/15/path = NodePath("Skeleton2D/voicecore/ass/thighleft:rotation_degrees")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0, 0.0 ]
}
tracks/16/type = "value"
tracks/16/path = NodePath("Skeleton2D/voicecore/ass/thighleft/legleft/footleft:position")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -77.7827, 111.118 ), Vector2( -106.388, 82.5125 ), Vector2( -84.5249, 101.012 ), Vector2( -106.388, 82.5125 ), Vector2( -77.7827, 111.118 ) ]
}
tracks/17/type = "value"
tracks/17/path = NodePath("Skeleton2D/voicecore/ass/thighleft/legleft/footleft:rotation_degrees")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0, 0.0 ]
}
tracks/18/type = "value"
tracks/18/path = NodePath("Skeleton2D/voicecore/ass/thighright:position")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 258.746, -242.872 ), Vector2( 266.162, -253.467 ), Vector2( 266.162, -253.467 ), Vector2( 266.162, -253.467 ), Vector2( 258.746, -242.872 ) ]
}
tracks/19/type = "value"
tracks/19/path = NodePath("Skeleton2D/voicecore/ass/thighright:rotation_degrees")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0, 0.0 ]
}
tracks/20/type = "value"
tracks/20/path = NodePath("Skeleton2D/voicecore/ass/thighright/legright:position")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 73.0205, 323.83 ), Vector2( 76, 310 ), Vector2( 89.5018, 344.336 ), Vector2( 76, 310 ), Vector2( 73.0205, 323.83 ) ]
}
tracks/21/type = "value"
tracks/21/path = NodePath("Skeleton2D/voicecore/ass/thighright/legright:rotation_degrees")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/imported = false
tracks/21/enabled = true
tracks/21/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0, 0.0 ]
}
tracks/22/type = "value"
tracks/22/path = NodePath("Skeleton2D/voicecore/upperbody/neck/face:position")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/imported = false
tracks/22/enabled = true
tracks/22/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -13.3484, 24.0271 ), Vector2( -8.34486, 19.1005 ), Vector2( -11.052, 29.241 ), Vector2( -10.9673, 12.1216 ), Vector2( -13.3484, 24.0271 ) ]
}
tracks/23/type = "value"
tracks/23/path = NodePath("Skeleton2D/voicecore/upperbody/neck/face:rotation_degrees")
tracks/23/interp = 1
tracks/23/loop_wrap = true
tracks/23/imported = false
tracks/23/enabled = true
tracks/23/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 7.0, 0.0, 0.0 ]
}
tracks/24/type = "value"
tracks/24/path = NodePath("Skeleton2D/voicecore/ass/thighright/legright/footright:position")
tracks/24/interp = 1
tracks/24/loop_wrap = true
tracks/24/imported = false
tracks/24/enabled = true
tracks/24/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 73.0205, 117.468 ), Vector2( 100.567, 90 ), Vector2( 45.129, 100 ), Vector2( 100.567, 77.2084 ), Vector2( 73.0205, 117.468 ) ]
}
tracks/25/type = "value"
tracks/25/path = NodePath("Skeleton2D/voicecore/ass/thighright/legright/footright:rotation_degrees")
tracks/25/interp = 1
tracks/25/loop_wrap = true
tracks/25/imported = false
tracks/25/enabled = true
tracks/25/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0, 0.0 ]
}
tracks/26/type = "value"
tracks/26/path = NodePath("Skeleton2D/voicecore/upperbody/lefttit:position")
tracks/26/interp = 1
tracks/26/loop_wrap = true
tracks/26/imported = false
tracks/26/enabled = true
tracks/26/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -217.579, 9.34387 ), Vector2( -210.163, -4.42917 ), Vector2( -215.775, 15 ), Vector2( -210.163, -4.42917 ), Vector2( -217.579, 9.34387 ) ]
}
tracks/27/type = "value"
tracks/27/path = NodePath("Skeleton2D/voicecore/upperbody/lefttit:rotation_degrees")
tracks/27/interp = 1
tracks/27/loop_wrap = true
tracks/27/imported = false
tracks/27/enabled = true
tracks/27/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0, 0.0 ]
}
tracks/28/type = "value"
tracks/28/path = NodePath("Skeleton2D/voicecore/upperbody/neck:position")
tracks/28/interp = 1
tracks/28/loop_wrap = true
tracks/28/imported = false
tracks/28/enabled = true
tracks/28/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -10.6788, -89.4343 ), Vector2( -12.2662, -94.1965 ), Vector2( -11.559, -84 ), Vector2( -12.2662, -94.1965 ), Vector2( -10.6788, -89.4343 ) ]
}
tracks/29/type = "value"
tracks/29/path = NodePath("Skeleton2D/voicecore/upperbody/neck:rotation_degrees")
tracks/29/interp = 1
tracks/29/loop_wrap = true
tracks/29/imported = false
tracks/29/enabled = true
tracks/29/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0, 0.0 ]
}
tracks/30/type = "value"
tracks/30/path = NodePath("Skeleton2D/voicecore/ass/tail:position")
tracks/30/interp = 1
tracks/30/loop_wrap = true
tracks/30/imported = false
tracks/30/enabled = true
tracks/30/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 198.523, 156.365 ), Vector2( 194.518, 137.677 ), Vector2( 202.874, 169.472 ), Vector2( 194.518, 137.677 ), Vector2( 198.523, 156.365 ) ]
}
tracks/31/type = "value"
tracks/31/path = NodePath("Skeleton2D/voicecore/ass/tail:rotation_degrees")
tracks/31/interp = 1
tracks/31/loop_wrap = true
tracks/31/imported = false
tracks/31/enabled = true
tracks/31/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0, 0.0 ]
}
tracks/32/type = "value"
tracks/32/path = NodePath("Skeleton2D/dickpussy:position")
tracks/32/interp = 1
tracks/32/loop_wrap = true
tracks/32/imported = false
tracks/32/enabled = true
tracks/32/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 941.987, 681.477 ), Vector2( 941.987, 681.477 ), Vector2( 944.819, 689.028 ), Vector2( 941.987, 681.477 ), Vector2( 941.987, 681.477 ) ]
}
tracks/33/type = "value"
tracks/33/path = NodePath("Skeleton2D/dickpussy:rotation_degrees")
tracks/33/interp = 1
tracks/33/loop_wrap = true
tracks/33/imported = false
tracks/33/enabled = true
tracks/33/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0, 0.0 ]
}
tracks/34/type = "value"
tracks/34/path = NodePath("Skeleton2D/voicecore/belly:position")
tracks/34/interp = 1
tracks/34/loop_wrap = true
tracks/34/imported = false
tracks/34/enabled = true
tracks/34/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 25.3619, -8.00903 ), Vector2( 21.5209, -15.8546 ), Vector2( 17.031, 13.3294 ), Vector2( 21.5209, -15.8546 ), Vector2( 25.3619, -8.00903 ) ]
}
tracks/35/type = "value"
tracks/35/path = NodePath("Skeleton2D/voicecore/belly:rotation_degrees")
tracks/35/interp = 1
tracks/35/loop_wrap = true
tracks/35/imported = false
tracks/35/enabled = true
tracks/35/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, -5.0, 0.0, 0.0 ]
}
tracks/36/type = "value"
tracks/36/path = NodePath("Skeleton2D/hair:position")
tracks/36/interp = 1
tracks/36/loop_wrap = true
tracks/36/imported = false
tracks/36/enabled = true
tracks/36/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 952.603, 232.951 ), Vector2( 952.603, 232.951 ), Vector2( 952, 248 ), Vector2( 952.603, 232.951 ), Vector2( 952.603, 232.951 ) ]
}
tracks/37/type = "value"
tracks/37/path = NodePath("Skeleton2D/hair:rotation_degrees")
tracks/37/interp = 1
tracks/37/loop_wrap = true
tracks/37/imported = false
tracks/37/enabled = true
tracks/37/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0, 0.0 ]
}
tracks/38/type = "value"
tracks/38/path = NodePath("Skeleton2D/hair/hairleft:position")
tracks/38/interp = 1
tracks/38/loop_wrap = true
tracks/38/imported = false
tracks/38/enabled = true
tracks/38/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -78.7556, 77.4207 ), Vector2( -74, 81.389 ), Vector2( -72, 87.739 ), Vector2( -77, 81.389 ), Vector2( -78.7556, 77.4207 ) ]
}
tracks/39/type = "value"
tracks/39/path = NodePath("Skeleton2D/hair/hairleft:rotation_degrees")
tracks/39/interp = 1
tracks/39/loop_wrap = true
tracks/39/imported = false
tracks/39/enabled = true
tracks/39/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, -3.0, 0.0, 0.0 ]
}
tracks/40/type = "value"
tracks/40/path = NodePath("Skeleton2D/dickpussy/sex:position")
tracks/40/interp = 1
tracks/40/loop_wrap = true
tracks/40/imported = false
tracks/40/enabled = true
tracks/40/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -1.15781, -95.451 ), Vector2( 6, -95.451 ), Vector2( 4, -94.379 ), Vector2( 2.0206, -113.462 ), Vector2( -1.15781, -95.451 ) ]
}
tracks/41/type = "value"
tracks/41/path = NodePath("Skeleton2D/dickpussy/sex:rotation_degrees")
tracks/41/interp = 1
tracks/41/loop_wrap = true
tracks/41/imported = false
tracks/41/enabled = true
tracks/41/keys = {
"times": PoolRealArray( 0, 1.1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 5.0, 5.0, 5.0, 0.0 ]
}
tracks/42/type = "method"
tracks/42/path = NodePath(".")
tracks/42/interp = 1
tracks/42/loop_wrap = true
tracks/42/imported = false
tracks/42/enabled = true
tracks/42/keys = {
"times": PoolRealArray( 0, 0.8, 1.4, 2.5, 3.6 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"values": [ {
"args": [ 2, 1 ],
"method": "switch_texture"
}, {
"args": [ 0, 2 ],
"method": "switch_texture"
}, {
"args": [ 1, 2 ],
"method": "switch_texture"
}, {
"args": [ 1, 1 ],
"method": "switch_texture"
}, {
"args": [ 0, 1 ],
"method": "switch_texture"
} ]
}

[sub_resource type="Animation" id=2]
resource_name = "idle"
length = 6.0
tracks/0/type = "value"
tracks/0/path = NodePath("Skeleton2D/voicecore/ass:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -1.47577, 207.297 ), Vector2( -1.47577, 203.292 ), Vector2( -1.47577, 205.041 ), Vector2( -1.47577, 207.297 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Skeleton2D/voicecore/ass:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 1.0, -0.4, 0.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("Skeleton2D/voicecore:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 964.594, 512.179 ), Vector2( 964.594, 520.664 ), Vector2( 964.594, 516.959 ), Vector2( 964.594, 512.179 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("Skeleton2D/voicecore:rotation_degrees")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("Skeleton2D/voicecore/upperbody/righttit:position")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 181.538, -2.66968 ), Vector2( 180.278, -5.18951 ), Vector2( 176.066, 5.43511 ), Vector2( 181.538, -2.66968 ) ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("Skeleton2D/voicecore/upperbody/righttit:rotation_degrees")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("Skeleton2D/voicecore/ass/thighleft/legleft:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -80.9575, 336.529 ), Vector2( -79.465, 325.049 ), Vector2( -92.0528, 322.457 ), Vector2( -80.9575, 336.529 ) ]
}
tracks/7/type = "value"
tracks/7/path = NodePath("Skeleton2D/voicecore/ass/thighleft/legleft:rotation_degrees")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/8/type = "value"
tracks/8/path = NodePath("Skeleton2D/voicecore/ass/tail/tailbend:position")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -7.5509, 130.255 ), Vector2( -1.90369, 149.346 ), Vector2( -4.36937, 141.011 ), Vector2( -7.5509, 130.255 ) ]
}
tracks/9/type = "value"
tracks/9/path = NodePath("Skeleton2D/voicecore/ass/tail/tailbend:rotation_degrees")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/10/type = "value"
tracks/10/path = NodePath("Skeleton2D/voicecore/upperbody:position")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -7.22711, -110.327 ), Vector2( -7.22711, -113.69 ), Vector2( -7.22711, -112.222 ), Vector2( -7.22711, -110.327 ) ]
}
tracks/11/type = "value"
tracks/11/path = NodePath("Skeleton2D/voicecore/upperbody:rotation_degrees")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/12/type = "value"
tracks/12/path = NodePath("Skeleton2D/hair/hairright:position")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 72.0813, 80.0904 ), Vector2( 72.8304, 86.0836 ), Vector2( 72.5033, 83.4669 ), Vector2( 72.0813, 80.0904 ) ]
}
tracks/13/type = "value"
tracks/13/path = NodePath("Skeleton2D/hair/hairright:rotation_degrees")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/14/type = "value"
tracks/14/path = NodePath("Skeleton2D/voicecore/ass/thighleft:position")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -259.565, -248.239 ), Vector2( -255, -257.512 ), Vector2( -255.657, -263.049 ), Vector2( -259.565, -248.239 ) ]
}
tracks/15/type = "value"
tracks/15/path = NodePath("Skeleton2D/voicecore/ass/thighleft:rotation_degrees")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/16/type = "value"
tracks/16/path = NodePath("Skeleton2D/voicecore/ass/thighleft/legleft/footleft:position")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -77.7827, 111.118 ), Vector2( -110.722, 59.6263 ), Vector2( -100, 85 ), Vector2( -77.7827, 111.118 ) ]
}
tracks/17/type = "value"
tracks/17/path = NodePath("Skeleton2D/voicecore/ass/thighleft/legleft/footleft:rotation_degrees")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, -5.0, -8.0, 0.0 ]
}
tracks/18/type = "value"
tracks/18/path = NodePath("Skeleton2D/voicecore/ass/thighright:position")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 258.746, -242.872 ), Vector2( 262.727, -244.277 ), Vector2( 257.877, -237.283 ), Vector2( 258.746, -242.872 ) ]
}
tracks/19/type = "value"
tracks/19/path = NodePath("Skeleton2D/voicecore/ass/thighright:rotation_degrees")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 1.0, 0.56338, 0.0 ]
}
tracks/20/type = "value"
tracks/20/path = NodePath("Skeleton2D/voicecore/ass/thighright/legright:position")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 73.0205, 323.83 ), Vector2( 79.551, 319.595 ), Vector2( 76.6997, 321.444 ), Vector2( 73.0205, 323.83 ) ]
}
tracks/21/type = "value"
tracks/21/path = NodePath("Skeleton2D/voicecore/ass/thighright/legright:rotation_degrees")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/imported = false
tracks/21/enabled = true
tracks/21/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/22/type = "value"
tracks/22/path = NodePath("Skeleton2D/voicecore/upperbody/neck/face:position")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/imported = false
tracks/22/enabled = true
tracks/22/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -13.3484, 24.0271 ), Vector2( -11.455, 12 ), Vector2( -12.2817, 39.6307 ), Vector2( -13.3484, 24.0271 ) ]
}
tracks/23/type = "value"
tracks/23/path = NodePath("Skeleton2D/voicecore/upperbody/neck/face:rotation_degrees")
tracks/23/interp = 1
tracks/23/loop_wrap = true
tracks/23/imported = false
tracks/23/enabled = true
tracks/23/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.2, 0.112676, 0.0 ]
}
tracks/24/type = "value"
tracks/24/path = NodePath("Skeleton2D/voicecore/ass/thighright/legright/footright:position")
tracks/24/interp = 1
tracks/24/loop_wrap = true
tracks/24/imported = false
tracks/24/enabled = true
tracks/24/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 73.0205, 117.468 ), Vector2( 102.445, 80.3774 ), Vector2( 89.5979, 96.5718 ), Vector2( 73.0205, 117.468 ) ]
}
tracks/25/type = "value"
tracks/25/path = NodePath("Skeleton2D/voicecore/ass/thighright/legright/footright:rotation_degrees")
tracks/25/interp = 1
tracks/25/loop_wrap = true
tracks/25/imported = false
tracks/25/enabled = true
tracks/25/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/26/type = "value"
tracks/26/path = NodePath("Skeleton2D/voicecore/upperbody/lefttit:position")
tracks/26/interp = 1
tracks/26/loop_wrap = true
tracks/26/imported = false
tracks/26/enabled = true
tracks/26/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -217.579, 9.34387 ), Vector2( -213.799, 4.3042 ), Vector2( -209.1, 11.2668 ), Vector2( -217.579, 9.34387 ) ]
}
tracks/27/type = "value"
tracks/27/path = NodePath("Skeleton2D/voicecore/upperbody/lefttit:rotation_degrees")
tracks/27/interp = 1
tracks/27/loop_wrap = true
tracks/27/imported = false
tracks/27/enabled = true
tracks/27/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/28/type = "value"
tracks/28/path = NodePath("Skeleton2D/voicecore/upperbody/neck:position")
tracks/28/interp = 1
tracks/28/loop_wrap = true
tracks/28/imported = false
tracks/28/enabled = true
tracks/28/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -10.6788, -89.4343 ), Vector2( -10.679, -85 ), Vector2( -10.6789, -86.9361 ), Vector2( -10.6788, -89.4343 ) ]
}
tracks/29/type = "value"
tracks/29/path = NodePath("Skeleton2D/voicecore/upperbody/neck:rotation_degrees")
tracks/29/interp = 1
tracks/29/loop_wrap = true
tracks/29/imported = false
tracks/29/enabled = true
tracks/29/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/30/type = "value"
tracks/30/path = NodePath("Skeleton2D/voicecore/ass/tail:position")
tracks/30/interp = 1
tracks/30/loop_wrap = true
tracks/30/imported = false
tracks/30/enabled = true
tracks/30/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 198.523, 156.365 ), Vector2( 198.314, 144.38 ), Vector2( 198.405, 149.613 ), Vector2( 198.523, 156.365 ) ]
}
tracks/31/type = "value"
tracks/31/path = NodePath("Skeleton2D/voicecore/ass/tail:rotation_degrees")
tracks/31/interp = 1
tracks/31/loop_wrap = true
tracks/31/imported = false
tracks/31/enabled = true
tracks/31/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/32/type = "value"
tracks/32/path = NodePath("Skeleton2D/dickpussy:position")
tracks/32/interp = 1
tracks/32/loop_wrap = true
tracks/32/imported = false
tracks/32/enabled = true
tracks/32/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 941.987, 681.477 ), Vector2( 946.749, 681.477 ), Vector2( 944.669, 681.477 ), Vector2( 941.987, 681.477 ) ]
}
tracks/33/type = "value"
tracks/33/path = NodePath("Skeleton2D/dickpussy:rotation_degrees")
tracks/33/interp = 1
tracks/33/loop_wrap = true
tracks/33/imported = false
tracks/33/enabled = true
tracks/33/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/34/type = "value"
tracks/34/path = NodePath("Skeleton2D/voicecore/belly:position")
tracks/34/interp = 1
tracks/34/loop_wrap = true
tracks/34/imported = false
tracks/34/enabled = true
tracks/34/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 25.3619, -8.00903 ), Vector2( 20.8721, -20.3561 ), Vector2( 18.0702, -5.44077 ), Vector2( 25.3619, -8.00903 ) ]
}
tracks/35/type = "value"
tracks/35/path = NodePath("Skeleton2D/voicecore/belly:rotation_degrees")
tracks/35/interp = 1
tracks/35/loop_wrap = true
tracks/35/imported = false
tracks/35/enabled = true
tracks/35/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/36/type = "value"
tracks/36/path = NodePath("Skeleton2D/hair:position")
tracks/36/interp = 1
tracks/36/loop_wrap = true
tracks/36/imported = false
tracks/36/enabled = true
tracks/36/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 952.603, 232.951 ), Vector2( 956, 235 ), Vector2( 954, 238.049 ), Vector2( 952.603, 232.951 ) ]
}
tracks/37/type = "value"
tracks/37/path = NodePath("Skeleton2D/hair:rotation_degrees")
tracks/37/interp = 1
tracks/37/loop_wrap = true
tracks/37/imported = false
tracks/37/enabled = true
tracks/37/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 4.0, 2.25352, 0.0 ]
}
tracks/38/type = "value"
tracks/38/path = NodePath("Skeleton2D/hair/hairleft:position")
tracks/38/interp = 1
tracks/38/loop_wrap = true
tracks/38/imported = false
tracks/38/enabled = true
tracks/38/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -78.7556, 77.4207 ), Vector2( -78.0065, 85.6614 ), Vector2( -77, 82.063 ), Vector2( -78.7556, 77.4207 ) ]
}
tracks/39/type = "value"
tracks/39/path = NodePath("Skeleton2D/hair/hairleft:rotation_degrees")
tracks/39/interp = 1
tracks/39/loop_wrap = true
tracks/39/imported = false
tracks/39/enabled = true
tracks/39/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/40/type = "value"
tracks/40/path = NodePath("Skeleton2D/dickpussy/sex:position")
tracks/40/interp = 1
tracks/40/loop_wrap = true
tracks/40/imported = false
tracks/40/enabled = true
tracks/40/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -1.15781, -95.451 ), Vector2( -1.15781, -95.451 ), Vector2( -1.15781, -95.451 ), Vector2( -1.15781, -95.451 ) ]
}
tracks/41/type = "value"
tracks/41/path = NodePath("Skeleton2D/dickpussy/sex:rotation_degrees")
tracks/41/interp = 1
tracks/41/loop_wrap = true
tracks/41/imported = false
tracks/41/enabled = true
tracks/41/keys = {
"times": PoolRealArray( 0, 1.9, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}

[sub_resource type="Animation" id=7]
resource_name = "slow"
length = 6.0
tracks/0/type = "value"
tracks/0/path = NodePath("Skeleton2D/voicecore/ass:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -1.47577, 207.297 ), Vector2( -1.476, 200 ), Vector2( -4.30431, 212.134 ), Vector2( -1.47577, 207.297 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Skeleton2D/voicecore/ass:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("Skeleton2D/voicecore:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 964.594, 512.179 ), Vector2( 964.594, 512.179 ), Vector2( 964.594, 512.179 ), Vector2( 964.594, 512.179 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("Skeleton2D/voicecore:rotation_degrees")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("Skeleton2D/voicecore/upperbody/righttit:position")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 181.538, -2.66968 ), Vector2( 190, 2 ), Vector2( 180, 2 ), Vector2( 181.538, -2.66968 ) ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("Skeleton2D/voicecore/upperbody/righttit:rotation_degrees")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("Skeleton2D/voicecore/ass/thighleft/legleft:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -80.9575, 336.529 ), Vector2( -80.9575, 336.529 ), Vector2( -80.9575, 336.529 ), Vector2( -80.9575, 336.529 ) ]
}
tracks/7/type = "value"
tracks/7/path = NodePath("Skeleton2D/voicecore/ass/thighleft/legleft:rotation_degrees")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/8/type = "value"
tracks/8/path = NodePath("Skeleton2D/voicecore/ass/tail/tailbend:position")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -7.5509, 130.255 ), Vector2( -7.5509, 130.255 ), Vector2( -7.5509, 130.255 ), Vector2( -7.5509, 130.255 ) ]
}
tracks/9/type = "value"
tracks/9/path = NodePath("Skeleton2D/voicecore/ass/tail/tailbend:rotation_degrees")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/10/type = "value"
tracks/10/path = NodePath("Skeleton2D/voicecore/upperbody:position")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -7.22711, -110.327 ), Vector2( -6.386, -95 ), Vector2( -6.80664, -101.403 ), Vector2( -7.22711, -110.327 ) ]
}
tracks/11/type = "value"
tracks/11/path = NodePath("Skeleton2D/voicecore/upperbody:rotation_degrees")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/12/type = "value"
tracks/12/path = NodePath("Skeleton2D/hair/hairright:position")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 72.0813, 80.0904 ), Vector2( 72.0813, 80.0904 ), Vector2( 72.0813, 77.9714 ), Vector2( 72.0813, 80.0904 ) ]
}
tracks/13/type = "value"
tracks/13/path = NodePath("Skeleton2D/hair/hairright:rotation_degrees")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/14/type = "value"
tracks/14/path = NodePath("Skeleton2D/voicecore/ass/thighleft:position")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -259.565, -248.239 ), Vector2( -250.118, -242.179 ), Vector2( -242.29, -262.313 ), Vector2( -259.565, -248.239 ) ]
}
tracks/15/type = "value"
tracks/15/path = NodePath("Skeleton2D/voicecore/ass/thighleft:rotation_degrees")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, -4.0, 6.0, 0.0 ]
}
tracks/16/type = "value"
tracks/16/path = NodePath("Skeleton2D/voicecore/ass/thighleft/legleft/footleft:position")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -77.7827, 111.118 ), Vector2( -65.5099, 97.7996 ), Vector2( -39.6042, 101.862 ), Vector2( -77.7827, 111.118 ) ]
}
tracks/17/type = "value"
tracks/17/path = NodePath("Skeleton2D/voicecore/ass/thighleft/legleft/footleft:rotation_degrees")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/18/type = "value"
tracks/18/path = NodePath("Skeleton2D/voicecore/ass/thighright:position")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 258.746, -242.872 ), Vector2( 253.984, -238.11 ), Vector2( 249.749, -276.365 ), Vector2( 258.746, -242.872 ) ]
}
tracks/19/type = "value"
tracks/19/path = NodePath("Skeleton2D/voicecore/ass/thighright:rotation_degrees")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 4.0, -6.0, 0.0 ]
}
tracks/20/type = "value"
tracks/20/path = NodePath("Skeleton2D/voicecore/ass/thighright/legright:position")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 73.0205, 323.83 ), Vector2( 73.0205, 323.83 ), Vector2( 73.0205, 323.83 ), Vector2( 73.0205, 323.83 ) ]
}
tracks/21/type = "value"
tracks/21/path = NodePath("Skeleton2D/voicecore/ass/thighright/legright:rotation_degrees")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/imported = false
tracks/21/enabled = true
tracks/21/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/22/type = "value"
tracks/22/path = NodePath("Skeleton2D/voicecore/upperbody/neck/face:position")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/imported = false
tracks/22/enabled = true
tracks/22/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -13.3484, 24.0271 ), Vector2( -11.5667, 16.8999 ), Vector2( -12.4575, 20.4635 ), Vector2( -13.3484, 24.0271 ) ]
}
tracks/23/type = "value"
tracks/23/path = NodePath("Skeleton2D/voicecore/upperbody/neck/face:rotation_degrees")
tracks/23/interp = 1
tracks/23/loop_wrap = true
tracks/23/imported = false
tracks/23/enabled = true
tracks/23/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/24/type = "value"
tracks/24/path = NodePath("Skeleton2D/voicecore/ass/thighright/legright/footright:position")
tracks/24/interp = 1
tracks/24/loop_wrap = true
tracks/24/imported = false
tracks/24/enabled = true
tracks/24/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 73.0205, 117.468 ), Vector2( 58.3506, 90.1401 ), Vector2( 66.7362, 96.3264 ), Vector2( 73.0205, 117.468 ) ]
}
tracks/25/type = "value"
tracks/25/path = NodePath("Skeleton2D/voicecore/ass/thighright/legright/footright:rotation_degrees")
tracks/25/interp = 1
tracks/25/loop_wrap = true
tracks/25/imported = false
tracks/25/enabled = true
tracks/25/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/26/type = "value"
tracks/26/path = NodePath("Skeleton2D/voicecore/upperbody/lefttit:position")
tracks/26/interp = 1
tracks/26/loop_wrap = true
tracks/26/imported = false
tracks/26/enabled = true
tracks/26/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -217.579, 9.34387 ), Vector2( -220, 15 ), Vector2( -212, 10.803 ), Vector2( -217.579, 9.34387 ) ]
}
tracks/27/type = "value"
tracks/27/path = NodePath("Skeleton2D/voicecore/upperbody/lefttit:rotation_degrees")
tracks/27/interp = 1
tracks/27/loop_wrap = true
tracks/27/imported = false
tracks/27/enabled = true
tracks/27/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/28/type = "value"
tracks/28/path = NodePath("Skeleton2D/voicecore/upperbody/neck:position")
tracks/28/interp = 1
tracks/28/loop_wrap = true
tracks/28/imported = false
tracks/28/enabled = true
tracks/28/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -10.6788, -89.4343 ), Vector2( -10.6788, -89.4343 ), Vector2( -10.6788, -89.4343 ), Vector2( -10.6788, -89.4343 ) ]
}
tracks/29/type = "value"
tracks/29/path = NodePath("Skeleton2D/voicecore/upperbody/neck:rotation_degrees")
tracks/29/interp = 1
tracks/29/loop_wrap = true
tracks/29/imported = false
tracks/29/enabled = true
tracks/29/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/30/type = "value"
tracks/30/path = NodePath("Skeleton2D/voicecore/ass/tail:position")
tracks/30/interp = 1
tracks/30/loop_wrap = true
tracks/30/imported = false
tracks/30/enabled = true
tracks/30/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 198.523, 156.365 ), Vector2( 198.523, 156.365 ), Vector2( 198.523, 156.365 ), Vector2( 198.523, 156.365 ) ]
}
tracks/31/type = "value"
tracks/31/path = NodePath("Skeleton2D/voicecore/ass/tail:rotation_degrees")
tracks/31/interp = 1
tracks/31/loop_wrap = true
tracks/31/imported = false
tracks/31/enabled = true
tracks/31/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/32/type = "value"
tracks/32/path = NodePath("Skeleton2D/dickpussy:position")
tracks/32/interp = 1
tracks/32/loop_wrap = true
tracks/32/imported = false
tracks/32/enabled = true
tracks/32/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 941.987, 681.477 ), Vector2( 941.987, 681.477 ), Vector2( 941.987, 681.477 ), Vector2( 941.987, 681.477 ) ]
}
tracks/33/type = "value"
tracks/33/path = NodePath("Skeleton2D/dickpussy:rotation_degrees")
tracks/33/interp = 1
tracks/33/loop_wrap = true
tracks/33/imported = false
tracks/33/enabled = true
tracks/33/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/34/type = "value"
tracks/34/path = NodePath("Skeleton2D/voicecore/belly:position")
tracks/34/interp = 1
tracks/34/loop_wrap = true
tracks/34/imported = false
tracks/34/enabled = true
tracks/34/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 25.3619, -8.00903 ), Vector2( 20.907, 11 ), Vector2( 15, 10 ), Vector2( 25.3619, -8.00903 ) ]
}
tracks/35/type = "value"
tracks/35/path = NodePath("Skeleton2D/voicecore/belly:rotation_degrees")
tracks/35/interp = 1
tracks/35/loop_wrap = true
tracks/35/imported = false
tracks/35/enabled = true
tracks/35/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 1.0, 2.0, 0.0 ]
}
tracks/36/type = "value"
tracks/36/path = NodePath("Skeleton2D/hair:position")
tracks/36/interp = 1
tracks/36/loop_wrap = true
tracks/36/imported = false
tracks/36/enabled = true
tracks/36/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 952.603, 232.951 ), Vector2( 953.603, 242.951 ), Vector2( 953.103, 242.189 ), Vector2( 952.603, 232.951 ) ]
}
tracks/37/type = "value"
tracks/37/path = NodePath("Skeleton2D/hair:rotation_degrees")
tracks/37/interp = 1
tracks/37/loop_wrap = true
tracks/37/imported = false
tracks/37/enabled = true
tracks/37/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/38/type = "value"
tracks/38/path = NodePath("Skeleton2D/hair/hairleft:position")
tracks/38/interp = 1
tracks/38/loop_wrap = true
tracks/38/imported = false
tracks/38/enabled = true
tracks/38/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -78.7556, 77.4207 ), Vector2( -78.7556, 77.4207 ), Vector2( -78.7556, 77.4207 ), Vector2( -78.7556, 77.4207 ) ]
}
tracks/39/type = "value"
tracks/39/path = NodePath("Skeleton2D/hair/hairleft:rotation_degrees")
tracks/39/interp = 1
tracks/39/loop_wrap = true
tracks/39/imported = false
tracks/39/enabled = true
tracks/39/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/40/type = "value"
tracks/40/path = NodePath("Skeleton2D/dickpussy/sex:position")
tracks/40/interp = 1
tracks/40/loop_wrap = true
tracks/40/imported = false
tracks/40/enabled = true
tracks/40/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -1.15781, -95.451 ), Vector2( -1.158, -100 ), Vector2( 1.67053, -84.9975 ), Vector2( -1.15781, -95.451 ) ]
}
tracks/41/type = "value"
tracks/41/path = NodePath("Skeleton2D/dickpussy/sex:rotation_degrees")
tracks/41/interp = 1
tracks/41/loop_wrap = true
tracks/41/imported = false
tracks/41/enabled = true
tracks/41/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0, 0.0, 0.0 ]
}
tracks/42/type = "method"
tracks/42/path = NodePath(".")
tracks/42/interp = 1
tracks/42/loop_wrap = true
tracks/42/imported = false
tracks/42/enabled = true
tracks/42/keys = {
"times": PoolRealArray( 0, 0.8, 1.7, 4.4, 5 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"values": [ {
"args": [ 2, 1 ],
"method": "switch_texture"
}, {
"args": [ 0, 1 ],
"method": "switch_texture"
}, {
"args": [ 1, 1 ],
"method": "switch_texture"
}, {
"args": [ 1, 1 ],
"method": "switch_texture"
}, {
"args": [ 0, 0 ],
"method": "switch_texture"
} ]
}

[sub_resource type="Animation" id=3]
resource_name = "black"
length = 2.5
tracks/0/type = "value"
tracks/0/path = NodePath("voice:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1.3, 2.5 ),
"transitions": PoolRealArray( 2, 1, 1 ),
"update": 0,
"values": [ Color( 0, 0, 0, 0 ), Color( 0, 0, 0, 1 ), Color( 0, 0, 0, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("player:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1.1, 2.5 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 0, 0, 0, 0 ), Color( 0, 0, 0, 0 ), Color( 0, 0, 0, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("voice/expression:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1.3, 2.5 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ false, true, true ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("voice/mouth:visible")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 1.3, 2.5 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ false, true, true ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("both:modulate")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 2.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 0, 0, 0, 0 ), Color( 0, 0, 0, 1 ) ]
}

[sub_resource type="Animation" id=4]
resource_name = "white"
length = 2.5
tracks/0/type = "value"
tracks/0/path = NodePath("voice:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 2.5 ),
"transitions": PoolRealArray( 2, 1 ),
"update": 0,
"values": [ Color( 0, 0, 0, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("player:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 2.5 ),
"transitions": PoolRealArray( 2, 1 ),
"update": 0,
"values": [ Color( 0, 0, 0, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("voice/expression:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("voice/mouth:visible")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("both:modulate")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 2.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 0, 0, 0, 1 ), Color( 1, 1, 1, 1 ) ]
}

[node name="catbadend1" type="CanvasLayer"]
layer = 4
script = ExtResource( 18 )

[node name="Skeleton2D" type="Skeleton2D" parent="."]

[node name="voicecore" type="Bone2D" parent="Skeleton2D"]
position = Vector2( 964.594, 512.179 )
rest = Transform2D( 1, 0, 0, 1, 964.594, 512.179 )

[node name="ass" type="Bone2D" parent="Skeleton2D/voicecore"]
position = Vector2( -1.47577, 207.297 )
rest = Transform2D( 1, 0, 0, 1, -1.47577, 207.297 )

[node name="thighleft" type="Bone2D" parent="Skeleton2D/voicecore/ass"]
position = Vector2( -259.565, -248.239 )
rest = Transform2D( 1, 0, 0, 1, -259.565, -248.239 )

[node name="legleft" type="Bone2D" parent="Skeleton2D/voicecore/ass/thighleft"]
position = Vector2( -80.9575, 336.529 )
rest = Transform2D( 1, 0, 0, 1, -80.9575, 336.529 )

[node name="footleft" type="Bone2D" parent="Skeleton2D/voicecore/ass/thighleft/legleft"]
position = Vector2( -77.7827, 111.118 )
rest = Transform2D( 1, 0, 0, 1, -77.7827, 111.118 )

[node name="thighright" type="Bone2D" parent="Skeleton2D/voicecore/ass"]
position = Vector2( 258.746, -242.872 )
rest = Transform2D( 1, 0, 0, 1, 258.746, -242.872 )

[node name="legright" type="Bone2D" parent="Skeleton2D/voicecore/ass/thighright"]
position = Vector2( 73.0205, 323.83 )
rest = Transform2D( 1, 0, 0, 1, 73.0205, 323.83 )

[node name="footright" type="Bone2D" parent="Skeleton2D/voicecore/ass/thighright/legright"]
position = Vector2( 73.0205, 117.468 )
rest = Transform2D( 1, 0, 0, 1, 73.0205, 117.468 )

[node name="tail" type="Bone2D" parent="Skeleton2D/voicecore/ass"]
position = Vector2( 198.523, 156.365 )
rest = Transform2D( 1, 0, 0, 1, 198.523, 156.365 )

[node name="tailbend" type="Bone2D" parent="Skeleton2D/voicecore/ass/tail"]
position = Vector2( -7.5509, 130.255 )
rest = Transform2D( 1, 0, 0, 1, -7.5509, 130.255 )

[node name="upperbody" type="Bone2D" parent="Skeleton2D/voicecore"]
position = Vector2( -7.22711, -110.327 )
rest = Transform2D( 1, 0, 0, 1, -7.22711, -110.327 )

[node name="lefttit" type="Bone2D" parent="Skeleton2D/voicecore/upperbody"]
position = Vector2( -217.579, 9.34387 )
rest = Transform2D( 1, 0, 0, 1, -217.579, 9.34387 )

[node name="righttit" type="Bone2D" parent="Skeleton2D/voicecore/upperbody"]
position = Vector2( 181.538, -2.66968 )
rest = Transform2D( 1, 0, 0, 1, 181.538, -2.66968 )

[node name="neck" type="Bone2D" parent="Skeleton2D/voicecore/upperbody"]
position = Vector2( -10.6788, -89.4343 )
rest = Transform2D( 1, 0, 0, 1, -10.6788, -89.4343 )

[node name="face" type="Bone2D" parent="Skeleton2D/voicecore/upperbody/neck"]
position = Vector2( -13.3484, 24.0271 )
rest = Transform2D( 1, 0, 0, 1, -13.3484, 24.0271 )

[node name="belly" type="Bone2D" parent="Skeleton2D/voicecore"]
position = Vector2( 25.3619, -8.00903 )
rest = Transform2D( 1, 0, 0, 1, 25.3619, -8.00903 )

[node name="hair" type="Bone2D" parent="Skeleton2D"]
position = Vector2( 952.603, 232.951 )
rest = Transform2D( 1, 0, 0, 1, 952.603, 232.951 )

[node name="hairleft" type="Bone2D" parent="Skeleton2D/hair"]
position = Vector2( -78.7556, 77.4207 )
rest = Transform2D( 1, 0, 0, 1, -78.7556, 77.4207 )

[node name="hairright" type="Bone2D" parent="Skeleton2D/hair"]
position = Vector2( 72.0813, 80.0904 )
rest = Transform2D( 1, 0, 0, 1, 72.0813, 80.0904 )

[node name="dickpussy" type="Bone2D" parent="Skeleton2D"]
position = Vector2( 941.987, 681.477 )
rest = Transform2D( 1, 0, 0, 1, 941.987, 681.477 )

[node name="sex" type="Bone2D" parent="Skeleton2D/dickpussy"]
position = Vector2( -1.15781, -95.451 )
rest = Transform2D( 1, 0, 0, 1, -1.15781, -95.451 )
default_length = 5.0

[node name="backgrounds" type="Node2D" parent="."]

[node name="day" type="Sprite" parent="backgrounds"]
texture = ExtResource( 21 )
centered = false

[node name="night" type="Sprite" parent="backgrounds"]
self_modulate = Color( 1, 1, 1, 0 )
texture = ExtResource( 20 )
centered = false

[node name="AnimationPlayer" type="AnimationPlayer" parent="backgrounds"]
autoplay = "daytime"
anims/daytime = SubResource( 11 )
anims/nighttime = SubResource( 12 )

[node name="borderright" type="Sprite" parent="backgrounds"]
position = Vector2( 1919, -14 )
scale = Vector2( 1, 1.025 )
texture = ExtResource( 22 )
centered = false

[node name="borderleft" type="Sprite" parent="backgrounds"]
position = Vector2( -15, -14 )
scale = Vector2( 1, 1.025 )
texture = ExtResource( 22 )
centered = false
flip_h = true

[node name="borderbottom" type="Sprite" parent="backgrounds"]
position = Vector2( -15, 1079 )
scale = Vector2( 1.015, 1 )
texture = ExtResource( 23 )
centered = false

[node name="silhouette" type="Node2D" parent="."]
visible = false

[node name="left" type="Sprite" parent="silhouette"]
texture = ExtResource( 13 )
centered = false
offset = Vector2( 500, 445 )
__meta__ = {
"_edit_lock_": true
}

[node name="middle" type="Sprite" parent="silhouette"]
texture = ExtResource( 10 )
centered = false
offset = Vector2( 865, 405 )
__meta__ = {
"_edit_lock_": true
}

[node name="right" type="Sprite" parent="silhouette"]
texture = ExtResource( 8 )
centered = false
offset = Vector2( 1160, 530 )
__meta__ = {
"_edit_lock_": true
}

[node name="rules" type="Sprite" parent="silhouette"]
texture = ExtResource( 11 )
centered = false
offset = Vector2( 790, 450 )
__meta__ = {
"_edit_lock_": true
}

[node name="silhouettesfade" type="AnimationPlayer" parent="silhouette"]
anims/RESET = SubResource( 5 )
anims/appear = SubResource( 6 )

[node name="player" type="Node2D" parent="."]
modulate = Color( 0, 0, 0, 0 )
material = ExtResource( 24 )
__meta__ = {
"_edit_lock_": true
}

[node name="shadow" type="Polygon2D" parent="player"]
position = Vector2( 665, 685 )
texture = ExtResource( 19 )
skeleton = NodePath("../../Skeleton2D")
polygon = PoolVector2Array( -10.6017, 415.702, 338.985, 407.438, 495.183, 402.479, 792.704, 396.694, 795.183, -11.5703, 18.3239, 31.405, 257.167, 219.835, 428.241, 247.107, 344.77, 214.05 )
uv = PoolVector2Array( -10.6017, 415.702, 338.985, 407.438, 495.183, 402.479, 792.704, 396.694, 795.183, -11.5703, 18.3239, 31.405, 257.167, 219.835, 428.241, 247.107, 344.77, 214.05 )
polygons = [ PoolIntArray( 6, 8, 7, 2, 1 ), PoolIntArray( 6, 5, 0, 1 ), PoolIntArray( 5, 4, 7, 8, 6 ), PoolIntArray( 7, 4, 3, 2 ) ]
bones = [ "voicecore", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft/footleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright/legright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright/legright/footright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/tail", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/tail/tailbend", PoolRealArray( 0, 0, 0.54, 1, 0.54, 0, 0, 0, 0 ), "voicecore/upperbody", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/lefttit", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/righttit", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/neck", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/neck/face", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/belly", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair/hairleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair/hairright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "dickpussy", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "dickpussy/sex", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ) ]
internal_vertex_count = 3
__meta__ = {
"_edit_lock_": true
}

[node name="dick" type="Polygon2D" parent="player"]
use_parent_material = true
position = Vector2( 885, 565 )
z_index = 1
texture = ExtResource( 6 )
skeleton = NodePath("../../Skeleton2D")
polygon = PoolVector2Array( 39.1691, 12.2727, 75.0782, -3.63636, 109.624, 6.36364, 112.351, 155, 93.7304, 196.865, 54.232, 197.179, 37.8055, 162.273 )
uv = PoolVector2Array( 39.1691, 12.2727, 75.0782, -3.63636, 109.624, 6.36364, 112.351, 155, 95.0782, 200.909, 52.8055, 200.909, 37.8055, 162.273 )
polygons = [ PoolIntArray( 0, 1, 2, 3, 6 ), PoolIntArray( 6, 5, 4, 3 ) ]
bones = [ "voicecore", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass", PoolRealArray( 0.57, 0.57, 0.57, 1, 1, 1, 1 ), "voicecore/ass/thighleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft/footleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright/legright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright/legright/footright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/tail", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/tail/tailbend", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/lefttit", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/righttit", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/neck", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/neck/face", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/belly", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "hair", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "hair/hairleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "hair/hairright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "dickpussy", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "dickpussy/sex", PoolRealArray( 1, 1, 1, 1, 0, 0, 1 ) ]
__meta__ = {
"_edit_lock_": true
}

[node name="hands" type="Polygon2D" parent="player"]
use_parent_material = true
position = Vector2( 775, 685 )
z_index = 1
texture = ExtResource( 3 )
skeleton = NodePath("../../Skeleton2D")
polygon = PoolVector2Array( 25.3165, -7.59494, 106.962, 33.8608, 263.924, 34.8101, 341.456, -12.0253, 378.481, 40.5063, 304.114, 94.6202, 189.873, 77.8481, 73.7342, 87.3418, -18.038, 46.519 )
uv = PoolVector2Array( 25.3165, -7.59494, 106.962, 33.8608, 263.924, 34.8101, 341.456, -12.0253, 378.481, 40.5063, 321.203, 86.076, 189.873, 77.8481, 73.7342, 87.3418, -18.038, 46.519 )
polygons = [ PoolIntArray( 7, 1, 0, 8 ), PoolIntArray( 2, 5, 4, 3 ), PoolIntArray( 7, 1, 2, 5, 6 ) ]
bones = [ "voicecore", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass", PoolRealArray( 0.06, 0.06, 0.06, 0.06, 0.06, 0.06, 0, 0.06, 0.06 ), "voicecore/ass/thighleft", PoolRealArray( 0.92, 0.92, 0, 0, 0, 0, 0, 0.46, 0.46 ), "voicecore/ass/thighleft/legleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft/footleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright", PoolRealArray( 0, 0, 0.92, 0.92, 0.46, 0.46, 0, 0, 0 ), "voicecore/ass/thighright/legright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright/legright/footright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/tail", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/dickpussy", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/lefttit", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/righttit", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/neck", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/neck/face", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/belly", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair/hairleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair/hairright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0 ) ]
__meta__ = {
"_edit_lock_": true
}

[node name="legs" type="Polygon2D" parent="player"]
use_parent_material = true
position = Vector2( 665, 685 )
texture = ExtResource( 12 )
skeleton = NodePath("../../Skeleton2D")
polygon = PoolVector2Array( -9.0164, -9.83607, 295.082, 30.3279, 603.279, -10.6557, 551.639, 315.574, 608.197, 398.361, 454.918, 400.82, 369.672, 402.459, 245.902, 402.459, 92.6229, 402.459, -14.7541, 402.459, 43.4426, 304.918, 87.7049, 12.2951, 137.705, 145.902, 503.279, 11.4754, 457.377, 139.344, 448.361, 279.508, 442.623, 327.049, 256.557, 183.607, 328.689, 186.066, 294.262, 161.475, 415.038, 234.586, 451.88, 215.038, 158.878, 328.037, 146.218, 278.992, 291.589, 316.822 )
uv = PoolVector2Array( -9.83607, -17.2131, 295.082, 30.3279, 600, -30.3279, 551.639, 315.574, 608.197, 398.361, 454.918, 400.82, 369.672, 402.459, 245.902, 402.459, 92.6229, 402.459, -14.7541, 402.459, 43.4426, 304.918, 87.7049, 12.2951, 137.705, 145.902, 503.279, 11.4754, 457.377, 139.344, 448.361, 279.508, 442.623, 327.049, 258.197, 177.869, 327.049, 181.148, 295.902, 143.443, 415.038, 234.586, 451.88, 215.038, 158.878, 328.037, 147.664, 291.589, 291.589, 316.822 )
polygons = [ PoolIntArray( 9, 10, 23, 22, 8 ), PoolIntArray( 10, 0, 11, 12, 23 ), PoolIntArray( 8, 22, 7 ), PoolIntArray( 6, 24, 17, 19, 18, 20, 16, 5 ), PoolIntArray( 12, 17, 24, 6, 7, 22, 23 ), PoolIntArray( 0, 1, 2, 13, 14, 18, 19, 17, 12, 11 ), PoolIntArray( 13, 2, 3, 15, 21, 14 ), PoolIntArray( 18, 14, 21, 20 ), PoolIntArray( 20, 21, 15, 16 ), PoolIntArray( 15, 3, 4, 5, 16 ) ]
bones = [ "voicecore", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft/footleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright/legright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright/legright/footright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/tail", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/tail/tailbend", PoolRealArray( 0, 0, 0, 0.49, 0.49, 0.49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.49, 0.49, 0, 0, 0, 0, 0.49, 0, 0, 0 ), "voicecore/ass/dickpussy", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/lefttit", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/righttit", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/neck", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/neck/face", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/belly", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair/hairleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair/hairright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ) ]
internal_vertex_count = 14
__meta__ = {
"_edit_lock_": true
}

[node name="voice" type="Node2D" parent="."]
modulate = Color( 0, 0, 0, 0 )
__meta__ = {
"_edit_lock_": true
}

[node name="cattail" type="Polygon2D" parent="voice"]
position = Vector2( 825, 680 )
texture = ExtResource( 5 )
skeleton = NodePath("../../Skeleton2D")
polygon = PoolVector2Array( 16.4223, 29.0323, 108.798, 3.22581, 185.631, 23.4604, 91.7889, 97.654, 418.987, 202.532, 380.159, 358.73, 280.952, 320.635, 241.27, 406.349, 141.27, 408.73, 193.651, 283.333, 267.46, 234.921, 23.8095, 167.46, -7.93651, 100 )
uv = PoolVector2Array( 16.4223, 29.0323, 108.798, 3.22581, 185.631, 23.4604, 91.7889, 97.654, 418.987, 202.532, 380.159, 358.73, 280.952, 320.635, 241.27, 406.349, 141.27, 408.73, 193.651, 283.333, 267.46, 234.921, 23.8095, 167.46, -7.93651, 100 )
polygons = [ PoolIntArray( 0, 1, 2, 3, 12 ), PoolIntArray( 12, 11, 3 ), PoolIntArray( 5, 6, 10, 4 ), PoolIntArray( 10, 9, 8, 7, 6 ), PoolIntArray( 11, 3, 10 ), PoolIntArray( 3, 4, 10 ) ]
bones = [ "voicecore", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft/footleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright/legright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright/legright/footright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/tail", PoolRealArray( 0, 0, 0, 0.49, 0.49, 0.98, 0.98, 0, 0, 0.49, 0.49, 0.49, 0.49 ), "voicecore/ass/tail/tailbend", PoolRealArray( 0, 0, 0, 0, 0.98, 0.98, 0.98, 0, 0, 0, 0.98, 0, 0 ), "voicecore/ass/dickpussy", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/lefttit", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/righttit", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/neck", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/neck/face", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/belly", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair/hairleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair/hairright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ) ]
__meta__ = {
"_edit_lock_": true
}

[node name="head" type="Polygon2D" parent="voice"]
position = Vector2( 790, 125 )
texture = ExtResource( 14 )
skeleton = NodePath("../../Skeleton2D")
polygon = PoolVector2Array( 164.251, 303.865, 100, 288.889, 28.0193, 257.488, 33.3333, 228.019, 0.966184, 196.135, 43.4783, 5.7971, 111.594, 1.44928, 246.377, 2.89855, 290.338, 18.8406, 321.256, 190.338, 294.686, 218.841, 306.763, 250.725, 247.826, 291.787, 165.217, 254.589, 110.628, 231.884, 213.043, 206.763, 237.681, 175.362, 98.0676, 168.116, 120.29, 80.6763, 194.686, 77.2947 )
uv = PoolVector2Array( 164.251, 303.865, 100, 288.889, 28.0193, 257.488, 33.3333, 228.019, 0.966184, 196.135, 43.4783, 5.7971, 111.594, 1.44928, 246.377, 2.89855, 290.338, 18.8406, 321.256, 190.338, 294.686, 218.841, 306.763, 250.725, 247.826, 291.787, 165.217, 254.589, 110.628, 231.884, 213.043, 206.763, 237.681, 175.362, 98.0676, 168.116, 120.29, 80.6763, 194.686, 77.2947 )
polygons = [ PoolIntArray( 5, 6, 7, 8, 9, 10, 11, 12, 16, 19, 18, 17, 14, 1, 2, 3, 4 ), PoolIntArray( 18, 17, 16, 19 ), PoolIntArray( 17, 14, 13, 15, 16 ), PoolIntArray( 14, 13, 15, 16, 12, 0, 1 ) ]
bones = [ "voicecore", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft/footleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright/legright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright/legright/footright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/tail", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/tail/tailbend", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/dickpussy", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/lefttit", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/righttit", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/neck", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0 ), "voicecore/upperbody/neck/face", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.06, 0, 0, 0, 0, 0, 0 ), "voicecore/belly", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair/hairleft", PoolRealArray( 0, 0, 0.98, 0.98, 0.98, 0.98, 0.98, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.49, 0 ), "hair/hairright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0.98, 0.98, 0.98, 0.98, 0.98, 0, 0, 0, 0, 0, 0, 0, 0.49 ) ]
internal_vertex_count = 7
__meta__ = {
"_edit_lock_": true
}

[node name="expression" type="Polygon2D" parent="voice"]
visible = false
position = Vector2( 870, 255 )
texture = ExtResource( 1 )
skeleton = NodePath("../../Skeleton2D")
polygon = PoolVector2Array( 2.30352, 39.5664, 4.47154, 61.1111, 30.6233, 75.8808, 79.4038, 70.5962, 138.076, 69.6477, 144.038, 60.4336, 157.588, 27.3713, 131.03, 1.21951, 76.1518, 1.62602, 17.6205, 7.53012, 77.1003, 32.7913 )
uv = PoolVector2Array( 2.30352, 37.6694, 4.47154, 61.1111, 30.6233, 75.8808, 79.4038, 70.5962, 138.076, 69.6477, 144.038, 60.4336, 160.027, 23.9837, 131.03, 1.21951, 76.1518, 1.62602, 18.1572, 4.47154, 76.4228, 29.5393 )
polygons = [ PoolIntArray( 0, 10, 8, 9 ), PoolIntArray( 10, 6, 7, 8 ), PoolIntArray( 10, 3, 4, 6 ), PoolIntArray( 10, 0, 1, 2, 3 ) ]
bones = [ "voicecore", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft/footleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright/legright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright/legright/footright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/tail", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/tail/tailbend", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/dickpussy", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/lefttit", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/righttit", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/neck", PoolRealArray( 0.98, 0.98, 0.98, 0.98, 0.98, 0.98, 0.98, 0.98, 0.98, 0.98, 0.98 ), "voicecore/upperbody/neck/face", PoolRealArray( 0.54, 0, 0, 0, 0, 0, 0.54, 0, 0, 0, 0.54 ), "voicecore/belly", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair/hairleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair/hairright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ) ]
internal_vertex_count = 1
__meta__ = {
"_edit_lock_": true
}

[node name="mouth" type="Polygon2D" parent="voice"]
visible = false
position = Vector2( 920, 330 )
texture = ExtResource( 2 )
skeleton = NodePath("../../Skeleton2D")
polygon = PoolVector2Array( 4.7153, 6.04982, 61.4769, 0.978648, 56.4057, 26.8683, 31.9395, 38.4342, 12.7224, 30.605 )
uv = PoolVector2Array( 6.49466, 5.51601, 57.5623, 0.800712, 55.8719, 26.9573, 31.9395, 38.4342, 12.7224, 30.605 )
polygons = [ PoolIntArray( 0, 1, 2, 3, 4 ) ]
bones = [ "voicecore", PoolRealArray( 0, 0, 0, 0, 0 ), "voicecore/ass", PoolRealArray( 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft", PoolRealArray( 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft", PoolRealArray( 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft/footleft", PoolRealArray( 0, 0, 0, 0, 0 ), "voicecore/ass/thighright", PoolRealArray( 0, 0, 0, 0, 0 ), "voicecore/ass/thighright/legright", PoolRealArray( 0, 0, 0, 0, 0 ), "voicecore/ass/thighright/legright/footright", PoolRealArray( 0, 0, 0, 0, 0 ), "voicecore/ass/tail", PoolRealArray( 0, 0, 0, 0, 0 ), "voicecore/ass/tail/tailbend", PoolRealArray( 0, 0, 0, 0, 0 ), "voicecore/ass/dickpussy", PoolRealArray( 0, 0, 0, 0, 0 ), "voicecore/upperbody", PoolRealArray( 0, 0, 0, 0, 0 ), "voicecore/upperbody/lefttit", PoolRealArray( 0, 0, 0, 0, 0 ), "voicecore/upperbody/righttit", PoolRealArray( 0, 0, 0, 0, 0 ), "voicecore/upperbody/neck", PoolRealArray( 0.98, 0.98, 0.98, 0.98, 0.98 ), "voicecore/upperbody/neck/face", PoolRealArray( 0, 0, 0.54, 1, 0.54 ), "voicecore/belly", PoolRealArray( 0, 0, 0, 0, 0 ), "hair", PoolRealArray( 0, 0, 0, 0, 0 ), "hair/hairleft", PoolRealArray( 0, 0, 0, 0, 0 ), "hair/hairright", PoolRealArray( 0, 0, 0, 0, 0 ) ]
__meta__ = {
"_edit_lock_": true
}

[node name="tits" type="Polygon2D" parent="voice"]
position = Vector2( 715, 345 )
texture = ExtResource( 17 )
skeleton = NodePath("../../Skeleton2D")
polygon = PoolVector2Array( 244.348, 40.4348, 124.348, 2.17391, 4.78261, 24.3478, 0.869565, 137.826, 89.1304, 204.348, 156.522, 200.87, 144.348, 262.174, 243.043, 329.13, 349.13, 260.87, 333.043, 196.522, 387.391, 202.609, 472.174, 161.739, 498.261, 25.6522, 366.957, 2.17391, 197.826, 150, 291.304, 152.174, 243.043, 108.696 )
uv = PoolVector2Array( 244.348, 40.4348, 124.348, 2.17391, 4.78261, 24.3478, 4.34783, 140, 89.1304, 204.348, 156.522, 200.87, 144.348, 262.174, 243.043, 329.13, 349.13, 260.87, 330.87, 198.696, 387.391, 202.609, 472.174, 161.739, 498.261, 25.6522, 371.304, 3.04348, 199.565, 147.826, 278.261, 144.783, 243.043, 108.696 )
polygons = [ PoolIntArray( 5, 14, 16, 0, 1, 2, 3, 4 ), PoolIntArray( 0, 13, 12, 11, 10, 9, 15, 16 ), PoolIntArray( 5, 14, 16, 15, 9, 8, 7, 6 ) ]
bones = [ "voicecore", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft/footleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright/legright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright/legright/footright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/tail", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/tail/tailbend", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/dickpussy", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody", PoolRealArray( 0.49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.49, 0.49, 0.49 ), "voicecore/upperbody/lefttit", PoolRealArray( 0, 0.98, 0.98, 1, 0.98, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/righttit", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.98, 0.98, 0.98, 0.98, 0, 0, 0 ), "voicecore/upperbody/neck", PoolRealArray( 0.49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/neck/face", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/belly", PoolRealArray( 0, 0, 0, 0, 0, 0.98, 0, 0, 0, 0.98, 0, 0, 0, 0, 0.98, 0.98, 0.98 ), "hair", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair/hairleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair/hairright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ) ]
internal_vertex_count = 3
__meta__ = {
"_edit_lock_": true
}

[node name="legs" type="Polygon2D" parent="voice"]
position = Vector2( 490, 430 )
texture = ExtResource( 16 )
skeleton = NodePath("../../Skeleton2D")
polygon = PoolVector2Array( 20.4918, 543.443, 3.27869, 478.689, 68.0328, 359.016, 132.787, 3.27869, 244.262, 4.91803, 421.311, 78.6885, 517.213, 82.7869, 663.934, 4.09836, 805.738, 4.91803, 877.869, 339.344, 945.902, 475.41, 917.213, 547.541, 822.951, 547.541, 726.229, 432.787, 743.443, 331.967, 715.574, 243.443, 654.098, 313.115, 547.541, 347.541, 472.951, 329.508, 396.721, 349.18, 309.836, 316.393, 227.049, 249.18, 193.443, 331.148, 221.311, 441.803, 113.934, 550.82, 255.738, 118.033, 690.984, 119.672, 472.131, 222.131, 136.066, 363.115, 809.016, 367.213 )
uv = PoolVector2Array( 22.1311, 540.984, 3.27869, 478.689, 68.0328, 359.016, 132.787, 3.27869, 244.262, 4.91803, 421.311, 78.6885, 517.213, 82.7869, 663.934, 4.09836, 805.738, 4.91803, 877.869, 339.344, 945.902, 475.41, 917.213, 547.541, 822.951, 547.541, 726.229, 432.787, 743.443, 331.967, 715.574, 243.443, 654.098, 313.115, 547.541, 347.541, 472.951, 329.508, 396.721, 349.18, 309.836, 316.393, 227.049, 249.18, 193.443, 331.148, 221.311, 441.803, 113.934, 550.82, 255.738, 118.033, 690.984, 119.672, 472.131, 222.131, 136.066, 363.115, 809.016, 367.213 )
polygons = [ PoolIntArray( 0, 1, 2, 28, 22, 23, 24 ), PoolIntArray( 2, 3, 4, 25, 21, 22, 28 ), PoolIntArray( 4, 5, 27, 18, 19, 20, 21, 25 ), PoolIntArray( 5, 6, 27 ), PoolIntArray( 18, 27, 6, 7, 26, 15, 16, 17 ), PoolIntArray( 7, 8, 9, 29, 14, 15, 26 ), PoolIntArray( 14, 13, 12, 11, 10, 9, 29 ) ]
bones = [ "voicecore", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft", PoolRealArray( 0, 0, 0, 0.5, 0.5, 0.5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.5, 0.96, 0.96, 0.96, 0, 0, 0, 0.5, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft", PoolRealArray( 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.57, 1, 1, 1, 0.57, 0, 0, 1, 0 ), "voicecore/ass/thighleft/legleft/footleft", PoolRealArray( 0.5, 0.5, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0.5, 0, 0, 0, 1, 0 ), "voicecore/ass/thighright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0.5, 0.5, 0.5, 0, 0, 0, 0, 0, 0, 0.96, 0.92, 0.96, 0.5, 0, 0, 0, 0, 0, 0, 0, 0.5, 0, 0, 0 ), "voicecore/ass/thighright/legright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0.57, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.57, 0, 0, 1 ), "voicecore/ass/thighright/legright/footright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0.5, 0.5, 0.5, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1 ), "voicecore/ass/tail", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/tail/tailbend", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/lefttit", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/righttit", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/neck", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/neck/face", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/belly", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair/hairleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair/hairright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "dickpussy", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "dickpussy/sex", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ) ]
internal_vertex_count = 5
__meta__ = {
"_edit_lock_": true
}

[node name="pussy" type="Polygon2D" parent="voice"]
position = Vector2( 885, 565 )
texture = ExtResource( 15 )
skeleton = NodePath("../../Skeleton2D")
polygon = PoolVector2Array( 73.3471, 4.95868, 12.8099, 32.2314, 17.562, 114.05, 75, 154.959, 139.876, 105.165, 137.81, 17.9752 )
uv = PoolVector2Array( 73.3471, 4.95868, 12.8099, 32.2314, 17.562, 114.05, 75, 154.959, 139.876, 105.165, 137.81, 17.9752 )
polygons = [ PoolIntArray( 0, 1, 2, 3 ), PoolIntArray( 0, 5, 4, 3 ) ]
bones = [ "voicecore", PoolRealArray( 0, 0, 0, 0, 0, 0 ), "voicecore/ass", PoolRealArray( 1, 1, 1, 1, 1, 1 ), "voicecore/ass/thighleft", PoolRealArray( 0, 0, 1, 0.5, 1, 0.5 ), "voicecore/ass/thighleft/legleft", PoolRealArray( 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft/footleft", PoolRealArray( 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright", PoolRealArray( 0, 0.5, 1, 0.5, 1, 0 ), "voicecore/ass/thighright/legright", PoolRealArray( 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright/legright/footright", PoolRealArray( 0, 0, 0, 0, 0, 0 ), "voicecore/ass/tail", PoolRealArray( 0, 0, 0, 0, 0, 0 ), "voicecore/ass/tail/tailbend", PoolRealArray( 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody", PoolRealArray( 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/lefttit", PoolRealArray( 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/righttit", PoolRealArray( 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/neck", PoolRealArray( 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/neck/face", PoolRealArray( 0, 0, 0, 0, 0, 0 ), "voicecore/belly", PoolRealArray( 0, 0, 0, 0, 0, 0 ), "hair", PoolRealArray( 0, 0, 0, 0, 0, 0 ), "hair/hairleft", PoolRealArray( 0, 0, 0, 0, 0, 0 ), "hair/hairright", PoolRealArray( 0, 0, 0, 0, 0, 0 ), "dickpussy", PoolRealArray( 0, 0, 0, 0, 0, 0 ), "dickpussy/sex", PoolRealArray( 1, 0, 0, 1, 0, 0 ) ]
__meta__ = {
"_edit_lock_": true
}

[node name="both" type="Node2D" parent="."]
visible = false
modulate = Color( 0, 0, 0, 0 )
z_index = 1
__meta__ = {
"_edit_lock_": true
}

[node name="cum" type="Polygon2D" parent="both"]
visible = false
self_modulate = Color( 1, 1, 1, 0 )
position = Vector2( 885, 565 )
texture = ExtResource( 4 )
skeleton = NodePath("../../Skeleton2D")
polygon = PoolVector2Array( 75.8755, 2.72373, -3.89105, 4.28016, 2.33463, 143.58, 87.1595, 173.152, 154.086, 136.576, 151.362, -12.4514, 74.7124, 48.2587 )
uv = PoolVector2Array( 75.8755, 2.72373, -3.89105, 4.28016, 2.33463, 143.58, 87.1595, 173.152, 154.086, 136.576, 151.362, -12.4514, 74.7124, 48.2587 )
polygons = [ PoolIntArray( 1, 6, 5, 0 ), PoolIntArray( 1, 2, 3, 6 ), PoolIntArray( 6, 5, 4, 3 ) ]
bones = [ "voicecore", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass", PoolRealArray( 0, 0.49, 0.49, 0, 0.49, 0.49, 0.43 ), "voicecore/ass/thighleft", PoolRealArray( 0, 0.49, 0.49, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft/footleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright", PoolRealArray( 0, 0, 0, 0, 0.49, 0.49, 0 ), "voicecore/ass/thighright/legright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright/legright/footright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/tail", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/tail/tailbend", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/lefttit", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/righttit", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/neck", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/neck/face", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "voicecore/belly", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "hair", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "hair/hairleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "hair/hairright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "dickpussy", PoolRealArray( 0, 0, 0, 0, 0, 0, 0 ), "dickpussy/sex", PoolRealArray( 0, 0, 0, 1, 0, 0, 0.43 ) ]
internal_vertex_count = 1
__meta__ = {
"_edit_lock_": true
}

[node name="sweat" type="Polygon2D" parent="both"]
visible = false
position = Vector2( 715, 320 )
texture = ExtResource( 9 )
skeleton = NodePath("../../Skeleton2D")
polygon = PoolVector2Array( 269.264, -27.2727, 180.52, 10.8225, 207.359, 64.0693, 98.7013, 19.0476, -19.2771, 104.819, 60.8434, 196.386, 187.156, 250.459, 97.7273, 430.682, -15.873, 442.857, 31.746, 733.333, 122.222, 726.984, 144.444, 549.206, 341.27, 569.841, 339.682, 755.556, 455.556, 763.492, 512.698, 420.635, 338.095, 401.587, 371.429, 219.048, 560.317, 141.27, 511.111, 19.0476, 319.048, 60.3175, 269.444, 87.037, 275, 230.556, 240.23, 419.54 )
uv = PoolVector2Array( 269.264, -27.2727, 180.52, 10.8225, 207.359, 64.0693, 98.7013, 19.0476, -19.2771, 104.819, 60.8434, 196.386, 187.156, 250.459, 97.7273, 430.682, -15.873, 442.857, 31.746, 733.333, 122.222, 726.984, 144.444, 549.206, 341.27, 569.841, 339.682, 755.556, 455.556, 763.492, 512.698, 420.635, 338.095, 401.587, 371.429, 219.048, 560.317, 141.27, 511.111, 19.0476, 319.048, 60.3175, 269.444, 87.037, 275, 230.556, 242.529, 426.437 )
polygons = [ PoolIntArray( 1, 2, 21, 20, 0 ), PoolIntArray( 2, 3, 4, 5, 6, 22, 21 ), PoolIntArray( 21, 20, 19, 18, 17, 22 ), PoolIntArray( 6, 7, 23, 16, 17, 22 ), PoolIntArray( 7, 8, 9, 10, 11, 23 ), PoolIntArray( 11, 12, 13, 14, 15, 16, 23 ) ]
bones = [ "voicecore", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0.49, 0, 0, 0, 0, 0, 0, 0, 0, 0.49, 0, 0, 0, 0, 0, 0, 0.49 ), "voicecore/ass/thighleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighleft/legleft/footleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright/legright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/thighright/legright/footright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/tail", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/tail/tailbend", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.49, 0.98, 0.49, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/ass/dickpussy", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/upperbody/lefttit", PoolRealArray( 0, 0, 0, 0.49, 0.49, 0.49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.06, 0.06, 0, 0, 0, 0 ), "voicecore/upperbody/righttit", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.43, 0.43, 0, 0, 0, 0 ), "voicecore/upperbody/neck", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.49, 0, 0 ), "voicecore/upperbody/neck/face", PoolRealArray( 0.49, 0.49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "voicecore/belly", PoolRealArray( 0, 0, 0, 0, 0, 0, 0.49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.49, 0, 0, 0, 0, 0.49, 0 ), "hair", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair/hairleft", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "hair/hairright", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ) ]
internal_vertex_count = 3
__meta__ = {
"_edit_lock_": true
}

[node name="materialanims" type="AnimationPlayer" parent="both"]
anims/appear = SubResource( 8 )

[node name="voiceanims" type="AnimationPlayer" parent="."]
anims/RESET = SubResource( 1 )
anims/fast = SubResource( 9 )
anims/idle = SubResource( 2 )
anims/slow = SubResource( 7 )

[node name="otheranims" type="AnimationPlayer" parent="."]
anims/black = SubResource( 3 )
anims/white = SubResource( 4 )

[connection signal="animation_finished" from="voiceanims" to="." method="_on_voiceanims_animation_finished"]
