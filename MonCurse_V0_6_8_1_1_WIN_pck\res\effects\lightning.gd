extends Line2D

#const debug = true
#
#func _ready():
#	create_lightning(Vector2(300,300),Vector2(600,400))
#
#func _input(event):
#	if event.is_action_pressed("ui_click"):
#		self.clear_points()
#		create_lightning(Vector2(300,300),get_global_mouse_position())


const basepoints = 7
const forkchance = 0.2
const subforkchance = 0.2
var maxdeviation = 80
var halfdeviated = Vector2(1,1)
var biasdeviation = Vector2(25,25)
var direction = Vector2(1,1)
var amplitude = 1.0
#var forkjuice = 3
func create_lightning(startlocv,endlocv):
	var totalpoints = int(basepoints + (endlocv-startlocv).length() *0.03)
	add_point(startlocv)
	$light.position = endlocv
	var length = (endlocv-startlocv)
	var lengthnormalized = (endlocv-startlocv).normalized()
	biasdeviation = lengthnormalized*halfdeviated
	var biasdeviationleft = biasdeviation.rotated(-PI*0.4)
	var biasdeviationright = biasdeviation.rotated(PI*0.4)
#	var points_array = []
	amplitude = 1.0
	var removeamplitude = (0.9/totalpoints)
	direction = length.normalized()
	maxdeviation = 50 + sqrt(length.length())*2
	halfdeviated = Vector2(maxdeviation,maxdeviation)*0.5
	var nofork = 1
#	for _inc in range(totalpoints):
#		points_array.append(randf())
#	points_array.sort()
	for inc in range(totalpoints):
#		var pointlocv = startlocv + length*points_array[inc] + amplitude*Vector2((maxdeviation*randf()) - (maxdeviation*0.5),(maxdeviation*randf()) - (maxdeviation*0.5))# + biasdeviation
		var pointlocv = startlocv + length*((float(inc)+1.0)/totalpoints) + 0.6*amplitude*Vector2(maxdeviation*randf(),maxdeviation*randf())# + biasdeviation
#		var pointlocv = startlocv + length*((float(inc)+1.0)/totalpoints) + Vector2(maxdeviation*randf(),maxdeviation*randf())+quarterdeviated
		add_point(pointlocv)
#		if forkjuice > 0:
		if nofork <= 0:
			if randf() < forkchance:
				add_fork(pointlocv,0,biasdeviationleft,true)
				nofork = 2
	#			forkjuice -= 2
			if randf() < forkchance:
				add_fork(pointlocv,0,biasdeviationright,false)
				nofork = 2
#				forkjuice -= 2
		else:
			nofork -= 1
		amplitude -= removeamplitude
#		forkjuice = clamp(forkjuice+int(3*amplitude),0,int(12*amplitude))
	add_point(endlocv)
	$AnimationPlayer.stop()
	$AnimationPlayer.play("zap")

func add_fork(startlocv,forknum,rotatedbias,rotatedleft):
	var limitpow = pow(0.5,forknum+1)
	var pointlocv = startlocv + amplitude*(randf()+0.5+limitpow)*(rotatedbias + Vector2((maxdeviation*randf()) - (maxdeviation*0.5),(maxdeviation*randf()) - (maxdeviation*0.5)))
	add_point(pointlocv)
	if forknum < 3:# and forkjuice > 0:
		if rotatedleft == false:
			if randf() < subforkchance:
				add_fork(pointlocv,forknum+1,rotatedbias.rotated(-PI*limitpow),!rotatedleft)
			if randf() < subforkchance:
				add_fork(pointlocv,forknum+1,rotatedbias.rotated(-PI*limitpow),!rotatedleft)
#				forkjuice -= 2
		else:
			if randf() < subforkchance:
				add_fork(pointlocv,forknum+1,rotatedbias.rotated(PI*limitpow),!rotatedleft)
			if randf() < subforkchance:
				add_fork(pointlocv,forknum+1,rotatedbias.rotated(PI*limitpow),!rotatedleft)
#				forkjuice -= 2
	add_point(startlocv)


func _on_AnimationPlayer_animation_finished(_anim_name):
#	if debug == false:
	queue_free()
#	else:
#		print("Please change lightning to non-debug mode, thank you.")
