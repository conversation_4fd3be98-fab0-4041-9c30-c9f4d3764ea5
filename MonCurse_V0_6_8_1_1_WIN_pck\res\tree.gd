extends TileMap

var bigtree = false
# Declare member variables here. Examples:
# var a = 2
# var b = "text"

const tilesize = 128
const Leafnode = preload("res://effects/fallingleaves.tscn")

enum {OLDTREE,NEWTREE,BIGTREE,PILLAR}
# Called when the node enters the scene tree for the first time.
func justlikemaketree(start,length,_altcolors,leafchance,windspeed,newtree):
	var return_climb_array = PoolVector2Array([])
	var bare = randi()%2
	if windspeed < 1 and randf() > 0.5:
		bare = 0
	for section in range(length-1):
		if newtree == NEWTREE:
			set_cellv(start+Vector2(0,section),(randi()%2)+12,randf() > 0.5)#+altcolors*6)
			return_climb_array.append(start+Vector2(0,section))
			if randf() > 0.42:
				set_cellv(start+Vector2(1,section),(randi()%2)+18,false)
				return_climb_array.append(start+Vector2(1,section))
			if randf() > 0.42:
				set_cellv(start+Vector2(-1,section),(randi()%2)+18,true)
				return_climb_array.append(start+Vector2(-1,section))
		else:
			if newtree == OLDTREE:
				set_cellv(start+Vector2(0,section),(randi()%2)+6)#+altcolors*6)
			else:
				set_cellv(start+Vector2(0,section),(randi()%2)+22)
			return_climb_array.append(start+Vector2(0,section))
	if newtree == NEWTREE:
		set_cellv(start+Vector2(0,length-1),14)#+altcolors*6)
		set_cellv(start+Vector2(-1,length),15)#+altcolors*6)
		set_cellv(start+Vector2(-1,-1),16)#+altcolors*6)
		if bare == 0:
			set_cellv(start+Vector2(0,-2),17)
	else:
		if newtree == OLDTREE:
			set_cellv(start+Vector2(0,length-1),8)#+altcolors*6)
			set_cellv(start+Vector2(-1,length),9)#+altcolors*6)
			set_cellv(start+Vector2(-1,-1),(bare)+10)#+altcolors*6)
		else:
			set_cellv(start+Vector2(0,length-1),21)#+altcolors*6)
			set_cellv(start,20)#+altcolors*6)
	if newtree != PILLAR:
		return_climb_array.append(start+Vector2(0,length-1))
		return_climb_array.append(start+Vector2(-1,length))
		return_climb_array.append(start+Vector2(0,length))
		return_climb_array.append(start+Vector2(1,length))
		return_climb_array.append(start+Vector2(-1,-1))
		return_climb_array.append(start+Vector2(0,-1))
		return_climb_array.append(start+Vector2(1,-1))
	else:
		z_index -= 1
	if bare == 0 and leafchance > randf()*2 and newtree != PILLAR:
		var newleaf = Leafnode.instance()
		add_child(newleaf)
		newleaf.position = (start+Vector2(0.5,-0.5))*tilesize
		newleaf.emitting = true
		newleaf.speed_scale = windspeed
		newleaf.lifetime = (12+12*windspeed)/2
		newleaf.preprocess = randf()*newleaf.lifetime
#		newleaf.use_parent_material = true
#		if altcolors > 0:
#		newleaf.texture = load("res://effects/treeleafalt.png")
		if leafchance < 1:
			newleaf.amount = 1
		elif leafchance > 2:
			newleaf.amount = 3
#			newleaf.lifetime = 15
	return return_climb_array

