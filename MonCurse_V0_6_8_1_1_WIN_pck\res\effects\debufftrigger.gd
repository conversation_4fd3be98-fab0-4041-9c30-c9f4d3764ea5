extends Node2D


func bounce(debuffid):
	var color
	if debuffid > 0:
		$Control/Label.set_text(Playervariables.debuffidentifyarray[debuffid-1]+"  ")
		color = Playervariables.debuffcolorarray[debuffid-1]
	else:
		$Control/Label.set_text(Playervariables.debuffidentifynegativearray[abs(debuffid)-1]+"  ")
		color = Playervariables.debuffcolornegativearray[abs(debuffid)-1]
	$Control/Sprite.set_modulate(color)
	$Control/Label.set_modulate(color)
	$Control/TextureRect.texture = load("res://Assets/ui/debufficons/"+str(debuffid)+"m.png")
	$AnimationPlayer.play("bounce")


func _on_AnimationPlayer_animation_finished(_anim_name):
	queue_free()
