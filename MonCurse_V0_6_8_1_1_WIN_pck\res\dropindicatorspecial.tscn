[gd_scene load_steps=2 format=2]

[ext_resource path="res://Assets/eggindicator.png" type="Texture" id=1]

[node name="dropindicator" type="Node2D"]
modulate = Color( 0.898438, 0.5896, 0.821228, 0.588235 )
z_index = 15

[node name="down" type="Sprite" parent="."]
position = Vector2( 0, 129 )
rotation = -1.5708
texture = ExtResource( 1 )

[node name="downleft" type="Sprite" parent="."]
position = Vector2( -80, 129 )
rotation = -1.5708
texture = ExtResource( 1 )

[node name="downright" type="Sprite" parent="."]
position = Vector2( 80, 129 )
rotation = -1.5708
texture = ExtResource( 1 )
