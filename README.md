# MonCurse 游戏解包和修改工具

这是一套用于MonCurse游戏的解包、资源提取和存档修改工具。

## 文件说明

### 1. godot-unpacker.py
- **功能**: 解包Godot游戏的.pck资源文件
- **来源**: 来自 https://github.com/tehskai/godot-unpacker
- **用法**: `python godot-unpacker.py MonCurse_V0.6.8.1.1_WIN.pck`
- **输出**: 解包后的资源文件到 `MonCurse_V0_6_8_1_1_WIN_pck/` 目录

### 2. save_editor.py
- **功能**: 修改游戏存档文件
- **用法**: `python save_editor.py moncurse/save1.dat`
- **功能列表**:
  - 分析存档内容
  - 修改玩家名称
  - 修改碎片数量（游戏内货币）
  - 修改角色职业
  - 自动备份原存档

### 3. resource_browser.py
- **功能**: 浏览和提取游戏资源
- **用法**: `python resource_browser.py MonCurse_V0_6_8_1_1_WIN_pck/res`
- **功能列表**:
  - 浏览目录结构
  - 搜索特定文件
  - 查看文件内容（文本、JSON）
  - 提取文件到指定目录
  - 显示文件信息

## 使用步骤

### 第一步：解包游戏资源
```bash
python godot-unpacker.py MonCurse_V0.6.8.1.1_WIN.pck
```
这会创建 `MonCurse_V0_6_8_1_1_WIN_pck/` 目录，包含所有游戏资源。

### 第二步：浏览资源
```bash
python resource_browser.py MonCurse_V0_6_8_1_1_WIN_pck/res
```
使用交互式界面浏览游戏资源：
- 图片资源在 `Assets/` 目录
- 对话文件在 `Conversations/` 目录
- 角色立绘在 `DialogueArt/` 目录
- 音乐文件在 `music/` 目录
- 音效文件在 `zapsplat/` 目录

### 第三步：修改存档
```bash
python save_editor.py moncurse/save1.dat
```
可以修改：
- 玩家名称
- 碎片数量（0-255）
- 角色职业（0-8）

## 游戏资源结构

### 重要目录
- `Assets/`: 游戏UI和图标资源
- `DialogueArt/`: 角色立绘和CG
- `Conversations/`: 对话脚本（JSON格式）
- `Background/`: 背景图片
- `Enemy/`: 敌人角色资源
- `MC/`: 主角相关资源
- `music/`: 背景音乐
- `zapsplat/`: 音效文件

### 角色职业对照表
- 0: 人类
- 1: NAMAN
- 2: CUPID  
- 3: 猫娘
- 4: 狐娘
- 5: 羊娘
- 6: 狼娘
- 7: 小恶魔
- 8: BUFFER

## 存档文件结构

存档文件（save1.dat）采用二进制格式：
- 第1字节：存档版本号（当前为15）
- 第2-6字节：玩家名称（Pascal字符串格式）
- 第7字节：当前职业ID
- 第8字节：替代职业ID
- 第9字节：玩家碎片数量
- 后续字节：各种游戏进度数据

## 注意事项

1. **备份重要**: 修改存档前会自动创建备份文件
2. **编码问题**: 某些文件可能包含特殊字符，工具会尽力处理
3. **版本兼容**: 工具基于v0.6.8.1.1版本开发，其他版本可能需要调整
4. **安全性**: 仅修改本地文件，不涉及网络操作

## 常见用途

### 提取游戏图片
1. 使用resource_browser.py搜索.png文件
2. 使用提取功能将图片复制到指定目录

### 查看对话内容
1. 进入Conversations目录
2. 查看.json文件内容了解剧情

### 修改游戏进度
1. 使用save_editor.py修改碎片数量
2. 更改角色职业体验不同内容

### 研究游戏机制
1. 查看playervariables.gd了解游戏变量
2. 分析对话文件了解剧情分支

## 技术细节

- 基于Python 3开发
- 支持Godot引擎的.pck文件格式
- 处理二进制存档文件
- 支持UTF-8编码的文本文件
- 自动处理文件路径和编码问题

## 免责声明

这些工具仅供学习和研究目的使用。请尊重游戏开发者的版权，不要用于商业用途或分发游戏内容。
