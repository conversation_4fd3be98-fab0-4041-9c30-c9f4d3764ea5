[gd_scene load_steps=9 format=2]

[ext_resource path="res://zapsplat/zapsplat_multimedia_pop_up_tone_short_007_78859.ogg" type="AudioStream" id=1]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_coins_collect_hit_burst_74520.ogg" type="AudioStream" id=2]
[ext_resource path="res://zapsplat/zapsplat_multimedia_pop_up_tone_short_005_78857.ogg" type="AudioStream" id=3]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_hit_thud_ping_bright_negative_lose_die_73560.ogg" type="AudioStream" id=4]
[ext_resource path="res://zapsplat/zapsplat_multimedia_pop_up_close_003_78851.ogg" type="AudioStream" id=5]
[ext_resource path="res://zapsplat/multimedia_button_click_003.ogg" type="AudioStream" id=6]
[ext_resource path="res://originalsfx/Bfxr/SelectBlip.wav" type="AudioStream" id=7]
[ext_resource path="res://zapsplat/zapsplat_transport_boat_kayak_slide_launch_into_water_from_wooden_surface_14313.ogg" type="AudioStream" id=8]

[node name="SFX" type="Node"]

[node name="ui" type="AudioStreamPlayer" parent="."]
stream = ExtResource( 1 )
volume_db = -10.0
pitch_scale = 1.2
bus = "SFX"

[node name="uif" type="AudioStreamPlayer" parent="."]
stream = ExtResource( 3 )
volume_db = -15.0
bus = "SFX"

[node name="uib" type="AudioStreamPlayer" parent="."]
stream = ExtResource( 5 )
volume_db = -10.0
bus = "SFX"

[node name="purchase" type="AudioStreamPlayer" parent="."]
stream = ExtResource( 2 )
bus = "SFX"

[node name="warning" type="AudioStreamPlayer" parent="."]
stream = ExtResource( 4 )
volume_db = -5.0
pitch_scale = 0.7
bus = "SFX"

[node name="ramble" type="AudioStreamPlayer" parent="."]
stream = ExtResource( 6 )
volume_db = -18.0

[node name="hoverclick" type="AudioStreamPlayer" parent="."]
stream = ExtResource( 7 )
volume_db = -25.0
pitch_scale = 0.7

[node name="canoe" type="AudioStreamPlayer" parent="."]
stream = ExtResource( 8 )
volume_db = -14.0
pitch_scale = 1.1
