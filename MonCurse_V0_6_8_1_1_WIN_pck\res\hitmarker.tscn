[gd_scene load_steps=10 format=2]

[ext_resource path="res://font/OpenSans-ExtraBold.ttf" type="DynamicFontData" id=1]
[ext_resource path="res://hitmarker.gd" type="Script" id=2]
[ext_resource path="res://Assets/materials/texteffect.gdshader" type="Shader" id=3]

[sub_resource type="ShaderMaterial" id=5]
resource_local_to_scene = true
shader = ExtResource( 3 )
shader_param/first_color = Color( 0.301961, 0.133333, 0.227451, 1 )
shader_param/second_color = Color( 0.780392, 0.345098, 0.376471, 1 )
shader_param/position = -0.465
shader_param/size = 0.518
shader_param/angle = 90.0

[sub_resource type="DynamicFont" id=1]
size = 80
outline_size = 4
outline_color = Color( 0, 0, 0, 0.705882 )
font_data = ExtResource( 1 )

[sub_resource type="Animation" id=6]
resource_name = "blocked"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = false
tracks/0/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 2, 0.5 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = false
tracks/1/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 2, 1 ),
"update": 0,
"values": [ 0.0, -90.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 2, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("Label:rect_position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 0.52, 1 ),
"update": 0,
"values": [ Vector2( -204, 0 ), Vector2( -204, -200 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath(".:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 0.1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ false, true ]
}

[sub_resource type="Animation" id=2]
resource_name = "hitmarker"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = false
tracks/0/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 2, 0.5 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = false
tracks/1/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 2, 1 ),
"update": 0,
"values": [ 0.0, -90.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 2, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("Label:rect_position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( -53, -103 ), Vector2( 0, 100 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath(".:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 0.1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ false, true ]
}

[sub_resource type="Animation" id=3]
resource_name = "hitmarkerplayer"
length = 4.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 2, 4 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Label:rect_position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.7, 4 ),
"transitions": PoolRealArray( 0.5, 1, 1 ),
"update": 0,
"values": [ Vector2( 44, -83 ), Vector2( 44, -130 ), Vector2( 44, -180 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("Label:self_modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = false
tracks/2/keys = {
"times": PoolRealArray( 0, 0.5, 1, 1.5, 2 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.384314, 0.407843, 1, 1 ), Color( 1, 1, 1, 1 ), Color( 0.4, 0.486275, 0.878431, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = false
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 64, 20 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath(".:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 0.1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ false, true ]
}

[sub_resource type="Animation" id=4]
resource_name = "hitmarkerplayer decay"
length = 4.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 1.5 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 0.588235 ), Color( 1, 1, 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Label:rect_position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 44, -5 ), Vector2( 44, -20 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 64, 20 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:visible")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ false, true ]
}

[node name="hitmarker" type="Node2D"]
visible = false
position = Vector2( 64, 20 )
z_index = 17
script = ExtResource( 2 )

[node name="GridContainer" type="GridContainer" parent="."]
anchor_left = 0.5
anchor_right = 0.5
margin_bottom = 64.0
grow_horizontal = 2
grow_vertical = 0
mouse_filter = 2
columns = 3

[node name="Label" type="Label" parent="."]
material = SubResource( 5 )
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.57952
margin_left = -204.0
margin_right = -84.0
margin_bottom = 223.0
custom_colors/font_color = Color( 1, 0.235294, 0.235294, 1 )
custom_fonts/font = SubResource( 1 )
text = "???
"

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
anims/blocked = SubResource( 6 )
anims/hitmarker = SubResource( 2 )
anims/hitmarkerplayer = SubResource( 3 )
"anims/hitmarkerplayer decay" = SubResource( 4 )

[connection signal="animation_finished" from="AnimationPlayer" to="." method="_on_AnimationPlayer_animation_finished"]
