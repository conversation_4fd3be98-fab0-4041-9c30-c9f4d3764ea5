[gd_scene load_steps=4 format=2]

[ext_resource path="res://Assets/ui/energyorb2.png" type="Texture" id=1]
[ext_resource path="res://energyorbeffect.gd" type="Script" id=2]

[sub_resource type="Animation" id=1]
resource_name = "flyin"
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3, 1 ),
"transitions": PoolRealArray( 0.3, 0.5, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 50, 0 ), Vector2( 64, 64 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.6, 1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.313726 ), Color( 1, 1, 1, 0.470588 ), Color( 1, 1, 1, 0 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:rotation_degrees")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ 0.0, 360.0 ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:scale")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.3, 1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0.6, 0.6 ), Vector2( 1, 1 ), Vector2( 0.4, 0.4 ) ]
}

[node name="orbsprite" type="Sprite"]
self_modulate = Color( 1, 1, 1, 0.313726 )
scale = Vector2( 0.6, 0.6 )
z_index = 80
texture = ExtResource( 1 )
script = ExtResource( 2 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
anims/flyin = SubResource( 1 )

[connection signal="animation_finished" from="AnimationPlayer" to="." method="_on_AnimationPlayer_animation_finished"]
