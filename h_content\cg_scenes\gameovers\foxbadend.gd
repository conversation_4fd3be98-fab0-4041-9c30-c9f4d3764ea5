extends CanvasLayer


const hairghost = preload("res://DialogueArt/CG/gameovers/fox/saunaruleshair.png")
const hairnormal = preload("res://DialogueArt/CG/gameovers/fox/saunaruleshairnormal.png")

const bust1 = preload("res://DialogueArt/CG/gameovers/fox/saunarulesbustsmall.png")
const bust2 = preload("res://DialogueArt/CG/gameovers/fox/saunarulesbustmedium.png")
const bust3 = preload("res://DialogueArt/CG/gameovers/fox/saunarulesbustlarge.png")
const bust4 = preload("res://DialogueArt/CG/gameovers/fox/saunarulesbusthuge.png")

#1250x, 1166y : rules bust
#238x, 588y: rules BODY AND HAIR AND NORMALHAIR
#1298x, 863y: elite variant (dick)

var scenerank = false
func _ready():
	$sprites/scene2.visible = false
	$sprites/scene1.visible = false
	$sprites/background2.visible = false
	$sprites/background.visible = false
	if Playervariables.possessionrank == true:
		scenerank = true
		if Playervariables.prefdict[Playervariables.pref.FUTAENEMY] == true:
			futa = true
			$sprites/scene2/shika/elitevariant.visible = true
		else:
			futa = false
			$sprites/scene2/shika/elitevariant.visible = false
	else:
		futa = false
		$sprites/scene2/shika/elitevariant.visible = false
	set_hsv(false)
	set_player_corruptions()
	var _c = get_tree().root.connect("size_changed", self, "_on_viewport_size_changed")
	_on_viewport_size_changed()

func set_player_corruptions():
	$sprites/scene2/rulesbody/rulesbust.texture = get("bust"+str(clamp(Playervariables.playerbustsize,1,4)))

var futa = false
func set_hsv(normalhair = false):
#	var colourarray = Playervariables.baseplayercolourarray
#	var skincolHSV = Playervariables.newcolorarrayskin[colourarray[2]]
#	for node in skinnodes: #I really don't need to do this for every node, it's redundant...
#	$sprites/scene2/rulesbody.material.set_shader_param("hue_shift",skincolHSV.x)
#	$sprites/scene2/rulesbody.material.set_shader_param("sat_mul",skincolHSV.y)
#	$sprites/scene2/rulesbody.material.set_shader_param("val_mul",skincolHSV.z)
	if scenerank == true:
		var haircolHSV = Playervariables.RANKhsv[Playervariables.GHOSTFOX]
		$sprites/scene2/shika.use_parent_material = false
		$sprites/scene2/ruleshair.use_parent_material = false
		$sprites/scene1/ruleswisp.use_parent_material = false
		if normalhair == false:
			$sprites/scene2/ruleshair.material.set_shader_param("hue_shift",haircolHSV.x)
			$sprites/scene2/ruleshair.material.set_shader_param("sat_mul",haircolHSV.y)
			$sprites/scene2/ruleshair.material.set_shader_param("val_mul",haircolHSV.z)
		$sprites/scene1/ruleswisp.material.set_shader_param("hue_shift",haircolHSV.x)
		$sprites/scene1/ruleswisp.material.set_shader_param("sat_mul",haircolHSV.y)
		$sprites/scene1/ruleswisp.material.set_shader_param("val_mul",haircolHSV.z)
		var foxhsv = Playervariables.RANKhsv[Playervariables.FOXGIRL]
		$sprites/scene2/shika.material.set_shader_param("hue_shift",foxhsv.x)
		$sprites/scene2/shika.material.set_shader_param("sat_mul",foxhsv.y)
		$sprites/scene2/shika.material.set_shader_param("val_mul",foxhsv.z)
	else:
		$sprites/scene2/shika.use_parent_material = true
	if normalhair == true:
		var haircolHSV = Playervariables.newcolorarrayhair[Playervariables.truecolourarray[1]]
		$sprites/scene2/ruleshair.use_parent_material = false
		$sprites/scene2/ruleshair.material.set_shader_param("hue_shift",haircolHSV.x)
		$sprites/scene2/ruleshair.material.set_shader_param("sat_mul",haircolHSV.y)
		$sprites/scene2/ruleshair.material.set_shader_param("val_mul",haircolHSV.z)
	else:
		if scenerank == false:
			$sprites/scene2/ruleshair.use_parent_material = true


const basesize = Vector2(2384,2033)
var firstrun = true
var recentsizechange = false
func _on_viewport_size_changed():
	if recentsizechange == false:
		recentsizechange = true #this makes it so it only updates the viewport every 4 frames
		if firstrun == false:
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
		else:
			firstrun = false
		var viewportrect = get_parent().get_viewport_rect().size
		var proportion = viewportrect/basesize
		var maxproportion = max(proportion.x,proportion.y)
		$sprites.scale = Vector2(maxproportion,maxproportion)
		var newhoveranim
		var hoverposition = 0
		if $hover.is_playing() == true:
			hoverposition = $hover.current_animation_position
		if $hover.has_animation("newhover"):
			newhoveranim = $hover.get_animation("newhover")
		else:
			newhoveranim = $hover.get_animation("hover").duplicate()
		if (proportion.x > proportion.y):
			newhoveranim.track_set_key_value(0,0,Vector2(0,0))
			newhoveranim.track_set_key_value(0,1,Vector2(0,viewportrect.y - (basesize.y * maxproportion)))
			newhoveranim.track_set_key_value(0,2,Vector2(0,0))
			newhoveranim.track_set_key_value(0,3,Vector2(0,viewportrect.y - (basesize.y * maxproportion)))
			newhoveranim.track_set_key_value(0,4,Vector2(0,0))
		else:
			newhoveranim.track_set_key_value(0,0,Vector2(0,0))
			newhoveranim.track_set_key_value(0,1,Vector2(viewportrect.x - (basesize.x * maxproportion),0))
			newhoveranim.track_set_key_value(0,2,Vector2(0,0))
			newhoveranim.track_set_key_value(0,3,Vector2(viewportrect.x - (basesize.x * maxproportion),0))
			newhoveranim.track_set_key_value(0,4,Vector2(0,0))
		if $hover.has_animation("newhover") == false:
			$hover.add_animation("newhover",newhoveranim)
		$hover.stop()
		$hover.play("newhover")
		$hover.advance(hoverposition/$hover.playback_speed)
		recentsizechange = false

func action(num = 0):
	if Playervariables.consent == true:
		match num:
			0: #background appears
				$sprites.set_modulate(Color(1,1,1,1))
				if $AnimationPlayer.is_playing():
					$AnimationPlayer.advance(9)
				$AnimationPlayer.play("appearbg")
			1: #set to scene1
				$sprites/background.visible = true
				$sprites/background2.visible = false
				$sprites/scene1.visible = true
				$sprites/scene1/ruleswisp.visible = true
				$sprites/scene2.visible = false
				action(0)
			2: #set to scene2
				$sprites/background.visible = false
				$sprites/background2.visible = true
				$sprites/scene2/ruleshair.texture = hairghost
				set_hsv(false)
				$sprites/scene1.visible = false
				$sprites/scene2.visible = true
				action(0)
			3: #action 8 and 9 at same time
				action(8)
				action(9)
			4: #scene appears
				if $AnimationPlayer.is_playing():
					$AnimationPlayer.advance(9)
				$AnimationPlayer.play("appear")
			5: #scene3
				$sprites.set_modulate(Color(1,1,1,1))
				$sprites/scene2/ruleshair.texture = hairnormal
				set_hsv(true)
				action(8)
			6: #scene fades
				if $AnimationPlayer.is_playing():
					$AnimationPlayer.advance(9)
				$AnimationPlayer.play_backwards("appear")
			7: #bg fades
				if $AnimationPlayer.is_playing():
					$AnimationPlayer.advance(9)
				$AnimationPlayer.play_backwards("appearbg")
			8: #shake
				if $AnimationPlayer.is_playing():
					$AnimationPlayer.advance(9)
				var newhoveranim
				if $AnimationPlayer.has_animation("newshake"):
					newhoveranim = $AnimationPlayer.get_animation("newshake")
				else:
					newhoveranim = $AnimationPlayer.get_animation("shake").duplicate()
				newhoveranim.track_set_key_value(0,1,Vector2(15*$sprites.scale.x,0))
				newhoveranim.track_set_key_value(0,2,Vector2(0,-15*$sprites.scale.x))
				newhoveranim.track_set_key_value(2,1,Vector2(15*$sprites.scale.x,0))
				newhoveranim.track_set_key_value(2,2,Vector2(0,-15*$sprites.scale.x))
				if $AnimationPlayer.has_animation("newshake") == false:
					$AnimationPlayer.add_animation("newshake",newhoveranim)
				$AnimationPlayer.play("newshake")
			9:
				$sprites.set_modulate(Color(0.5,0.5,0.5,1))
