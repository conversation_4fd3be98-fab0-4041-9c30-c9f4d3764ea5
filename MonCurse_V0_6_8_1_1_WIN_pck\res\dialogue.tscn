[gd_scene load_steps=86 format=2]

[ext_resource path="res://dialogue.gd" type="Script" id=1]
[ext_resource path="res://font/OpenSans-ExtraBold.ttf" type="DynamicFontData" id=2]
[ext_resource path="res://font/simonetta/Simonetta-BlackItalic.ttf" type="DynamicFontData" id=3]
[ext_resource path="res://Settingscreen.tscn" type="PackedScene" id=4]
[ext_resource path="res://eventtext.tscn" type="PackedScene" id=5]
[ext_resource path="res://ConversationV1.tscn" type="PackedScene" id=6]
[ext_resource path="res://Assets/ui/convov2key.png" type="Texture" id=7]
[ext_resource path="res://Assets/ui/healthmeterwithbg.png" type="Texture" id=8]
[ext_resource path="res://Assets/ui/convov2keyhover.png" type="Texture" id=9]
[ext_resource path="res://Assets/ui/convov2keypress.png" type="Texture" id=10]
[ext_resource path="res://damagetaken.tres" type="Animation" id=11]
[ext_resource path="res://Assets/ui/monmusuloweruitransmiddle.png" type="Texture" id=12]
[ext_resource path="res://Assets/ui/convov2keyupicon.png" type="Texture" id=13]
[ext_resource path="res://Assets/ui/convov2keycloseicon.png" type="Texture" id=14]
[ext_resource path="res://Assets/ui/convov2keydownicon.png" type="Texture" id=15]
[ext_resource path="res://Assets/blacksquare.png" type="Texture" id=16]
[ext_resource path="res://Assets/ui/monmusuinventoryui.png" type="Texture" id=17]
[ext_resource path="res://Assets/ui/shardresource.png" type="Texture" id=18]
[ext_resource path="res://Assets/lightgodod.png" type="Texture" id=19]
[ext_resource path="res://Assets/itemblacksquare.png" type="Texture" id=20]
[ext_resource path="res://Assets/ui/inventory.png" type="Texture" id=21]
[ext_resource path="res://Assets/ui/healthmeterorb.png" type="Texture" id=22]
[ext_resource path="res://Assets/ui/lootbox.png" type="Texture" id=23]
[ext_resource path="res://Assets/ui/monmusuclosehover.png" type="Texture" id=24]
[ext_resource path="res://Assets/ui/cameraborder.png" type="Texture" id=25]
[ext_resource path="res://Assets/materials/texteffect.gdshader" type="Shader" id=26]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_underwater_bubble_wet_collect_item_earn_point_bonus_001_78395.ogg" type="AudioStream" id=27]
[ext_resource path="res://font/OpenSans-BoldItalic.ttf" type="DynamicFontData" id=28]
[ext_resource path="res://Assets/ui/chatcycle.png" type="Texture" id=29]
[ext_resource path="res://Assets/ui/monmusubuttonhalfhover.png" type="Texture" id=30]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_bright_positive_sparkle_019_40536.ogg" type="AudioStream" id=31]
[ext_resource path="res://Assets/ui/healthmeterhover.png" type="Texture" id=32]
[ext_resource path="res://Assets/ui/inventoryhover.png" type="Texture" id=33]
[ext_resource path="res://Assets/6464whitesq.png" type="Texture" id=34]
[ext_resource path="res://font/simonetta/Simonetta-Regular.ttf" type="DynamicFontData" id=35]
[ext_resource path="res://Assets/ui/monmusubuttonhalf.png" type="Texture" id=36]
[ext_resource path="res://Assets/ui/monmusuclose.png" type="Texture" id=37]
[ext_resource path="res://Assets/ui/monmususcroller.png" type="Texture" id=38]
[ext_resource path="res://Assets/directionalhologram.png" type="Texture" id=39]
[ext_resource path="res://font/Audiowide-Regular.ttf" type="DynamicFontData" id=40]
[ext_resource path="res://Assets/delay.png" type="Texture" id=41]
[ext_resource path="res://Assets/materials/water.shader" type="Shader" id=42]
[ext_resource path="res://Assets/ui/delaycounter.png" type="Texture" id=43]
[ext_resource path="res://Assets/materials/hologramshader.shader" type="Shader" id=44]
[ext_resource path="res://Assets/enemyselectorhologram.png" type="Texture" id=45]
[ext_resource path="res://Assets/ui/climbtoggle.png" type="Texture" id=48]
[ext_resource path="res://Assets/ui/climbtoggledisable.png" type="Texture" id=49]
[ext_resource path="res://Assets/ui/climbtoggleoff.png" type="Texture" id=50]
[ext_resource path="res://Assets/ui/informationbar.png" type="Texture" id=51]
[ext_resource path="res://corruptionmeter.tscn" type="PackedScene" id=52]

[sub_resource type="Animation" id=21]
resource_name = "flash"
length = 2.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5, 1, 1.5, 2 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.901961 ), Color( 0.313726, 0.313726, 0.313726, 0.470588 ), Color( 1, 1, 1, 0.784314 ), Color( 0.705882, 0.705882, 0.705882, 0.27451 ), Color( 1, 1, 1, 0.901961 ) ]
}

[sub_resource type="Animation" id=1]
length = 1.2
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3, 0.6, 0.9, 1.2 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.5, 0.8, 1.4, 1 ), Color( 1, 1, 1, 1 ), Color( 0.5, 0.8, 1.4, 1 ), Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=2]
length = 0.5
tracks/0/type = "value"
tracks/0/path = NodePath("events:rect_position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 20, 64 ), Vector2( 20, 20 ) ]
}

[sub_resource type="Animation" id=18]
resource_name = "deckscroll"
length = 0.8
tracks/0/type = "value"
tracks/0/path = NodePath("..:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 3, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:anchor_right")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 3, 1 ),
"update": 0,
"values": [ 0.5, 1.1 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("../../decknum:self_modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 3, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("../../decknum:visible")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ false, true ]
}

[sub_resource type="ShaderMaterial" id=23]
shader = ExtResource( 26 )
shader_param/first_color = Color( 0.392157, 0.415686, 0.631373, 1 )
shader_param/second_color = Color( 1, 0.939338, 0.914063, 1 )
shader_param/position = -0.454
shader_param/size = 0.54
shader_param/angle = 90.0

[sub_resource type="DynamicFont" id=19]
size = 40
outline_size = 1
outline_color = Color( 0, 0, 0, 0.627451 )
font_data = ExtResource( 28 )

[sub_resource type="DynamicFont" id=3]
size = 20
outline_size = 1
outline_color = Color( 0.701961, 0.701961, 0.701961, 0.768627 )
font_data = ExtResource( 40 )

[sub_resource type="Animation" id=4]
length = 5.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 2.5, 5 ),
"transitions": PoolRealArray( 0.5, 0.5, 0.5 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:anchor_right")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 5 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ 0.0, 2.0 ]
}

[sub_resource type="Animation" id=5]
length = 0.5
tracks/0/type = "value"
tracks/0/path = NodePath(".:margin_bottom")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ 120, 0.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ false, true ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=35]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("../deploy:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("../deploy:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ true ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("../backdrop:modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=36]
resource_name = "hide"
tracks/0/type = "value"
tracks/0/path = NodePath("../deploy:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 1 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("../deploy:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 0.3, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:self_modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 0.3, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("../backdrop:modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 0.3, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}

[sub_resource type="Animation" id=37]
resource_name = "show"
tracks/0/type = "value"
tracks/0/path = NodePath("../deploy:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 0.5 ),
"update": 1,
"values": [ true ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("../deploy:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:self_modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("../backdrop:modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=30]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath("TextureRect:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:margin_top")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:rect_scale")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath(".:margin_bottom")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ 0 ]
}

[sub_resource type="Animation" id=20]
resource_name = "healed"
length = 2.5
tracks/0/type = "value"
tracks/0/path = NodePath(".:margin_left")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = false
tracks/0/keys = {
"times": PoolRealArray( 0, 1.9, 2, 2.1, 2.2, 2.3, 2.4, 2.5 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 10, -5, 30, 10.0, 5, 10, 0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:margin_top")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.5, 1.9, 2, 2.1, 2.3, 2.5 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 0, -5, 5, -5, -10, 0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:self_modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.5, 1.9, 2.5 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 1 ), Color( 0.823529, 1, 0.901961, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("TextureRect:self_modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 1.2, 1.9, 2.5 ),
"transitions": PoolRealArray( 1, 1, 2, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 1 ), Color( 0.12549, 1, 0, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/4/type = "method"
tracks/4/path = NodePath("../..")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 1.8 ),
"transitions": PoolRealArray( 1 ),
"values": [ {
"args": [  ],
"method": "update_health"
} ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("healthpickupmain:playing")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 1.8, 2.4 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}
tracks/6/type = "value"
tracks/6/path = NodePath(".:rect_scale")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ) ]
}
tracks/7/type = "value"
tracks/7/path = NodePath("shards2:modulate")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"times": PoolRealArray( 0, 1.2, 2.3, 2.5 ),
"transitions": PoolRealArray( 0.5, 1, 1, 1 ),
"update": 0,
"values": [ Color( 0.678431, 0.517647, 0.827451, 0.784314 ), Color( 0.678431, 0.517647, 0.827451, 0 ), Color( 0.678431, 0.517647, 0.827451, 0 ), Color( 0.678431, 0.517647, 0.827451, 0.784314 ) ]
}

[sub_resource type="Animation" id=38]
resource_name = "healedsprings"
length = 2.5
tracks/0/type = "value"
tracks/0/path = NodePath(".:margin_left")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = false
tracks/0/keys = {
"times": PoolRealArray( 0, 1.9, 2, 2.1, 2.2, 2.3, 2.4, 2.5 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 10, -5, 30, 10.0, 5, 10, 0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:margin_top")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.5, 1.9, 2, 2.1, 2.3, 2.5 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ -130, -130, -135, -125, -135, -140, -130 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:self_modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.5, 1.9, 2.5 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 1 ), Color( 0.823529, 1, 0.901961, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("TextureRect:self_modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 1.2, 1.9, 2.5 ),
"transitions": PoolRealArray( 1, 1, 2, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 1 ), Color( 0.12549, 1, 0, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/4/type = "method"
tracks/4/path = NodePath("../..")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 1.8, 2.5 ),
"transitions": PoolRealArray( 1, 1 ),
"values": [ {
"args": [  ],
"method": "update_health"
}, {
"args": [  ],
"method": "update_health"
} ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("healthpickupmain:playing")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 1.8, 2.4 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}
tracks/6/type = "value"
tracks/6/path = NodePath(".:rect_scale")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 1, 1.1 ) ]
}
tracks/7/type = "value"
tracks/7/path = NodePath("shards2:modulate")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"times": PoolRealArray( 0, 1.2, 2.3, 2.5 ),
"transitions": PoolRealArray( 0.5, 1, 1, 1 ),
"update": 0,
"values": [ Color( 0.678431, 0.517647, 0.827451, 0.784314 ), Color( 0.678431, 0.517647, 0.827451, 0 ), Color( 0.678431, 0.517647, 0.827451, 0 ), Color( 0.678431, 0.517647, 0.827451, 0.784314 ) ]
}

[sub_resource type="Animation" id=31]
resource_name = "springs"
length = 0.5
tracks/0/type = "value"
tracks/0/path = NodePath(".:margin_left")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = false
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:margin_top")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ -130 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:self_modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.2, 0.5 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.823529, 1, 0.901961, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("TextureRect:self_modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.2, 0.5 ),
"transitions": PoolRealArray( 1, 2, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.619608, 1, 0.462745, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath(".:rect_scale")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ Vector2( 1.7, 1.7 ), Vector2( 1, 1.1 ) ]
}
tracks/5/type = "value"
tracks/5/path = NodePath(".:margin_bottom")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ -130 ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("resleft:margin_top")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ 0.0 ]
}

[sub_resource type="Shader" id=39]
code = "shader_type canvas_item;

uniform vec4 first_color : hint_color = vec4(1.0);
uniform vec4 second_color : hint_color = vec4(0.0, 0.0, 0.0, 1.0);
uniform float position : hint_range(-0.5, 0.5) = 0.0;
uniform float size : hint_range(0.5, 2) = 0.5;
uniform float angle : hint_range(0.0, 360.0) = 0.0;

void fragment() {
	vec4 base = texture(TEXTURE, UV);
	float pivot = position + 0.5;
	vec2 uv = UV - pivot;
	float rotated = uv.x * cos(radians(angle)) - uv.y * sin(radians(angle)); 
	float pos = smoothstep((1.0 - size) + position, size + 0.0001 + position, rotated + pivot);
	vec4 tempcolor = mix(first_color, second_color, pos);
	COLOR.rgb = tempcolor.rgb;
	COLOR.a = base.a;
}"

[sub_resource type="ShaderMaterial" id=24]
shader = SubResource( 39 )
shader_param/first_color = Color( 0.290196, 0.0862745, 0.0862745, 1 )
shader_param/second_color = Color( 0.909804, 0.4, 0.4, 1 )
shader_param/position = -0.434
shader_param/size = 0.551
shader_param/angle = 90.0

[sub_resource type="DynamicFont" id=6]
size = 60
outline_size = 2
outline_color = Color( 0, 0, 0, 0.54902 )
font_data = ExtResource( 2 )

[sub_resource type="ShaderMaterial" id=7]
shader = ExtResource( 42 )
shader_param/blue_tint = Color( 0.780392, 0.180392, 0.658824, 1 )
shader_param/sprite_scale = Vector2( 128, 64 )
shader_param/scale_x = 0.2
shader_param/amplitude = 200.0

[sub_resource type="Animation" id=15]
resource_name = "flashlight"
tracks/0/type = "value"
tracks/0/path = NodePath(".:energy")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5, 1 ),
"transitions": PoolRealArray( 2, 1, 1 ),
"update": 0,
"values": [ 0.2, 0.6, 0.2 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:enabled")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}

[sub_resource type="Animation" id=16]
resource_name = "warningflash"
length = 1.5
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:energy")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.7, 1.5 ),
"transitions": PoolRealArray( 2, 1, 1 ),
"update": 0,
"values": [ 0.2, 1.0, 0.2 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:enabled")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}

[sub_resource type="ShaderMaterial" id=32]
shader = ExtResource( 44 )
shader_param/speed = 5.0
shader_param/hologramTexture = ExtResource( 45 )

[sub_resource type="Shader" id=33]
code = "shader_type canvas_item;

uniform float speed = 0.5;
uniform sampler2D hologramTexture;

vec2 tilingAndOffset(vec2 uv, float offset) {
    return mod(uv - vec2(0.0, offset), 1);
}

void fragment() {
    float offset = float(TIME * speed / 100.0);
    vec2 tiling = tilingAndOffset(UV, offset);
    
    vec4 noise = texture(hologramTexture, tiling);
//    vec4 colorLines = linesColor * vec4(vec3(linesColorIntensity), 1.0);
    vec4 emission = 0.2 * noise;
    
    vec4 albedo = vec4(0,0,0,0);
    float alpha = dot(noise.rgb, vec3(1.0));
    vec4 hologram;
    hologram.rgb = emission.rgb + (1.0 - emission.rgb) * albedo.rgb * albedo.a;
    hologram.a = emission.a + (1.0 - emission.a) * alpha;
    hologram.a = hologram.a + (1.0 - hologram.a) * albedo.a;
    COLOR = texture(TEXTURE, UV);
    COLOR.rgb = COLOR.rgb + (1.0 - COLOR.rgb) * hologram.rgb;
    COLOR.a = min(COLOR.a, hologram.a);
}"

[sub_resource type="ShaderMaterial" id=34]
shader = SubResource( 33 )
shader_param/speed = 8.0
shader_param/hologramTexture = ExtResource( 39 )

[sub_resource type="Animation" id=8]
resource_name = "peek"
tracks/0/type = "value"
tracks/0/path = NodePath(".:anchor_top")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 0.3, 1 ),
"update": 0,
"values": [ -0.04, 0.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:anchor_bottom")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 0.3, 1 ),
"update": 0,
"values": [ 0.11, 0.15 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 0.784314, 0.784314, 0.784314, 0.588235 ), Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=25]
resource_name = "cycle"
length = 3.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:rect_rotation")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1.5, 3 ),
"transitions": PoolRealArray( 0.4, 0.4, 1 ),
"update": 0,
"values": [ 0.0, -180.0, -360.0 ]
}

[sub_resource type="Animation" id=9]
length = 1.5
tracks/0/type = "value"
tracks/0/path = NodePath("VBoxContainer:margin_top")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1.5 ),
"transitions": PoolRealArray( 0.3, 1 ),
"update": 0,
"values": [ 0.0, -200 ]
}

[sub_resource type="DynamicFont" id=10]
size = 32
outline_size = 2
outline_color = Color( 0.607843, 0.607843, 0.607843, 0.627451 )
font_data = ExtResource( 3 )

[sub_resource type="DynamicFont" id=13]
size = 18
outline_size = 2
outline_color = Color( 0, 0, 0, 0.627451 )
font_data = ExtResource( 35 )

[sub_resource type="DynamicFont" id=14]
size = 48
outline_size = 5
outline_color = Color( 0.145098, 0.145098, 0.145098, 1 )
font_data = ExtResource( 2 )

[sub_resource type="DynamicFont" id=27]
size = 24
outline_size = 4
outline_color = Color( 0.145098, 0.145098, 0.145098, 1 )
font_data = ExtResource( 2 )

[sub_resource type="Shader" id=28]
code = "shader_type canvas_item;

uniform float speed = 0.5;
uniform sampler2D hologramTexture;

vec2 tilingAndOffset(vec2 uv, float offset) {
    return mod(uv - vec2(0.0, offset), 1);
}

void fragment() {
    float offset = float(TIME * speed / 100.0);
    vec2 tiling = tilingAndOffset(vec2(UV.y,UV.x), offset);
    
    vec4 noise = texture(hologramTexture, tiling);
//    vec4 colorLines = linesColor * vec4(vec3(linesColorIntensity), 1.0);
    vec4 emission = 0.2 * noise;
    
    vec4 albedo = vec4(0,0,0,0);
    float alpha = dot(noise.rgb, vec3(1.0));
    vec4 hologram;
    hologram.rgb = emission.rgb + (1.0 - emission.rgb) * albedo.rgb * albedo.a;
    hologram.a = emission.a + (1.0 - emission.a) * alpha;
    hologram.a = hologram.a + (1.0 - hologram.a) * albedo.a;
    COLOR = texture(TEXTURE, UV);
    COLOR.rgb = COLOR.rgb + (1.0 - COLOR.rgb) * hologram.rgb;
    COLOR.a = min(COLOR.a, hologram.a);
}"

[sub_resource type="ShaderMaterial" id=29]
shader = SubResource( 28 )
shader_param/speed = 5.0
shader_param/hologramTexture = ExtResource( 39 )

[sub_resource type="Animation" id=26]
resource_name = "chest"
tracks/0/type = "value"
tracks/0/path = NodePath(".:anchor_top")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ 0.33, 0.43, 0.21 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:anchor_bottom")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.2, 1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ 0.47, 0.52, 0.35 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0.1, 0.3, 1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:visible")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.1, 1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ false, true, false ]
}
tracks/4/type = "value"
tracks/4/path = NodePath(".:rect_scale")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 0.3, 1 ),
"transitions": PoolRealArray( 1, 2, 1 ),
"update": 0,
"values": [ Vector2( 0.8, 0.8 ), Vector2( 1, 1 ), Vector2( 0.1, 1 ) ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("open3:playing")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}

[node name="Dialogue" type="Control"]
anchor_right = 1.0
anchor_bottom = 1.0
margin_top = -0.396851
margin_bottom = -0.396851
script = ExtResource( 1 )

[node name="screenfilter" type="Control" parent="."]
anchor_right = 1.0
anchor_bottom = 1.0

[node name="cameramode" type="TextureRect" parent="."]
visible = false
self_modulate = Color( 1, 1, 1, 0.901961 )
anchor_left = 0.03
anchor_top = 0.06
anchor_right = 0.97
anchor_bottom = 0.77
mouse_filter = 2
texture = ExtResource( 25 )
expand = true

[node name="camera" type="AnimationPlayer" parent="cameramode"]
playback_speed = 0.25
anims/flash = SubResource( 21 )

[node name="damageinstances" type="Control" parent="."]
visible = false
anchor_left = 0.1
anchor_top = 0.1
anchor_right = 0.1
anchor_bottom = 0.1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="ConversationV1" parent="." instance=ExtResource( 6 )]

[node name="nameright" parent="ConversationV1/lowerui" index="3"]
__meta__ = {
"_edit_use_anchors_": false
}

[node name="choice" parent="ConversationV1" index="4"]
visible = true
modulate = Color( 1, 1, 1, 1 )

[node name="upperui" type="NinePatchRect" parent="."]
anchor_right = 1.0
margin_top = -115.0
margin_bottom = 55.0
grow_vertical = 0
rect_min_size = Vector2( 650, 170 )
texture = ExtResource( 12 )
patch_margin_left = 132
patch_margin_top = 75
patch_margin_right = 132
patch_margin_bottom = 75
axis_stretch_horizontal = 2
axis_stretch_vertical = 2

[node name="events" type="VBoxContainer" parent="upperui"]
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = 20.0
margin_top = 20.0
margin_right = -20.0
margin_bottom = -20.0
grow_horizontal = 0
custom_constants/separation = 5
alignment = 2

[node name="eventtext" parent="upperui/events" instance=ExtResource( 5 )]
margin_top = 91.0
margin_bottom = 130.0
custom_colors/font_color = Color( 0, 0, 0, 1 )
text = "---"
autowrap = true

[node name="scrollindicator" type="TextureRect" parent="upperui"]
anchor_left = 0.4
anchor_top = 1.0
anchor_right = 0.6
anchor_bottom = 1.0
margin_top = -20.0
rect_min_size = Vector2( 50, 29 )
texture = ExtResource( 38 )
expand = true
stretch_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="blink" type="AnimationPlayer" parent="upperui/scrollindicator"]
anims/blink = SubResource( 1 )

[node name="scrollup" type="AnimationPlayer" parent="upperui"]
anims/newtextscroll = SubResource( 2 )

[node name="alerts" type="VBoxContainer" parent="upperui"]
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = 15.0
margin_right = -15.0
margin_bottom = 40.0
grow_horizontal = 0
mouse_filter = 2
alignment = 2

[node name="deployablecontainer" type="Control" parent="."]
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
margin_top = -120.0
margin_bottom = 112.0
rect_min_size = Vector2( 600, 123 )

[node name="backdrop" type="Control" parent="deployablecontainer"]
anchor_left = 0.5
anchor_top = 0.25
anchor_right = 0.5
anchor_bottom = 0.25
grow_horizontal = 2
size_flags_horizontal = 0
size_flags_vertical = 0

[node name="dust" type="Control" parent="deployablecontainer"]
anchor_left = 0.5
anchor_top = 0.25
anchor_right = 0.5
anchor_bottom = 0.25
grow_horizontal = 2
size_flags_horizontal = 0
size_flags_vertical = 0
__meta__ = {
"_edit_use_anchors_": false
}

[node name="lowerui2" type="NinePatchRect" parent="deployablecontainer"]
anchor_left = 0.12
anchor_right = 0.88
anchor_bottom = 1.0
grow_horizontal = 2
texture = ExtResource( 12 )
patch_margin_left = 132
patch_margin_top = 75
patch_margin_right = 132
patch_margin_bottom = 75
axis_stretch_horizontal = 2
axis_stretch_vertical = 2

[node name="playerdebuffs" type="HBoxContainer" parent="deployablecontainer/lowerui2"]
margin_top = -128.0
rect_pivot_offset = Vector2( 0, 128 )
custom_constants/separation = -1

[node name="deckfade" type="AnimationPlayer" parent="deployablecontainer/lowerui2"]
root_node = NodePath("../deckbacking/playeritemdeck")
anims/deckscroll = SubResource( 18 )

[node name="decknum" type="Label" parent="deployablecontainer/lowerui2"]
visible = false
self_modulate = Color( 1, 1, 1, 0 )
material = SubResource( 23 )
anchor_top = -0.5
anchor_bottom = -0.5
grow_vertical = 2
custom_colors/font_color = Color( 0, 0, 0, 1 )
custom_fonts/font = SubResource( 19 )
text = "5"
align = 1

[node name="deckbacking" type="TextureRect" parent="deployablecontainer/lowerui2"]
modulate = Color( 1, 1, 1, 0 )
self_modulate = Color( 1, 1, 1, 0.647059 )
anchor_left = -0.5
anchor_top = -0.5
anchor_right = 0.5
anchor_bottom = 0.5
mouse_filter = 2
texture = ExtResource( 20 )
expand = true

[node name="playeritemdeck" type="HBoxContainer" parent="deployablecontainer/lowerui2/deckbacking"]
modulate = Color( 0.784314, 0.784314, 0.784314, 0.705882 )
anchor_left = -0.05
anchor_right = 0.5
anchor_bottom = 0.6
grow_horizontal = 0
grow_vertical = 0
mouse_filter = 2
custom_constants/separation = -80
alignment = 2

[node name="delaycounter" type="TextureRect" parent="deployablecontainer/lowerui2"]
visible = false
modulate = Color( 0.784314, 0.784314, 0.784314, 0.784314 )
anchor_right = 1.0
margin_top = -57.1225
margin_bottom = 6.87753
texture = ExtResource( 43 )
expand = true
stretch_mode = 6

[node name="delaylabel" type="Label" parent="deployablecontainer/lowerui2/delaycounter"]
anchor_left = 0.5
anchor_right = 0.5
margin_left = -17.0
margin_top = 12.0
margin_right = -5.0
margin_bottom = 35.0
grow_horizontal = 0
grow_vertical = 0
custom_colors/font_color = Color( 0.505882, 0.109804, 0.109804, 1 )
custom_fonts/font = SubResource( 3 )
text = "4"
align = 2
valign = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="delaylabel2" type="Label" parent="deployablecontainer/lowerui2/delaycounter"]
anchor_left = 0.5
anchor_right = 0.5
margin_left = 5.0
margin_top = 12.0
margin_right = 17.0
margin_bottom = 35.0
grow_vertical = 0
custom_colors/font_color = Color( 0.247059, 0.133333, 0, 1 )
custom_fonts/font = SubResource( 3 )
text = "3"
valign = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="delay" type="TextureRect" parent="deployablecontainer/lowerui2"]
visible = false
modulate = Color( 1, 1, 1, 0 )
margin_left = -200.0
margin_top = -52.0
margin_bottom = 72.0
texture = ExtResource( 41 )
stretch_mode = 4
__meta__ = {
"_edit_use_anchors_": false
}

[node name="AnimationPlayer" type="AnimationPlayer" parent="deployablecontainer/lowerui2/delay"]
anims/delay = SubResource( 4 )

[node name="climbtoggle" type="TextureButton" parent="deployablecontainer/lowerui2"]
visible = false
anchor_left = 0.5
anchor_right = 0.5
grow_horizontal = 2
grow_vertical = 0
action_mode = 0
texture_normal = ExtResource( 48 )
texture_pressed = ExtResource( 50 )
texture_disabled = ExtResource( 49 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="deployablecontainer/lowerui2/climbtoggle"]
anims/showclimb = SubResource( 5 )

[node name="hold_skills" type="AnimationPlayer" parent="deployablecontainer/lowerui2"]
playback_speed = 1.5
anims/RESET = SubResource( 35 )
anims/hide = SubResource( 36 )
anims/show = SubResource( 37 )

[node name="deploy" type="Control" parent="deployablecontainer"]
anchor_left = 0.5
anchor_right = 0.5
grow_horizontal = 2
grow_vertical = 0

[node name="healthbox" type="TextureButton" parent="deployablecontainer"]
anchor_right = 0.12
anchor_bottom = 1.0
grow_horizontal = 0
action_mode = 0
texture_normal = ExtResource( 8 )
texture_hover = ExtResource( 32 )
expand = true

[node name="healthpickupmain" type="AudioStreamPlayer" parent="deployablecontainer/healthbox"]
stream = ExtResource( 27 )
volume_db = -18.0
pitch_scale = 1.3
bus = "SFX"

[node name="AnimationPlayer" type="AnimationPlayer" parent="deployablecontainer/healthbox"]
anims/RESET = SubResource( 30 )
anims/damagetaken = ExtResource( 11 )
anims/healed = SubResource( 20 )
anims/healedsprings = SubResource( 38 )
anims/springs = SubResource( 31 )

[node name="resleft" type="Label" parent="deployablecontainer/healthbox"]
material = SubResource( 24 )
use_parent_material = true
anchor_left = 0.5
anchor_top = 0.225
anchor_right = 0.5
anchor_bottom = 0.225
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 1, 0.65098, 0.823529, 1 )
custom_fonts/font = SubResource( 6 )
text = "35"
align = 1
valign = 1

[node name="TextureRect" type="TextureRect" parent="deployablecontainer/healthbox"]
material = SubResource( 7 )
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource( 22 )
expand = true
stretch_mode = 1

[node name="Light2D" type="Light2D" parent="deployablecontainer/healthbox"]
position = Vector2( 65.0356, 59.6902 )
texture = ExtResource( 19 )
texture_scale = 0.5
color = Color( 1, 0, 0, 1 )
energy = 0.2
range_layer_min = -5
range_layer_max = 5
__meta__ = {
"_edit_lock_": true
}

[node name="AnimationPlayer" type="AnimationPlayer" parent="deployablecontainer/healthbox/Light2D"]
playback_speed = 0.5
anims/flashlight = SubResource( 15 )
anims/warningflash = SubResource( 16 )

[node name="shards2" type="TextureRect" parent="deployablecontainer/healthbox"]
visible = false
modulate = Color( 0.678431, 0.517647, 0.827451, 0.784314 )
material = SubResource( 32 )
anchor_left = -0.2
anchor_top = -0.7
anchor_right = 1.2
anchor_bottom = -0.1
texture = ExtResource( 18 )
expand = true
stretch_mode = 6

[node name="resleft2" type="Label" parent="deployablecontainer/healthbox/shards2"]
material = SubResource( 24 )
use_parent_material = true
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 1, 0.65098, 0.823529, 1 )
custom_fonts/font = SubResource( 6 )
text = "35"
align = 1
valign = 1

[node name="hologram" type="TextureRect" parent="deployablecontainer/healthbox/shards2"]
material = SubResource( 34 )
anchor_left = 0.42
anchor_top = 0.85
anchor_right = 0.58
anchor_bottom = 1.2
grow_horizontal = 2
grow_vertical = 0
texture = ExtResource( 34 )
expand = true

[node name="inventorybutton" type="TextureButton" parent="deployablecontainer"]
anchor_left = 0.88
anchor_right = 1.0
anchor_bottom = 1.0
margin_right = 0.66748
action_mode = 0
texture_normal = ExtResource( 21 )
texture_hover = ExtResource( 33 )
expand = true

[node name="queue" type="Timer" parent="."]
one_shot = true

[node name="conversationv2" type="Control" parent="."]
visible = false
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 2

[node name="VBoxContainer" type="VBoxContainer" parent="conversationv2"]
anchor_top = 0.6
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
mouse_filter = 2
custom_constants/separation = 0
alignment = 1

[node name="controls" type="TextureRect" parent="conversationv2"]
modulate = Color( 0.784314, 0.784314, 0.784314, 0.588235 )
self_modulate = Color( 0.392157, 0.392157, 0.392157, 0.392157 )
anchor_left = 0.3
anchor_top = -0.04
anchor_right = 0.7
anchor_bottom = 0.11
grow_horizontal = 2
mouse_filter = 0
texture = ExtResource( 51 )
expand = true

[node name="scrolldown" type="TextureButton" parent="conversationv2/controls"]
self_modulate = Color( 0.784314, 0.784314, 0.784314, 1 )
anchor_left = 0.025
anchor_top = 0.05
anchor_right = 0.3
anchor_bottom = 0.95
texture_normal = ExtResource( 7 )
texture_pressed = ExtResource( 10 )
texture_hover = ExtResource( 9 )
expand = true
__meta__ = {
"_edit_use_anchors_": false
}

[node name="TextureRect" type="TextureRect" parent="conversationv2/controls/scrolldown"]
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 2
texture = ExtResource( 15 )
expand = true
__meta__ = {
"_edit_use_anchors_": false
}

[node name="scrollup" type="TextureButton" parent="conversationv2/controls"]
self_modulate = Color( 0.784314, 0.784314, 0.784314, 1 )
anchor_left = 0.325
anchor_top = 0.05
anchor_right = 0.6
anchor_bottom = 0.95
texture_normal = ExtResource( 7 )
texture_pressed = ExtResource( 10 )
texture_hover = ExtResource( 9 )
expand = true
__meta__ = {
"_edit_use_anchors_": false
}

[node name="TextureRect2" type="TextureRect" parent="conversationv2/controls/scrollup"]
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 2
texture = ExtResource( 13 )
expand = true

[node name="closeconvo" type="TextureButton" parent="conversationv2/controls"]
self_modulate = Color( 0.901961, 0.627451, 0.627451, 1 )
anchor_left = 0.7
anchor_top = 0.05
anchor_right = 0.975
anchor_bottom = 0.95
margin_left = -3.36737
margin_top = 1.12247
margin_right = -3.36737
margin_bottom = 1.12247
texture_normal = ExtResource( 7 )
texture_pressed = ExtResource( 10 )
texture_hover = ExtResource( 9 )
expand = true
__meta__ = {
"_edit_use_anchors_": false
}

[node name="TextureRect3" type="TextureRect" parent="conversationv2/controls/closeconvo"]
anchor_right = 1.0
anchor_bottom = 1.0
margin_top = -1.12247
margin_bottom = -1.12247
mouse_filter = 2
texture = ExtResource( 14 )
expand = true

[node name="controlspeek" type="AnimationPlayer" parent="conversationv2/controls"]
playback_speed = 2.0
anims/peek = SubResource( 8 )

[node name="chatcycle" type="TextureRect" parent="conversationv2/controls"]
visible = false
anchor_left = 0.3
anchor_top = 1.1
anchor_right = 0.7
anchor_bottom = 2.0
rect_pivot_offset = Vector2( 81.92, 40 )
mouse_filter = 2
texture = ExtResource( 29 )
expand = true
stretch_mode = 6

[node name="cycle" type="AnimationPlayer" parent="conversationv2/controls/chatcycle"]
anims/cycle = SubResource( 25 )

[node name="scrollconvo" type="AnimationPlayer" parent="conversationv2"]
anims/scrollconvo = SubResource( 9 )

[node name="waittime" type="Timer" parent="conversationv2"]
one_shot = true

[node name="corruptionbar" type="Control" parent="."]
visible = false
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
margin_top = -30.0
margin_bottom = -30.0
grow_horizontal = 2
grow_vertical = 0
mouse_filter = 2

[node name="hbox" parent="corruptionbar" instance=ExtResource( 52 )]
margin_left = 450.0
margin_top = -160.0
margin_right = 0.0
margin_bottom = 0.0
grow_horizontal = 2
grow_vertical = 0

[node name="hidetime" type="Timer" parent="corruptionbar"]
wait_time = 1.5
one_shot = true

[node name="inventoryveil" type="TextureRect" parent="."]
visible = false
self_modulate = Color( 1, 1, 1, 0.784314 )
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 0
texture = ExtResource( 16 )
expand = true

[node name="inventory" type="TextureRect" parent="inventoryveil"]
anchor_left = 0.2
anchor_top = 0.2
anchor_right = 0.8
anchor_bottom = 0.8
texture = ExtResource( 17 )
expand = true
stretch_mode = 1

[node name="Status" type="TextureButton" parent="inventoryveil/inventory"]
show_behind_parent = true
anchor_left = 0.05
anchor_top = -0.15
anchor_right = 0.3
anchor_bottom = 0.05
texture_normal = ExtResource( 36 )
texture_hover = ExtResource( 30 )
expand = true

[node name="Label" type="Label" parent="inventoryveil/inventory/Status"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_colors/font_color = Color( 0.0980392, 0.0784314, 0.0784314, 1 )
custom_fonts/font = SubResource( 10 )
text = "Status"
align = 1
valign = 1

[node name="Ability" type="TextureButton" parent="inventoryveil/inventory"]
show_behind_parent = true
anchor_left = 0.35
anchor_top = -0.15
anchor_right = 0.6
anchor_bottom = 0.05
texture_normal = ExtResource( 36 )
texture_hover = ExtResource( 30 )
expand = true

[node name="Label2" type="Label" parent="inventoryveil/inventory/Ability"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_colors/font_color = Color( 0.0980392, 0.0784314, 0.0784314, 1 )
custom_fonts/font = SubResource( 10 )
text = "Skills"
align = 1
valign = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Settings" type="TextureButton" parent="inventoryveil/inventory"]
show_behind_parent = true
anchor_left = 0.65
anchor_top = -0.15
anchor_right = 0.9
anchor_bottom = 0.05
texture_normal = ExtResource( 36 )
texture_hover = ExtResource( 30 )
expand = true
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label3" type="Label" parent="inventoryveil/inventory/Settings"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_colors/font_color = Color( 0.0980392, 0.0784314, 0.0784314, 1 )
custom_fonts/font = SubResource( 10 )
text = "Settings"
align = 1
valign = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Statusscreen" type="ScrollContainer" parent="inventoryveil/inventory"]
anchor_left = 0.05
anchor_top = 0.1
anchor_right = 0.95
anchor_bottom = 0.95
margin_bottom = -20.0
mouse_filter = 1
scroll_horizontal_enabled = false

[node name="newstatus" type="GridContainer" parent="inventoryveil/inventory/Statusscreen"]
columns = 2

[node name="TabInformation" type="Label" parent="inventoryveil/inventory"]
anchor_left = -0.3
anchor_right = 1.3
anchor_bottom = 0.1
grow_horizontal = 2
custom_colors/font_color = Color( 1, 1, 1, 0.784314 )
custom_fonts/font = SubResource( 13 )
text = "debugtext"
align = 1
valign = 2
autowrap = true

[node name="Abilityscreen" type="ScrollContainer" parent="inventoryveil/inventory"]
visible = false
anchor_left = 0.05
anchor_top = 0.1
anchor_right = 0.95
anchor_bottom = 0.95
margin_bottom = -20.0
rect_clip_content = false
mouse_filter = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="abilitygrid" type="GridContainer" parent="inventoryveil/inventory/Abilityscreen"]
columns = 10
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Settingscreen" parent="inventoryveil/inventory" instance=ExtResource( 4 )]
visible = false

[node name="closebutton" type="TextureButton" parent="inventoryveil/inventory"]
anchor_left = 0.95
anchor_top = -0.11
anchor_right = 1.05
anchor_bottom = 0.07
texture_normal = ExtResource( 37 )
texture_hover = ExtResource( 24 )
expand = true
stretch_mode = 5
__meta__ = {
"_edit_use_anchors_": false
}

[node name="shards" type="TextureRect" parent="inventoryveil/inventory"]
modulate = Color( 0.745098, 0.54902, 0.635294, 0.705882 )
anchor_left = -0.3
anchor_bottom = 1.0
texture = ExtResource( 18 )
expand = true
stretch_mode = 6

[node name="shardcount" type="Label" parent="inventoryveil/inventory/shards"]
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
grow_horizontal = 0
grow_vertical = 2
custom_colors/font_color = Color( 0.92549, 0.92549, 0.945098, 1 )
custom_fonts/font = SubResource( 14 )
text = "6"
align = 1
valign = 1

[node name="maxshards" type="Label" parent="inventoryveil/inventory/shards"]
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
custom_colors/font_color = Color( 0.705882, 0.705882, 0.717647, 1 )
custom_fonts/font = SubResource( 27 )
text = "/20"
align = 1
valign = 1

[node name="hologram" type="TextureRect" parent="inventoryveil/inventory/shards"]
visible = false
self_modulate = Color( 0.866898, 0.761719, 1, 1 )
show_behind_parent = true
material = SubResource( 29 )
anchor_left = 0.7
anchor_top = 0.47
anchor_right = 1.142
anchor_bottom = 0.53
grow_horizontal = 2
grow_vertical = 0
texture = ExtResource( 34 )
expand = true

[node name="skiplevelbutton" type="TextureButton" parent="inventoryveil/inventory"]
self_modulate = Color( 1, 0.823529, 0.823529, 1 )
show_behind_parent = true
anchor_left = -0.25
anchor_top = -0.2
anchor_right = -0.05
mouse_filter = 1
texture_normal = ExtResource( 36 )
expand = true
stretch_mode = 5

[node name="Label2" type="Label" parent="inventoryveil/inventory/skiplevelbutton"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_colors/font_color = Color( 0.0980392, 0.0784314, 0.0784314, 1 )
custom_fonts/font = SubResource( 10 )
text = "Skip Lvl"
align = 1
valign = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label3" type="Label" parent="inventoryveil/inventory/skiplevelbutton"]
visible = false
modulate = Color( 1, 1, 1, 0.705882 )
show_behind_parent = true
anchor_right = 1.0
anchor_bottom = 1.0
margin_top = -45.0
margin_right = 464.0
margin_bottom = -60.0
custom_colors/font_color = Color( 0.0980392, 0.0784314, 0.0784314, 1 )
custom_fonts/font = SubResource( 10 )
text = "Warning! Exhausts by 20."
align = 1
valign = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="TextureRect" type="TextureRect" parent="inventoryveil/inventory/skiplevelbutton/Label3"]
self_modulate = Color( 1, 0.823529, 0.823529, 1 )
show_behind_parent = true
anchor_right = 1.0
margin_bottom = 64.0
texture = ExtResource( 51 )
stretch_mode = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="doubleclicksk" type="Timer" parent="inventoryveil/inventory/skiplevelbutton"]
wait_time = 2.0
one_shot = true

[node name="lootchest" type="TextureRect" parent="."]
visible = false
modulate = Color( 1, 1, 1, 0 )
anchor_left = 0.35
anchor_top = 0.21
anchor_right = 0.65
anchor_bottom = 0.35
grow_horizontal = 2
grow_vertical = 2
rect_scale = Vector2( 0.1, 1 )
rect_pivot_offset = Vector2( 153.6, 42 )
mouse_filter = 2
texture = ExtResource( 23 )
expand = true
stretch_mode = 6

[node name="showloot" type="AnimationPlayer" parent="lootchest"]
playback_speed = 0.85
anims/chest = SubResource( 26 )

[node name="open3" type="AudioStreamPlayer" parent="lootchest"]
stream = ExtResource( 31 )
volume_db = -23.0
bus = "SFX"

[connection signal="pressed" from="deployablecontainer/lowerui2/climbtoggle" to="." method="_on_climbtoggle_pressed"]
[connection signal="pressed" from="deployablecontainer/healthbox" to="." method="_on_healthbox_pressed"]
[connection signal="animation_finished" from="deployablecontainer/healthbox/AnimationPlayer" to="." method="_on_AnimationPlayer_animation_finished"]
[connection signal="pressed" from="deployablecontainer/inventorybutton" to="." method="inventoryengage"]
[connection signal="timeout" from="queue" to="." method="_on_queue_timeout"]
[connection signal="pressed" from="conversationv2/controls/scrolldown" to="." method="_on_scrolldown_pressed"]
[connection signal="pressed" from="conversationv2/controls/scrollup" to="." method="_on_scrollup_pressed"]
[connection signal="pressed" from="conversationv2/controls/closeconvo" to="." method="_on_closeconvo_pressed"]
[connection signal="timeout" from="conversationv2/waittime" to="." method="_on_waittime_timeout"]
[connection signal="timeout" from="corruptionbar/hidetime" to="." method="_on_hidetime_timeout"]
[connection signal="pressed" from="inventoryveil/inventory/Status" to="." method="_on_Status_pressed"]
[connection signal="pressed" from="inventoryveil/inventory/Ability" to="." method="_on_Ability_pressed"]
[connection signal="pressed" from="inventoryveil/inventory/Settings" to="." method="_on_Settings_pressed"]
[connection signal="pressed" from="inventoryveil/inventory/closebutton" to="." method="_on_closebutton_pressed"]
[connection signal="pressed" from="inventoryveil/inventory/skiplevelbutton" to="." method="_on_skiplevelbutton_pressed"]
[connection signal="timeout" from="inventoryveil/inventory/skiplevelbutton/doubleclicksk" to="." method="_on_doubleclicksk_timeout"]

[editable path="ConversationV1"]
