[gd_scene load_steps=18 format=2]

[ext_resource path="res://DialogueArt/shaking.tres" type="Animation" id=1]
[ext_resource path="res://DialogueArt/jump.tres" type="Animation" id=2]
[ext_resource path="res://DialogueArt/fadepitch.tres" type="Animation" id=3]
[ext_resource path="res://DialogueArt/talking.tres" type="Animation" id=4]
[ext_resource path="res://DialogueArt/effects.gd" type="Script" id=5]
[ext_resource path="res://chareffectstalking.tscn" type="PackedScene" id=6]
[ext_resource path="res://DialogueArt/reset.tres" type="Animation" id=7]

[sub_resource type="Animation" id=10]
resource_name = "descend"
length = 1.1
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1, 1.1 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, -1800 ), Vector2( 0, -810 ), Vector2( 0, -480 ), Vector2( 0, -315 ), Vector2( 0, -216 ), Vector2( -2, -150 ), Vector2( 5, -102.857 ), Vector2( -8, -67.5 ), Vector2( 14, -40 ), Vector2( -17, -18 ), Vector2( 4, 26 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=1]
resource_name = "fadepitchnotalk"
length = 0.5
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 0.54902, 0.54902, 0.54902, 1 ), Color( 1, 1, 1, 0 ) ]
}

[sub_resource type="Animation" id=9]
resource_name = "shiftleft"
length = 1.2
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1.2 ),
"transitions": PoolRealArray( 2, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -600, 0 ) ]
}

[sub_resource type="Animation" id=7]
resource_name = "silhouette"
length = 0.5
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 0, 0, 0, 0 ), Color( 0, 0, 0, 1 ) ]
}

[sub_resource type="Animation" id=2]
resource_name = "silhouetteopaque"
length = 0.5
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0, 0, 0, 1 ) ]
}

[sub_resource type="Animation" id=8]
length = 0.8
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0, 160 ) ]
}

[sub_resource type="Animation" id=3]
resource_name = "twirl"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1, 2 ),
"transitions": PoolRealArray( 0.5, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 750, 40 ), Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1, 2 ),
"transitions": PoolRealArray( 0.5, 2, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( -1, 1 ), Vector2( 1, 1 ) ]
}

[sub_resource type="Animation" id=4]
resource_name = "zoomin"
length = 0.5
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.705882, 0.705882, 0.705882, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 2, 2 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -150, -150 ) ]
}

[sub_resource type="Animation" id=5]
length = 0.4
tracks/0/type = "value"
tracks/0/path = NodePath("..:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.4 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 0.54902, 0.54902, 0.54902, 1 ), Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=6]
resource_name = "zoomin"
tracks/0/type = "value"
tracks/0/path = NodePath("..:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 1.1, 1.1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("..:position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -10, -10 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("..:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.705882, 0.705882, 0.705882, 1 ) ]
}

[node name="effects" type="AnimationPlayer"]
anims/descend = SubResource( 10 )
anims/fadepitch = ExtResource( 3 )
anims/fadepitchnotalk = SubResource( 1 )
anims/jump = ExtResource( 2 )
anims/reset = ExtResource( 7 )
anims/shaking = ExtResource( 1 )
anims/shiftleft = SubResource( 9 )
anims/silhouette = SubResource( 7 )
anims/silhouetteopaque = SubResource( 2 )
anims/sink = SubResource( 8 )
anims/talking = ExtResource( 4 )
anims/twirl = SubResource( 3 )
anims/zoomin = SubResource( 4 )
script = ExtResource( 5 )

[node name="talking" parent="." instance=ExtResource( 6 )]
anims/talking2 = SubResource( 5 )

[node name="zoomer" type="AnimationPlayer" parent="."]
anims/zoomin = SubResource( 6 )
