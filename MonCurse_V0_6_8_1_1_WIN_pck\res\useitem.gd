extends Node2D


# Declare member variables here. Examples:
# var a = 2
# var b = "text"


# Called when the node enters the scene tree for the first time.
func use_effect(movedict):
#	var movedict = Playervariables.get("Move"+str(movenum))
	var usetexture = load("res://Assets/abilityicons/"+str(movedict.get("name"))+".png")
	$use2.texture = usetexture
	$use2/use1.texture = usetexture
	material.set_shader_param("linesColor",Playervariables.raritycolorsarray[movedict["types"][5]])
	material.set_shader_param("linesColorIntensity",1+(movedict["types"][5]/2))
func _on_useanim_animation_finished(_anim_name):
	queue_free()
