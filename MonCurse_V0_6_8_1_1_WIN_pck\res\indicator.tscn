[gd_scene load_steps=32 format=2]

[ext_resource path="res://Assets/ui/indicatorq.png" type="Texture" id=1]
[ext_resource path="res://Assets/ui/rightclickindicator.png" type="Texture" id=2]
[ext_resource path="res://Assets/ui/leftclickindicator.png" type="Texture" id=3]
[ext_resource path="res://Assets/ui/touchcamera.png" type="Texture" id=4]
[ext_resource path="res://Assets/ui/tapindicator.png" type="Texture" id=5]

[sub_resource type="AtlasTexture" id=1]
atlas = ExtResource( 1 )
region = Rect2( 0, 0, 64, 64 )

[sub_resource type="AtlasTexture" id=2]
atlas = ExtResource( 1 )
region = Rect2( 64, 0, 64, 64 )

[sub_resource type="AtlasTexture" id=3]
atlas = ExtResource( 1 )
region = Rect2( 128, 0, 64, 64 )

[sub_resource type="AtlasTexture" id=4]
atlas = ExtResource( 1 )
region = Rect2( 192, 0, 64, 64 )

[sub_resource type="AtlasTexture" id=5]
atlas = ExtResource( 1 )
region = Rect2( 0, 64, 64, 64 )

[sub_resource type="AtlasTexture" id=6]
atlas = ExtResource( 1 )
region = Rect2( 64, 64, 64, 64 )

[sub_resource type="AtlasTexture" id=7]
atlas = ExtResource( 1 )
region = Rect2( 128, 64, 64, 64 )

[sub_resource type="AtlasTexture" id=8]
atlas = ExtResource( 1 )
region = Rect2( 192, 64, 64, 64 )

[sub_resource type="AtlasTexture" id=9]
atlas = ExtResource( 1 )
region = Rect2( 0, 128, 64, 64 )

[sub_resource type="AtlasTexture" id=10]
atlas = ExtResource( 1 )
region = Rect2( 64, 128, 64, 64 )

[sub_resource type="AtlasTexture" id=11]
atlas = ExtResource( 1 )
region = Rect2( 128, 128, 64, 64 )

[sub_resource type="AtlasTexture" id=12]
atlas = ExtResource( 1 )
region = Rect2( 192, 128, 64, 64 )

[sub_resource type="AtlasTexture" id=22]
atlas = ExtResource( 3 )
region = Rect2( 64, 0, 64, 64 )

[sub_resource type="AtlasTexture" id=23]
atlas = ExtResource( 3 )
region = Rect2( 0, 0, 64, 64 )

[sub_resource type="AtlasTexture" id=19]
atlas = ExtResource( 2 )
region = Rect2( 0, 0, 64, 64 )

[sub_resource type="AtlasTexture" id=20]
atlas = ExtResource( 2 )
region = Rect2( 64, 0, 64, 64 )

[sub_resource type="AtlasTexture" id=15]
atlas = ExtResource( 1 )
region = Rect2( 0, 256, 128, 128 )

[sub_resource type="AtlasTexture" id=16]
atlas = ExtResource( 1 )
region = Rect2( 128, 256, 128, 128 )

[sub_resource type="AtlasTexture" id=17]
atlas = ExtResource( 1 )
region = Rect2( 0, 384, 128, 128 )

[sub_resource type="AtlasTexture" id=18]
atlas = ExtResource( 1 )
region = Rect2( 128, 384, 128, 128 )

[sub_resource type="AtlasTexture" id=25]
atlas = ExtResource( 5 )
region = Rect2( 0, 0, 64, 64 )

[sub_resource type="AtlasTexture" id=26]
atlas = ExtResource( 5 )
region = Rect2( 64, 0, 64, 64 )

[sub_resource type="SpriteFrames" id=13]
animations = [ {
"frames": [ SubResource( 1 ), SubResource( 2 ), SubResource( 3 ), SubResource( 4 ) ],
"loop": true,
"name": "default",
"speed": 3.0
}, {
"frames": [ SubResource( 5 ), SubResource( 6 ), SubResource( 7 ), SubResource( 8 ), SubResource( 9 ), SubResource( 10 ), SubResource( 11 ), SubResource( 12 ) ],
"loop": true,
"name": "feature",
"speed": 3.0
}, {
"frames": [ SubResource( 22 ), SubResource( 23 ) ],
"loop": true,
"name": "leftclick",
"speed": 2.0
}, {
"frames": [ SubResource( 19 ), SubResource( 20 ) ],
"loop": true,
"name": "rightclick",
"speed": 2.0
}, {
"frames": [ SubResource( 15 ), SubResource( 16 ), SubResource( 17 ), SubResource( 18 ) ],
"loop": true,
"name": "sub",
"speed": 3.0
}, {
"frames": [ SubResource( 25 ), SubResource( 26 ) ],
"loop": true,
"name": "tap",
"speed": 1.5
} ]

[sub_resource type="Animation" id=21]
resource_name = "float"
length = 8.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:offset")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1, 2, 3, 4, 5, 6, 7, 8 ),
"transitions": PoolRealArray( 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8 ),
"update": 0,
"values": [ Vector2( -10, 65 ), Vector2( -43, 53 ), Vector2( -55, 20 ), Vector2( -43, -13 ), Vector2( -10, -25 ), Vector2( 23, -13 ), Vector2( 35, 20 ), Vector2( 23, 53 ), Vector2( -10, 65 ) ]
}

[sub_resource type="Animation" id=14]
length = 6.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:offset")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 3, 6 ),
"transitions": PoolRealArray( 1, 1, 2 ),
"update": 0,
"values": [ Vector2( 0, -90 ), Vector2( 0, -110 ), Vector2( 0, -90 ) ]
}

[sub_resource type="Animation" id=24]
resource_name = "leftydrag"
length = 3.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:offset")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3, 3 ),
"transitions": PoolRealArray( 2, 0.5, 1 ),
"update": 0,
"values": [ Vector2( 0, -80 ), Vector2( 0, -60 ), Vector2( 200, -300 ) ]
}

[node name="Node2D" type="AnimatedSprite"]
modulate = Color( 1, 1, 1, 0.627451 )
frames = SubResource( 13 )
frame = 3
playing = true
offset = Vector2( 0, -90 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
anims/float = SubResource( 21 )
anims/indicator = SubResource( 14 )
anims/leftydrag = SubResource( 24 )

[node name="touchscreen" type="Sprite" parent="."]
visible = false
show_behind_parent = true
scale = Vector2( 0.5, 0.5 )
texture = ExtResource( 4 )
