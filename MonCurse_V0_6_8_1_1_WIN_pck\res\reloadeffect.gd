extends Control


const reload_tab = preload("res://reloadtab.tscn")

func load_tabs(num,curses):
	for i in range(num):
		var new_tab = reload_tab.instance()
		new_tab.rect_position = 128*Vector2(randf(),randf())
		if curses > 0 and randf() < curses/(num-i):
			new_tab.set_modulate(Color(0.5,0.5,1))
			curses -= 1
		$centre.add_child(new_tab)
		for _i2 in range(int(clamp(10-sqrt(num),1,10))):
			yield(get_tree(),"idle_frame")
	$Timer.start(1.5)

func _on_Timer_timeout():
	if $centre.get_children().size() == 0:
		queue_free()
	else:
		print("Failed to unload RELOADEFFECT, will try again in 1.5 seconds.")
		$Timer.start(1.5)
