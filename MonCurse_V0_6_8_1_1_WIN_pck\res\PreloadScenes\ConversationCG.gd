extends Node

const ConversationV1 = preload("res://ConversationV1.tscn") #ConversationCG

const ChapterText = preload("res://DialogueArt/CG/chaptertext.tscn") ##ConversationCG
const MonmusuforestBG = preload("res://DialogueArt/CG/monmusuforest.tscn") #ConversationCG
#const NameInput = preload("res://DialogueArt/CG/nameinput.tscn") #ConversationCG
#const ColorInput = preload("res://DialogueArt/CG/colorinput.tscn") #ConversationCG
#const CombatSFX = preload("res://DialogueArt/CG/combatsfx.tscn") #ConversationCG
#const BlinkEffect = preload("res://DialogueArt/CG/blinkeffect.tscn") #ConversationCG

const talkingremorse = preload("res://music/talkingremorse.ogg") #Conversation #MUSIC
const talkingsavvy = preload("res://music/talkingsavvy.ogg") #Conversation #MUSIC
const talkingharp = preload("res://music/rambadend1.ogg") #Conversation #MUSIC
const talkingpiano = preload("res://music/rambadend2.ogg") #Conversation #MUSIC

#const RewardSymbol = preload("res://DialogueArt/CG/shikasymbol.tscn")

const nightsky = preload("res://Background/nightsky.png") #ConversationCG

const petalmenumiddle = preload("res://Assets/ui/petalmenumiddle.png") #ConversationCG
const petalmenupetal = preload("res://Assets/ui/petalmenupetal.png") #ConversationCG
const petalmenupetalhover = preload("res://Assets/ui/petalmenupetalhover.png") #ConversationCG
const petalmenupetalburned = preload("res://Assets/ui/petalmenupetalburned.png") #ConversationCG
