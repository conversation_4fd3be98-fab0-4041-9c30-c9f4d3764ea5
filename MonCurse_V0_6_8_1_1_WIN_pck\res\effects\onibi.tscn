[gd_scene load_steps=7 format=2]

[ext_resource path="res://Background/onibi.png" type="Texture" id=1]
[ext_resource path="res://effects/onibi.gd" type="Script" id=2]

[sub_resource type="AtlasTexture" id=1]
atlas = ExtResource( 1 )
region = Rect2( 0, 0, 200, 200 )

[sub_resource type="AtlasTexture" id=2]
atlas = ExtResource( 1 )
region = Rect2( 200, 0, 200, 200 )

[sub_resource type="SpriteFrames" id=3]
animations = [ {
"frames": [ SubResource( 1 ), SubResource( 2 ) ],
"loop": true,
"name": "default",
"speed": 1.0
} ]

[sub_resource type="Animation" id=4]
resource_name = "onibi"
length = 8.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:offset")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 8 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 4.6, 6.2, 8 ),
"transitions": PoolRealArray( 0.7, 1, 1, 1 ),
"update": 0,
"values": [ Color( 0.666667, 0.666667, 0.666667, 0 ), Color( 0.666667, 0.666667, 0.666667, 0.529412 ), Color( 0.666667, 0.666667, 0.666667, 0.392157 ), Color( 0.666667, 0.666667, 0.666667, 0 ) ]
}

[node name="AnimatedSprite" type="AnimatedSprite"]
modulate = Color( 1, 1, 1, 0 )
frames = SubResource( 3 )
speed_scale = 0.3
playing = true
script = ExtResource( 2 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
anims/onibi = SubResource( 4 )

[connection signal="animation_finished" from="AnimationPlayer" to="." method="_on_AnimationPlayer_animation_finished"]
