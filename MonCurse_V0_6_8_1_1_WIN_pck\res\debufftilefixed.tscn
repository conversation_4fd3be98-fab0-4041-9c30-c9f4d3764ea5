[gd_scene load_steps=13 format=2]

[ext_resource path="res://font/Audiowide-Regular.ttf" type="DynamicFontData" id=1]
[ext_resource path="res://Assets/ui/debufficons/debuffborder.png" type="Texture" id=2]
[ext_resource path="res://Assets/ui/debuffaffectlight.png" type="Texture" id=3]
[ext_resource path="res://debufftilefixed.gd" type="Script" id=4]
[ext_resource path="res://Assets/ui/debufficons/radialdebuffborder.png" type="Texture" id=5]
[ext_resource path="res://Assets/ui/debufficons/radialdebuffborderfill.png" type="Texture" id=6]
[ext_resource path="res://Assets/ui/debufficons/1d.png" type="Texture" id=7]

[sub_resource type="DynamicFont" id=1]
resource_local_to_scene = true
size = 40
outline_size = 2
outline_color = Color( 0.603922, 0.603922, 0.603922, 0.627451 )
font_data = ExtResource( 1 )

[sub_resource type="Animation" id=2]
resource_name = "count"
tracks/0/type = "value"
tracks/0/path = NodePath("Label:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 0.784314, 0.784314, 0.627451 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Label:margin_bottom")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 0.3, 1 ),
"update": 0,
"values": [ -64, 0 ]
}

[sub_resource type="Animation" id=3]
resource_name = "countrev"
length = 0.7
tracks/0/type = "value"
tracks/0/path = NodePath("Label2:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray(  ),
"transitions": PoolRealArray(  ),
"update": 1,
"values": [  ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Label2:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.7 ),
"transitions": PoolRealArray( 0.3, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("Label:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.7 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("Label2:margin_bottom")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.7 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ 13, -60 ]
}

[sub_resource type="Animation" id=6]
resource_name = "hide"
length = 0.6
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.6 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0.6 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:rect_min_size")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = false
tracks/2/keys = {
"times": PoolRealArray( 0, 0.6 ),
"transitions": PoolRealArray( 4, 1 ),
"update": 0,
"values": [ Vector2( 128, 128 ), Vector2( 0, 128 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:rect_size")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = false
tracks/3/keys = {
"times": PoolRealArray( 0, 0.6 ),
"transitions": PoolRealArray( 4, 1 ),
"update": 0,
"values": [ Vector2( 128, 128 ), Vector2( 0, 128 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath(".:margin_right")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = false
tracks/4/keys = {
"times": PoolRealArray( 0, 0.6 ),
"transitions": PoolRealArray( 4, 1 ),
"update": 1,
"values": [ 128.0, 0 ]
}

[sub_resource type="Animation" id=5]
resource_name = "show"
length = 0.6
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.6 ),
"transitions": PoolRealArray( 1, 0.5 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:rect_min_size")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = false
tracks/2/keys = {
"times": PoolRealArray( 0, 0.6 ),
"transitions": PoolRealArray( 0.25, 1 ),
"update": 0,
"values": [ Vector2( 0, 128 ), Vector2( 128, 128 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:rect_size")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = false
tracks/3/keys = {
"times": PoolRealArray( 0, 0.6 ),
"transitions": PoolRealArray( 0.25, 1 ),
"update": 0,
"values": [ Vector2( 0, 128 ), Vector2( 128, 128 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath(".:margin_right")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = false
tracks/4/keys = {
"times": PoolRealArray( 0, 0.6 ),
"transitions": PoolRealArray( 0.25, 1 ),
"update": 1,
"values": [ 0, 128.0 ]
}

[node name="debufftile" type="TextureRect"]
visible = false
margin_right = 128.0
margin_bottom = 128.0
rect_min_size = Vector2( 128, 128 )
texture = ExtResource( 7 )
expand = true
script = ExtResource( 4 )

[node name="Label" type="Label" parent="."]
self_modulate = Color( 1, 0.935294, 0.935294, 0.888235 )
anchor_right = 1.0
margin_left = 10.0
margin_top = -64.0
margin_right = -10.0
margin_bottom = -1.0
grow_horizontal = 2
grow_vertical = 0
custom_colors/font_color = Color( 1, 0.862745, 0.784314, 1 )
custom_colors/font_outline_modulate = Color( 1, 0, 0, 1 )
custom_fonts/font = SubResource( 1 )
text = "6"
align = 1
valign = 2

[node name="Label2" type="Label" parent="."]
self_modulate = Color( 1, 1, 1, 0 )
anchor_right = 1.0
margin_left = 10.0
margin_top = -107.0
margin_right = -10.0
margin_bottom = -60.0
grow_vertical = 0
custom_colors/font_color = Color( 1, 0.862745, 0.784314, 1 )
custom_colors/font_outline_modulate = Color( 1, 0, 0, 1 )
custom_fonts/font = SubResource( 1 )
text = "7"
align = 1
valign = 2

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
anims/count = SubResource( 2 )

[node name="countrev" type="AnimationPlayer" parent="."]
anims/countrev = SubResource( 3 )

[node name="affectlight" type="TextureRect" parent="."]
visible = false
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource( 3 )

[node name="debuffnum" type="Node2D" parent="."]
visible = false
z_index = 1
z_as_relative = false

[node name="buffattack" type="VBoxContainer" parent="."]
visible = false
show_behind_parent = true
anchor_right = 1.0
anchor_bottom = 1.0
grow_vertical = 0
mouse_filter = 2
custom_constants/separation = -103
alignment = 2

[node name="showhide" type="AnimationPlayer" parent="."]
anims/hide = SubResource( 6 )
anims/show = SubResource( 5 )

[node name="border" type="TextureRect" parent="."]
visible = false
show_behind_parent = true
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource( 2 )

[node name="fillborder" type="TextureProgress" parent="."]
visible = false
show_behind_parent = true
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
grow_horizontal = 2
grow_vertical = 2
max_value = 6.0
texture_under = ExtResource( 5 )
texture_progress = ExtResource( 6 )
fill_mode = 4
