extends Node

const baroverst = preload("res://Assets/ui/stonebarmiddle.png") #GUI
const barst = preload("res://Assets/ui/stonebarover.png") #GUI
const barunderst = preload("res://Assets/ui/stonebarunder.png") #GUI
const buttonst = preload("res://Assets/ui/monmusubuttonhalf.png") #GUI
const buttonsthover = preload("res://Assets/ui/monmusubuttonhalfhover.png") #GUI
const buttonsthoverpress = preload("res://Assets/ui/monmusubuttonhalfhoverpress.png") #GUI
const closest = preload("res://Assets/ui/monmusuclose.png") #GUI
const closesthover = preload("res://Assets/ui/monmusuclosehover.png") #GUI

const number0 = preload("res://Assets/ui/0.png") #GUI
const number1 = preload("res://Assets/ui/1.png") #GUI
const number2 = preload("res://Assets/ui/2.png") #GUI
const number3 = preload("res://Assets/ui/3.png") #GUI
const number4 = preload("res://Assets/ui/4.png") #GUI
const number5 = preload("res://Assets/ui/5.png") #GUI
const number6 = preload("res://Assets/ui/6.png") #GUI
const number7 = preload("res://Assets/ui/7.png") #GUI
const number8 = preload("res://Assets/ui/8.png") #GUI
const number9 = preload("res://Assets/ui/9.png") #GUI

const Pop = preload("res://alertlabel.tscn") #GUI
const Passiveshow = preload("res://passivedeploy.tscn") #GUI
const Backer = preload("res://backer.tscn") #GUI
const Deployables = preload("res://Deployable.tscn") #GUI
const Eventtext = preload("res://eventtext.tscn") #GUI
const EnemyArt = preload("res://enemyart.tscn") #GUI
const AnnounceAttack = preload("res://effects/announceattack2.tscn")

const Enemydebuff = preload("res://enemydebuff.tscn") #GUI
#const Enemytext = preload("res://Assets/ui/debufficons/enemytext.png") #GUI

const Newdebufftile = preload("res://debufftilefixed.tscn") #GUI
const Energyorb = preload("res://energyorbeffect.tscn") #GUI
const Healthorb = preload("res://effects/healthorb.tscn") #GUI
const DebuffMeter = preload("res://debuffmeter.tscn") #GUI
const DebuffMeterDescription = preload("res://debuffmeterdescription.tscn") #GUI
const NewDebuffDescriber = preload("res://newdebuffdescriber.tscn") #GUI
const Speechbubble = preload("res://Conversations/speechbubble.tscn") #GUI

const warning = "sfwwarning.tscn" #GUI
const sfwwarning = "res://Assets/sfwwarning.png" #GUI
const meatwarning = "res://Assets/ui/escalationcards/obtainedmeat.png" #GUI
const mapwarning = "res://Assets/ui/escalationcards/obtainedmap.png" #GUI
const nsfwwarning = "res://Assets/nsfwwarning.png" #GUI

const damagepill = preload("res://Assets/ui/damagecount.png") #GUI
const damageicon0 = preload("res://Assets/ui/damageicons/0.png") #GUI
const damageicon1 = preload("res://Assets/ui/damageicons/1.png") #GUI
const damageicon2 = preload("res://Assets/ui/damageicons/2.png") #GUI
const damageicon3 = preload("res://Assets/ui/damageicons/3.png") #GUI
const damageicon4 = preload("res://Assets/ui/damageicons/4.png") #GUI
const damageicon5 = preload("res://Assets/ui/damageicons/5.png") #GUI
const damageicon6 = preload("res://Assets/ui/damageicons/6.png") #GUI
const directionauto = preload("res://Assets/ui/directionauto.png") #GUI
const directionthrough = preload("res://Assets/ui/directionthrough.png") #GUI
const direction0 = preload("res://Assets/ui/direction0.png") #GUI
const direction1 = preload("res://Assets/ui/direction1.png") #GUI
const direction2 = preload("res://Assets/ui/direction2.png") #GUI
const direction3 = preload("res://Assets/ui/direction3.png") #GUI
const blocktilenone = preload("res://Assets/ui/blocktilenone.png") #GUI
const blocktile0 = preload("res://Assets/ui/blocktile0.png") #GUI
const blocktile1 = preload("res://Assets/ui/blocktile1.png") #GUI
const blocktile2 = preload("res://Assets/ui/blocktile2.png") #GUI
const blocktile3 = preload("res://Assets/ui/blocktile3.png") #GUI
const blocktile4 = preload("res://Assets/ui/blocktile4.png") #GUI
const blocktile5 = preload("res://Assets/ui/blocktile5.png") #GUI
#const safem = preload("res://Assets/ui/debufficons/safem.png") #GUI
#const debuffnm1 = preload("res://Assets/ui/debufficons/-1m.png") #GUI
#const debuffnm2 = preload("res://Assets/ui/debufficons/-2m.png") #GUI
#const debuffm1 = preload("res://Assets/ui/debufficons/1m.png") #GUI
#const debuffm2 = preload("res://Assets/ui/debufficons/2m.png") #GUI
#const debuffm3 = preload("res://Assets/ui/debufficons/3m.png") #GUI
#const debuffm4 = preload("res://Assets/ui/debufficons/4m.png") #GUI
#const debuffm5 = preload("res://Assets/ui/debufficons/5m.png") #GUI
#const debuffm6 = preload("res://Assets/ui/debufficons/6m.png") #GUI
#const debuffm7 = preload("res://Assets/ui/debufficons/7m.png") #GUI
#const debuffm8 = preload("res://Assets/ui/debufficons/8m.png") #GUI
#const debuffm9 = preload("res://Assets/ui/debufficons/9m.png") #GUI
#
#const debuffdn1 = preload("res://Assets/ui/debufficons/-1d.png") #GUI
#const debuffdn2 = preload("res://Assets/ui/debufficons/-2d.png") #GUI
#const debuffd1 = preload("res://Assets/ui/debufficons/1d.png") #GUI
#const debuffd2 = preload("res://Assets/ui/debufficons/2d.png") #GUI
#const debuffd3 = preload("res://Assets/ui/debufficons/3d.png") #GUI
#const debuffd4 = preload("res://Assets/ui/debufficons/4d.png") #GUI
#const debuffd5 = preload("res://Assets/ui/debufficons/5d.png") #GUI
#const debuffd6 = preload("res://Assets/ui/debufficons/6d.png") #GUI
#const debuffd7 = preload("res://Assets/ui/debufficons/7d.png") #GUI
#const debuffd8 = preload("res://Assets/ui/debufficons/8d.png") #GUI
#const debuffd9 = preload("res://Assets/ui/debufficons/9d.png") #GUI
#
#const debuffn1 = preload("res://Assets/ui/debufficons/-1.png") #GUI
#const debuffn2 = preload("res://Assets/ui/debufficons/-2.png") #GUI
#const debuff1 = preload("res://Assets/ui/debufficons/1.png") #GUI
#const debuff2 = preload("res://Assets/ui/debufficons/2.png") #GUI
#const debuff3 = preload("res://Assets/ui/debufficons/3.png") #GUI
#const debuff4 = preload("res://Assets/ui/debufficons/4.png") #GUI
#const debuff5 = preload("res://Assets/ui/debufficons/5.png") #GUI
#const debuff6 = preload("res://Assets/ui/debufficons/6.png") #GUI
#const debuff7 = preload("res://Assets/ui/debufficons/7.png") #GUI
#const debuff8 = preload("res://Assets/ui/debufficons/8.png") #GUI
#const debuff9 = preload("res://Assets/ui/debufficons/9.png") #GUI

const attackdir1 = preload("res://Assets/ui/attackdir1.png") #GUI
const attackdir2 = preload("res://Assets/ui/attackdir2.png") #GUI
const attackdir4 = preload("res://Assets/ui/attackdir4.png") #GUI
const attackdir5 = preload("res://Assets/ui/attackdir5.png") #GUI
const attackdir8 = preload("res://Assets/ui/attackdir8.png") #GUI

const noticeevent = preload("res://Assets/ui/Attackedtileevent.png") #GUI
const noticeattack = preload("res://Assets/ui/Attackedtile.png") #GUI
const noticeblock = preload("res://Assets/ui/Attackedtileblocked.png") #GUI
