extends HBoxContainer

var saverace = -1

func _ready():
	if saverace > -1:
		var _c = get_tree().root.connect("size_changed", self, "_on_viewport_size_changed")
		_on_viewport_size_changed()

var firstrun = true
var recentsizechange = false
func _on_viewport_size_changed():
	if recentsizechange == false:
		recentsizechange = true #this makes it so it only updates the viewport every 4 frames
		if firstrun == false:
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
		else:
			firstrun = false
		var ratio = get_viewport_rect().size/Playervariables.basescreensize
		self.rect_scale = ratio
		self.margin_top = (ratio.y * -128) #- 64
		recentsizechange = false


func _on_free_time_timeout():
	$drain.play("fade")
#	queue_free()


func _on_drain_animation_finished(anim_name):
	if saverace > -1:
		if anim_name in ["drainbar","fade"]:
			queue_free()
