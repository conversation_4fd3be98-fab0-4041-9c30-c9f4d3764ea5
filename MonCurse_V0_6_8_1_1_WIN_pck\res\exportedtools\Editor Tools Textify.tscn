[gd_scene load_steps=14 format=2]

[ext_resource path="res://Assets/uibutton.png" type="Texture" id=1]
[ext_resource path="res://exportedtools/Editor Tools Textify.gd" type="Script" id=2]
[ext_resource path="res://font/OpenSans-ExtraBold.ttf" type="DynamicFontData" id=3]
[ext_resource path="res://font/OpenSans-Light.ttf" type="DynamicFontData" id=4]
[ext_resource path="res://Assets/ui/qadesclosebuttonalt.png" type="Texture" id=5]
[ext_resource path="res://Assets/ui/key.png" type="Texture" id=6]
[ext_resource path="res://Assets/ui/keyhover.png" type="Texture" id=7]
[ext_resource path="res://Assets/ui/keypress.png" type="Texture" id=8]
[ext_resource path="res://Assets/movebarfill.png" type="Texture" id=9]

[sub_resource type="DynamicFont" id=1]
size = 24
font_data = ExtResource( 3 )

[sub_resource type="DynamicFont" id=3]
font_data = ExtResource( 3 )

[sub_resource type="DynamicFont" id=2]
size = 12
font_data = ExtResource( 4 )

[sub_resource type="DynamicFont" id=4]
size = 18
font_data = ExtResource( 3 )

[node name="Editor Tools Textify" type="Control"]
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource( 2 )

[node name="accept" type="TextureButton" parent="."]
self_modulate = Color( 0.733333, 0.733333, 0.752941, 1 )
anchor_left = 0.1
anchor_top = 0.49
anchor_right = 0.55
anchor_bottom = 0.66
texture_normal = ExtResource( 6 )
texture_pressed = ExtResource( 8 )
texture_hover = ExtResource( 7 )
expand = true

[node name="Label" type="Label" parent="accept"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_fonts/font = SubResource( 1 )
text = "Generate Translation File"
align = 1
valign = 1

[node name="Label2" type="Label" parent="."]
anchor_left = 0.1
anchor_top = 0.8
anchor_right = 0.55
anchor_bottom = 1.0
custom_fonts/font = SubResource( 1 )
text = "If the above version is incorrect,
change it to the game's current version."
align = 1
valign = 1

[node name="reject" type="TextEdit" parent="."]
self_modulate = Color( 1, 0.498039, 0.498039, 1 )
anchor_left = 0.1
anchor_top = 0.7
anchor_right = 0.55
anchor_bottom = 0.8
custom_fonts/font = SubResource( 3 )
text = "v0.0.0.0"

[node name="TextureRect" type="TextureRect" parent="."]
self_modulate = Color( 0.709804, 0.54902, 0.54902, 1 )
anchor_left = 0.1
anchor_top = 0.05
anchor_right = 0.55
anchor_bottom = 0.45
mouse_filter = 2
texture = ExtResource( 1 )
expand = true

[node name="Label" type="Label" parent="TextureRect"]
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
custom_fonts/font = SubResource( 1 )
align = 1
valign = 1

[node name="sub" type="Label" parent="TextureRect"]
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
custom_fonts/font = SubResource( 1 )
align = 1

[node name="all" type="Label" parent="."]
visible = false
anchor_left = 0.1
anchor_top = 0.75
anchor_right = 0.55
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
custom_fonts/font = SubResource( 2 )
align = 1
autowrap = true

[node name="quit" type="TextureButton" parent="."]
anchor_left = 1.0
anchor_right = 1.0
grow_horizontal = 0
texture_normal = ExtResource( 5 )

[node name="VBoxContainer" type="VBoxContainer" parent="."]
visible = false
anchor_top = 0.5
anchor_right = 0.2
anchor_bottom = 1.0
alignment = 2

[node name="CheckBox" type="CheckBox" parent="VBoxContainer"]
margin_top = 192.0
margin_right = 213.0
margin_bottom = 216.0
pressed = true
text = "Include Option Lines"

[node name="CheckBox2" type="CheckBox" parent="VBoxContainer"]
margin_top = 220.0
margin_right = 213.0
margin_bottom = 244.0
pressed = true
text = "Include Main Text (Why not?)"

[node name="Translation" type="CheckBox" parent="VBoxContainer"]
margin_top = 248.0
margin_right = 213.0
margin_bottom = 272.0
pressed = true
text = "Translation Version"

[node name="Translation2" type="CheckBox" parent="VBoxContainer"]
margin_top = 276.0
margin_right = 213.0
margin_bottom = 300.0
pressed = true
text = "Extra Translatables"

[node name="TextureButton" type="TextureButton" parent="."]
visible = false
anchor_top = 0.3
anchor_bottom = 0.3
margin_right = 40.0
margin_bottom = 40.0
texture_normal = ExtResource( 1 )

[node name="Label" type="Label" parent="TextureButton"]
margin_right = 40.0
margin_bottom = 14.0
text = "instant skip!!
do all!!"

[node name="new2" type="TextureRect" parent="."]
anchor_left = 0.65
anchor_top = 0.27
anchor_right = 0.95
anchor_bottom = 0.5
texture = ExtResource( 1 )
expand = true

[node name="Label3" type="Label" parent="new2"]
anchor_top = -0.2
anchor_right = 1.0
anchor_bottom = -0.1
custom_fonts/font = SubResource( 4 )
text = "OPTIONS"
align = 1
valign = 1

[node name="VBoxContainer" type="VBoxContainer" parent="new2"]
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = 40.0
margin_top = 40.0
margin_right = -40.0
margin_bottom = -40.0

[node name="CheckBox" type="CheckBox" parent="new2/VBoxContainer"]
margin_right = 227.0
margin_bottom = 28.0
text = "Export Current Translation"
icon = ExtResource( 9 )

[node name="Label" type="Label" parent="new2/VBoxContainer/CheckBox"]
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
align = 1
valign = 2
autowrap = true

[connection signal="pressed" from="accept" to="." method="_on_accept_pressed"]
[connection signal="text_changed" from="reject" to="." method="_on_reject_text_changed"]
[connection signal="pressed" from="quit" to="." method="_on_quit_pressed"]
[connection signal="toggled" from="VBoxContainer/CheckBox" to="." method="_on_CheckBox_toggled"]
[connection signal="toggled" from="VBoxContainer/CheckBox2" to="." method="_on_CheckBox2_toggled"]
[connection signal="pressed" from="TextureButton" to="." method="_on_TextureButton_pressed"]
[connection signal="pressed" from="new2/VBoxContainer/CheckBox" to="." method="_on_CheckBox_pressed"]
