[gd_resource type="TileSet" load_steps=18 format=2]

[ext_resource path="res://Background/fixedscenefeatures.png" type="Texture" id=1]
[ext_resource path="res://Background/dooroutalt.png" type="Texture" id=2]
[ext_resource path="res://Background/foliagetiles.png" type="Texture" id=3]
[ext_resource path="res://Background/doorinalt.png" type="Texture" id=4]
[ext_resource path="res://Background/doorin.png" type="Texture" id=5]
[ext_resource path="res://Background/doorout.png" type="Texture" id=6]
[ext_resource path="res://Background/kitsuneshrine2.png" type="Texture" id=7]
[ext_resource path="res://Background/doordesert.png" type="Texture" id=8]
[ext_resource path="res://Background/furniture.png" type="Texture" id=9]
[ext_resource path="res://Background/doorinretreat.png" type="Texture" id=10]
[ext_resource path="res://Background/pathway.png" type="Texture" id=11]
[ext_resource path="res://Background/Doors.png" type="Texture" id=12]
[ext_resource path="res://Background/villageobjects.png" type="Texture" id=13]
[ext_resource path="res://Background/villageprops.png" type="Texture" id=15]
[ext_resource path="res://Background/specialtree.png" type="Texture" id=16]
[ext_resource path="res://Background/specialtreeroots.png" type="Texture" id=17]
[ext_resource path="res://Background/stone staircase village.png" type="Texture" id=18]

[resource]
5/name = "doorin.png 5"
5/texture = ExtResource( 5 )
5/tex_offset = Vector2( 0, 0 )
5/modulate = Color( 1, 1, 1, 1 )
5/region = Rect2( 0, 0, 384, 384 )
5/tile_mode = 0
5/occluder_offset = Vector2( 0, 0 )
5/navigation_offset = Vector2( 0, 0 )
5/shape_offset = Vector2( 0, 0 )
5/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
5/shape_one_way = false
5/shape_one_way_margin = 0.0
5/shapes = [  ]
5/z_index = 0
6/name = "doorout.png 6"
6/texture = ExtResource( 6 )
6/tex_offset = Vector2( 0, 0 )
6/modulate = Color( 1, 1, 1, 1 )
6/region = Rect2( 0, 0, 384, 384 )
6/tile_mode = 0
6/occluder_offset = Vector2( 0, 0 )
6/navigation_offset = Vector2( 0, 0 )
6/shape_offset = Vector2( 0, 0 )
6/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
6/shape_one_way = false
6/shape_one_way_margin = 0.0
6/shapes = [  ]
6/z_index = 0
7/name = "kitsuneshrine2.png 7"
7/texture = ExtResource( 7 )
7/tex_offset = Vector2( 0, 0 )
7/modulate = Color( 1, 1, 1, 1 )
7/region = Rect2( 0, 0, 384, 320 )
7/tile_mode = 0
7/occluder_offset = Vector2( 0, 0 )
7/navigation_offset = Vector2( 0, 0 )
7/shape_offset = Vector2( 0, 0 )
7/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
7/shape_one_way = false
7/shape_one_way_margin = 0.0
7/shapes = [  ]
7/z_index = 0
9/name = "foliagetiles.png 9"
9/texture = ExtResource( 3 )
9/tex_offset = Vector2( 0, 0 )
9/modulate = Color( 1, 1, 1, 1 )
9/region = Rect2( 512, 256, 128, 128 )
9/tile_mode = 0
9/occluder_offset = Vector2( 0, 0 )
9/navigation_offset = Vector2( 0, 0 )
9/shape_offset = Vector2( 0, 0 )
9/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
9/shape_one_way = false
9/shape_one_way_margin = 0.0
9/shapes = [  ]
9/z_index = 0
10/name = "doorinalt.png 10"
10/texture = ExtResource( 4 )
10/tex_offset = Vector2( 0, 0 )
10/modulate = Color( 1, 1, 1, 1 )
10/region = Rect2( 0, 0, 384, 384 )
10/tile_mode = 0
10/occluder_offset = Vector2( 0, 0 )
10/navigation_offset = Vector2( 0, 0 )
10/shape_offset = Vector2( 0, 0 )
10/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
10/shape_one_way = false
10/shape_one_way_margin = 0.0
10/shapes = [  ]
10/z_index = 0
11/name = "dooroutalt.png 11"
11/texture = ExtResource( 2 )
11/tex_offset = Vector2( 0, 0 )
11/modulate = Color( 1, 1, 1, 1 )
11/region = Rect2( 0, 0, 384, 384 )
11/tile_mode = 0
11/occluder_offset = Vector2( 0, 0 )
11/navigation_offset = Vector2( 0, 0 )
11/shape_offset = Vector2( 0, 0 )
11/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
11/shape_one_way = false
11/shape_one_way_margin = 0.0
11/shapes = [  ]
11/z_index = 0
13/name = "fixedscenefeatures.png 13"
13/texture = ExtResource( 1 )
13/tex_offset = Vector2( -9, -146 )
13/modulate = Color( 1, 1, 1, 1 )
13/region = Rect2( 0, 0, 160, 256 )
13/tile_mode = 0
13/occluder_offset = Vector2( 0, 0 )
13/navigation_offset = Vector2( 0, 0 )
13/shape_offset = Vector2( 0, 0 )
13/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
13/shape_one_way = false
13/shape_one_way_margin = 0.0
13/shapes = [  ]
13/z_index = 0
14/name = "fixedscenefeatures.png 14"
14/texture = ExtResource( 1 )
14/tex_offset = Vector2( 11, -79 )
14/modulate = Color( 1, 1, 1, 1 )
14/region = Rect2( 160, 0, 160, 192 )
14/tile_mode = 0
14/occluder_offset = Vector2( 0, 0 )
14/navigation_offset = Vector2( 0, 0 )
14/shape_offset = Vector2( 0, 0 )
14/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
14/shape_one_way = false
14/shape_one_way_margin = 0.0
14/shapes = [  ]
14/z_index = 0
15/name = "fixedscenefeatures.png 15"
15/texture = ExtResource( 1 )
15/tex_offset = Vector2( -9, -92 )
15/modulate = Color( 1, 1, 1, 1 )
15/region = Rect2( 320, 0, 160, 224 )
15/tile_mode = 0
15/occluder_offset = Vector2( 0, 0 )
15/navigation_offset = Vector2( 0, 0 )
15/shape_offset = Vector2( 0, 0 )
15/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
15/shape_one_way = false
15/shape_one_way_margin = 0.0
15/shapes = [  ]
15/z_index = 0
16/name = "fixedscenefeatures.png 16"
16/texture = ExtResource( 1 )
16/tex_offset = Vector2( 0, 0 )
16/modulate = Color( 1, 1, 1, 1 )
16/region = Rect2( 0, 384, 128, 128 )
16/tile_mode = 0
16/occluder_offset = Vector2( 0, 0 )
16/navigation_offset = Vector2( 0, 0 )
16/shape_offset = Vector2( 0, 0 )
16/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
16/shape_one_way = false
16/shape_one_way_margin = 0.0
16/shapes = [  ]
16/z_index = 0
17/name = "fixedscenefeatures.png 17"
17/texture = ExtResource( 1 )
17/tex_offset = Vector2( 0, 0 )
17/modulate = Color( 1, 1, 1, 1 )
17/region = Rect2( 128, 384, 128, 128 )
17/tile_mode = 0
17/occluder_offset = Vector2( 0, 0 )
17/navigation_offset = Vector2( 0, 0 )
17/shape_offset = Vector2( 0, 0 )
17/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
17/shape_one_way = false
17/shape_one_way_margin = 0.0
17/shapes = [  ]
17/z_index = 0
18/name = "fixedscenefeatures.png 18"
18/texture = ExtResource( 1 )
18/tex_offset = Vector2( 0, 0 )
18/modulate = Color( 1, 1, 1, 1 )
18/region = Rect2( 256, 384, 128, 128 )
18/tile_mode = 0
18/occluder_offset = Vector2( 0, 0 )
18/navigation_offset = Vector2( 0, 0 )
18/shape_offset = Vector2( 0, 0 )
18/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
18/shape_one_way = false
18/shape_one_way_margin = 0.0
18/shapes = [  ]
18/z_index = 0
19/name = "fixedscenefeatures.png 19"
19/texture = ExtResource( 1 )
19/tex_offset = Vector2( 0, 0 )
19/modulate = Color( 1, 1, 1, 1 )
19/region = Rect2( 0, 512, 128, 128 )
19/tile_mode = 0
19/occluder_offset = Vector2( 0, 0 )
19/navigation_offset = Vector2( 0, 0 )
19/shape_offset = Vector2( 0, 0 )
19/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
19/shape_one_way = false
19/shape_one_way_margin = 0.0
19/shapes = [  ]
19/z_index = 0
20/name = "fixedscenefeatures.png 20"
20/texture = ExtResource( 1 )
20/tex_offset = Vector2( 0, 0 )
20/modulate = Color( 1, 1, 1, 1 )
20/region = Rect2( 128, 512, 128, 128 )
20/tile_mode = 0
20/occluder_offset = Vector2( 0, 0 )
20/navigation_offset = Vector2( 0, 0 )
20/shape_offset = Vector2( 0, 0 )
20/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
20/shape_one_way = false
20/shape_one_way_margin = 0.0
20/shapes = [  ]
20/z_index = 0
21/name = "fixedscenefeatures.png 21"
21/texture = ExtResource( 1 )
21/tex_offset = Vector2( 0, 0 )
21/modulate = Color( 1, 1, 1, 1 )
21/region = Rect2( 1152, 0, 128, 128 )
21/tile_mode = 0
21/occluder_offset = Vector2( 0, 0 )
21/navigation_offset = Vector2( 0, 0 )
21/shape_offset = Vector2( 0, 0 )
21/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
21/shape_one_way = false
21/shape_one_way_margin = 0.0
21/shapes = [  ]
21/z_index = 0
22/name = "fixedscenefeatures.png 22"
22/texture = ExtResource( 1 )
22/tex_offset = Vector2( 0, 0 )
22/modulate = Color( 1, 1, 1, 1 )
22/region = Rect2( 1152, 128, 128, 128 )
22/tile_mode = 0
22/occluder_offset = Vector2( 0, 0 )
22/navigation_offset = Vector2( 0, 0 )
22/shape_offset = Vector2( 0, 0 )
22/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
22/shape_one_way = false
22/shape_one_way_margin = 0.0
22/shapes = [  ]
22/z_index = 0
23/name = "fixedscenefeatures.png 23"
23/texture = ExtResource( 1 )
23/tex_offset = Vector2( 0, 0 )
23/modulate = Color( 1, 1, 1, 1 )
23/region = Rect2( 1152, 256, 128, 128 )
23/tile_mode = 0
23/occluder_offset = Vector2( 0, 0 )
23/navigation_offset = Vector2( 0, 0 )
23/shape_offset = Vector2( 0, 0 )
23/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
23/shape_one_way = false
23/shape_one_way_margin = 0.0
23/shapes = [  ]
23/z_index = 0
24/name = "fixedscenefeatures.png 24"
24/texture = ExtResource( 1 )
24/tex_offset = Vector2( 0, 0 )
24/modulate = Color( 1, 1, 1, 1 )
24/region = Rect2( 1280, 0, 128, 128 )
24/tile_mode = 0
24/occluder_offset = Vector2( 0, 0 )
24/navigation_offset = Vector2( 0, 0 )
24/shape_offset = Vector2( 0, 0 )
24/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
24/shape_one_way = false
24/shape_one_way_margin = 0.0
24/shapes = [  ]
24/z_index = 0
25/name = "fixedscenefeatures.png 25"
25/texture = ExtResource( 1 )
25/tex_offset = Vector2( 0, 0 )
25/modulate = Color( 1, 1, 1, 1 )
25/region = Rect2( 1280, 128, 128, 128 )
25/tile_mode = 0
25/occluder_offset = Vector2( 0, 0 )
25/navigation_offset = Vector2( 0, 0 )
25/shape_offset = Vector2( 0, 0 )
25/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
25/shape_one_way = false
25/shape_one_way_margin = 0.0
25/shapes = [  ]
25/z_index = 0
26/name = "fixedscenefeatures.png 26"
26/texture = ExtResource( 1 )
26/tex_offset = Vector2( 0, 0 )
26/modulate = Color( 1, 1, 1, 1 )
26/region = Rect2( 1280, 256, 128, 128 )
26/tile_mode = 0
26/occluder_offset = Vector2( 0, 0 )
26/navigation_offset = Vector2( 0, 0 )
26/shape_offset = Vector2( 0, 0 )
26/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
26/shape_one_way = false
26/shape_one_way_margin = 0.0
26/shapes = [  ]
26/z_index = 0
27/name = "fixedscenefeatures.png 27"
27/texture = ExtResource( 1 )
27/tex_offset = Vector2( 0, 0 )
27/modulate = Color( 1, 1, 1, 1 )
27/region = Rect2( 1152, 384, 128, 128 )
27/tile_mode = 0
27/occluder_offset = Vector2( 0, 0 )
27/navigation_offset = Vector2( 0, 0 )
27/shape_offset = Vector2( 0, 0 )
27/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
27/shape_one_way = false
27/shape_one_way_margin = 0.0
27/shapes = [  ]
27/z_index = 0
28/name = "fixedscenefeatures.png 28"
28/texture = ExtResource( 1 )
28/tex_offset = Vector2( 0, -128 )
28/modulate = Color( 1, 1, 1, 1 )
28/region = Rect2( 640, 384, 256, 256 )
28/tile_mode = 0
28/occluder_offset = Vector2( 0, 0 )
28/navigation_offset = Vector2( 0, 0 )
28/shape_offset = Vector2( 0, 0 )
28/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
28/shape_one_way = false
28/shape_one_way_margin = 0.0
28/shapes = [  ]
28/z_index = 0
29/name = "fixedscenefeatures.png 29"
29/texture = ExtResource( 1 )
29/tex_offset = Vector2( 0, -128 )
29/modulate = Color( 1, 1, 1, 1 )
29/region = Rect2( 512, 384, 128, 256 )
29/tile_mode = 0
29/occluder_offset = Vector2( 0, 0 )
29/navigation_offset = Vector2( 0, 0 )
29/shape_offset = Vector2( 0, 0 )
29/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
29/shape_one_way = false
29/shape_one_way_margin = 0.0
29/shapes = [  ]
29/z_index = 0
30/name = "furniture.png 30"
30/texture = ExtResource( 9 )
30/tex_offset = Vector2( 0, 11 )
30/modulate = Color( 1, 1, 1, 1 )
30/region = Rect2( 0, 0, 256, 128 )
30/tile_mode = 0
30/occluder_offset = Vector2( 0, 0 )
30/navigation_offset = Vector2( 0, 0 )
30/shape_offset = Vector2( 0, 0 )
30/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
30/shape_one_way = false
30/shape_one_way_margin = 0.0
30/shapes = [  ]
30/z_index = 0
31/name = "furniture.png 31"
31/texture = ExtResource( 9 )
31/tex_offset = Vector2( 0, 11 )
31/modulate = Color( 1, 1, 1, 1 )
31/region = Rect2( 128, 128, 256, 128 )
31/tile_mode = 0
31/occluder_offset = Vector2( 0, 0 )
31/navigation_offset = Vector2( 0, 0 )
31/shape_offset = Vector2( 0, 0 )
31/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
31/shape_one_way = false
31/shape_one_way_margin = 0.0
31/shapes = [  ]
31/z_index = 0
32/name = "fixedscenefeatures.png 32"
32/texture = ExtResource( 1 )
32/tex_offset = Vector2( 0, 15 )
32/modulate = Color( 1, 1, 1, 1 )
32/region = Rect2( 896, 512, 128, 128 )
32/tile_mode = 0
32/occluder_offset = Vector2( 0, 0 )
32/navigation_offset = Vector2( 0, 0 )
32/shape_offset = Vector2( 0, 0 )
32/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
32/shape_one_way = false
32/shape_one_way_margin = 0.0
32/shapes = [  ]
32/z_index = 0
33/name = "fixedscenefeatures.png 33"
33/texture = ExtResource( 1 )
33/tex_offset = Vector2( 0, 15 )
33/modulate = Color( 1, 1, 1, 1 )
33/region = Rect2( 1024, 512, 128, 128 )
33/tile_mode = 0
33/occluder_offset = Vector2( 0, 0 )
33/navigation_offset = Vector2( 0, 0 )
33/shape_offset = Vector2( 0, 0 )
33/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
33/shape_one_way = false
33/shape_one_way_margin = 0.0
33/shapes = [  ]
33/z_index = 0
34/name = "fixedscenefeatures.png 34"
34/texture = ExtResource( 1 )
34/tex_offset = Vector2( 108, 0 )
34/modulate = Color( 1, 1, 1, 1 )
34/region = Rect2( 896, 640, 256, 256 )
34/tile_mode = 0
34/occluder_offset = Vector2( 0, 0 )
34/navigation_offset = Vector2( 0, 0 )
34/shape_offset = Vector2( 0, 0 )
34/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
34/shape_one_way = false
34/shape_one_way_margin = 0.0
34/shapes = [  ]
34/z_index = 0
35/name = "fixedscenefeatures.png 35"
35/texture = ExtResource( 1 )
35/tex_offset = Vector2( 0, 0 )
35/modulate = Color( 1, 1, 1, 1 )
35/region = Rect2( 1152, 512, 512, 384 )
35/tile_mode = 0
35/occluder_offset = Vector2( 0, 0 )
35/navigation_offset = Vector2( 0, 0 )
35/shape_offset = Vector2( 0, 0 )
35/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
35/shape_one_way = false
35/shape_one_way_margin = 0.0
35/shapes = [  ]
35/z_index = 0
36/name = "fixedscenefeatures.png 36"
36/texture = ExtResource( 1 )
36/tex_offset = Vector2( 0, 0 )
36/modulate = Color( 1, 1, 1, 1 )
36/region = Rect2( 640, 640, 128, 256 )
36/tile_mode = 0
36/occluder_offset = Vector2( 0, 0 )
36/navigation_offset = Vector2( 0, 0 )
36/shape_offset = Vector2( 0, 0 )
36/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
36/shape_one_way = false
36/shape_one_way_margin = 0.0
36/shapes = [  ]
36/z_index = 0
37/name = "fixedscenefeatures.png 37"
37/texture = ExtResource( 1 )
37/tex_offset = Vector2( 0, 0 )
37/modulate = Color( 1, 1, 1, 1 )
37/region = Rect2( 384, 640, 128, 256 )
37/tile_mode = 0
37/occluder_offset = Vector2( 0, 0 )
37/navigation_offset = Vector2( 0, 0 )
37/shape_offset = Vector2( 0, 0 )
37/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
37/shape_one_way = false
37/shape_one_way_margin = 0.0
37/shapes = [  ]
37/z_index = 0
38/name = "fixedscenefeatures.png 38"
38/texture = ExtResource( 1 )
38/tex_offset = Vector2( 0, 0 )
38/modulate = Color( 1, 1, 1, 1 )
38/region = Rect2( 512, 640, 128, 256 )
38/tile_mode = 0
38/occluder_offset = Vector2( 0, 0 )
38/navigation_offset = Vector2( 0, 0 )
38/shape_offset = Vector2( 0, 0 )
38/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
38/shape_one_way = false
38/shape_one_way_margin = 0.0
38/shapes = [  ]
38/z_index = 0
39/name = "fixedscenefeatures.png 39"
39/texture = ExtResource( 1 )
39/tex_offset = Vector2( 0, 0 )
39/modulate = Color( 1, 1, 1, 1 )
39/region = Rect2( 1408, 0, 128, 128 )
39/tile_mode = 0
39/occluder_offset = Vector2( 0, 0 )
39/navigation_offset = Vector2( 0, 0 )
39/shape_offset = Vector2( 0, 0 )
39/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
39/shape_one_way = false
39/shape_one_way_margin = 0.0
39/shapes = [  ]
39/z_index = 0
40/name = "fixedscenefeatures.png 40"
40/texture = ExtResource( 1 )
40/tex_offset = Vector2( 0, 0 )
40/modulate = Color( 1, 1, 1, 1 )
40/region = Rect2( 384, 384, 128, 128 )
40/tile_mode = 0
40/occluder_offset = Vector2( 0, 0 )
40/navigation_offset = Vector2( 0, 0 )
40/shape_offset = Vector2( 0, 0 )
40/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
40/shape_one_way = false
40/shape_one_way_margin = 0.0
40/shapes = [  ]
40/z_index = 0
41/name = "fixedscenefeatures.png 41"
41/texture = ExtResource( 1 )
41/tex_offset = Vector2( 0, 0 )
41/modulate = Color( 1, 1, 1, 1 )
41/region = Rect2( 384, 512, 128, 128 )
41/tile_mode = 0
41/occluder_offset = Vector2( 0, 0 )
41/navigation_offset = Vector2( 0, 0 )
41/shape_offset = Vector2( 0, 0 )
41/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
41/shape_one_way = false
41/shape_one_way_margin = 0.0
41/shapes = [  ]
41/z_index = 0
42/name = "fixedscenefeatures.png 42"
42/texture = ExtResource( 1 )
42/tex_offset = Vector2( 0, 0 )
42/modulate = Color( 1, 1, 1, 1 )
42/region = Rect2( 896, 384, 128, 128 )
42/tile_mode = 0
42/occluder_offset = Vector2( 0, 0 )
42/navigation_offset = Vector2( 0, 0 )
42/shape_offset = Vector2( 0, 0 )
42/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
42/shape_one_way = false
42/shape_one_way_margin = 0.0
42/shapes = [  ]
42/z_index = 0
43/name = "fixedscenefeatures.png 43"
43/texture = ExtResource( 1 )
43/tex_offset = Vector2( 0, 0 )
43/modulate = Color( 1, 1, 1, 1 )
43/region = Rect2( 1024, 384, 128, 128 )
43/tile_mode = 0
43/occluder_offset = Vector2( 0, 0 )
43/navigation_offset = Vector2( 0, 0 )
43/shape_offset = Vector2( 0, 0 )
43/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
43/shape_one_way = false
43/shape_one_way_margin = 0.0
43/shapes = [  ]
43/z_index = 0
44/name = "fixedscenefeatures.png 44"
44/texture = ExtResource( 1 )
44/tex_offset = Vector2( 0, 0 )
44/modulate = Color( 1, 1, 1, 1 )
44/region = Rect2( 256, 512, 128, 128 )
44/tile_mode = 0
44/occluder_offset = Vector2( 0, 0 )
44/navigation_offset = Vector2( 0, 0 )
44/shape_offset = Vector2( 0, 0 )
44/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
44/shape_one_way = false
44/shape_one_way_margin = 0.0
44/shapes = [  ]
44/z_index = 0
45/name = "fixedscenefeatures.png 45"
45/texture = ExtResource( 1 )
45/tex_offset = Vector2( 0, 0 )
45/modulate = Color( 1, 1, 1, 1 )
45/region = Rect2( 1408, 128, 128, 128 )
45/tile_mode = 0
45/occluder_offset = Vector2( 0, 0 )
45/navigation_offset = Vector2( 0, 0 )
45/shape_offset = Vector2( 0, 0 )
45/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
45/shape_one_way = false
45/shape_one_way_margin = 0.0
45/shapes = [  ]
45/z_index = 0
46/name = "fixedscenefeatures.png 46"
46/texture = ExtResource( 1 )
46/tex_offset = Vector2( 0, 0 )
46/modulate = Color( 1, 1, 1, 1 )
46/region = Rect2( 1280, 384, 128, 128 )
46/tile_mode = 0
46/occluder_offset = Vector2( 0, 0 )
46/navigation_offset = Vector2( 0, 0 )
46/shape_offset = Vector2( 0, 0 )
46/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
46/shape_one_way = false
46/shape_one_way_margin = 0.0
46/shapes = [  ]
46/z_index = 0
47/name = "fixedscenefeatures.png 47"
47/texture = ExtResource( 1 )
47/tex_offset = Vector2( 0, 0 )
47/modulate = Color( 1, 1, 1, 1 )
47/region = Rect2( 1408, 384, 128, 128 )
47/tile_mode = 0
47/occluder_offset = Vector2( 0, 0 )
47/navigation_offset = Vector2( 0, 0 )
47/shape_offset = Vector2( 0, 0 )
47/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
47/shape_one_way = false
47/shape_one_way_margin = 0.0
47/shapes = [  ]
47/z_index = 0
48/name = "fixedscenefeatures.png 48"
48/texture = ExtResource( 1 )
48/tex_offset = Vector2( 0, 0 )
48/modulate = Color( 1, 1, 1, 1 )
48/region = Rect2( 1408, 256, 128, 128 )
48/tile_mode = 0
48/occluder_offset = Vector2( 0, 0 )
48/navigation_offset = Vector2( 0, 0 )
48/shape_offset = Vector2( 0, 0 )
48/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
48/shape_one_way = false
48/shape_one_way_margin = 0.0
48/shapes = [  ]
48/z_index = 0
49/name = "doorinretreat.png 49"
49/texture = ExtResource( 10 )
49/tex_offset = Vector2( 0, 0 )
49/modulate = Color( 1, 1, 1, 1 )
49/region = Rect2( 0, 0, 384, 384 )
49/tile_mode = 0
49/occluder_offset = Vector2( 0, 0 )
49/navigation_offset = Vector2( 0, 0 )
49/shape_offset = Vector2( 0, 0 )
49/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
49/shape_one_way = false
49/shape_one_way_margin = 0.0
49/shapes = [  ]
49/z_index = 0
50/name = "fixedscenefeatures.png 50"
50/texture = ExtResource( 1 )
50/tex_offset = Vector2( 0, 0 )
50/modulate = Color( 1, 1, 1, 1 )
50/region = Rect2( 0, 640, 128, 128 )
50/tile_mode = 0
50/occluder_offset = Vector2( 0, 0 )
50/navigation_offset = Vector2( 0, 0 )
50/shape_offset = Vector2( 0, 0 )
50/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
50/shape_one_way = false
50/shape_one_way_margin = 0.0
50/shapes = [  ]
50/z_index = 0
51/name = "fixedscenefeatures.png 51"
51/texture = ExtResource( 1 )
51/tex_offset = Vector2( 0, 0 )
51/modulate = Color( 1, 1, 1, 1 )
51/region = Rect2( 0, 768, 128, 128 )
51/tile_mode = 0
51/occluder_offset = Vector2( 0, 0 )
51/navigation_offset = Vector2( 0, 0 )
51/shape_offset = Vector2( 0, 0 )
51/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
51/shape_one_way = false
51/shape_one_way_margin = 0.0
51/shapes = [  ]
51/z_index = 0
52/name = "fixedscenefeatures.png 52"
52/texture = ExtResource( 1 )
52/tex_offset = Vector2( 0, 0 )
52/modulate = Color( 1, 1, 1, 1 )
52/region = Rect2( 128, 640, 128, 128 )
52/tile_mode = 0
52/occluder_offset = Vector2( 0, 0 )
52/navigation_offset = Vector2( 0, 0 )
52/shape_offset = Vector2( 0, 0 )
52/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
52/shape_one_way = false
52/shape_one_way_margin = 0.0
52/shapes = [  ]
52/z_index = 0
53/name = "fixedscenefeatures.png 53"
53/texture = ExtResource( 1 )
53/tex_offset = Vector2( 0, 0 )
53/modulate = Color( 1, 1, 1, 1 )
53/region = Rect2( 256, 640, 128, 128 )
53/tile_mode = 0
53/occluder_offset = Vector2( 0, 0 )
53/navigation_offset = Vector2( 0, 0 )
53/shape_offset = Vector2( 0, 0 )
53/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
53/shape_one_way = false
53/shape_one_way_margin = 0.0
53/shapes = [  ]
53/z_index = 0
54/name = "fixedscenefeatures.png 54"
54/texture = ExtResource( 1 )
54/tex_offset = Vector2( 0, 0 )
54/modulate = Color( 1, 1, 1, 1 )
54/region = Rect2( 0, 896, 128, 128 )
54/tile_mode = 0
54/occluder_offset = Vector2( 0, 0 )
54/navigation_offset = Vector2( 0, 0 )
54/shape_offset = Vector2( 0, 0 )
54/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
54/shape_one_way = false
54/shape_one_way_margin = 0.0
54/shapes = [  ]
54/z_index = 0
55/name = "fixedscenefeatures.png 55"
55/texture = ExtResource( 1 )
55/tex_offset = Vector2( 0, 0 )
55/modulate = Color( 1, 1, 1, 1 )
55/region = Rect2( 1536, 0, 128, 128 )
55/tile_mode = 0
55/occluder_offset = Vector2( 0, 0 )
55/navigation_offset = Vector2( 0, 0 )
55/shape_offset = Vector2( 0, 0 )
55/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
55/shape_one_way = false
55/shape_one_way_margin = 0.0
55/shapes = [  ]
55/z_index = 0
56/name = "fixedscenefeatures.png 56"
56/texture = ExtResource( 1 )
56/tex_offset = Vector2( 0, 0 )
56/modulate = Color( 1, 1, 1, 1 )
56/region = Rect2( 128, 896, 128, 128 )
56/tile_mode = 0
56/occluder_offset = Vector2( 0, 0 )
56/navigation_offset = Vector2( 0, 0 )
56/shape_offset = Vector2( 0, 0 )
56/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
56/shape_one_way = false
56/shape_one_way_margin = 0.0
56/shapes = [  ]
56/z_index = 0
57/name = "pathway.png 57"
57/texture = ExtResource( 11 )
57/tex_offset = Vector2( -128, 20 )
57/modulate = Color( 1, 1, 1, 1 )
57/region = Rect2( 0, 0, 384, 128 )
57/tile_mode = 0
57/occluder_offset = Vector2( 0, 0 )
57/navigation_offset = Vector2( 0, 0 )
57/shape_offset = Vector2( 0, 0 )
57/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
57/shape_one_way = false
57/shape_one_way_margin = 0.0
57/shapes = [  ]
57/z_index = 0
58/name = "pathway.png 58"
58/texture = ExtResource( 11 )
58/tex_offset = Vector2( -128, -108 )
58/modulate = Color( 1, 1, 1, 1 )
58/region = Rect2( 0, 128, 384, 256 )
58/tile_mode = 0
58/occluder_offset = Vector2( 0, 0 )
58/navigation_offset = Vector2( 0, 0 )
58/shape_offset = Vector2( 0, 0 )
58/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
58/shape_one_way = false
58/shape_one_way_margin = 0.0
58/shapes = [  ]
58/z_index = 0
59/name = "pathway.png 59"
59/texture = ExtResource( 11 )
59/tex_offset = Vector2( -128, -120 )
59/modulate = Color( 1, 1, 1, 1 )
59/region = Rect2( 0, 384, 384, 256 )
59/tile_mode = 0
59/occluder_offset = Vector2( 0, 0 )
59/navigation_offset = Vector2( 0, 0 )
59/shape_offset = Vector2( 0, 0 )
59/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
59/shape_one_way = false
59/shape_one_way_margin = 0.0
59/shapes = [  ]
59/z_index = 0
60/name = "pathway.png 60"
60/texture = ExtResource( 11 )
60/tex_offset = Vector2( -128, 0 )
60/modulate = Color( 1, 1, 1, 1 )
60/region = Rect2( 0, 640, 384, 128 )
60/tile_mode = 0
60/occluder_offset = Vector2( 0, 0 )
60/navigation_offset = Vector2( 0, 0 )
60/shape_offset = Vector2( 0, 0 )
60/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
60/shape_one_way = false
60/shape_one_way_margin = 0.0
60/shapes = [  ]
60/z_index = 0
61/name = "pathway.png 61"
61/texture = ExtResource( 11 )
61/tex_offset = Vector2( -128, 0 )
61/modulate = Color( 1, 1, 1, 1 )
61/region = Rect2( 0, 768, 384, 128 )
61/tile_mode = 0
61/occluder_offset = Vector2( 0, 0 )
61/navigation_offset = Vector2( 0, 0 )
61/shape_offset = Vector2( 0, 0 )
61/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
61/shape_one_way = false
61/shape_one_way_margin = 0.0
61/shapes = [  ]
61/z_index = 0
62/name = "fixedscenefeatures.png 62"
62/texture = ExtResource( 1 )
62/tex_offset = Vector2( 0, 0 )
62/modulate = Color( 1, 1, 1, 1 )
62/region = Rect2( 128, 768, 128, 128 )
62/tile_mode = 0
62/occluder_offset = Vector2( 0, 0 )
62/navigation_offset = Vector2( 0, 0 )
62/shape_offset = Vector2( 0, 0 )
62/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
62/shape_one_way = false
62/shape_one_way_margin = 0.0
62/shapes = [  ]
62/z_index = 0
63/name = "fixedscenefeatures.png 63"
63/texture = ExtResource( 1 )
63/tex_offset = Vector2( 0, 0 )
63/modulate = Color( 1, 1, 1, 1 )
63/region = Rect2( 1536, 128, 128, 128 )
63/tile_mode = 0
63/occluder_offset = Vector2( 0, 0 )
63/navigation_offset = Vector2( 0, 0 )
63/shape_offset = Vector2( 0, 0 )
63/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
63/shape_one_way = false
63/shape_one_way_margin = 0.0
63/shapes = [  ]
63/z_index = 0
64/name = "fixedscenefeatures.png 64"
64/texture = ExtResource( 1 )
64/tex_offset = Vector2( 0, 0 )
64/modulate = Color( 1, 1, 1, 1 )
64/region = Rect2( 1536, 256, 128, 128 )
64/tile_mode = 0
64/occluder_offset = Vector2( 0, 0 )
64/navigation_offset = Vector2( 0, 0 )
64/shape_offset = Vector2( 0, 0 )
64/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
64/shape_one_way = false
64/shape_one_way_margin = 0.0
64/shapes = [  ]
64/z_index = 0
65/name = "Doors.png 65"
65/texture = ExtResource( 12 )
65/tex_offset = Vector2( 0, 13 )
65/modulate = Color( 1, 1, 1, 1 )
65/region = Rect2( 0, 32, 128, 256 )
65/tile_mode = 0
65/occluder_offset = Vector2( 0, 0 )
65/navigation_offset = Vector2( 0, 0 )
65/shape_offset = Vector2( 0, 0 )
65/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
65/shape_one_way = false
65/shape_one_way_margin = 0.0
65/shapes = [  ]
65/z_index = -1
66/name = "Doors.png 66"
66/texture = ExtResource( 12 )
66/tex_offset = Vector2( 0, 13 )
66/modulate = Color( 1, 1, 1, 1 )
66/region = Rect2( 128, 32, 128, 256 )
66/tile_mode = 0
66/occluder_offset = Vector2( 0, 0 )
66/navigation_offset = Vector2( 0, 0 )
66/shape_offset = Vector2( 0, 0 )
66/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
66/shape_one_way = false
66/shape_one_way_margin = 0.0
66/shapes = [  ]
66/z_index = -1
67/name = "fixedscenefeatures.png 67"
67/texture = ExtResource( 1 )
67/tex_offset = Vector2( 0, 0 )
67/modulate = Color( 1, 1, 1, 1 )
67/region = Rect2( 1280, 896, 128, 128 )
67/tile_mode = 0
67/occluder_offset = Vector2( 0, 0 )
67/navigation_offset = Vector2( 0, 0 )
67/shape_offset = Vector2( 0, 0 )
67/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
67/shape_one_way = false
67/shape_one_way_margin = 0.0
67/shapes = [  ]
67/z_index = 0
68/name = "fixedscenefeatures.png 68"
68/texture = ExtResource( 1 )
68/tex_offset = Vector2( 0, 0 )
68/modulate = Color( 1, 1, 1, 1 )
68/region = Rect2( 1408, 896, 128, 128 )
68/tile_mode = 0
68/occluder_offset = Vector2( 0, 0 )
68/navigation_offset = Vector2( 0, 0 )
68/shape_offset = Vector2( 0, 0 )
68/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
68/shape_one_way = false
68/shape_one_way_margin = 0.0
68/shapes = [  ]
68/z_index = 0
69/name = "fixedscenefeatures.png 69"
69/texture = ExtResource( 1 )
69/tex_offset = Vector2( 0, 0 )
69/modulate = Color( 1, 1, 1, 1 )
69/region = Rect2( 1536, 896, 128, 128 )
69/tile_mode = 0
69/occluder_offset = Vector2( 0, 0 )
69/navigation_offset = Vector2( 0, 0 )
69/shape_offset = Vector2( 0, 0 )
69/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
69/shape_one_way = false
69/shape_one_way_margin = 0.0
69/shapes = [  ]
69/z_index = 0
70/name = "fixedscenefeatures.png 70"
70/texture = ExtResource( 1 )
70/tex_offset = Vector2( 0, 0 )
70/modulate = Color( 1, 1, 1, 1 )
70/region = Rect2( 256, 896, 128, 128 )
70/tile_mode = 0
70/occluder_offset = Vector2( 0, 0 )
70/navigation_offset = Vector2( 0, 0 )
70/shape_offset = Vector2( 0, 0 )
70/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
70/shape_one_way = false
70/shape_one_way_margin = 0.0
70/shapes = [  ]
70/z_index = 0
71/name = "fixedscenefeatures.png 71"
71/texture = ExtResource( 1 )
71/tex_offset = Vector2( 0, 0 )
71/modulate = Color( 1, 1, 1, 1 )
71/region = Rect2( 384, 896, 128, 128 )
71/tile_mode = 0
71/occluder_offset = Vector2( 0, 0 )
71/navigation_offset = Vector2( 0, 0 )
71/shape_offset = Vector2( 0, 0 )
71/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
71/shape_one_way = false
71/shape_one_way_margin = 0.0
71/shapes = [  ]
71/z_index = 0
72/name = "villageobjects.png 72"
72/texture = ExtResource( 13 )
72/tex_offset = Vector2( -128, 64 )
72/modulate = Color( 1, 1, 1, 1 )
72/region = Rect2( 0, 0, 384, 384 )
72/tile_mode = 0
72/occluder_offset = Vector2( 0, 0 )
72/navigation_offset = Vector2( 0, 0 )
72/shape_offset = Vector2( 0, 0 )
72/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
72/shape_one_way = false
72/shape_one_way_margin = 0.0
72/shapes = [  ]
72/z_index = -1
73/name = "pathway.png 73"
73/texture = ExtResource( 11 )
73/tex_offset = Vector2( -128, 0 )
73/modulate = Color( 1, 1, 1, 1 )
73/region = Rect2( 0, 896, 384, 128 )
73/tile_mode = 0
73/occluder_offset = Vector2( 0, 0 )
73/navigation_offset = Vector2( 0, 0 )
73/shape_offset = Vector2( 0, 0 )
73/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
73/shape_one_way = false
73/shape_one_way_margin = 0.0
73/shapes = [  ]
73/z_index = 0
74/name = "fixedscenefeatures.png 74"
74/texture = ExtResource( 1 )
74/tex_offset = Vector2( 0, 0 )
74/modulate = Color( 1, 1, 1, 1 )
74/region = Rect2( 1536, 384, 128, 128 )
74/tile_mode = 0
74/occluder_offset = Vector2( 0, 0 )
74/navigation_offset = Vector2( 0, 0 )
74/shape_offset = Vector2( 0, 0 )
74/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
74/shape_one_way = false
74/shape_one_way_margin = 0.0
74/shapes = [  ]
74/z_index = 0
75/name = "fixedscenefeatures.png 75"
75/texture = ExtResource( 1 )
75/tex_offset = Vector2( 0, 0 )
75/modulate = Color( 1, 1, 1, 1 )
75/region = Rect2( 1152, 896, 128, 128 )
75/tile_mode = 0
75/occluder_offset = Vector2( 0, 0 )
75/navigation_offset = Vector2( 0, 0 )
75/shape_offset = Vector2( 0, 0 )
75/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
75/shape_one_way = false
75/shape_one_way_margin = 0.0
75/shapes = [  ]
75/z_index = 0
76/name = "fixedscenefeatures.png 76"
76/texture = ExtResource( 1 )
76/tex_offset = Vector2( 0, 0 )
76/modulate = Color( 1, 1, 1, 1 )
76/region = Rect2( 256, 768, 128, 128 )
76/tile_mode = 0
76/occluder_offset = Vector2( 0, 0 )
76/navigation_offset = Vector2( 0, 0 )
76/shape_offset = Vector2( 0, 0 )
76/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
76/shape_one_way = false
76/shape_one_way_margin = 0.0
76/shapes = [  ]
76/z_index = 0
77/name = "fixedscenefeatures.png 77"
77/texture = ExtResource( 1 )
77/tex_offset = Vector2( 0, 0 )
77/modulate = Color( 1, 1, 1, 1 )
77/region = Rect2( 1664, 0, 128, 128 )
77/tile_mode = 0
77/occluder_offset = Vector2( 0, 0 )
77/navigation_offset = Vector2( 0, 0 )
77/shape_offset = Vector2( 0, 0 )
77/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
77/shape_one_way = false
77/shape_one_way_margin = 0.0
77/shapes = [  ]
77/z_index = 0
79/name = "fixedscenefeatures.png 79"
79/texture = ExtResource( 1 )
79/tex_offset = Vector2( 0, 0 )
79/modulate = Color( 1, 1, 1, 1 )
79/region = Rect2( 1024, 896, 128, 128 )
79/tile_mode = 0
79/occluder_offset = Vector2( 0, 0 )
79/navigation_offset = Vector2( 0, 0 )
79/shape_offset = Vector2( 0, 0 )
79/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
79/shape_one_way = false
79/shape_one_way_margin = 0.0
79/shapes = [  ]
79/z_index = 0
80/name = "villageprops.png 80"
80/texture = ExtResource( 15 )
80/tex_offset = Vector2( 0, 0 )
80/modulate = Color( 1, 1, 1, 1 )
80/region = Rect2( 256, 0, 128, 128 )
80/tile_mode = 0
80/occluder_offset = Vector2( 0, 0 )
80/navigation_offset = Vector2( 0, 0 )
80/shape_offset = Vector2( 0, 0 )
80/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
80/shape_one_way = false
80/shape_one_way_margin = 0.0
80/shapes = [  ]
80/z_index = 0
81/name = "villageprops.png 81"
81/texture = ExtResource( 15 )
81/tex_offset = Vector2( 0, 0 )
81/modulate = Color( 1, 1, 1, 1 )
81/region = Rect2( 384, 0, 128, 128 )
81/tile_mode = 0
81/occluder_offset = Vector2( 0, 0 )
81/navigation_offset = Vector2( 0, 0 )
81/shape_offset = Vector2( 0, 0 )
81/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
81/shape_one_way = false
81/shape_one_way_margin = 0.0
81/shapes = [  ]
81/z_index = 0
82/name = "villageprops.png 82"
82/texture = ExtResource( 15 )
82/tex_offset = Vector2( 0, 0 )
82/modulate = Color( 1, 1, 1, 1 )
82/region = Rect2( 512, 0, 128, 128 )
82/tile_mode = 0
82/occluder_offset = Vector2( 0, 0 )
82/navigation_offset = Vector2( 0, 0 )
82/shape_offset = Vector2( 0, 0 )
82/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
82/shape_one_way = false
82/shape_one_way_margin = 0.0
82/shapes = [  ]
82/z_index = 0
83/name = "villageprops.png 83"
83/texture = ExtResource( 15 )
83/tex_offset = Vector2( 30, 5 )
83/modulate = Color( 1, 1, 1, 1 )
83/region = Rect2( 256, 128, 128, 128 )
83/tile_mode = 0
83/occluder_offset = Vector2( 0, 0 )
83/navigation_offset = Vector2( 0, 0 )
83/shape_offset = Vector2( 0, 0 )
83/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
83/shape_one_way = false
83/shape_one_way_margin = 0.0
83/shapes = [  ]
83/z_index = 1
84/name = "villageprops.png 84"
84/texture = ExtResource( 15 )
84/tex_offset = Vector2( 0, 0 )
84/modulate = Color( 1, 1, 1, 1 )
84/region = Rect2( 384, 128, 128, 128 )
84/tile_mode = 0
84/occluder_offset = Vector2( 0, 0 )
84/navigation_offset = Vector2( 0, 0 )
84/shape_offset = Vector2( 0, 0 )
84/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
84/shape_one_way = false
84/shape_one_way_margin = 0.0
84/shapes = [  ]
84/z_index = 0
85/name = "villageprops.png 85"
85/texture = ExtResource( 15 )
85/tex_offset = Vector2( 0, 0 )
85/modulate = Color( 1, 1, 1, 1 )
85/region = Rect2( 512, 128, 128, 128 )
85/tile_mode = 0
85/occluder_offset = Vector2( 0, 0 )
85/navigation_offset = Vector2( 0, 0 )
85/shape_offset = Vector2( 0, 0 )
85/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
85/shape_one_way = false
85/shape_one_way_margin = 0.0
85/shapes = [  ]
85/z_index = 0
86/name = "villageprops.png 86"
86/texture = ExtResource( 15 )
86/tex_offset = Vector2( 70, 0 )
86/modulate = Color( 1, 1, 1, 1 )
86/region = Rect2( 384, 256, 128, 128 )
86/tile_mode = 0
86/occluder_offset = Vector2( 0, 0 )
86/navigation_offset = Vector2( 0, 0 )
86/shape_offset = Vector2( 0, 0 )
86/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
86/shape_one_way = false
86/shape_one_way_margin = 0.0
86/shapes = [  ]
86/z_index = 0
87/name = "villageprops.png 87"
87/texture = ExtResource( 15 )
87/tex_offset = Vector2( 0, 0 )
87/modulate = Color( 1, 1, 1, 1 )
87/region = Rect2( 512, 256, 128, 128 )
87/tile_mode = 0
87/occluder_offset = Vector2( 0, 0 )
87/navigation_offset = Vector2( 0, 0 )
87/shape_offset = Vector2( 0, 0 )
87/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
87/shape_one_way = false
87/shape_one_way_margin = 0.0
87/shapes = [  ]
87/z_index = 0
88/name = "villageprops.png 88"
88/texture = ExtResource( 15 )
88/tex_offset = Vector2( 0, 0 )
88/modulate = Color( 1, 1, 1, 1 )
88/region = Rect2( 256, 384, 128, 128 )
88/tile_mode = 0
88/occluder_offset = Vector2( 0, 0 )
88/navigation_offset = Vector2( 0, 0 )
88/shape_offset = Vector2( 0, 0 )
88/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
88/shape_one_way = false
88/shape_one_way_margin = 0.0
88/shapes = [  ]
88/z_index = 0
89/name = "villageprops.png 89"
89/texture = ExtResource( 15 )
89/tex_offset = Vector2( 0, 0 )
89/modulate = Color( 1, 1, 1, 1 )
89/region = Rect2( 384, 384, 128, 128 )
89/tile_mode = 0
89/occluder_offset = Vector2( 0, 0 )
89/navigation_offset = Vector2( 0, 0 )
89/shape_offset = Vector2( 0, 0 )
89/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
89/shape_one_way = false
89/shape_one_way_margin = 0.0
89/shapes = [  ]
89/z_index = 0
90/name = "villageprops.png 90"
90/texture = ExtResource( 15 )
90/tex_offset = Vector2( -64, 0 )
90/modulate = Color( 1, 1, 1, 1 )
90/region = Rect2( 512, 384, 128, 128 )
90/tile_mode = 0
90/occluder_offset = Vector2( 0, 0 )
90/navigation_offset = Vector2( 0, 0 )
90/shape_offset = Vector2( 0, 0 )
90/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
90/shape_one_way = false
90/shape_one_way_margin = 0.0
90/shapes = [  ]
90/z_index = 0
91/name = "villageprops.png 91"
91/texture = ExtResource( 15 )
91/tex_offset = Vector2( 0, 16 )
91/modulate = Color( 1, 1, 1, 1 )
91/region = Rect2( 0, 512, 128, 128 )
91/tile_mode = 0
91/occluder_offset = Vector2( 0, 0 )
91/navigation_offset = Vector2( 0, 0 )
91/shape_offset = Vector2( 0, 0 )
91/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
91/shape_one_way = false
91/shape_one_way_margin = 0.0
91/shapes = [  ]
91/z_index = 0
92/name = "villageprops.png 92"
92/texture = ExtResource( 15 )
92/tex_offset = Vector2( 0, 16 )
92/modulate = Color( 1, 1, 1, 1 )
92/region = Rect2( 0, 384, 128, 128 )
92/tile_mode = 0
92/occluder_offset = Vector2( 0, 0 )
92/navigation_offset = Vector2( 0, 0 )
92/shape_offset = Vector2( 0, 0 )
92/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
92/shape_one_way = false
92/shape_one_way_margin = 0.0
92/shapes = [  ]
92/z_index = 0
93/name = "villageprops.png 93"
93/texture = ExtResource( 15 )
93/tex_offset = Vector2( 0, 7 )
93/modulate = Color( 1, 1, 1, 1 )
93/region = Rect2( 384, 512, 128, 128 )
93/tile_mode = 0
93/occluder_offset = Vector2( 0, 0 )
93/navigation_offset = Vector2( 0, 0 )
93/shape_offset = Vector2( 0, 0 )
93/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
93/shape_one_way = false
93/shape_one_way_margin = 0.0
93/shapes = [  ]
93/z_index = 0
94/name = "villageprops.png 94"
94/texture = ExtResource( 15 )
94/tex_offset = Vector2( 0, 12 )
94/modulate = Color( 1, 1, 1, 1 )
94/region = Rect2( 128, 384, 128, 128 )
94/tile_mode = 0
94/occluder_offset = Vector2( 0, 0 )
94/navigation_offset = Vector2( 0, 0 )
94/shape_offset = Vector2( 0, 0 )
94/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
94/shape_one_way = false
94/shape_one_way_margin = 0.0
94/shapes = [  ]
94/z_index = 0
95/name = "villageprops.png 95"
95/texture = ExtResource( 15 )
95/tex_offset = Vector2( 0, 10 )
95/modulate = Color( 1, 1, 1, 1 )
95/region = Rect2( 128, 512, 256, 128 )
95/tile_mode = 0
95/occluder_offset = Vector2( 0, 0 )
95/navigation_offset = Vector2( 0, 0 )
95/shape_offset = Vector2( 0, 0 )
95/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
95/shape_one_way = false
95/shape_one_way_margin = 0.0
95/shapes = [  ]
95/z_index = 0
96/name = "villageprops.png 96"
96/texture = ExtResource( 15 )
96/tex_offset = Vector2( 0, 0 )
96/modulate = Color( 1, 1, 1, 1 )
96/region = Rect2( 512, 512, 128, 128 )
96/tile_mode = 0
96/occluder_offset = Vector2( 0, 0 )
96/navigation_offset = Vector2( 0, 0 )
96/shape_offset = Vector2( 0, 0 )
96/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
96/shape_one_way = false
96/shape_one_way_margin = 0.0
96/shapes = [  ]
96/z_index = 0
97/name = "villageprops.png 97"
97/texture = ExtResource( 15 )
97/tex_offset = Vector2( 0, -14 )
97/modulate = Color( 1, 1, 1, 1 )
97/region = Rect2( 640, 0, 128, 128 )
97/tile_mode = 0
97/occluder_offset = Vector2( 0, 0 )
97/navigation_offset = Vector2( 0, 0 )
97/shape_offset = Vector2( 0, 0 )
97/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
97/shape_one_way = false
97/shape_one_way_margin = 0.0
97/shapes = [  ]
97/z_index = 0
98/name = "villageprops.png 98"
98/texture = ExtResource( 15 )
98/tex_offset = Vector2( 0, -14 )
98/modulate = Color( 1, 1, 1, 1 )
98/region = Rect2( 640, 128, 128, 128 )
98/tile_mode = 0
98/occluder_offset = Vector2( 0, 0 )
98/navigation_offset = Vector2( 0, 0 )
98/shape_offset = Vector2( 0, 0 )
98/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
98/shape_one_way = false
98/shape_one_way_margin = 0.0
98/shapes = [  ]
98/z_index = 0
99/name = "villageprops.png 99"
99/texture = ExtResource( 15 )
99/tex_offset = Vector2( 0, -14 )
99/modulate = Color( 1, 1, 1, 1 )
99/region = Rect2( 640, 256, 128, 128 )
99/tile_mode = 0
99/occluder_offset = Vector2( 0, 0 )
99/navigation_offset = Vector2( 0, 0 )
99/shape_offset = Vector2( 0, 0 )
99/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
99/shape_one_way = false
99/shape_one_way_margin = 0.0
99/shapes = [  ]
99/z_index = 0
100/name = "villageprops.png 100"
100/texture = ExtResource( 15 )
100/tex_offset = Vector2( 0, 15 )
100/modulate = Color( 1, 1, 1, 1 )
100/region = Rect2( 0, 0, 256, 256 )
100/tile_mode = 0
100/occluder_offset = Vector2( 0, 0 )
100/navigation_offset = Vector2( 0, 0 )
100/shape_offset = Vector2( 0, 0 )
100/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
100/shape_one_way = false
100/shape_one_way_margin = 0.0
100/shapes = [  ]
100/z_index = 0
101/name = "villageprops.png 101"
101/texture = ExtResource( 15 )
101/tex_offset = Vector2( -19, -50 )
101/modulate = Color( 1, 1, 1, 1 )
101/region = Rect2( 640, 384, 64, 128 )
101/tile_mode = 0
101/occluder_offset = Vector2( 0, 0 )
101/navigation_offset = Vector2( 0, 0 )
101/shape_offset = Vector2( 0, 0 )
101/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
101/shape_one_way = false
101/shape_one_way_margin = 0.0
101/shapes = [  ]
101/z_index = 1
102/name = "villageprops.png 102"
102/texture = ExtResource( 15 )
102/tex_offset = Vector2( 83, -50 )
102/modulate = Color( 1, 1, 1, 1 )
102/region = Rect2( 704, 384, 64, 128 )
102/tile_mode = 0
102/occluder_offset = Vector2( 0, 0 )
102/navigation_offset = Vector2( 0, 0 )
102/shape_offset = Vector2( 0, 0 )
102/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
102/shape_one_way = false
102/shape_one_way_margin = 0.0
102/shapes = [  ]
102/z_index = 1
103/name = "specialtree.png 103"
103/texture = ExtResource( 16 )
103/tex_offset = Vector2( 0, 45 )
103/modulate = Color( 1, 1, 1, 1 )
103/region = Rect2( 0, 0, 704, 896 )
103/tile_mode = 0
103/occluder_offset = Vector2( 0, 0 )
103/navigation_offset = Vector2( 0, 0 )
103/shape_offset = Vector2( 0, 0 )
103/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
103/shape_one_way = false
103/shape_one_way_margin = 0.0
103/shapes = [  ]
103/z_index = 1
104/name = "specialtreeroots.png 104"
104/texture = ExtResource( 17 )
104/tex_offset = Vector2( -40, 88 )
104/modulate = Color( 1, 1, 1, 1 )
104/region = Rect2( 0, 0, 576, 256 )
104/tile_mode = 0
104/occluder_offset = Vector2( 0, 0 )
104/navigation_offset = Vector2( 0, 0 )
104/shape_offset = Vector2( 0, 0 )
104/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
104/shape_one_way = false
104/shape_one_way_margin = 0.0
104/shapes = [  ]
104/z_index = 10
105/name = "stone staircase village.png 105"
105/texture = ExtResource( 18 )
105/tex_offset = Vector2( -21, -71 )
105/modulate = Color( 1, 1, 1, 1 )
105/region = Rect2( 0, 0, 320, 384 )
105/tile_mode = 0
105/occluder_offset = Vector2( 0, 0 )
105/navigation_offset = Vector2( 0, 0 )
105/shape_offset = Vector2( 0, 0 )
105/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
105/shape_one_way = false
105/shape_one_way_margin = 0.0
105/shapes = [  ]
105/z_index = -6
106/name = "stone staircase village.png 106"
106/texture = ExtResource( 18 )
106/tex_offset = Vector2( -72, -66 )
106/modulate = Color( 1, 1, 1, 1 )
106/region = Rect2( 0, 384, 320, 384 )
106/tile_mode = 0
106/occluder_offset = Vector2( 0, 0 )
106/navigation_offset = Vector2( 0, 0 )
106/shape_offset = Vector2( 0, 0 )
106/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
106/shape_one_way = false
106/shape_one_way_margin = 0.0
106/shapes = [  ]
106/z_index = -6
107/name = "fixedscenefeatures.png 107"
107/texture = ExtResource( 1 )
107/tex_offset = Vector2( 0, 0 )
107/modulate = Color( 1, 1, 1, 1 )
107/region = Rect2( 896, 896, 128, 128 )
107/tile_mode = 0
107/occluder_offset = Vector2( 0, 0 )
107/navigation_offset = Vector2( 0, 0 )
107/shape_offset = Vector2( 0, 0 )
107/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
107/shape_one_way = false
107/shape_one_way_margin = 0.0
107/shapes = [  ]
107/z_index = 0
108/name = "villageprops.png 108"
108/texture = ExtResource( 15 )
108/tex_offset = Vector2( 0, 0 )
108/modulate = Color( 1, 1, 1, 1 )
108/region = Rect2( 768, 0, 640, 256 )
108/tile_mode = 0
108/occluder_offset = Vector2( 0, 0 )
108/navigation_offset = Vector2( 0, 0 )
108/shape_offset = Vector2( 0, 0 )
108/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
108/shape_one_way = false
108/shape_one_way_margin = 0.0
108/shapes = [  ]
108/z_index = -1
109/name = "villageprops.png 109"
109/texture = ExtResource( 15 )
109/tex_offset = Vector2( -12, -21 )
109/modulate = Color( 1, 1, 1, 1 )
109/region = Rect2( 1408, 0, 320, 256 )
109/tile_mode = 0
109/occluder_offset = Vector2( 0, 0 )
109/navigation_offset = Vector2( 0, 0 )
109/shape_offset = Vector2( 0, 0 )
109/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
109/shape_one_way = false
109/shape_one_way_margin = 0.0
109/shapes = [  ]
109/z_index = 0
110/name = "villageprops.png 110"
110/texture = ExtResource( 15 )
110/tex_offset = Vector2( 0, -10 )
110/modulate = Color( 1, 1, 1, 1 )
110/region = Rect2( 640, 512, 256, 128 )
110/tile_mode = 0
110/occluder_offset = Vector2( 0, 0 )
110/navigation_offset = Vector2( 0, 0 )
110/shape_offset = Vector2( 0, 0 )
110/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
110/shape_one_way = false
110/shape_one_way_margin = 0.0
110/shapes = [  ]
110/z_index = 0
111/name = "villageprops.png 111"
111/texture = ExtResource( 15 )
111/tex_offset = Vector2( 0, 0 )
111/modulate = Color( 1, 1, 1, 1 )
111/region = Rect2( 768, 384, 128, 128 )
111/tile_mode = 0
111/occluder_offset = Vector2( 0, 0 )
111/navigation_offset = Vector2( 0, 0 )
111/shape_offset = Vector2( 0, 0 )
111/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
111/shape_one_way = false
111/shape_one_way_margin = 0.0
111/shapes = [  ]
111/z_index = 0
112/name = "fixedscenefeatures.png 112"
112/texture = ExtResource( 1 )
112/tex_offset = Vector2( 0, 0 )
112/modulate = Color( 1, 1, 1, 1 )
112/region = Rect2( 512, 896, 128, 128 )
112/tile_mode = 0
112/occluder_offset = Vector2( 0, 0 )
112/navigation_offset = Vector2( 0, 0 )
112/shape_offset = Vector2( 0, 0 )
112/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
112/shape_one_way = false
112/shape_one_way_margin = 0.0
112/shapes = [  ]
112/z_index = 0
113/name = "villageprops.png 113"
113/texture = ExtResource( 15 )
113/tex_offset = Vector2( 0, 64 )
113/modulate = Color( 1, 1, 1, 1 )
113/region = Rect2( 768, 320, 128, 64 )
113/tile_mode = 0
113/occluder_offset = Vector2( 0, 0 )
113/navigation_offset = Vector2( 0, 0 )
113/shape_offset = Vector2( 0, 0 )
113/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
113/shape_one_way = false
113/shape_one_way_margin = 0.0
113/shapes = [  ]
113/z_index = 1
114/name = "villageprops.png 114"
114/texture = ExtResource( 15 )
114/tex_offset = Vector2( 0, 64 )
114/modulate = Color( 1, 1, 1, 1 )
114/region = Rect2( 896, 320, 128, 64 )
114/tile_mode = 0
114/occluder_offset = Vector2( 0, 0 )
114/navigation_offset = Vector2( 0, 0 )
114/shape_offset = Vector2( 0, 0 )
114/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
114/shape_one_way = false
114/shape_one_way_margin = 0.0
114/shapes = [  ]
114/z_index = 1
115/name = "villageprops.png 115"
115/texture = ExtResource( 15 )
115/tex_offset = Vector2( 0, 64 )
115/modulate = Color( 1, 1, 1, 1 )
115/region = Rect2( 768, 256, 128, 64 )
115/tile_mode = 0
115/occluder_offset = Vector2( 0, 0 )
115/navigation_offset = Vector2( 0, 0 )
115/shape_offset = Vector2( 0, 0 )
115/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
115/shape_one_way = false
115/shape_one_way_margin = 0.0
115/shapes = [  ]
115/z_index = 1
116/name = "villageprops.png 116"
116/texture = ExtResource( 15 )
116/tex_offset = Vector2( 0, 64 )
116/modulate = Color( 1, 1, 1, 1 )
116/region = Rect2( 896, 256, 128, 64 )
116/tile_mode = 0
116/occluder_offset = Vector2( 0, 0 )
116/navigation_offset = Vector2( 0, 0 )
116/shape_offset = Vector2( 0, 0 )
116/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
116/shape_one_way = false
116/shape_one_way_margin = 0.0
116/shapes = [  ]
116/z_index = 1
117/name = "fixedscenefeatures.png 117"
117/texture = ExtResource( 1 )
117/tex_offset = Vector2( 0, 0 )
117/modulate = Color( 1, 1, 1, 1 )
117/region = Rect2( 1664, 128, 128, 128 )
117/tile_mode = 0
117/occluder_offset = Vector2( 0, 0 )
117/navigation_offset = Vector2( 0, 0 )
117/shape_offset = Vector2( 0, 0 )
117/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
117/shape_one_way = false
117/shape_one_way_margin = 0.0
117/shapes = [  ]
117/z_index = 0
118/name = "fixedscenefeatures.png 118"
118/texture = ExtResource( 1 )
118/tex_offset = Vector2( 0, 0 )
118/modulate = Color( 1, 1, 1, 1 )
118/region = Rect2( 1664, 896, 128, 128 )
118/tile_mode = 0
118/occluder_offset = Vector2( 0, 0 )
118/navigation_offset = Vector2( 0, 0 )
118/shape_offset = Vector2( 0, 0 )
118/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
118/shape_one_way = false
118/shape_one_way_margin = 0.0
118/shapes = [  ]
118/z_index = 0
119/name = "fixedscenefeatures.png 119"
119/texture = ExtResource( 1 )
119/tex_offset = Vector2( 0, 0 )
119/modulate = Color( 1, 1, 1, 1 )
119/region = Rect2( 1664, 256, 128, 128 )
119/tile_mode = 0
119/occluder_offset = Vector2( 0, 0 )
119/navigation_offset = Vector2( 0, 0 )
119/shape_offset = Vector2( 0, 0 )
119/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
119/shape_one_way = false
119/shape_one_way_margin = 0.0
119/shapes = [  ]
119/z_index = 0
120/name = "fixedscenefeatures.png 120"
120/texture = ExtResource( 1 )
120/tex_offset = Vector2( 0, 0 )
120/modulate = Color( 1, 1, 1, 1 )
120/region = Rect2( 1664, 384, 128, 128 )
120/tile_mode = 0
120/occluder_offset = Vector2( 0, 0 )
120/navigation_offset = Vector2( 0, 0 )
120/shape_offset = Vector2( 0, 0 )
120/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
120/shape_one_way = false
120/shape_one_way_margin = 0.0
120/shapes = [  ]
120/z_index = 0
121/name = "fixedscenefeatures.png 121"
121/texture = ExtResource( 1 )
121/tex_offset = Vector2( 0, 0 )
121/modulate = Color( 1, 1, 1, 1 )
121/region = Rect2( 1664, 512, 128, 128 )
121/tile_mode = 0
121/occluder_offset = Vector2( 0, 0 )
121/navigation_offset = Vector2( 0, 0 )
121/shape_offset = Vector2( 0, 0 )
121/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
121/shape_one_way = false
121/shape_one_way_margin = 0.0
121/shapes = [  ]
121/z_index = 0
122/name = "fixedscenefeatures.png 122"
122/texture = ExtResource( 1 )
122/tex_offset = Vector2( 0, 0 )
122/modulate = Color( 1, 1, 1, 1 )
122/region = Rect2( 1664, 640, 128, 128 )
122/tile_mode = 0
122/occluder_offset = Vector2( 0, 0 )
122/navigation_offset = Vector2( 0, 0 )
122/shape_offset = Vector2( 0, 0 )
122/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
122/shape_one_way = false
122/shape_one_way_margin = 0.0
122/shapes = [  ]
122/z_index = 0
123/name = "fixedscenefeatures.png 123"
123/texture = ExtResource( 1 )
123/tex_offset = Vector2( 0, 0 )
123/modulate = Color( 1, 1, 1, 1 )
123/region = Rect2( 640, 896, 128, 128 )
123/tile_mode = 0
123/occluder_offset = Vector2( 0, 0 )
123/navigation_offset = Vector2( 0, 0 )
123/shape_offset = Vector2( 0, 0 )
123/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
123/shape_one_way = false
123/shape_one_way_margin = 0.0
123/shapes = [  ]
123/z_index = 0
124/name = "fixedscenefeatures.png 124"
124/texture = ExtResource( 1 )
124/tex_offset = Vector2( 0, 0 )
124/modulate = Color( 1, 1, 1, 1 )
124/region = Rect2( 768, 896, 128, 128 )
124/tile_mode = 0
124/occluder_offset = Vector2( 0, 0 )
124/navigation_offset = Vector2( 0, 0 )
124/shape_offset = Vector2( 0, 0 )
124/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
124/shape_one_way = false
124/shape_one_way_margin = 0.0
124/shapes = [  ]
124/z_index = 0
125/name = "fixedscenefeatures.png 125"
125/texture = ExtResource( 1 )
125/tex_offset = Vector2( -12, -128 )
125/modulate = Color( 1, 1, 1, 1 )
125/region = Rect2( 768, 640, 128, 256 )
125/tile_mode = 0
125/occluder_offset = Vector2( 0, 0 )
125/navigation_offset = Vector2( 0, 0 )
125/shape_offset = Vector2( 0, 0 )
125/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
125/shape_one_way = false
125/shape_one_way_margin = 0.0
125/shapes = [  ]
125/z_index = 0
126/name = "fixedscenefeatures.png 126"
126/texture = ExtResource( 1 )
126/tex_offset = Vector2( 0, 0 )
126/modulate = Color( 1, 1, 1, 1 )
126/region = Rect2( 1664, 768, 128, 128 )
126/tile_mode = 0
126/occluder_offset = Vector2( 0, 0 )
126/navigation_offset = Vector2( 0, 0 )
126/shape_offset = Vector2( 0, 0 )
126/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
126/shape_one_way = false
126/shape_one_way_margin = 0.0
126/shapes = [  ]
126/z_index = 0
127/name = "villageprops.png 127"
127/texture = ExtResource( 15 )
127/tex_offset = Vector2( 0, 0 )
127/modulate = Color( 1, 1, 1, 1 )
127/region = Rect2( 0, 256, 256, 128 )
127/tile_mode = 0
127/occluder_offset = Vector2( 0, 0 )
127/navigation_offset = Vector2( 0, 0 )
127/shape_offset = Vector2( 0, 0 )
127/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
127/shape_one_way = false
127/shape_one_way_margin = 0.0
127/shapes = [  ]
127/z_index = 0
128/name = "villageprops.png 128"
128/texture = ExtResource( 15 )
128/tex_offset = Vector2( 0, 64 )
128/modulate = Color( 1, 1, 1, 1 )
128/region = Rect2( 1024, 320, 128, 64 )
128/tile_mode = 0
128/occluder_offset = Vector2( 0, 0 )
128/navigation_offset = Vector2( 0, 0 )
128/shape_offset = Vector2( 0, 0 )
128/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
128/shape_one_way = false
128/shape_one_way_margin = 0.0
128/shapes = [  ]
128/z_index = 0
129/name = "villageprops.png 129"
129/texture = ExtResource( 15 )
129/tex_offset = Vector2( 0, 0 )
129/modulate = Color( 1, 1, 1, 1 )
129/region = Rect2( 1024, 256, 256, 64 )
129/tile_mode = 0
129/occluder_offset = Vector2( 0, 0 )
129/navigation_offset = Vector2( 0, 0 )
129/shape_offset = Vector2( 0, 0 )
129/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
129/shape_one_way = false
129/shape_one_way_margin = 0.0
129/shapes = [  ]
129/z_index = 0
130/name = "villageprops.png 130"
130/texture = ExtResource( 15 )
130/tex_offset = Vector2( 0, 64 )
130/modulate = Color( 1, 1, 1, 1 )
130/region = Rect2( 1152, 320, 128, 64 )
130/tile_mode = 0
130/occluder_offset = Vector2( 0, 0 )
130/navigation_offset = Vector2( 0, 0 )
130/shape_offset = Vector2( 0, 0 )
130/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
130/shape_one_way = false
130/shape_one_way_margin = 0.0
130/shapes = [  ]
130/z_index = 0
131/name = "villageprops.png 131"
131/texture = ExtResource( 15 )
131/tex_offset = Vector2( 0, 22 )
131/modulate = Color( 1, 1, 1, 1 )
131/region = Rect2( 1280, 288, 128, 96 )
131/tile_mode = 0
131/occluder_offset = Vector2( 0, 0 )
131/navigation_offset = Vector2( 0, 0 )
131/shape_offset = Vector2( 0, 0 )
131/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
131/shape_one_way = false
131/shape_one_way_margin = 0.0
131/shapes = [  ]
131/z_index = 0
132/name = "villageprops.png 132"
132/texture = ExtResource( 15 )
132/tex_offset = Vector2( -60, 0 )
132/modulate = Color( 1, 1, 1, 1 )
132/region = Rect2( 1408, 288, 128, 224 )
132/tile_mode = 0
132/occluder_offset = Vector2( 0, 0 )
132/navigation_offset = Vector2( 0, 0 )
132/shape_offset = Vector2( 0, 0 )
132/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
132/shape_one_way = false
132/shape_one_way_margin = 0.0
132/shapes = [  ]
132/z_index = 0
133/name = "fixedscenefeatures.png 133"
133/texture = ExtResource( 1 )
133/tex_offset = Vector2( 0, 0 )
133/modulate = Color( 1, 1, 1, 1 )
133/region = Rect2( 1024, 0, 128, 128 )
133/tile_mode = 0
133/occluder_offset = Vector2( 0, 0 )
133/navigation_offset = Vector2( 0, 0 )
133/shape_offset = Vector2( 0, 0 )
133/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
133/shape_one_way = false
133/shape_one_way_margin = 0.0
133/shapes = [  ]
133/z_index = 0
134/name = "fixedscenefeatures.png 134"
134/texture = ExtResource( 1 )
134/tex_offset = Vector2( 0, 0 )
134/modulate = Color( 1, 1, 1, 1 )
134/region = Rect2( 0, 256, 128, 128 )
134/tile_mode = 0
134/occluder_offset = Vector2( 0, 0 )
134/navigation_offset = Vector2( 0, 0 )
134/shape_offset = Vector2( 0, 0 )
134/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
134/shape_one_way = false
134/shape_one_way_margin = 0.0
134/shapes = [  ]
134/z_index = 0
135/name = "fixedscenefeatures.png 135"
135/texture = ExtResource( 1 )
135/tex_offset = Vector2( 0, 0 )
135/modulate = Color( 1, 1, 1, 1 )
135/region = Rect2( 128, 256, 128, 128 )
135/tile_mode = 0
135/occluder_offset = Vector2( 0, 0 )
135/navigation_offset = Vector2( 0, 0 )
135/shape_offset = Vector2( 0, 0 )
135/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
135/shape_one_way = false
135/shape_one_way_margin = 0.0
135/shapes = [  ]
135/z_index = 0
136/name = "doordesert.png 136"
136/texture = ExtResource( 8 )
136/tex_offset = Vector2( -236, -352 )
136/modulate = Color( 1, 1, 1, 1 )
136/region = Rect2( 0, 0, 640, 500 )
136/tile_mode = 0
136/occluder_offset = Vector2( 0, 0 )
136/navigation_offset = Vector2( 0, 0 )
136/shape_offset = Vector2( 0, 0 )
136/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
136/shape_one_way = false
136/shape_one_way_margin = 0.0
136/shapes = [  ]
136/z_index = -3
137/name = "doordesert.png 137"
137/texture = ExtResource( 8 )
137/tex_offset = Vector2( -236, -352 )
137/modulate = Color( 1, 1, 1, 1 )
137/region = Rect2( 0, 500, 640, 500 )
137/tile_mode = 0
137/occluder_offset = Vector2( 0, 0 )
137/navigation_offset = Vector2( 0, 0 )
137/shape_offset = Vector2( 0, 0 )
137/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
137/shape_one_way = false
137/shape_one_way_margin = 0.0
137/shapes = [  ]
137/z_index = -3
138/name = "doordesert.png 138"
138/texture = ExtResource( 8 )
138/tex_offset = Vector2( -256, -36 )
138/modulate = Color( 1, 1, 1, 1 )
138/region = Rect2( 0, 1000, 640, 300 )
138/tile_mode = 0
138/occluder_offset = Vector2( 0, 0 )
138/navigation_offset = Vector2( 0, 0 )
138/shape_offset = Vector2( 0, 0 )
138/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
138/shape_one_way = false
138/shape_one_way_margin = 0.0
138/shapes = [  ]
138/z_index = -7
139/name = "fixedscenefeatures.png 139"
139/texture = ExtResource( 1 )
139/tex_offset = Vector2( 0, 0 )
139/modulate = Color( 1, 1, 1, 1 )
139/region = Rect2( 256, 256, 128, 128 )
139/tile_mode = 0
139/occluder_offset = Vector2( 0, 0 )
139/navigation_offset = Vector2( 0, 0 )
139/shape_offset = Vector2( 0, 0 )
139/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
139/shape_one_way = false
139/shape_one_way_margin = 0.0
139/shapes = [  ]
139/z_index = 0
