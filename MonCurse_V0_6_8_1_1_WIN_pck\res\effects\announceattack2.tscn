[gd_scene load_steps=7 format=2]

[ext_resource path="res://font/OpenSans-ExtraBold.ttf" type="DynamicFontData" id=1]
[ext_resource path="res://effects/announceattack2.gd" type="Script" id=2]

[sub_resource type="Shader" id=1]
code = "shader_type canvas_item;

uniform vec4 first_color : hint_color = vec4(1.0);
uniform vec4 second_color : hint_color = vec4(0.0, 0.0, 0.0, 1.0);
uniform float position : hint_range(-0.5, 0.5) = 0.0;
uniform float size : hint_range(0.5, 2) = 0.5;
uniform float angle : hint_range(0.0, 360.0) = 0.0;

void fragment() {
	vec4 base = texture(TEXTURE, UV);
	float pivot = position + 0.5;
	vec2 uv = UV - pivot;
	float rotated = uv.x * cos(radians(angle)) - uv.y * sin(radians(angle)); 
	float pos = smoothstep((1.0 - size) + position, size + 0.0001 + position, rotated + pivot);
	vec4 tempcolor = mix(first_color, second_color, pos);
	COLOR.rgb = mix(base, tempcolor, 1.0).rgb;
	COLOR.a = base.a;
}"

[sub_resource type="ShaderMaterial" id=2]
shader = SubResource( 1 )
shader_param/first_color = Color( 0.905882, 0.843137, 0.933333, 1 )
shader_param/second_color = Color( 0.313726, 0.0901961, 0.141176, 1 )
shader_param/position = -0.457
shader_param/size = 0.538
shader_param/angle = 92.0

[sub_resource type="DynamicFont" id=3]
size = 30
outline_size = 1
outline_color = Color( 0, 0, 0, 1 )
font_data = ExtResource( 1 )

[sub_resource type="Animation" id=4]
resource_name = "attack"
length = 3.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 1.8, 2.6, 3 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0.588235 ), Color( 1, 1, 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Label:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.4, 0.8, 1.8, 3 ),
"transitions": PoolRealArray( 1, 0.5, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 0.745098 ), Color( 1, 1, 1, 0.54902 ), Color( 1, 1, 1, 0.54902 ), Color( 1, 1, 1, 0 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:rect_scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.7, 2.3, 3 ),
"transitions": PoolRealArray( 0.5, 1, 2, 1 ),
"update": 0,
"values": [ Vector2( 1, 1.4 ), Vector2( 1, 1 ), Vector2( 1, 1 ), Vector2( 1, 1.4 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:margin_top")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.7, 2.6 ),
"transitions": PoolRealArray( 2, 0.5, 1 ),
"update": 0,
"values": [ -40, -80, -120 ]
}

[node name="Node2D" type="Node2D"]
z_index = 15
script = ExtResource( 2 )

[node name="Label" type="Label" parent="."]
modulate = Color( 0.823529, 1, 0.784314, 0.941176 )
self_modulate = Color( 1, 1, 1, 0 )
material = SubResource( 2 )
margin_top = -40.0
grow_horizontal = 2
grow_vertical = 2
rect_scale = Vector2( 1, 1.4 )
custom_fonts/font = SubResource( 3 )
text = "Test Attack"

[node name="Label" type="Label" parent="Label"]
self_modulate = Color( 1, 1, 1, 0 )
margin_right = 40.0
margin_bottom = 14.0
custom_fonts/font = SubResource( 3 )
text = "Test Attack"

[node name="AnimationPlayer" type="AnimationPlayer" parent="Label"]
anims/attack = SubResource( 4 )

[node name="Timer" type="Timer" parent="Label"]
one_shot = true

[connection signal="animation_finished" from="Label/AnimationPlayer" to="." method="_on_AnimationPlayer_animation_finished"]
