[gd_scene load_steps=6 format=2]

[ext_resource path="res://effects/particle0.png" type="Texture" id=1]
[ext_resource path="res://effects/hitparticles.gd" type="Script" id=2]

[sub_resource type="Gradient" id=1]
offsets = PoolRealArray( 0, 0.296053, 1 )
colors = PoolColorArray( 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=2]
gradient = SubResource( 1 )

[sub_resource type="ParticlesMaterial" id=3]
flag_disable_z = true
direction = Vector3( 0, -1, 0 )
gravity = Vector3( 0, 600, 0 )
initial_velocity = 600.0
initial_velocity_random = 0.4
angular_velocity = 400.0
angular_velocity_random = 0.5
orbit_velocity = 0.0
orbit_velocity_random = 0.0
color_ramp = SubResource( 2 )

[node name="hitparticles" type="Particles2D"]
z_index = 6
emitting = false
lifetime = 1.2
one_shot = true
explosiveness = 0.95
process_material = SubResource( 3 )
texture = ExtResource( 1 )
script = ExtResource( 2 )

[node name="Timer" type="Timer" parent="."]
wait_time = 1.3
one_shot = true
autostart = true

[connection signal="timeout" from="Timer" to="." method="_on_Timer_timeout"]
