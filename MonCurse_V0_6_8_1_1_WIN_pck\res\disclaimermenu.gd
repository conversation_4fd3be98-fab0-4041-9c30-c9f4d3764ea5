extends TextureRect

func _ready():
	$Label.set_text(Playervariables.consentmessage)
	if Playervariables.modarray[Playervariables.mods.TRANSLATION] == true:
		if Playervariables.default_font_dict["font_attack_announce"] != null:
			$TextureButton2/Label3.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_announce"].font_data
		if Playervariables.transdict.has("mainmenu"):
			if Playervariables.transdict["mainmenu"].has("yes"):
				$TextureButton2/Label3.set_text(Playervariables.transdict["mainmenu"]["yes"])
			if Playervariables.transdict["mainmenu"].has("cancel"):
				$TextureButton3/Label3.set_text(Playervariables.transdict["mainmenu"]["cancel"])
	var _c = get_tree().root.connect("size_changed", self, "_on_viewport_size_changed")
	_on_viewport_size_changed()

func _on_TextureButton2_pressed():
#	Playervariables.firstload = false
	get_node("/root/Master").show_disclaimer(false,false)
	get_parent().queue_free()


func _on_TextureButton3_pressed():
	get_tree().quit()


var firstrun = true
var recentsizechange = false
func _on_viewport_size_changed():
	if recentsizechange == false:
		recentsizechange = true #this makes it so it only updates the viewport every 4 frames
		if firstrun == false:
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
		else:
			firstrun = false
		recentsizechange = false
		var newsize = get_viewport_rect().size
#		var proportiony = (newsize.length()/600)
#		var proportionfull = (newsize.length()/1024)
#		var proportionx = (newsize.x/1024)
		$TextureButton2/Label3.get("custom_fonts/font").size = int(20*(newsize.x/Playervariables.basescreensize.x))
		var maintextproportion = 24.0*(newsize.y/Playervariables.basescreensize.y)
		if newsize.y > Playervariables.basescreensize.y:
			maintextproportion = maintextproportion*0.5 + 12
		if Playervariables.modarray[Playervariables.mods.TRANSLATION] == true and Playervariables.default_font_dict["font_attack_announce"] != null:
			maintextproportion = maintextproportion*Playervariables.default_font_dict["font_attack_announce_size"]
		$Label.get("custom_fonts/font").size = maintextproportion
