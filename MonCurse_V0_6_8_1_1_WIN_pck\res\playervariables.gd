extends TextureRect

#var debugallowbonus = true

const basescreensize = Vector2(1024,600)

const translation_node_names = PoolStringArray([
	"basic_attack_name",
	"soft_attack_name",
	"swipe_attack_name",
	"current_translation_code",
	"eswordarray",
	"veiltextdict",
	"consentmessage",
	"queststringarray",
	"namemissiondict",
	"spec_enemy_text_array",
	"areastringdict",
	"shortareastringdict",
	"chooselabelsarray",
	"petalmenutext",
	"settingstextdict",
	"tabinfodict",
	"customizationtextarray",
	"describecorruptiondict",
	"curseditemstransname",
	"curseditemdescribedict",
	"racearray",
	"raceclassarray",
	"bodypartdict",
	"monsternumarray",
	"monster_display_names",
	"notldebuffidentifyarray",
	"debuffidentifyarray",
	"debuffidentifynegativearray",
	"debuffdescriptionarray",
	"icondescribearray"
])

var playername = "Reset"
var tempname = "ERROR"

var queue_save = false
var gallery_mode = false #a debug toggle can change this on and off so don't use it for anything besides saving. so it's fine to leave it for gallery bad ends

func p_file_check():
	var file = File.new()
	if file.file_exists("res://p_file") == true:
		mobile_ads = true
		hungermechanics = true
		removesex = true
	else:
		mobile_ads = false
		hungermechanics = false
		removesex = false

var removesex = true
#var activate_iguana = true
var hungermechanics = true
#var no_like_actually_remove_the_sex_this_time = false
#var goat_furry_mascot_character = true
var christmas = false
var christmas_quest = false
var rope_quest = -1
var spec_ram_quest = 0#this is specifically for the 'get cow bell and ring' for special fox scene

var debugmodeon = false
var debugenemytracking = false

var basic_attack_name = "Basic Attack"
var soft_attack_name = "Soft Attack"
var swipe_attack_name = "Swipe Attack"

const TL_DEFAULT = "DEFAULT"
var current_translation_code = TL_DEFAULT

var Masterbus = AudioServer.get_bus_index("Master")
var Musicbus = AudioServer.get_bus_index("Music")
var SFXbus = AudioServer.get_bus_index("SFX")
var Ambiencebus = AudioServer.get_bus_index("Ambience")
var Mastervolume
var SFXvolume
var Musicvolume
var Ambiencevolume

#note: by the looks of it, unbirth defaults to on for new players? well, that's okay
enum pref{FUTAENEMY = 2, EXTREMEBE = 3,PREGNANCY=4,PSUEDOPREGNANCY=5,UNBIRTH=6}
var prefdict = {
2:true,
3:true,
4:true,
5:true,
6:false
}
var totalprefs = 0

var keepplayerscrolled = false

var touchscreenmode = false #whether or not to enable touch screen functions
var touchscreenalt = false #whether to use an alternate click, i.e. right click to move camera

const gameslownessdefault = 0.1
var gameslowness = 0.1

var force_load_convo = -1

var usedquipdict = {}
#var voiceusedquipdict = {}

enum tutorialstages{RESET=0,TUTORIAL1=1,TUTORIAL2=2,QADES=3,BUFFER=4,PUSSYGALORE=5,ALONGWALK=6,VOICE=7,SHIKATALK=8,BUFFER2=9,ROUTEA11=10,PARISH=11}#you are awarded the number when you finish the mentioned ENUM i.e. VOICE is given after talking to voice in bed
enum secrets{GRAVEYARD=0}
enum one_time_messages{SPRINGS_SAVE=0,SPRINGS_TUTORIAL=1}

var GalleryArrayHeadCG = [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0] #enum{NOSTUN = 0 BUFFER,KISSLOCKED,MILKLOCKED,ARMLOCKED,CATLOCKED,PUSSYLOCKED,RAMLOCKED,FOXLOCKED,HARPYLOCKED,PITCHLOCKED,SELFLOCKED,SCISSORLOCKED,IMPLOCKED,MUGLOCKED}, do not replace the first 0 it is NOSTUN
#var AbdomenSizesTransformations = [0,0,0,0] #MEDIUM2, LARGE3, HUGE4, MEGA5
var BustSizesTransformations = [0,0,0,0,0] #2, 3, 4, BUFFER, BUFFER
var MonsterGirlTransformations = [0,0,0,0,0,0,0,0,0,0]#SCALE FOR EACH NEW ENEMY#enum{raceHUMAN,raceNAMAN,raceCUPID,raceNEKO,raceKITSUNE,raceRAM,raceWOLF,raceHARPY,raceIMP,BUFFER}
var UniqueTransformations = [0,0,0,0,0,0,0,0,0,0]#SCALE FOR EACH NEW ENEMY#enum{raceHUMAN,raceNAMAN,raceCUPID,raceNEKO,raceKITSUNE,raceRAM,raceWOLF,raceHARPY,raceIMP,BUFFER}
var StagesCleared = [0,0,0,0,0,0,0]#TUTORIAL(1,2),BUFFER,BUFFER,BUFFER,BUFFER,BUFFER,BUFFER
enum StageRef{TUTORIAL,SECRET,ONETIMEMESSAGES,BUFFER3,BUFFER4,BUFFER5,BUFFER6}
var Endings = [0,0,0,0,0,0,0,0]#use a bit system for this later if there are multiple bad ends?
enum EndingsRef{GENERIC,CATGIRL,RAMGIRL,FOXGIRL,WEREWOLF,DRAGONHARPY,IMP,BUFFER2}#SCALE FOR EACH NEW ENEMY #DO NOT FORGET to ctrl-shift-f "endingsref" for cases like conversationv1 which uses these and needs to be linked up
var StoredTransformations = [0,0,0,0,0,0,0,0,0,0]#SCALE FOR EACH NEW ENEMY#limitedscaling??why???#enum{raceHUMAN,raceNAMAN,raceCUPID,raceNEKO,raceKITSUNE,raceRAM,raceWOLF,raceHARPY,raceIMP,BUFFER}
var MirrorTransformations = [0,0,0,0,0,0,0,0,0,0]#SCALE FOR EACH NEW ENEMY#limitedscaling???#enum{raceHUMAN,raceNAMAN,raceCUPID,raceNEKO,raceKITSUNE,raceRAM,raceWOLF,raceHARPY,raceIMP,BUFFER}
var MirrorClass = 0
var CurrentClass = 0
var MirrorAltClass = 0
var AltClass = 0
var GalleryClass = -1
#var TempImpClass = -1#will use AltClass in the future ... maybe? I dunno, it's for Imp's colors.
var ClassExp = [0,0,0,0,0,0,0,0,0,0]#enum{raceHUMAN,raceNAMAN,raceCUPID,raceNEKO,raceKITSUNE,raceRAM,raceWOLF,raceIMP,BUFFER}

enum{GameOverClass=0,MirrorGameOverClass=1}
var Class_Truth_Array = [true,true,false,false,false,false,false,false]#please do not set this without using save_class_truth
var Class_Truth_Bit = [3]
func load_class_truth():
	Class_Truth_Array = bit_to_array(Class_Truth_Bit,0)
	if CurrentClass == raceWOLF:
		wolf_collar = Class_Truth_Array[GameOverClass]
func save_class_truth(ref,truefalse):
	Class_Truth_Array[ref] = truefalse
	convert_to_bit(Class_Truth_Bit,0,ref,truefalse)
	if ref == 0 and CurrentClass == raceWOLF:
		wolf_collar = truefalse

var Voice_Cat = false

var zonetext = 0

enum{raceHUMAN=0,raceNAMAN=1,raceCUPID=2,raceNEKO=3,raceKITSUNE=4,raceRAM=5,raceWOLF=6,raceHARPY=7,raceIMP=8}#SCALE FOR EACH NEW ENEMY

const racetfsdict = {raceRAM:"ramtfs",
raceKITSUNE:"foxtfs",
raceNEKO:"cattfs",
raceHARPY:"harpytfs",
raceNAMAN:"busttfs",
raceWOLF:"wolftfs",
raceIMP:"imptfs"
}#SCALE FOR EACH NEW ENEMY and below too
enum curseditemtfs{ROPEGAG=0}#uses raceCUPID
enum ramtfs{HORNS=0,TOP=1,PANTS=2,EYES=3,COLOUR=7}
enum foxtfs{EARS=0,TAIL=1,HAIR=2,COLOUR=7}
enum cattfs{EARS=0,TAIL=1,ARMRIGHT=2,COLOUR=7}
enum wolftfs{EARS=0,TAIL=1,TOP=2,LEG=3,COLOUR=7}
enum harpytfs{ARMRIGHT=0,LEG=1,TAIL=2,BACKHORNS=3,COLOUR=7}
enum imptfs{BACKHORNS=0,TAIL=1,WINGS=2,BODY=3,LEG=4,FACE=5,COLOUR=7}
enum busttfs{size2=0,size3=1,size4=2}
const base2 = log(2)
func convert_to_bit(array,ref,input,addiftrue = true) -> void:
	var inputbit = int(1 * pow(2,int(clamp(input,0,63))))
	var targetbit = int(array[ref])
	var removebit 
	if targetbit == 0:
		removebit = 0
	else:
		removebit = int(1 * pow(2,int(log(targetbit)/base2)))
	while removebit > inputbit:
		if targetbit >= removebit:
			targetbit = targetbit - removebit
		removebit = removebit * 0.5
	if addiftrue == true: #make a false value true
		if targetbit < inputbit: #if true, value does not yet exist
			array[ref] += inputbit
	else: #make a true value false
		if targetbit >= inputbit:
			array[ref] -= inputbit
func bit_to_array(array,ref,bits = 8) -> Array:
	var remainingbit = int(array[ref])
	var currentdenomination = int(1 * pow(2,int(clamp(bits-1,0,63)))) #sequence for 8 bits: 1+2+4+8+16+32+64+128
	var resultarray = []
	if remainingbit >= currentdenomination*2:
		print("Serious BIT-TO-ARRAY error: You are processing a value that is larger than BITS value. Reducing REMAININGBIT value.")
		remainingbit = currentdenomination
	for _i in range(bits):
		if remainingbit >= currentdenomination:
			resultarray.append(true)
			remainingbit = remainingbit - currentdenomination
		else:
			resultarray.append(false)
		currentdenomination = int(currentdenomination * 0.5)
	resultarray.invert()
#	print("DEBUG: bit to array: Array:" +str(array) + " .ref:" + str(ref) + " .bits:" + str(bits) + " .RESULT ARRAY:" + str(resultarray))
	return resultarray

var tutorialscleared = 0
const save_path = "user://settings.cfg"
const savefile1 = "user://save1.dat"
const savefile2 = "user://save2.dat"
const savefile3 = "user://save3.dat"
var currentsavenum = 1
var config = ConfigFile.new()
#var currentsave = ConfigFile.new()
var load_response = config.load(save_path)
func savevalue(section,key,newvalue):
	config.set_value(section,key,newvalue)
	config.save(save_path)
#func saveprogress(section,key,newvalue):
#	currentsave.set_value(section,key,newvalue)
#	currentsave.save(savefile1)
const binaryvariables = 15
func saveprogress():
	if gallery_mode == true:#if you pick colors in the gallery, it won't save until you do enough other things, unfortunately.
#		print("Tried to save from within Gallery?")
		queue_save = true
		return
	else:
		queue_save = false
#	var newtimer = OS.get_ticks_msec()
	var file = File.new()
#	match int(currentsavenum):
#		1:file.open(savefile1, File.WRITE)
#		2:file.open(savefile2, File.WRITE)
#		3:file.open(savefile3, File.WRITE) #pray this overwrites the file and doesn't just infinitely extend it.
	file.open(self.get("savefile"+str(currentsavenum)), File.WRITE)
	if newsave == true:
		file.store_8(0)
		file.close()
		return
	file.store_8(binaryvariables) #not including itself
	#VARIABLE 1: PLAYERNAME
	file.store_pascal_string(playername)
	#VARIABLE 2: TRUECOLORARRAY
	store_array_on_file(file,truecolourarray,8)
	#VARIABLE 3: RESOURCEARRAY
	store_array_on_file(file,resourcearray,16)
	#VARIABLE 4: HEAD SCENE CG SEEN
	store_array_on_file(file,GalleryArrayHeadCG,16)
	#VARIABLE 5: BUST SIZES CG SEEN
	store_array_on_file(file,BustSizesTransformations,16)
	#VARIABLE 6: MONSTERGIRL TFS SEEN
	store_array_on_file(file,MonsterGirlTransformations,16)
	#VARIABLE 7: STAGE PROGRESS
	store_array_on_file(file,StagesCleared,8)
	#VARIABLE 8: ENDINGS SEEN
	store_array_on_file(file,Endings,8)
	#VARIABLE 9: TRANSFORMATIONS, BUT ONLY WHETHER SEEN OR NOT
	store_array_on_file(file,UniqueTransformations,8)
	#VARIABLE 10: YOUR CLASS + CLASS XP(not used yet?)
	file.store_8(CurrentClass)
	file.store_8(0)
#	store_array_on_file(file,ClassExp,16)#NOTE: VARIABLE 10 HAS BEEN TEMPORARILY DISABLED BY PUTTING A 0 AS THE STORE_8 FOR ARRAY SIZE
	#VARIABLE 11: CURRENT CORRUPTIONS + CLASS ALT STATE
	store_array_on_file(file,StoredTransformations,8)
	file.store_8(AltClass)
	#VARIABLE 12: PLAYER'S SHARDS COUNT
	file.store_8(playershards)

	file.store_8(MirrorClass)
	store_array_on_file(file,MirrorTransformations,8)
	file.store_8(MirrorAltClass)
	
	file.store_8(Class_Truth_Bit[0])
#	store_array_on_file(file,AbdomenSizesTransformations,16)
#var GalleryArrayHeadCG = [0,0,0,0,0] #enum{NOSTUN = 0 BUFFER,KISSLOCKED,MILKLOCKED,ARMLOCKED,BUFFER}
#var BustSizesTransformations = [0,0,0,0,0] #2, 3, 4, BUFFER, BUFFER
#var MonsterGirlTransformations = [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]#enum{raceHUMAN,raceNAMAN,raceCUPID,raceNEKO,raceKITSUNE,raceRAM,raceWOLF,BUFFER,BUFFER,BUFFER,BUFFER,BUFFER,BUFFER,BUFFER,BUFFER,BUFFER,BUFFER}
#	store_array_on_file(file,baseplayercolourarray,8)
#	var saved_color_2darray_size = saved_color_2darray.size()
#	file.store_8(saved_color_2darray_size)
#	for i in range(saved_color_2darray_size):
#		store_array_on_file(file,saved_color_2darray_size[i],8)
#	store_array_on_file(file,mirrorplayercolourarray,8)
	file.close()
	
#	OLD_SAVE_PROGRESS()
#
#	var comparefile = File.new()
#	comparefile.open(self.get("savefile"+str(currentsavenum)),file.READ)
#
#	var comparefile2 = File.new()
#	comparefile2.open("user://savetest.dat",file.READ)
#
#	while comparefile.eof_reached() == false:
#		if comparefile.get_8() == comparefile2.get_8():
#			pass
#		else:
#			print("ERROR, DIFFERENCE IN FILES")
#	print("Check completed.")
#
#func OLD_SAVE_PROGRESS():
##	var newtimer = OS.get_ticks_msec()
#	var file = File.new()
##	match int(currentsavenum):
##		1:file.open(savefile1, File.WRITE)
##		2:file.open(savefile2, File.WRITE)
##		3:file.open(savefile3, File.WRITE) #pray this overwrites the file and doesn't just infinitely extend it.
#	file.open("user://savetest.dat", File.WRITE)
#	if newsave == true:
#		file.store_8(0)
#		file.close()
#		return
#	file.store_8(binaryvariables) #not including itself
#	#VARIABLE 1: PLAYERNAME
#	file.store_pascal_string(playername)
#	#VARIABLE 2: TRUECOLORARRAY
#	file.store_8(truecolourarray.size())
#	for i in range(truecolourarray.size()):
#		file.store_8(truecolourarray[i])
#	#VARIABLE 3: RESOURCEARRAY
#	file.store_8(resourcearray.size())
#	for i in range(resourcearray.size()):
#		file.store_16(resourcearray[i])
#	#VARIABLE 4: HEAD SCENE CG SEEN
#	file.store_8(GalleryArrayHeadCG.size())
#	for i in range(GalleryArrayHeadCG.size()):
#		file.store_16(GalleryArrayHeadCG[i])
#	#VARIABLE 5: BUST SIZES CG SEEN
#	file.store_8(BustSizesTransformations.size())
#	for i in range(BustSizesTransformations.size()):
#		file.store_16(BustSizesTransformations[i])
#	#VARIABLE 6: MONSTERGIRL TFS SEEN
#	file.store_8(MonsterGirlTransformations.size())
#	for i in range(MonsterGirlTransformations.size()):
#		file.store_16(MonsterGirlTransformations[i])
#	file.store_8(StagesCleared.size())
#	for i in range(StagesCleared.size()):
#		file.store_8(StagesCleared[i])
#	file.store_8(Endings.size())
#	for i in range(Endings.size()):
#		file.store_8(Endings[i])
#	file.store_8(UniqueTransformations.size())
#	for i in range(UniqueTransformations.size()):
#		file.store_8(UniqueTransformations[i])
#	file.store_8(CurrentClass)
#	file.store_8(ClassExp.size())
#	for i in range(ClassExp.size()):
#		file.store_16(ClassExp[i])
#	file.store_8(StoredTransformations.size())
#	for i in range(StoredTransformations.size()):
#		file.store_8(StoredTransformations[i])
#	file.store_8(AltClass)
#	file.store_8(playershards)
#	file.store_8(AbdomenSizesTransformations.size())
#	for i in range(AbdomenSizesTransformations.size()):
#		file.store_16(AbdomenSizesTransformations[i])
#
#	file.close()
#

func store_array_on_file(file,array,bits):
	var arraysize = array.size()
	file.store_8(arraysize)
	if bits == 16:
		for i in range(arraysize):
			file.store_16(array[i])
	else:
		for i in range(arraysize):
			file.store_8(array[i])

var last_load_convo = 0
var newsave = false
#var firstsave = false
func loadprogress(savenum): #make sure to CORRUPT FROM STORED after load progress
	currentsavenum = clamp(savenum,1,3)
	savevalue("SaveFiles","Lastsave",currentsavenum)
	debugmodeon = false
	var file = File.new()
	var currentsavepath = self.get("savefile"+str(currentsavenum))
	if file.file_exists(currentsavepath) != true:
		loaddefaultprogress()
		return
	else:
		newsave = false
	file.open(currentsavepath, File.READ)
#	match int(currentsavenum):
#		1:file.open(savefile1, File.READ)
#		2:file.open(savefile2, File.READ)
#		3:file.open(savefile3, File.READ)
#	file.open(self.get("savefile"+str(currentsavenum)), File.READ)
	var VariablesInSave = file.get_8()
	if VariablesInSave < 6: #V0.4 or older?
		loaddefaultprogress()
		return
	#VARIABLE 1: PLAYERNAME
	playername = file.get_pascal_string()
	if playername == "Rules":
		debugmodeon = true
	#VARIABLE 2: TRUECOLORARRAY
	var truecolourarraysize = file.get_8()
	append_to_array(truecolourarray,truecolourarraysize,file,8)
	#VARIABLE 3: RESOURCEARRAY
	var resourcearraysize = file.get_8()
	append_to_array(resourcearray,resourcearraysize,file,16)
	#VARIABLE 4: HEAD SCENE CG SEEN
	var galleryarrayheadcgsize = file.get_8()
	append_to_array(GalleryArrayHeadCG,galleryarrayheadcgsize,file,16)
	#VARIABLE 5: BUST SIZES CG SEEN
	var bustsizestransformationssize = file.get_8()
	append_to_array(BustSizesTransformations,bustsizestransformationssize,file,16)
	#VARIABLE 6: MONSTERGIRL TFS SEEN
	var monstergirltransformationssize = file.get_8()
	append_to_array(MonsterGirlTransformations,monstergirltransformationssize,file,16)
	if VariablesInSave < 7: #V0.45
		file.close()
		return
	#VARIABLES 7: STAGES CLEARED
	var stagesclearedsize = file.get_8()
	append_to_array(StagesCleared,stagesclearedsize,file,8)
	if VariablesInSave < 8:
		file.close()
		return
	#VARIABLE 8: ENDS SEEN (Bit value?)
	var endingssize = file.get_8()
	append_to_array(Endings,endingssize,file,8)
	if VariablesInSave < 9:
		for i in range(monstergirltransformationssize):
			var num = MonsterGirlTransformations[i]
			if num == 1:
				if i == raceKITSUNE:
					UniqueTransformations[i] = 3
				else:
					UniqueTransformations[i] = 1
			elif num > 1 and i == raceNEKO:
				UniqueTransformations[i] = 3
			else:
				UniqueTransformations[i] = 0
		if BustSizesTransformations[0] > 0:
			UniqueTransformations[1] = 1
			if BustSizesTransformations[1] > 0:
				UniqueTransformations[1] = 3
				if BustSizesTransformations[2] > 0:
					UniqueTransformations[1] = 7
		file.close()
		return
	var uniquetfsize = file.get_8()
	append_to_array(UniqueTransformations,uniquetfsize,file,8)
	if VariablesInSave < 10:
		file.close()
		return
	CurrentClass = file.get_8()
	var classexpsize = file.get_8()
	for i in range(classexpsize):
		ClassExp[i] = file.get_16()
	var storedtransfromationssize = file.get_8()
	for i in range(storedtransfromationssize):
		StoredTransformations[i] = file.get_8()
	if VariablesInSave < 11:
		file.close()
		return
	AltClass = file.get_8()
	match AltClass:
		0:pass
		1:
			match CurrentClass:
				raceRAM:
					makeuprank = true
	if VariablesInSave < 12:
		file.close()
		return
	playershards = file.get_8()
	
	if VariablesInSave < 13:
		MirrorClass = CurrentClass
		MirrorTransformations = StoredTransformations.duplicate()
		MirrorAltClass = AltClass
		file.close()
		return
	
	MirrorClass = file.get_8()
	var mirrortransfromationssize = file.get_8()
	for i in range(mirrortransfromationssize):
		MirrorTransformations[i] = file.get_8()
	MirrorAltClass = file.get_8()
	if VariablesInSave < 14:
		if Endings[EndingsRef.FOXGIRL] == 1:
			Endings[EndingsRef.FOXGIRL] = 3
		file.close()
		return
	Class_Truth_Bit = [file.get_8()]
	load_class_truth()
	if VariablesInSave < 15:
		file.close()
		return
#	var basecolourarraysize = file.get_8()
#	append_to_array(baseplayercolourarray,basecolourarraysize,file,8)
#	var saved_color_2darray_size = file.get_8()
#	for i in range(saved_color_2darray_size):
#		var specific_colour_save_size = file.get_8()
#		append_to_array(saved_color_2darray[i],specific_colour_save_size,file,8)
#	var mirrorplayercolourarraysize = file.get_8()
#	append_to_array(mirrorplayercolourarray,mirrorplayercolourarraysize,file,8)
#	if VariablesInSave < 15:
#		return
	#VARIABLE 5: BUST SIZES CG SEEN
#	var abdomensizes = file.get_8()
#	append_to_array(AbdomenSizesTransformations,abdomensizes,file,16)
#	playername = file.get_pascal_string()
#	truecolourarray[0] = file.get_8()
#	truecolourarray[1] = file.get_8()
#	resourcearray[CAON] = file.get_16()
#	if VariablesInSave < 5:
#		file.close()
#		return
#	GalleryArrayHeadCG[0] = file.get_8()
#	GalleryArrayHeadCG[1] = file.get_8()
#	GalleryArrayHeadCG[2] = file.get_8()
#	GalleryArrayHeadCG[3] = file.get_8()
#	if VariablesInSave < 8:
#		file.close()
#		return
	file.close()
	return
func append_to_array(arraypointer,quantity,file,bits):
	var arraysize = arraypointer.size()
	for i in range(quantity):
		if i+1 <= arraysize:
			if bits == 16:
				arraypointer[i] = file.get_16()
			else:
				arraypointer[i] = file.get_8()
		else:
			if bits == 16:
				file.get_16()
			else:
				file.get_8()
func loaddefaultprogress():
	newsave = true
	playername = "Reset"
	for value in UniqueTransformations.size():
		UniqueTransformations[value] = 0
	for value in range(GalleryArrayHeadCG.size()):
		GalleryArrayHeadCG[value] = 0
	for value in range(BustSizesTransformations.size()):
		BustSizesTransformations[value] = 0
	for value in range(MonsterGirlTransformations.size()):
		MonsterGirlTransformations[value] = 0
	for value in range(resourcearray.size()):
		resourcearray[value] = 0
	for value in range(truecolourarray.size()):
		truecolourarray[value] = 0
	for value in range(StagesCleared.size()):
		StagesCleared[value] = 0
	for value in range(Endings.size()):
		Endings[value] = 0
	CurrentClass = 0
	AltClass = 0
	for value in range(ClassExp.size()):
		ClassExp[value] = 0
	corruptiondict = {}
	playerbustsize = 1
	playerabdomensize = 1
	for value in range(StoredTransformations.size()):
		StoredTransformations[value] = 0
	truecolourarray[0] = 2
	truecolourarray[1] = 2
	truecolourarray[2] = 0
	StagesCleared[0] = 0
	playershards = 0

func corrupt_from_stored(use_array,use_dict,mirror=false):#may be called during hotsprings so be careful what you add
	var dict_to_corrupt = {}
	if use_array[0] == 0:
		
#		var ramarray = bit_to_array(StoredTransformations,raceRAM)
#		if ramarray[ramtfs.HORNS] == true:
#			dict_to_corrupt["horns"] = raceRAM
#		if ramarray[ramtfs.TOP] == true:
#			dict_to_corrupt["top"] = raceRAM
#		if ramarray[ramtfs.PANTS] == true:
#			dict_to_corrupt["pants"] = raceRAM
#		if ramarray[ramtfs.EYES] == true:
#			dict_to_corrupt["eyes"] = raceRAM
		for race in racetfsdict:
			if race > 1:
				var useenum = get(racetfsdict[race])
				var enumkeys = useenum.keys()
				var usearray = bit_to_array(use_array,race)
				for i in enumkeys.size():
#					if usearray[i] == true:
					var num = useenum[enumkeys[i]]
					if usearray[num] == true:
						dict_to_corrupt[enumkeys[i].to_lower()] = race
#		var foxarray = bit_to_array(use_array,raceKITSUNE)
#		if foxarray[foxtfs.EARS] == true:
#			dict_to_corrupt["ears"] = raceKITSUNE
#		if foxarray[foxtfs.TAIL] == true:
#			dict_to_corrupt["tail"] = raceKITSUNE
#		if foxarray[foxtfs.HAIR] == true:
#			dict_to_corrupt["tail"] = raceKITSUNE
#
#		var catarray = bit_to_array(use_array,raceNEKO)
#		if catarray[cattfs.EARS] == true:
#			dict_to_corrupt["ears"] = raceNEKO
#		if catarray[cattfs.TAIL] == true:
#			dict_to_corrupt["tail"] = raceNEKO
			
		var bustarray = bit_to_array(use_array,raceNAMAN)
		if bustarray[busttfs.size4] == true:
			playerbustsize = 4
		elif bustarray[busttfs.size3] == true:
			playerbustsize = 3
		elif bustarray[busttfs.size2] == true:
			playerbustsize = 2
		else:
			playerbustsize = 1
#		var classcorruption = get("classcorruption"+str(CurrentClass)).duplicate()
#		if classcorruption.has("bust"):
#			if classcorruption["bust"] > 0:
#				playerbustsize = max(classcorruption["bust"],playerbustsize) #if positive, increases bust size to number
#		for corruptionkey in classcorruption:
#			if classcorruption[corruptionkey] > 0 and (dict_to_corrupt.has(corruptionkey) == false or dict_to_corrupt[corruptionkey] == 0):
#				dict_to_corrupt[corruptionkey] = classcorruption[corruptionkey]
	else:
		dict_to_corrupt = use_dict.duplicate(true)
	if CurrentClass == raceWOLF:
		wolf_collar = Class_Truth_Array[GameOverClass]
	else:
		wolf_collar = false
	if mirror == false:
		if wolf_collar == true:#CurrentClass == raceWOLF:
			tempname = "Bitch"
		else:
			if CurrentClass == raceNEKO:
				Voice_Cat = true
			else:
				Voice_Cat = false
			tempname = playername
	return dict_to_corrupt

var harpy_variants = false
var wolf_variants = false
var lastsavenum = 1
var resetanimation = false
var restorecorruptions = false
func resetvariables(truereset = false,allowrestore = true):
	if StoredTransformations[raceHUMAN] > 0:
		save_class_truth(GameOverClass,true)
		truereset = true
		StoredTransformations[raceHUMAN] = 0
#	finalstage = false
	mappool1 = ["Hazard Obstruction","Map Water","Hazard Cum"]
	mappool2 = ["Enemy Cat","Enemy Fox","Enemy Ram","Cat Rank","Fox Rank","Ram Rank"]
	mappool3 = ["Map Width","Map Depth","Map Water","Hazard Obstruction"]
#	haditems = PoolIntArray([])
	unusedpool = []
	escalationweight = {}
	escalationdict = {}
	for i in range(3):
		for key in get("mappool"+str(i+1)):
			escalationdict[key] = 0
			escalationweight[key] = 5
	for key in unusedpool:
		escalationdict[key] = 0
		escalationweight[key] = 5
	escalationweight["Hazard Cum"] = 0
	escalationweight["Cat Rank"] = 0
	escalationweight["Fox Rank"] = 0
	escalationweight["Ram Rank"] = 0
	Move13["special"][0] = [3]
	Move13["block"] = []
	playerstunstate = NOSTUN
	debufftriggerarray = debuffblankarray.duplicate()
	debufftriggerarray[1] = 5
#	for key in playerstatedict:
#		playerstatedict[key] = 0
#	if curseditemdict[PAWS] == true:
	if CurrentClass != raceRAM and corruptiondict.has("eyes") == true and corruptiondict["eyes"] == raceRAM:
		corruptiondict.erase("eyes")
#	if corruptiondict.has("armleft") == true and corruptiondict["armleft"] == raceNEKO:
#		corruptiondict.erase("armleft")
	for key in curseditemdict:
		curseditemdict[key] = false
	foxpendants = 0
	totalpaws = 0
	totalattackrangebuffs = 0
	if corruptiondict.has("hair") and corruptiondict["hair"] == raceKITSUNE:
#		if CurrentClass == raceKITSUNE:
		foxpendants = 1
#		else:
#			corruptiondict.erase("hair")
		curseditemdict[FOXPENDANT] = true#this may be fine because the player can't true reset then start a new mission without a soft reset?
	if corruptiondict.has("armright") == true and corruptiondict["armright"] == raceNEKO:
		if StagesCleared[StageRef.TUTORIAL] < tutorialstages.VOICE:
#		if CurrentClass == raceWOLF:
#			totalpaws = 1
#			curseditemdict[NORMALPAWS] = true
#		else:
			corruptiondict.erase("armright")
		else:
			totalpaws = 1
			curseditemdict[NORMALPAWS] = true#this may be fine because the player can't true reset then start a new mission without a soft reset?
	spawnedarray = []
	elitespawnedarray = []
	for value in retaineddebuffarray.size():
		retaineddebuffarray[value] = 0
	for value in monsternumarray.size():
		spawnedarray.append(0)
		elitespawnedarray.append(0)
	tier = -1
	torchlight = 0
	sturdyboots = 0
	if totalplayermoves > 0:# == false:
		awaitingconversation = ""
	totalplayermoves = 0
	autodiscardarrayinnate = []
	autodiscardarrayitem = []
	kissflipflop = 0
	outfithealth = 4
#	timestalkedtoshika = 0
#	timesdefeatedshika = 0
	questcondition = 99
	questcondition2 = 0
	
	if AltClass == 1:
		match CurrentClass:
			raceRAM:
				makeuprank = true
			raceKITSUNE:
				possessionrank = true
	else:
		makeuprank = false
	stagemapheld = false
#	lastconsumableitem = 0
	playermaxbasedamage = 1
	playerforage = 6 #playerfood from plants and stuff.
#	attackmaxrange = 1 #if you want to increase this, add a check that looks at mapstar and compares it to the skystar ID and disallows the attack if they don't line UP.
	tempcorruptiondict = {}
	maxresistance = 20
	playerresistance = 20 #better to turn this into just '1' so you resist '1' per resistance.
	playerexhaustrate = 0.2
#	if corruptiondict.has("body") == false or truereset == true:
#		baseplayercolourarray = truecolourarray.duplicate()
#	else:
#		baseplayercolourarray[pcol.EYES] = truecolourarray[pcol.EYES]
#		if baseplayercolourarray[pcol.HAIR] != 19:
#			baseplayercolourarray[pcol.HAIR] = truecolourarray[pcol.HAIR]
#		if baseplayercolourarray[pcol.SKIN] != 7:
#			baseplayercolourarray[pcol.SKIN] = truecolourarray[pcol.SKIN]
#		baseplayercolourarray[pcol.CLOTHES] = truecolourarray[pcol.CLOTHES]
#		baseplayercolourarray[pcol.PANTS] = truecolourarray[pcol.PANTS]
#	set_base_colour()
#	set_rank_materials()
#	set_hair_materials()
#	set_skin_materials()
#	set_clothes_materials()
	for i in range(sealsarray.size()):
		sealsarray[i] = 0
		sealplacearray[i] = -1
	var inventoryhash = playerinventory2darray.hash()
	var passivesize = passivearray.size()
	passivearray = []
	set_inventory_to_class()
	Move9["types"][2] = 0
	Move9.erase("uses")
	for value in racecorruptionarray.size():
		racecorruptionarray[value] = 0
	for value in bonusracecorruptionarray.size():
		racecorruptionarray[value] = 0
#	for value in racelockcorruptionarray.size():
#		racelockcorruptionarray[value] = false
	if truereset == true:
		p_file_check()
		christmas_quest = false
		spec_ram_quest = 0
		rope_quest = -1
		if CurrentClass == raceNEKO:# and goat_furry_mascot_character == true:
			Voice_Cat = true
		else:
			Voice_Cat = false
		if wolf_collar == true:#if CurrentClass == raceWOLF:
			tempname = "Bitch"
		else:
			tempname = playername
#		Move3["types"][2] = 0
#		Move3["special"][2] = 0
#		playerinsight = 0
		get_node("/root/Master").firstload = true
		restorecorruptions = false
		if gallery_mode == false:
			for value in StoredTransformations.size():
				StoredTransformations[value] = 0
		races = [-3]
		var prevcorruptdict = corruptiondict.duplicate()
		corruptiondict = get("classcorruption"+str(CurrentClass)).duplicate()
		if corruptiondict.has("bust"):
			if corruptiondict["bust"] <= 0:
				playerbustsize = min(-corruptiondict["bust"],playerbustsize) #if negative, reduces bust size to number
			else:
				playerbustsize = max(corruptiondict["bust"],playerbustsize) #if positive, increases bust size to number
			corruptiondict.erase("bust")
		else:
			playerbustsize = 1
		var erasearray = []
		for key in corruptiondict:
			if corruptiondict[key] < 0:
				if prevcorruptdict.has(key) == false:
					erasearray.append(key)
				else:
					var test_num = -2
					if key == "colour":
						test_num = CurrentClass
					else:
						match corruptiondict[key]:
							classdefine.PRESERVEALL:test_num = -1
							classdefine.PRESERVESELF:test_num = CurrentClass
							classdefine.PRESERVEWOLF:test_num = raceWOLF
							classdefine.PRESERVEIMP:test_num = raceIMP
							classdefine.PRESERVENEKO:test_num = raceNEKO
					if test_num == -1:
						corruptiondict[key] = prevcorruptdict[key]
					elif prevcorruptdict[key] == test_num:
						corruptiondict[key] = test_num
					else:
						erasearray.append(key)
		for erasekey in erasearray:
			corruptiondict.erase(erasekey)
		if gallery_mode == false:
			for key in corruptiondict:
	#			if prevcorruptdict.has(key) and prevcorruptdict[key] == corruptiondict[key]:
				if corruptiondict[key] > 0:
					var race = corruptiondict[key]
					var useenum = get(racetfsdict[race])
					var upperkey = key.to_upper()
					if useenum.has(upperkey):
						var reference = useenum.get(upperkey)
						convert_to_bit(StoredTransformations,race,reference,true)
						convert_to_bit(UniqueTransformations,race,reference,true)
					else:
						print("Serious error, tried to find an enum code that doesn't exist?")
			if playerbustsize > 1:
				convert_to_bit(StoredTransformations,raceNAMAN,playerbustsize-2,true)
				convert_to_bit(UniqueTransformations,raceNAMAN,playerbustsize-2,true)
			saveprogress()
	elif restorecorruptions == true and allowrestore == true:
		restorecorruptions = false
		corruptiondict = corrupt_from_stored(StoredTransformations,corruptiondict)
	if corruptiondict.has("top") and corruptiondict["top"] > 0:
		playeroutfit[0] = corruptiondict["top"]
	else:
		playeroutfit[0] = 1
#	if corruptiondict.has("pants") and corruptiondict["pants"] > 0:
#		if corruptiondict.has("leg") and corruptiondict["leg"] == raceHARPY:
#			corruptiondict.erase("pants")
#			playeroutfit[1] = 0
#		else:
#			playeroutfit[1] = corruptiondict["pants"]
#	elif corruptiondict.has("leg") == false:
#		playeroutfit[1] = 1
#	else:
#		playeroutfit[1] = 0
	playeroutfit[1] = int(clamp(get_corruption("pants"),1,99))
	if corruptiondict.has("leg") and corruptiondict["leg"] == raceWOLF:
		wolf_variants = true
	else:
		wolf_variants = false
	if rope_quest > -1:
		curseditemdict[ROPEGAG] = true
#	print("Otherwise, stored tfs:"+str(StoredTransformations))
#	print(corruptiondict)
	hypno = false
	if gallery_mode == false:
		set_base_colour()
	else:
		if get_corruption("armright") != raceNEKO:
			totalpaws = 0
			curseditemdict[NORMALPAWS] = false
#	set_rank_materials()
#	set_hair_materials()
#	set_skin_materials()
#	set_clothes_materials()
	identify_inventory()
	if lastsavenum == currentsavenum:
		if passivesize > 0 or inventoryhash != playerinventory2darray.hash():
			resetanimation = true
	else:
		resetanimation = false
		lastsavenum = currentsavenum
#	print("Inventoryhash:"+str(inventoryhash)+"<>"+str(playerinventory2darray.hash())+"..."+str(playerinventory2darray))
	player_holding_enemy = false
	calculate_abdomen()
	stat_calc()
	lewdness_calc()

#var nextuniqueid = 0

func identify_inventory():
#	for array in playerinnate2darray.size():
#		playerinnate2darray[array][1] = array#nextuniqueid
#		nextuniqueid += 1
	for arraynum in playerinventory2darray.size():
		playerinventory2darray[arraynum][1] = arraynum#nextuniqueid
#		nextuniqueid += 1
	for arraynum in playerconsumables2darray.size():
		playerconsumables2darray[arraynum][1] = arraynum#nextuniqueid
	if lastconsumableitem >= playerconsumables2darray.size():
		lastconsumableitem = int(clamp(playerconsumables2darray.size() -1,0,9999))
	var retain_limit = playerinventory2darray.size()
	for value in retain_deck_array:
		if value >= retain_limit:
			retain_deck_array.erase(value)
#		if playerconsumables2darray[arraynum][1] == lastconsumableitem:
#			lastconsumableitem = arraynum
#		nextuniqueid += 1

var mappool1 = ["Blank"]
var mappool2 = ["Blank"]
var mappool3 = ["Blank"]
var unusedpool = ["Blank"]

const newcolorarrayhorns = [Vector3(0,1,1),Vector3(0.49,4.0,1.8),Vector3(0.94,3,1.35),Vector3(0.9,2.1,0.9)]
const newcolorarrayhornsnames = ["ram","dragonharpy","harpy","imp"]
#HSV values. based off of rose hair style
const newcolorarrayhair = [Vector3(0.95,0.1,0.35),Vector3(0.95,0.65,0.6),Vector3(0,1,1),Vector3(0.65,1.1,1.2),Vector3(0.5,1,1),Vector3(0.047,1.15,1.45),Vector3(0.88,0.418,0.9),Vector3(0,0.5,1.5),Vector3(0.65,1,0.1),Vector3(0.7,0.2,1.2),Vector3(0.95,0.07,1.6),Vector3(0.85,0.25,0.2),Vector3(0.925,0.9,1.5),Vector3(0.95,1.35,1),Vector3(0.55,0.66,1.42),Vector3(0.43,0.66,1.42),Vector3(0.925,0.38,0.42),Vector3(0.01,0.7,1.4),Vector3(0.57,0.63,1.25),Vector3(0.62,0.89,0.81),Vector3(0.58,0.28,1.24),Vector3(0.045,0.7,1.4),Vector3(0.895,0.5,1.5),Vector3(0.055,0.8,1.28),Vector3(0.985,0.8,1.28),Vector3(0.89,1.35,0.93)]
const newcolorarrayhairnames = ["Dusky","Brown","Chestnut","Violet","Bluebell","Blonde","Ash","Straw","Matt","Ghost","Snow","Wolf","Cherry","Rose","Possessed","AltPossessed","Kitsune","Harpy","DragonHarpy","Imp","Werewolf","CatKnight","Tiger","Sheep","Satyr","Fuschia"]
#BASEPLAYERCOLOURARRAY! Option1: Eye color; 0=brown 1=blue 2=green; Option2: Hair color; 0=black 1=brown 2=rose 3=blue;
const newcolorarrayeye = [Vector3(0.35,0,-0.13),Vector3(0,0,0),Vector3(0.65,0.1,0.05),Vector3(0.92,0,0.1),Vector3(0.08,0.8,0),Vector3(0.22,0.7,0.1),Vector3(0.33,0.6,0.2),Vector3(0,0,-1),Vector3(0,-0.6,0.2),Vector3(0.59,-0.2,-0.1),Vector3(0.41,-0.1,-0.1),Vector3(0.28,0.2,0.1)]
const newcolorarrayeyenames = ["Mahogany","Sapphire","Emerald","Ocean","Amethyst","Ruby","Jasper","Pitch","Stone","Olivine","Zircon","Kitsune"]
#BASEPLAYERCOLOURARRAY! Option2: Skin color;
const newcolorarrayskin = [Vector3(0,0,0),Vector3(0,0.1,-0.1),Vector3(0,0.15,-0.2),Vector3(0,0.25,-0.35),Vector3(0,-0.1,-0.3),Vector3(0,-0.1,0.1),Vector3(0.012,0.33,-0.19),Vector3(0.63,-0.17,-0.1),Vector3(0.04,-0.3,0.12),Vector3(0.015,-0.3,0.1),Vector3(0.05,-0.05,-0.1),Vector3(0.008,-0.26,-0.04),Vector3(0.975,-0.05,-0.136)]
const newcolorarrayskinnames = ["Fair","Flushed","Tan","Dark","Dusky","Pale","Kitsune","Imp","CatKnight","RamGirl","Werewolf","Harpy","DragonHarpy"]
#BASEPLAYERCOLOURARRAY! Option2: Top clothes color; also pants
const newcolorarrayclothes = [Vector3(0,1,1),Vector3(0.83,0.8,1.1),Vector3(0.39,0.4,0.8),Vector3(0.165,1.1,0.8),Vector3(0.63,0.7,0.7),Vector3(0.74,0.2,1.2),Vector3(0.22,0.3,1.5)]
const newcolorarrayclothesnames = ["Fuschia","Lavender","Military","Rustic","Swamp","Steel","CatKnight"]

#enum es{JUMP,SINK,FALL,RUN,CLIMB,RESIST,ABILITY,NEUTRAL,ENEMY,OBJECT,BLOCKED,NOMOVES,WAIT,PUSH,TALK,ATTACK,FLY,SWIM,SWIMCLIMB,SWIMJUMP,SWIMCLIMBJUMP,DROPOJECT}
var eswordarray = ["Jump","Sink","Fall","Run","Climb","Resist","Ability","Neutral","Enemy","Object","Blocked","Wait\nNo Moves","Wait","Wait+\nPush","Talk","Attack","Fly","Swim","Swim+\nClimb","Swim+\nJump","Swim+\nJump+\nClimb","Drop","Embrace"]
var veiltextdict = {
	"noreset":"You cannot reset the save slot (VAR1) as it has no data.",
	"reset":"You are about to reset the save slot (VAR1) to the start. Continue?",
	"touchreset":"You are about to reset the save slot (VAR1) to the start. Continue?\nPress 'Yes' 3 times to confirm.",
	"start":"This save file (save slot VAR1) has no data on it yet. Would you like to start from the beginning?",
	"exit":"Close the application?",
	"loading":"Now loading...",
	"resetdone":"Save has been reset.",
	"patreon":"This will open a link to the game's Patreon page:\nhttps://www.patreon.com/moncurse\n\nHelp keep the game going!\n",
	"subscribestar":"This will open a link to the game's Subscribestar page:\nhttps://subscribestar.adult/moncurse\n\nHelp keep the game going!\n",
	"discord":"This will open a link to the official MonCurse discord server.\nYou can report bugs or just talk about games there.\n",
	"translationtools":"Translation\nTools",
	"crashprevention":"The game closed prematurely.\nIf your device has limited RAM click 'Yes' to enter low-performance mode.\n\nYou can also change this in the Settings."
	}
var consentmessage = "The following game contains sexually explicit themes and content. Please ensure you are of legal age to access such content.\n\nThis game is in active development and may change over time. Characters shown are of legal age and legal consent.\n\nThis message will only show once."

var queststringarray = ["Explore Shrine","Defeat Rams","Go To Shrine","Return Home","Route A11","OR\n??? Rams","Cursed Bondage:\n Please Enemies"]
var namemissiondict = {4:"A Long Walk",
11:"WIP"}

var spec_enemy_text_array = ["Readied","Flinch(!)Immune"]

var muffle_array = PoolStringArray(["mhff","hh","ffhf","fnnh","afh","mnnmn","hfff","hmf","fhuu"])

enum {VILLAGE=0,FOXSHRINE=1,MAPSPRINGS=2,CHEST=3,HIDDENSPRINGS=4,RURAL=5,HIDDENRURAL=6,ROAD=7,FOG=8,PARISH=9,HOME5=10,ICON=100,MOUNTAIN=101,HILL=102,FOREST=103,GRASSLAND=104,RIVER=105,WETLANDS=106,DESERT=107} #also update on escalatev2
var areastringdict = {
	MOUNTAIN:"Mountains",
	HILL:"Hills",
	FOREST:"Forest",
	GRASSLAND:"Grassland",
	RIVER:"River",
	VILLAGE:"Fugitive's Hideaway",
	MAPSPRINGS:"Hotsprings",
	FOXSHRINE:"Fox's Shrine",
	WETLANDS:"Wetlands",
	DESERT:"Desert"
}
var shortareastringdict = {
	VILLAGE:"Village",
	MAPSPRINGS:"Hotsprings",
	FOXSHRINE:"Shrine",
	RURAL:"Outskirts",
	PARISH:"Parish",
	HOME5:"Ram's Home",
	"T1":"T1",
	"T2":"T2",
	"T3":"T3",
	"T4":"T4",
	"T5":"T5",
	"T?":"T?",
	"Blocked":"Blocked",
	"Confirm?":"Confirm?",
	"End Expedition":"End Expedition",
	"Retry This Tile":"Retry This Tile",
	"Go Back":"<< Go Back"
}

enum chooselabel{IGNORE,ACCEPT,CHOOSEONE,LASTSUNTIL,CLASS,FOXCHOOSETWO}
var chooselabelsarray = [
	"Ignore & Discard",
	"Accept & Continue",
	"Choose One Skill:",
	"Lasts until end of mission",
	"Class Skills:",
	"Fox-Girl - Choose Two Skills:",
]

var petalmenutext = ["Skip All","Skip Once","Settings","Go Back Once","Go Back All"]

var settingstextdict = {"Scrollcontainer/settings/cen0/VBoxContainer/CheckBox6":"Fullscreen (F11)",
	"Scrollcontainer/settings/cen0/VBoxContainer/CheckBox10":"Low RAM Compatibility Mode",
	"Scrollcontainer/settings/cen0/VBoxContainer/CheckBox8":"Faster Turns",
	"Scrollcontainer/settings/cen0/VBoxContainer/CheckBox2":"Camera Automatically Follows Player",
	"Scrollcontainer/settings/cen1/Mastervolume/volumelabel":"Master Volume: ",
	"Scrollcontainer/settings/cen2/Musicvolume/volumelabel":"Music Volume: ",
	"Scrollcontainer/settings/cen3/SFXvolume/volumelabel":"SFX Volume: ",
	"Scrollcontainer/settings/cen4/Ambiencevolume/volumelabel":"Ambience Volume: ",
	"Scrollcontainer/settings/qcontrol/quittomainmenu/mainmenu":"Quit to\n Main Menu",
	"Scrollcontainer/settings/fcontrol/forfeit/mainmenu":"Forfeit &\nReturn"
}

var tabinfodict = {
	"status":"Status",
	"statusinfo":"Explains debuffs and conditions.",
	"skills":"Skills",
	"skillsinfo":"Inventory is fully automatic. Right-click to flag items for deletion.",
	"settings":"Settings",
	"settingsinfo":"The game saves unlocks automatically. Leaving returns you to the village."}

enum customtext{FINISHNAME,ENTERNAME,FIVEONLY,FINISH,CHANGELATER,ON,OFF,RETURN,CONTENTSAVED,CONSENTWARNING,CONTENTNSFW,CONTENTFUTA,CONTENTBE,CONTENTPREG,CONTENTFAKEPREG,CONTENTUNBIRTH}
var customizationtextarray = ["Click here to finish - 5 Character names only.", #WARNING!!! Do not add anything to this without consulting CONTENTINPUT because it uses this array's size to set values
"ENTER_NAME",
"5 Character names only.",
"Finish",
"Customization can also be changed later.\nClick above when done.",
"On",
"Off",
"Return",
"Content preferences are shared between all save files.",
"Warning! This disables ALL game content.",
"R18 Consent\n/NSFW",
"Futa (Enemy)",
"Extreme Breast\n Expansion",
"Pregnancy (WIP)",
"Cumflation &\nIncubation",
"Unbirth &\nSoft Vore"]#don't forget to update customtext above


var corruptiondict = {}
var describecorruptiondict = {
"horns5":"Commands authority. Move-only skills deal damage.",
"eyes5":"Ram's makeup. Grants Draining Kiss.",
"top5":"Ram's clothes. Leaves the wearer exposed.",
"pants5":"Ram's stockings and thong.",
"ears3":"Tiger's hallmark. +1 Movement.",
"tail3":"Agile, +1 Jump. Vulnerable to beads.",
"armright3":"Swipe attack. Basic attacks can fumble against cat-girls.",
"ears4":"Crafty fox's hallmark. +1 Skill Hotbar Capacity.",
"tail4":"Large, brush-like fluffy tail. Scavenge +1 skill.",
"hair4":"The fox's symbol. Forces a certain hairstyle.",
"armright7":"Clumsy fluffy wings. -1 fall speed, gliding flight.",
"bust2":"A larger bust retains milk.",
"bust3":"An even larger bust retains more milk.",
"bust4":"An absurdly huge bust retains milk.",
"leg7":"Egg-bearing hips. Gain 1 turn of flight after jumping.",
"tail6":"Huge tail. Gain block from heavy attacks.",
"ears6":"Pettable ears. +2 vision, flinch immunity.",
"top6":"Tar that's both sticky and slippery.",
"armright6":"Swipe attack.",
"body8":"Too weak to use basic attacks. Reacquire Knight Shift.",
"wings8":"Impish wings. Fly when you use skills.",
"tail8":"Heal at the end of every sex scene.",
"leg8":"Always able to move like an imp.",
"backhorns7":"Skills that move and leave you on an enemy's space deal +1 damage.",
"backhorns8":"Skills that move and leave you on an enemy's space deal +1 damage.",
"leg6":"Taller beast's legs. Deal damage after falling.",
"face8":"Fae, elven ears. Corruption gives dirty thoughts.",
"colour":"Stained in a monster's colors. Prevents removal of corruption.",
"tail7":"Long draconic tail. Drops on dazed enemies, harpy conversion."
}
#var Shikamemorydict = {}
#var Qadesmemorydict = {}
#var RamGirlmemorydict = {}
#var Pitchmemorydict = {}
#var CatKnightmemorydict = {}
#"horns" values: 0 is NONE. Ram horns are 5, as per their race.

#var storedlossscene = -1
#CURRENT LOSS SCENE:
#0: Cat-girl placeholder
#1: Shika placeholder
#2: Pitch placeholder
#3: Ram-girl placeholder
#var storedlossscenearray = ["Cat-girl (Heat >5)","Fox-girl (POSSESSION >5)","Wolf-girl (Impact >5)","Ram-girl (HYPNOSIS >5)"]

var stagemapheld = false

var upperuiposition = -200
var autodiscardarrayinnate = []#for switching classes
var autodiscardarrayitem = []

var wolf_collar = false
var torchlight = 0
var sturdyboots = 0
var foxpendants = 0
var totalpaws = 0
var totalattackrangebuffs = 0
#var playerinsight = 0
#var pelts = 0
enum{CLIMBBUFF=-9,WOLFPELT=-10,COWBELL=-11,PAWS=-12,WISHRING=-13,FOXPENDANT=-14,NORMALPAWS=-15,BEADS=-16,ROPEGAG=-18,PIXIEDUST=-19}
var curseditemdict = {
	CLIMBBUFF:false,
	WOLFPELT:false,
	COWBELL:false,
	PAWS:false,
	WISHRING:false,
	NORMALPAWS:false,
	BEADS:false,
	ROPEGAG:false,
	PIXIEDUST:false
	}

var curseditemfiledict = {
	CLIMBBUFF:null,
	WOLFPELT:"pelt",
	COWBELL:"bell",
	PAWS:"armright3",
	NORMALPAWS:"armright3",
	BEADS:"beads",
	FOXPENDANT:"hair4",
	ROPEGAG:"ropegag",
	PIXIEDUST:null
	}

var curseditemstringdict = {
	CLIMBBUFF:"claw",
	WOLFPELT:"pelt",
	COWBELL:"bell",
	PAWS:"paw",
	WISHRING:"ring",
	FOXPENDANT:"symbol",
	NORMALPAWS:"paw",
	ROPEGAG:"ropegag",
	PIXIEDUST:"pixiedust"}

var curseditemstransname = {
	"Status":"Status: ",
	"Cursed Item":"Cursed Item: ",
	"bust2":"Bust: Medium",
	"bust3":"Bust: Large",
	"bust4":"Bust: Huge",
	BEADS:"Hypno-Beads",
	CLIMBBUFF:"Climb Claws",
	WOLFPELT:"Wolf Pelt",
	COWBELL:"Milkmaid's Bell",
	PAWS:"Soft Paws",
	WISHRING:"Wishing Ring",
	FOXPENDANT:"Fox's Symbol",
	NORMALPAWS:"Paws",
	ROPEGAG:"Bondage",
	PIXIEDUST:"Pixie Dust"}

var curseditemdescribedict = {
	BEADS:"Weakens the wearer when pulled, tied to user's tail.",
	CLIMBBUFF:"You gain 'Running' when ending your turn climbing.",
	WOLFPELT:"The Werewolf tracks you and inflicts Hypnosis.",
	COWBELL:"Generates milk when hit by FORCE. Alerts enemies.",
	PAWS:"Basic attack deals SWEET damage.",
	FOXPENDANT:"+1 choice of Skill on the map.",
	NORMALPAWS:"Basic attack hits three tiles.",
	ROPEGAG:"Only removed by satisfying monster-girls.",
	PIXIEDUST:"Remind the dev to add something here."}

#var haditems = PoolIntArray([])
var randomcursearray = [WOLFPELT,COWBELL,NORMALPAWS,FOXPENDANT]

#const indexMEETING = 0; const indexDAMAGETAKEN = 1; const indexMINISCENE = 2
#var conversationsrecorddict = {
#	"WereWolf":PoolIntArray([0,0,0]),
#	"FoxGirl":PoolIntArray([0,0,0]),
#	"CatKnight":PoolIntArray([0,0,0]),
#	"RamGirl":PoolIntArray([0,0,0]),
#	"Qades":PoolIntArray([0,0,0])
#	}


#var playerhealth = 5
#var playermaxhealth = 5

#var introchoices = PoolIntArray([0,0,0,0])
var describe_charm_dict = {0:"body",
1:"tits",
2:"huge tits",
3:"bare form",
4:"lewd outfit",
5:"sweet scent",
6:"sweaty body",
7:"sluttiness",
8:"lewd pose",
9:"shameless pasties",
10:"fat ass"}

var lewdness = 0.0
var lewddescriptor1 = describe_charm_dict[0]
var lewddescriptor2 = describe_charm_dict[0]
var lewddescriptor3 = describe_charm_dict[0]
func lewdness_calc():
	lewdness = -0.1
	lewddescriptor1 = describe_charm_dict[0]
	lewddescriptor2 = describe_charm_dict[0]
	lewddescriptor3 = describe_charm_dict[0]
	match int(playerbustsize):
		3:
			lewdness += 0.1
			lewddescriptor1 = describe_charm_dict[1]
		4:
			lewdness += 0.2
			lewddescriptor1 = describe_charm_dict[2]
	if playeroutfit[0] == 0:
		lewdness += 0.1
		lewddescriptor2 = describe_charm_dict[3]
	elif playeroutfit[0] == raceRAM:
		lewdness += 0.1
		lewddescriptor2 = describe_charm_dict[4]
	elif playeroutfit[0] == raceWOLF:
		lewdness += 0.15
		lewddescriptor2 = describe_charm_dict[9]
	elif curseditemdict[WOLFPELT] == true:
		lewdness += 0.035
		lewddescriptor2 = describe_charm_dict[5]
	if corruptiondict.has("leg"):
		lewdness += 0.05
		lewddescriptor3 = describe_charm_dict[10]
	if sealsarray[SEAL] > 0:
		lewdness += 0.05
		lewddescriptor3 = describe_charm_dict[6]
	if sealsarray[SLUT] > 0:
		lewdness += 0.05
		lewddescriptor3 = describe_charm_dict[7]
	if sealsarray[OBEY] > 0:
		lewdness += 0.05
		lewddescriptor3 = describe_charm_dict[8]
#	print("Lewdness calc:"+str(lewdness)+"described: "+lewddescriptor1+"..."+lewddescriptor2)

#var cannot_wear_pants = false
var knightjump = false
var jumpflight = false
var pseudoflight = false
var glideflight = false
var chargebuff = false
var hands = true
var flinchimmune = false
const BASEhandsize = 3
const BASEplayervision = 7
const BASEplayerswimspeedmax = 2
const BASEplayerfallspeedmax = 4
const BASEplayermaxjumpspeed = 2
const BASEplayermaxspeed = 4
const BASEplayermaxbasedamage = 1
#const BASEplayerdodgechance = [0,0,0,0,0,0,0]
func stat_calc():
#	cannot_wear_pants = false
	pseudoflight = false
	glideflight = false
	jumpflight = false
	knightjump = false
	flinchimmune = false
	handsize = BASEhandsize
	playerswimspeedmax = BASEplayerswimspeedmax
	playerfallspeedmax = BASEplayerfallspeedmax
	playermaxjumpspeed = BASEplayermaxjumpspeed
	playermaxspeed = BASEplayermaxspeed
	playermaxbasedamage = BASEplayermaxbasedamage
	playervision = BASEplayervision
	sturdyboots = 0
#	if nextlocation == Tutorial2:
#		playervision = 5
	if passivearray.find(-7) != -1:
		playervision += 2
	if passivearray.find(-6) > -1:
		for item in passivearray:
			if item == -6:
				sturdyboots += 1
	curseditemdict[PIXIEDUST] = false
	harpy_variants = false
	if corruptiondict.has("tail"):
		match corruptiondict["tail"]:
			raceNEKO: 
				playermaxjumpspeed += 1
				playerfallspeedmax += 1
#			raceKITSUNE:
#				handsize += 1
			raceHARPY:
				harpy_variants = true
#			raceIMP:
#				curseditemdict[PIXIEDUST] = true
#			raceWOLF:
#				flinchimmune = true
	if corruptiondict.has("backhorns"):
		match corruptiondict["backhorns"]:
			raceHARPY:
				harpy_variants = true
	if get_corruption("body") == raceIMP or curseditemdict[ROPEGAG] == true:# and curseditemdict[PAWS] == false:
		playermaxbasedamage = 0
	else:
		playermaxbasedamage = 1
#		attackmaxrange = 0
#	else:
	attackmaxrange = 1+totalattackrangebuffs
	match get_corruption("leg"):
		raceHARPY:
			jumpflight = true
		raceIMP:
			knightjump = true
		raceWOLF:
			sturdyboots += 1
#	if corruptiondict.has("leg") and corruptiondict["leg"] == raceHARPY:
#		cannot_wear_pants = true
#		playeroutfit[1] = 0
#		if corruptiondict["leg"] == raceHARPY:
#		jumpflight = true
#		cannot_wear_pants = true
#	elif CurrentClass == raceWOLF and corruptiondict.has("top") and corruptiondict["top"] == raceWOLF:
#		cannot_wear_pants = true
#		playeroutfit[1] = 0
#	else:
#		cannot_wear_pants = false
	if corruptiondict.has("wings") and corruptiondict["wings"] == raceIMP:
		pseudoflight = true
	if curseditemdict[ROPEGAG] == true:
		hands = false
	elif corruptiondict.has("armright") and corruptiondict["armright"] != raceHUMAN:
		match corruptiondict["armright"]:
			raceHARPY: 
				hands = false
				playerfallspeedmax -= 1
				glideflight = true
			_: hands = true
#			raceNEKO:
#				if curseditemdict[PAWS] == true:
#					hands = true
	else:
		hands = true
	if corruptiondict.has("ears"):
		match corruptiondict["ears"]:
			raceNEKO: playermaxspeed += 1
			raceWOLF: 
				playervision += 2
				flinchimmune = true
			raceKITSUNE:
				handsize += 1
	if corruptiondict.has("horns") and corruptiondict["horns"] == raceRAM:
		chargebuff = true
	else:
		chargebuff = false

var questcondition = 99
var questcondition2 = 0
var queststart = false #referenced in speechdicts

var handsize = 3
var playervision = 7
var playerswimspeedmax = 2
var playerfallspeedmax = 4
var playermaxjumpspeed = 2
var playermaxspeed = 4
var playermaxbasedamage = 1
#var playerdodgechance = [0,0,0,0,0,0,0]
var playershards = 0 #player money sorta
var playershardsmax = 30
var playerforage = 6 #playerfood from plants and stuff.
const REQUIREDFORAGE = 6
var attackmaxrange = 1 #if you want to increase this, add a check that looks at mapstar and compares it to the skystar ID and disallows the attack if they don't line UP.
#var corruptionstage = 0 #alternative to the old system. You simply have a set number of 'corruptions'.
#var rawcorruption = 0
#var rawcorruptiondict = {} #vector 2 array
var conversation_beads_check = false
const tempcorruptionbreakpoints = {"beads":[9,18,27]}
var tempcorruptiondict = {} #for stuff like small horns before they become bighorns
var playerresistance = 20
var maxresistance = 20
var playerexhaustrate = 0.2
var playerminimumexhaust = 5

#func corruptplayer(race,value): #also accepts negative values! just make sure to give a race.
#	print(rawcorruptiondict)
#	print(rawcorruption)
#	print("two values above are corruptiondict and corruption. Two values below are after processing.")
#	if rawcorruptiondict.has("race") == true:
#		if rawcorruptiondict.race + value < 0:
#			value = rawcorruptiondict.race
#			rawcorruptiondict.race = 0
#		else:
#			rawcorruptiondict.race = rawcorruptiondict.race+value
#	else:
#		if value < 0:
#			value = 0
#		else:
#			rawcorruptiondict[race] = value
#	rawcorruption += value
#	print(rawcorruptiondict)
#	print(rawcorruption)

var altfoxconvo = false
var awaitingconversation = ""

#BASEPLAYERCOLOURARRAY! Option1: Eye color; 0=brown 1=blue 2=green; Option2: Hair color; 0=black 1=brown 2=rose 3=blue;
var makeuprank = false
var possessionrank = false
var gameoverrank = false
enum pcol{RANK=-1,EYES=0,HAIR=1,SKIN=2,CLOTHES=3,PANTS=4}
#var mirrorplayercolourarray = [2,2,0,0,0]
var baseplayercolourarray = [2,2,0,0,0]
var truecolourarray = [2,2,0,0,0]#EYES, HAIR, SKIN, CLOTHES, PANTS
#var baseplayercolourdescriptiondict = {0:["Brown","Blue","Green","Yellow"],
#1:["Black","Brown","Rose","Blue"]}
var playerabdomensize = 1 #0 is barely anything, 1 is small, 2 is 'regular', 3 is big
var playerbustsize = 1 #0 is barely anything, 1 is small, 2 is 'regular', 3 is big
var playeroutfit = [1,1] #0 is nothing, 1 is fuschia. index 0 is top, index 1 is pants.
var outfithealth = 4


#var saved_color_2darray = [[],[],[],[],[]]

#var testtimer = 0 #used to work out how long something takes in code
#var debugmethod = 0
#var testbuild = false
#var teststart = LevelSelect1

var races = [-3]#not actually races anymore, it's now monsternum. #player's races for damage detection. -3 is the monstenrum of "player"
#race IDs:
#0 = Human, 1 = Naman, 2 = Cupid, 3 = Neko, 4 = Kitsune, 5 = Ram, 6 = Wolf
#DAMAGE IDs:
#0 = PURE, 1 = FORCE, 2 = PRECISION, 3 = SPIRIT, 4 = SWEET, 5 = taboo, 6 = ENERGY
#RACE REFERENCE ARRAY:
var racearray = ["Human","Naman","Cupid","Neko","Kitsune","Ram","Werewolf","Harpy","Imp"]
var raceclassarray = ["Lancer","Cow","Cupid","Tiger","Priestess","Bard","Lycan","Harpy","Knight"]
var bodypartdict = {
	"bust":"bust",
	"armright":"arms",
	"hair":"hair",
	"pants":"pants",
	"top":"top",
	"horns":"horns",
	"backhorns":"horns",
	"ears":"ears",
	"tail":"tail",
	"eyes":"eyes",
	"leg":"legs",
	"body":"body",
	"face":"face",
	"wings":"wings",
	"colour":"color"
}

enum{CATGIRL = 0, FOXGIRL = 1, WEREWOLF = 2, RAMGIRL = 3, GHOSTFOX = 4, DRAGONHARPY = 5, IMP = 6}#SCALE FOR EACH NEW ENEMY
const monsternumarray = ["CatKnight","FoxGirl","WereWolf","RamGirl","GhostFox","DragonHarpy","Imp"]
var monster_display_names = {"HP":"HP",
	CATGIRL:["Cat-Knight","Tiger-Knight"],
	FOXGIRL:["Fox-Girl","Kutsu"],
	WEREWOLF:["WereWolf","Pitch"],
	RAMGIRL:["Ram-Girl","Satyr","Rambo",],
	GHOSTFOX:["Spirit","Spirit"],
	DRAGONHARPY:["Harpy","Drake"],
	IMP:["Spada","Belle","Fleur","Acorn"]}#rank0 = spade, rank1 = diamond, rank2 = heart, rank3 = club
#var RaceToAllegieance = {0:[],
#1:[],
#2:[],
#3:[CATGIRL,RAMGIRL],
#4:[FOXGIRL,GHOSTFOX],
#5:[RAMGIRL,CATGIRL],
#6:[WEREWOLF]}

var spawnedarray = []
var elitespawnedarray = []
var impspawnedarray = [0,0,0,0]
#var racecolorarray = [Color8(245,245,245),Color8(245,230,255),Color8(255,230,240),Color8(235,245,200),Color8(245,200,150),Color8(255,255,210),Color8(150,150,200)]
const enemyracearray = [raceNEKO,raceKITSUNE,raceWOLF,raceRAM,raceKITSUNE,raceHARPY,raceIMP]#SCALE FOR EACH NEW ENEMY #ONLY use for the below stuff
var racecolorarray = [Color8(215,215,215),Color8(220,200,240),Color8(240,200,220),Color8(220,220,160),Color8(220,160,140),Color8(240,240,180),Color8(140,140,180),Color8(240,150,190),Color8(210,180,240)]
#var racecolorarray = [Color8(200,200,200),Color8(200,170,230),Color8(230,170,200),Color8(170,200,140),Color8(200,140,110),Color8(230,230,140),Color8(110,110,140)]
var RANKhsv = [Vector3(0.85,0.75,1.25),Vector3(0.7,0.65,1.1),Vector3(0.4,1,0.05),Vector3(0.93,1.12,1.12),Vector3(0.9,0.7,1.1),Vector3(1,1,1),Vector3(1,1,1)]#CATGIRL,FOXGIRL,WOLFGIRL,RAMGIRL,GHOSTFOX,DRAGONHARPY,IMP
#var IMPRANKhsv = [Vector3(1,1,1),Vector3(0.47,1,1),Vector3(0.15,1,1),Vector3(0.7,1,1)]#rank0 = spade, rank1 = diamond, rank2 = heart, rank3 = club
var IMPRANKhsv = [Vector3(0,1,1),Vector3(0.14,1,1),Vector3(0.07,1,1),Vector3(0.93,1,1)]#rank0 = spade, rank1 = diamond, rank2 = heart, rank3 = club
var specialramhsv = Vector3(0.1,0.6,1.0)
var spec_ram_var = 0.0
#var racemultiplierarray = [0,0,0,0,0,0,1] #Werewolves have an innate multiplier.
#var racemultiplierarray = [2,2,2,2,2,2,4]
var racecorruptionarray = [0,0,0,0,0,0,0,0,0]#SCALE FOR EACH NEW ENEMY #scales from 0 to 100, returns nearest 1 values to 10 for bar?
var bonusracecorruptionarray = [0,0,0,0,0,0,0,0,0]#so dragonharpy for example can have separate bars for tail and color
#var racelockcorruptionarray = [false,false,false,false,false,false,false,false,false] #SCALE FOR EACH NEW ENEMY
#note that 'NAMAN' corruption is just breast size.
#TYPE REFERENCE ARRAY:
#var mapshader_hsvs = [Vector3(0.0,1.0,1.0),Vector3(0.85,0.8,1.2),Vector3(0.85,0.7,1.0),Vector3(0.0,1.0,1.0)]
#var mapshader_hsvs = [Vector3(0.92,0.4,0.97),Vector3(0.85,0.8,1.2),Vector3(0.85,0.7,1.0),Vector3(0.0,1.0,1.0)]
var mapshader_hsvs = [Vector3(0.92,0.5,0.97),Vector3(0.92,0.5,0.97),Vector3(0.85,0.7,1.0),Vector3(0.0,1.0,1.0),Vector3(0.0,1.0,1.0)]
enum {NEUTRAL,MOUNTAINHSV,PLAINSHSV,FORESTHSV,DESERTHSV}
var mapshader_current = NEUTRAL

var typecolorarray = [Color8(166,255,245),Color8(255,116,116),Color8(252,153,95),Color8(92,122,255),Color8(255,104,194),Color8(212,207,183),Color8(255,213,92)]

var debuffcolorarray = [Color8(186,210,227),Color8(255,142,252),Color8(0,172,200),Color8(186,172,145),Color8(111,180,255),Color8(254,254,197),Color8(230,82,92),Color8(117,117,117),Color8(220,220,240)]
var debuffcolornegativearray = [Color8(224,197,107),Color8(84,77,73)]

enum{retainedMILK}
var retaineddebuffarray = [0]

enum{CAON = 0,BUFFER1 = 1, BUFFER2 = 2}
var resourcearray = [0,0,0]
func change_resource(resourcenum,quantity):
	resourcearray[resourcenum] += quantity
	saveprogress()
#	currentsave.set_value("Resources","Caon",resourcearray[resourcenum])
#	currentsave.save(savefile1)

var player_harpied_count = 0
#var auto_abdomen_size = 0
var player_holding_enemy = false
var abdomen_mark = 0
var mark_power = 0
const affects_belly_array = [204,206]
func calculate_abdomen():
	var startsize = playerabdomensize
	abdomen_mark = raceHUMAN
	mark_power = 0.1
	var start_power = mark_power
	var sizevalue = 0#auto_abdomen_size
	if player_holding_enemy == true:
		sizevalue = 40
	if prefdict[pref.PSUEDOPREGNANCY] == true:
		for item in playerinventory2darray+playerconsumables2darray:
			if item[0] in affects_belly_array:
				if item.size() > 3:
					match item[0]:
						204:
							sizevalue += item[3]
							abdomen_mark = raceKITSUNE
							mark_power += 0.1*item[3]
						205:
							abdomen_mark = raceKITSUNE
							mark_power += 0.1*item[3]
						206:
							sizevalue += 1.5*item[3]
#					if item[0] == 204:
#						sizevalue += item[3]
#					elif item[0] == 206:
#						sizevalue += 1.5*item[3]
				else:
					sizevalue += 2
	if sizevalue > 21:
		sizevalue = (sizevalue-21)*0.5 + 21
	playerabdomensize = int(clamp(sqrt(sizevalue+1.5),1,5))
	#using eggs and above method:2=2EGG,3=5EGG,4=10EGG,5=18EGG
#	if sizevalue > 10:
#		sizevalue = 10 + (sizevalue-10)*0.44
#	playerabdomensize = int(clamp(1+(sizevalue*0.34),1,5))
	if playerabdomensize != startsize:
		get_tree().call_group("Abdomen","on_abdomen_size_changed",true,playerabdomensize > startsize)
	if mark_power != start_power:
		get_tree().call_group("Abdomen","abdomen_mark_case",abdomen_mark,mark_power)

const notldebuffidentifyarray = ["Drowsy","Hypnosis","Sweaty","Cum","Possession","Shredded","Heat","Impact","Milk"]
var debuffidentifyarray = ["Dazed","Hypnosis","Sweaty","Cum","Possession","Shredded","Heat","Impact","Milk"]
var debuffblankarray = []
var debufftriggerarray = []
#var debuffcaparray = [10,99,10,10,10,99,]
const notldebuffidentifynegativearray = ["Flinch","Knockback"]
var debuffidentifynegativearray = ["Flinch","Knockback"]
var debuffblanknegativearray = []
#var debuffdescriptionarray = ["Feeling sleepy. Each stack reduces all standard movement by 1. Doesn't last long.",
#"At a certain threshhold, become compelled to approach and give in.",
#"Worked up, prevents recovery from other debuffs. Water helps.",
#"Unable to use consumable items.",
#"Any movement that does not target the associated shrine's grounds is invalid.",
#"Clothes are shredded until the next scene.",
#"Flushed and hazy, poison taken is doubled and abilities become sealed.",
#"Sheer force has you dazed. You may find yourself unable to act.",
#"Provides food over time. Incredibly suspicious."]
var debuffdescriptionarray = ["Drowsy, movement is halved.",
"At risk of becoming obedient.",
"Can't recover from other conditions.",
"Gummed up. Cannot use consumables.",
"Knowledge and movement are that of the Kitsune's.",
"Damages clothes.",
"May restrict the usage of Innate skills.",
"At risk of becoming stunned.",
"Adds weight to one's chest."]
#Any movement that does not target the associated shrine's 

var tier = -1 #next tier
var nextlocation = 1 #determines what method of map generation is used for the next main scene
#var nextlocation = 1 setget debug_nextlocation_set, debug_nextlocation_get #determines what method of map generation is used for the next main scene
#func debug_nextlocation_get():
#	return nextlocation
#func debug_nextlocation_set(set):
#	print("DEBUG, nextlocation set to :"+str(nextlocation))
#	nextlocation = set

var nextexit = 0 #for multi-entrance scenes
enum {Village,ForestZone,ShopPath,SunsetZone,NightZone,Home,Gallery,Tutorial1,Tutorial2,Tutorial1s1,Tutorial1s2,Tutorial2s1,Tutorial2s2,Tutorial3s1,Tutorial3s2,Tutorial3s3,LevelSelect1,Mission4,Springs,Shrine,DarkRoom,RouteA11,NewVillage,WolfCombat,ImpDebug,Graveyard,RamHome,GlassedDesert}
#const ForestZone = 1;const BlastedVillage = 0;const ShopPath = 2;const SunsetZone = 3;const NightZone = 4
#const Home = 5; const Gallery = 6; const Tutorial1 = 7; const Tutorial2 = 8; const 
const fixedscenedict = {Home:"playerhome",Gallery:"gallery",Tutorial1:"tutorialmovement",Tutorial2:"tutorialability",
#const fixedscenedict = {Home:"debugmap",Gallery:"gallery",Tutorial1:"tutorialmovement",Tutorial2:"tutorialability",
Tutorial3s1:"tutorialmission3s1",Tutorial3s2:"tutorialmission3s2",Tutorial3s3:"tutorialmission3s3",
Tutorial1s1:"tutorialmission1s1",Tutorial1s2:"tutorialmission1s2",
Tutorial2s1:"tutorialmission2s1",Tutorial2s2:"tutorialmission2s2",
Village:"village",LevelSelect1:"levelselect1",NewVillage:"newvillage",WolfCombat:"wolfcombat",
Springs:"hotsprings",Shrine:"foxshrine",
DarkRoom:"darkroom",
ImpDebug:"impcontainment",Graveyard:"graveyard",
RamHome:"ramhome"}
const safezones = [Shrine,RamHome]#areas where you aren't supposed to attack or get items or whatever

var escalationweight = {}
var escalationdict = {} #{"Map Width":0, #you need to put ALL of these on 'resetvariables'...
#"Map Depth":0,
#"Map Water":0,
#"Cat Rank":0,
#"Enemy Cat":0,
#"Fox Rank":0,
#"Enemy Fox":0,
##"wolfgirlrank":0,
##"wolfgirlfrequency":0,
#"Ram Rank":0,
#"Enemy Ram":0,
#"Hazard Cum":0}
##or, this entire time, we could've just had it as part of the master node that is instanced in and out.

var kissflipflop = 0

var stagedescriptor = "Forest"
var stagenum = 0
#var finalstage = false
var laststagemessages = PoolStringArray([])
var lastzoom = Vector2(1,1)
var lastconsumableitem = 0

var retain_deck_array = []
var playerinventory2darray = []
var playerconsumables2darray = []
#var playerinnate2darray = []
var passivearray = []

#var timestalkedtoshika = 0
#var timesdefeatedshika = 0
var totalplayermoves = 0 #how many times ever the player has moved

enum{SLUT = 0,SEAL = 1,OBEY = 2}
var sealsarray = [0,0,0] #similar to debuffarray?
var sealplacearray = [-1,-1,-1]

enum{NOSTUN = 0,KISSLOCKED,MILKLOCKED,ARMLOCKED,CATLOCKED,PUSSYLOCKED,RAMLOCKED,FOXLOCKED,BUSTLOCKED,HARPYLOCKED,PITCHLOCKED,SELFLOCKED,SCISSORLOCKED,IMPLOCKED,MUGLOCKED}
var playerstunstate = NOSTUN
#var playerstatedict = {
##"Shredredded":0, #0 means not shredded 1 means shredded
#"Stunlocked":NOSTUN, #cannot take actions enemies do not take actions counts down until player is free
#}

var lastloadedvariables = 0
var batterysaver = false
var consent = true
var indicators = true
var camerafollow = true
var descriptions = false
#var transparentselectors = false
var reducelowergui = false
#var seenpitch = false
var fullscreen = false
var enemyidleanimations = false
#var firstload = false
var homefirstload = false
#var askconsent = false
func initiate_game(): #formerly _ready()
#	resetvariables()
#	Move205["name"] = "Fox Seal"
#	Move205["info"] = "Residual corruption from a Fox-girl."
	p_file_check()
	for _i in range(debuffidentifyarray.size()):
		debuffblankarray.append(0)
	for _i in range(debuffidentifynegativearray.size()):
		debuffblanknegativearray.append(0)
	if config.get_value("Volume","Master",-1) == -1:
		config.set_value("Volume","Master",50)
		config.set_value("Volume","Music",30)
		config.set_value("Volume","SFX",50)
		config.set_value("Volume","Ambience",30)
		Mastervolume = 50
		Musicvolume = 30
		SFXvolume = 50
		Ambiencevolume = 30
		config.set_value("Checkboxes","Consent",true)
		indicators = true
		config.set_value("Checkboxes","Indicators",true)
		indicators = true
		config.set_value("Checkboxes","Camerafollow",true)
		camerafollow = true
		config.set_value("Checkboxes","Descriptions",false)
		descriptions = false
		config.set_value("Checkboxes","Selectors",false)
#		transparentselectors = false
		config.set_value("Checkboxes","LowerGUI",false)
		reducelowergui = false
		config.set_value("Checkboxes","Fastmode",false)
		gameslowness = gameslownessdefault
#		config.set_value("Checkboxes","Batterysave",false)
		batterysaver = false
		config.set_value("Tutorial","Tutorialscleared",0)
		tutorialscleared = 0
#		config.set_value("Encounters","Pitch",false)
		for key in prefdict:
			config.set_value("Preferences",str(key),true)
			prefdict[key] = true
#		print("Error loading config file, using defaults.")
		config.set_value("SaveFiles","Lastsave",1)
		config.set_value("SaveFiles","Lastconvo",0)
		
		config.set_value("Translation","Lasttranslation",TL_DEFAULT)
		
		config.set_value("Crash","Loadedvariables",0)
		lastloadedvariables = 0
		
		totalprefs = config.set_value("Preferences","Sum",prefdict.keys().size())
		
		last_load_convo = 0
		currentsavenum = 1
		config.save(save_path)
		get_node("/root/Master").show_disclaimer(true)
	else:
#	Mastervolume = config.get_value("Checkboxes","Askconsent",true)
		Mastervolume = config.get_value("Volume","Master",50)
		Musicvolume = config.get_value("Volume","Music",30)
		SFXvolume = config.get_value("Volume","SFX",50)
		Ambiencevolume = config.get_value("Volume","Ambience",30)
		consent = config.get_value("Checkboxes","Consent",true)
#		indicators = config.get_value("Checkboxes","Indicators",true)
		indicators = true
		camerafollow = config.get_value("Checkboxes","Camerafollow",true)
		descriptions = false
		current_translation_code = config.get_value("Translation","Lasttranslation",TL_DEFAULT)
#		descriptions = config.get_value("Checkboxes","Descriptions",false)
		if config.get_value("Checkboxes","Fastmode",false) == true:
			gameslowness = 0
		else:
			gameslowness = gameslownessdefault
#		if touchscreenmode == true:
#			batterysaver = config.get_value("Checkboxes","Batterysave",false)
#		else:
		batterysaver = false
		last_load_convo = config.get_value("SaveFiles","Lastconvo",0)
#		if batterysaver == true:
#			set_battery_saver_size(true,true)
#		seenpitch = config.get_value("Encounters","Pitch",false)
#		transparentselectors = config.get_value("Checkboxes","Selectors",false)
#		transparentselectors = false
#		reducelowergui = config.get_value("Checkboxes","LowerGUI",false)
		reducelowergui = false
#		baseplayercolourarray = config.get_value("Playerinfo","colours",baseplayercolourarray)
		lastloadedvariables = config.get_value("Crash","Loadedvariables",0)

		currentsavenum = config.get_value("SaveFiles","Lastsave",1)
		tutorialscleared = config.get_value("Tutorial","Tutorialscleared",0)
		totalprefs = config.get_value("Preferences","Sum",0)
		if totalprefs < prefdict.keys().size() and tutorialscleared >= 3:
			awaitingconversation = "qadescontentwarning"
		for key in prefdict:
			if key == 6:
				if prefdict[5] == false or prefdict[4] == false:
					prefdict[key] = config.get_value("Preferences",str(key),false)
				else:
					prefdict[key] = config.get_value("Preferences",str(key),true)
			else:
				prefdict[key] = config.get_value("Preferences",str(key),true)
#		playername = config.get_value("Playerinfo","Name",playername)
#		fullscreen = config.get_value("Checkboxes","Fullscreen",false)

		get_node("/root/Master").show_disclaimer(false,true)
	AudioServer.set_bus_volume_db(Masterbus,linear2db(float(Mastervolume)/100))
	AudioServer.set_bus_volume_db(Musicbus,linear2db(float(Musicvolume)/100))
	AudioServer.set_bus_volume_db(SFXbus,linear2db(float(SFXvolume)/100))
	AudioServer.set_bus_volume_db(Ambiencebus,linear2db(float(Ambiencevolume)/100))
	loadprogress(currentsavenum) #make sure to CORRUPT FROM STORED after load progress
	if bit_to_array(Endings,EndingsRef.FOXGIRL,8)[2] == true and Home and StagesCleared[StageRef.TUTORIAL] < tutorialstages.SHIKATALK:
		altfoxconvo = true
	else:
		altfoxconvo = false
	corruptiondict = corrupt_from_stored(StoredTransformations,corruptiondict)
	if current_translation_code != TL_DEFAULT:
		if current_translation_code.find("internalmods") > -1:
			find_internal_mods()
		else:
			find_mods()
		if orderdictionary.has(current_translation_code):
			translate_game(current_translation_code)
		else:
			current_translation_code = TL_DEFAULT
			savevalue("Translation","Lasttranslation",TL_DEFAULT)
#	if currentsave.get_value("Resources","Caon",-99) == -99:
#		currentsave.set_value("Resources","Caon",0)
#		currentsave.save(savefile1)
#	else:
#		resourcearray[CAON] == currentsave.get_value("Resources","Caon",resourcearray[CAON])

func set_battery_saver_size(_truefalse,_nowarning=false):
	return
#	var sizescreen = OS.get_screen_size()
#	var newscreen = sizescreen
#	if truefalse == true:
#		if nowarning == false:
#			get_node("/root/Master").call("fullscreentest")
#		if sizescreen.x < 1300:
#			newscreen = newscreen*0.65
#		else:
#			newscreen = newscreen*0.5
#		if newscreen.x < 600:
#			newscreen.x = 600
#		if newscreen.y < 350:
#			newscreen.y = 350
#		print(sizescreen)
#		print(newscreen)
#		ProjectSettings.set_setting("display/window/size/viewport_width", floor(newscreen.x))
#		ProjectSettings.set_setting("display/window/size/viewport_height", floor(newscreen.y))
#		ProjectSettings.set_setting("display/window/stretch/mode",2)
#		ProjectSettings.set_setting("display/window/stretch/aspect",1)
#	else:
#		ProjectSettings.set_setting("display/window/stretch/mode",0)
#		ProjectSettings.set_setting("display/window/stretch/aspect",0)
#	print(OS.get_window_size())
##	OS.set_window_size(newscreen)
#	get_viewport().set_size_override(true, newscreen)
#	print(ProjectSettings.get_setting("display/window/stretch/mode"))
#	print(ProjectSettings.get_setting("display/window/stretch/aspect"))
#	batterysaver = truefalse
##	savevalue("Checkboxes","Batterysave",batterysaver)

const typearray = ["Pure","Force","Precision","Spirit","Sweet","Bitter","Energy"]
const PURE = 0; const FORCE = 1; const PRECISION = 2; const SPIRIT = 3; const SWEET = 4; const BITTER = 5; const ENERGY = 6
#[MOVEID/INVENTORYID/TYPE(1=item,2=innate,3=consumable)/USESLEFT or other counter]
#going to list custom mvoes now as dictionaries

enum movetypes{REQUIREDMOVES,REDUCEDATTACKCOUNT,DAMAGE,DIRECTIONS,DAMAGETYPE,RUNNING}
#Types currently are: [REQUIREDMOVES,REDUCEDATTACKCOUNT,DAMAGE,DIRECTIONS,DAMAGETYPE,RUNNING] currently 2 and 8 directions supported
const NOCRASH = -1; const NOREQUIREDMOVES = 0
const ONEDIR = 1; const TWODIR = 2; const FOURDIR = 4; const FOURDIAGONALDIR = 5; const EIGHTDIR = 8; const EIGHTDIAGONALDIR = 9;
enum rarity{COMMON=0,RARE=1,SUPER=2,ULTRA=3}
const raritycolorsarray = [Color(1,1,1,0.4),Color(0.4,0.4,1,0.7),Color(0.8,0.2,0.6,0.9),Color(1.0,0.6,0.2,1.0)]
#Specials currently are: [MODIFIER(HEAVY/FAST/DEPLOYABLE/etc),deployableID,MODIFIER2,self-buff,SFXIDexists currently 1 or 0)
enum specialtypes{MODIFIER,DEPLOYABLEID,MODIFIER2,SELFBUFF,SFX}
const DID = 0
const NOSFX = 0; const SFXISNAME = 1; const SFXMULTI = 2;
#Modifier1: 1 = Warp
#Modifier1: 2 = Heavy
#Modifier1: 3 = Fast
#Modifier1: 4 = Deployable
#Modifier1: 5 = Recovery (ticks DOWN debuffs)
#Modifier1: 6 = Block (provide blocking points though)
#Modifier1: 7 = de-self-runs you (you lose running if you use it)
const WARP = 1; const HEAVY = 2; const FAST = 3; const DEPLOYABLE = 4; const RECOVERY = 5; const BLOCK = 6; const CONSUMERUN = 7; const ECHO = 8
#-----
#Modifier2: 1 = Juggernaut/SWEEP (wind-UP) (crops move spaces until it reaches the desired height)
#Modifier2: 2 = Ramcharge (Buffs dash to hurt ALL in its path)
#-----
#Self-Buff: 1 = Windstep
#Self-Buff: 2 = Sense
#Self-Buff: 3 = Shika's KNOCKBACK buff
#-----
const SB = 0; const WINDSTEP = 1; const SENSE = 2; const DBNEXTATTACK = 3; const DRAIN = 4; const CLIMB = 5; const DRAINHEAL = 6;
enum describe{WINDSTEP,SENSE,EXTRAKNOCKBACK,DRAIN,DRAINHEAL,FAST,HEAVY,RECOVERY,ECHO,BLOCK}
var icondescribearray = [
"Windstep: Fall speed becomes 0.",
"Sense: Reveals enemy locations.",
"ExtraKnockback: Next attack gains knockback.",
"Drain: Grants effect on hit.",
"Drainheal: Grants health on hit.",
"Fast: Doesn't end the turn.",
"Heavy: Effect delayed until after enemy turn.",
"Recovery: Counts down your debuffs.",
"Echo: Repeats for each same-type hotbar skill.",
"Block: Temporary shield vs. damage and debuffs."
]
#enum {M2, WINDUP, CHARGE} <---- this is another way you can do it but it's just harder to look back at, I guess?
const M2 = 0; const WINDUP = 1; const CHARGE = 2; const ATTACKWINDUP = 3; const SCENETRIGGER = 4; const REMOVESEAL = 5;
#BLOCKINGNUM:Block duration.
#BLOCK: VECTOR3(DIRECTION,TYPE,DURABILITY) -- direction is 0 (RIGHT) 1 (DOWN) 2 (LEFT) 3 (UP) 4 (MOUSE direction) 5 (ALL)
const RIGHT = 0; const DOWN = 1; const LEFT = 2; const UP = 3; const MOUSE = 4; const ALL = 5
#NEW system for debuffs. Vector2(Identifier,Strength).
#Moves do not need to have CURSE but it will check for them.
#SELF-BUFFs currently are: 1 = WINDSTEP, 2 = SENSE, 3= some sort of shika reaction buff;
#MISC DEBUFFS that aren't like sticky debuffs.
#-1: Flinch. This is inflicted by the player's basic attack and prevents an enemy from using their reaction attack.
#-2: Knockback. Uses the ID of the attacker and moves the attacked away from them.
const FLINCH = -1; const KNOCKBACK = -2
#DEBUFFs currently are: 1 = DROWSY, 2 = HYPNOSIS, 3 = Sweaty
#1. DROWSY: Slows movement by 1 per stack.
#2. HYPNOSIS. Character moves towards the inflictor.
#3. Sweaty. Prevents other debuffs from ticking DOWN.
#4? Cum. Can't use consumables.
#5? Kitsune POSSESSION. Cannot leave shrine area.
#6? Marked. Makes tracking you easier.
#7? Heat. Skill-seals your abilities.
#8? Impact. Stuns you.
const DROWSY = 1; const HYPNOSIS = 2; const SWEATY = 3; const CUM = 4; const POSSESSION = 5; const MARKED = 6; const HEAT = 7; const IMPACT = 8; const MILK = 9
var debuff_capped_array = PoolIntArray([1,4,5])
enum{ITEM=1,INNATE=2,CONSUMABLE=3,CURSE=4}

var Move18 = {"name":"Explosive",#ITEM ability
"slot":CONSUMABLE,
"info":"Delayed, affected by gravity.",
"types":[NOREQUIREDMOVES,1,0,EIGHTDIR,FORCE,rarity.RARE],
"special":[[DEPLOYABLE,HEAVY],5,SB,M2,SFXISNAME], #4 indicates a DEPLOYABLE item that only attacks its first contact. "1" is the ID of the TORCH object.
#"directiontype":[Vector2(0,3),Vector2(0,2),Vector2(0,3),Vector2(0,2),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,2)], #X = move, Y = attack, 1 starts at top-LEFT
"debuff":[Vector2(FLINCH,1)],
"1a":[Vector2(0,0),Vector2(-1,-1),Vector2(-2,-1),Vector2(-3,0)],
"1m":[],
"2a":[Vector2(0,0),Vector2(0,-1),Vector2(0,-2)],
"2m":[],
"3a":[Vector2(0,0),Vector2(1,-1),Vector2(2,-1),Vector2(3,0)],
"3m":[],
"4a":[Vector2(0,0),Vector2(1,0),Vector2(2,0),Vector2(3,1)],
"4m":[],
"5a":[Vector2(0,0),Vector2(1,1),Vector2(2,2),Vector2(2,3)],
"5m":[],
"6a":[Vector2(0,0),Vector2(0,1),Vector2(0,2),Vector2(0,3)],
"6m":[],
"7a":[Vector2(0,0),Vector2(-1,1),Vector2(-2,2),Vector2(-2,3)],
"7m":[],
"8a":[Vector2(0,0),Vector2(-1,0),Vector2(-2,0),Vector2(-3,1)],
"8m":[]}
var Move17 = {"name":"Shortbow",#INNATE ability (female MC)
#"slot":INNATE,
"info":"",
"types":[NOREQUIREDMOVES,1,2,EIGHTDIR,PRECISION,rarity.COMMON],
#"directiontype":[Vector2(3,4)],
"special":[[DEPLOYABLE],DID,SB,ATTACKWINDUP,SFXISNAME], #identifier for special cases. Right now, 1 means you are WARPING, not jumping. 0 means no specials.
"debuff":[],
"3a":[Vector2(2,-2),Vector2(3,-3),Vector2(4,-4),Vector2(5,-5)],
"3m":[],
"4a":[Vector2(3,0),Vector2(4,0),Vector2(5,0),Vector2(6,0),Vector2(7,0)],
"4m":[],
"5a":[Vector2(2,2),Vector2(3,3),Vector2(4,4),Vector2(5,5)],
"5m":[],
"6a":[Vector2(0,3),Vector2(0,4),Vector2(0,5),Vector2(0,6),Vector2(0,7)],
"6m":[],
"8a":[Vector2(-3,0),Vector2(-4,0),Vector2(-5,0),Vector2(-6,0),Vector2(-7,0)],
"8m":[],
"7a":[Vector2(-2,2),Vector2(-3,3),Vector2(-4,4),Vector2(-5,5)],
"7m":[],
"2a":[Vector2(0,-3),Vector2(0,-4),Vector2(0,-5),Vector2(0,-6),Vector2(0,-7)],
"2m":[],
"1a":[Vector2(-2,-2),Vector2(-3,-3),Vector2(-4,-4),Vector2(-5,-5)],
"1m":[]}
var Move16 = {"name":"Struggle",#CONSUMABLE ability
"slot":CONSUMABLE,
"uses":0,
"info":"Appears if you cannot use pitons. Climb surfaces.",
"types":[NOREQUIREDMOVES,1,0,EIGHTDIR,PURE,rarity.COMMON],
"special":[[],DID,CLIMB,M2,NOSFX], #4 indicates a DEPLOYABLE item that only attacks its first contact. "2" is the ID of the PITON object.
"debuff":[],
"selfdebuff":[Vector2(FLINCH,2),Vector2(DROWSY,2)],
#"directiontype":[Vector2(0,3),Vector2(0,2),Vector2(0,3),Vector2(0,2),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,2)], #X = move, Y = attack, 1 starts at top-LEFT
"1m":[Vector2(-1,-1)],
"1a":[],
"2m":[Vector2(0,-1)],
"2a":[],
"3m":[Vector2(1,-1)],
"3a":[],
"4m":[Vector2(1,0)],
"4a":[],
"5m":[Vector2(1,1)],
"5a":[],
"6m":[Vector2(0,1)],
"6a":[],
"7m":[Vector2(-1,1)],
"7a":[],
"8m":[Vector2(-1,0)],
"8a":[]}
var Move15 = {"name":"Pickaxe",
#"slot":ITEM,
"info":"",
"types":[NOREQUIREDMOVES,2,3,EIGHTDIR,FORCE,rarity.RARE],
#"directiontype":[Vector2(0,3)],
"special":[[HEAVY],DID,SB,M2,SFXISNAME], #2 means the attack is SLOW, and will take place after the turn ends and enemies move
"debuff":[],
"1a":[Vector2(-1,-1),Vector2(0,-1)],
"1m":[],
"2a":[Vector2(0,-1),Vector2(1,-1)],
"2m":[],
"3a":[Vector2(1,-1),Vector2(1,0)],
"3m":[],
"4a":[Vector2(1,0),Vector2(1,1)],
"4m":[],
"5a":[Vector2(1,1),Vector2(0,1)],
"5m":[],
"6a":[Vector2(0,1),Vector2(-1,1)],
"6m":[],
"7a":[Vector2(-1,1),Vector2(-1,0)],
"7m":[],
"8a":[Vector2(-1,0),Vector2(-1,-1)],
"8m":[]}
var Move14 = {"name":"Scimitar",#INNATE ability
#"slot":ITEM,
"info":"",
"types":[NOREQUIREDMOVES,1,1,FOURDIR,PRECISION,rarity.COMMON],
"special":[[FAST],DID,SB,M2,SFXISNAME], #3 indicates a 'FAST' attack, does not end turn.
#"directiontype":[Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1)], #Move1,Attack1,Move2,Attack2
"debuff":[],
"1a":[Vector2(1,-1),Vector2(2,-1),Vector2(2,-2)],
"1m":[],
"2a":[Vector2(1,1),Vector2(1,2),Vector2(2,2)],
"2m":[],
"3a":[Vector2(-1,1),Vector2(-2,1),Vector2(-2,2)],
"3m":[],
"4a":[Vector2(-1,-1),Vector2(-1,-2),Vector2(-2,-2)],
"4m":[]}
var Move13 = {"name":"Reposition",#INNATE ability (male MC)
#"slot":INNATE,
"info":"",
"types":[1,1,0,EIGHTDIR,PURE,rarity.COMMON],
"special":[[FAST],DID,SB,M2,SFXISNAME], #7 indicates 'eats run' which means run is set to 0
#"directiontype":[Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1)], #Move1,Attack1,Move2,Attack2
"debuff":[],
"1a":[],
"1m":[Vector2(-1,-1)],#,Vector2(-2,-2)],
"2a":[],
"2m":[Vector2(0,-1)],#,Vector2(0,-2)],
"3a":[],
"3m":[Vector2(1,-1)],#,Vector2(2,-2)],
"4a":[],
"4m":[Vector2(1,0)],#,Vector2(2,0)],
"5a":[],
"5m":[Vector2(1,1)],#,Vector2(2,2)],
"6a":[],
"6m":[Vector2(0,1)],#,Vector2(0,2)],
"7a":[],
"7m":[Vector2(-1,1)],#,Vector2(-2,2)],
"8a":[],
"8m":[Vector2(-1,0)]}#,Vector2(-2,0)]}
var Move12 = {"name":"Buckler",#ITEM ability
#"slot":ITEM,
"info":"",
"types":[NOREQUIREDMOVES,0,0,FOURDIR,FORCE,rarity.COMMON],
"special":[[BLOCK,FAST],DID,SB,M2,SFXISNAME],
#"blocknum":2,
#"block":[[MOUSE,FORCE,4],[MOUSE,PRECISION,4],[MOUSE,BITTER,2],[MOUSE,ENERGY,2]],
"block":[[ALL,PURE,2]], #temporary, I guess
"debuff":[],
"1m":[],
"1a":[],
"2m":[],
"2a":[],
"3m":[],
"3a":[],
"4m":[],
"4a":[]} #special[3] = 1 is the WIND'S STEP buff which prevents you from falling that same turn.
var Move11 = {"name":"Windstep",#INNATE ability
#"slot":INNATE,
"info":"",
"types":[NOREQUIREDMOVES,0,0,ONEDIR,PURE,rarity.COMMON],
"special":[[FAST],DID,WINDSTEP,M2,SFXISNAME],
"debuff":[],
"1m":[],
"1a":[]} #special[3] = 1 is the WIND'S STEP buff which prevents you from falling that same turn.
var Move10 = {"name":"Piton",#ITEM ability
"slot":CONSUMABLE,
"info":"Makes a wall climbable.",
"types":[NOREQUIREDMOVES,1,0,EIGHTDIR,PRECISION,rarity.COMMON],
"special":[[DEPLOYABLE],2,SB,ATTACKWINDUP,SFXISNAME], #4 indicates a DEPLOYABLE item that only attacks its first contact. "2" is the ID of the PITON object.
"debuff":[],
#"directiontype":[Vector2(0,3),Vector2(0,2),Vector2(0,3),Vector2(0,2),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,2)], #X = move, Y = attack, 1 starts at top-LEFT
"1a":[Vector2(0,0),Vector2(-1,-1),Vector2(-2,-2)],
"1m":[],
"2a":[Vector2(0,0),Vector2(0,-1),Vector2(0,-2)],
"2m":[],
"3a":[Vector2(0,0),Vector2(1,-1),Vector2(2,-2)],
"3m":[],
"4a":[Vector2(0,0),Vector2(1,0),Vector2(2,0)],
"4m":[],
"5a":[Vector2(0,0),Vector2(1,1),Vector2(2,2)],
"5m":[],
"6a":[Vector2(0,0),Vector2(0,1),Vector2(0,2)],
"6m":[],
"7a":[Vector2(0,0),Vector2(-1,1),Vector2(-2,2)],
"7m":[],
"8a":[Vector2(0,0),Vector2(-1,0),Vector2(-2,0)],
"8m":[]}
var Move9 = {"name":"Torch",#ITEM ability
"slot":CONSUMABLE,
"info":"Falls, produces light, removes bushes and goop. Ghosts hate it.",
"types":[NOREQUIREDMOVES,1,0,EIGHTDIR,ENERGY,rarity.COMMON],
"special":[[DEPLOYABLE],1,SB,M2,SFXISNAME], #4 indicates a DEPLOYABLE item that only attacks its first contact. "1" is the ID of the TORCH object.
#"directiontype":[Vector2(0,3),Vector2(0,2),Vector2(0,3),Vector2(0,2),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,2)], #X = move, Y = attack, 1 starts at top-LEFT
"debuff":[Vector2(FLINCH,1)],
"1a":[Vector2(0,0),Vector2(-1,-1),Vector2(-2,-1),Vector2(-3,0)],
"1m":[],
"2a":[Vector2(0,0),Vector2(0,-1),Vector2(0,-2)],
"2m":[],
"3a":[Vector2(0,0),Vector2(1,-1),Vector2(2,-1),Vector2(3,0)],
"3m":[],
"4a":[Vector2(0,0),Vector2(1,0),Vector2(2,0),Vector2(3,1)],
"4m":[],
"5a":[Vector2(0,0),Vector2(1,1),Vector2(2,2),Vector2(2,3)],
"5m":[],
"6a":[Vector2(0,0),Vector2(0,1),Vector2(0,2),Vector2(0,3)],
"6m":[],
"7a":[Vector2(0,0),Vector2(-1,1),Vector2(-2,2),Vector2(-2,3)],
"7m":[],
"8a":[Vector2(0,0),Vector2(-1,0),Vector2(-2,0),Vector2(-3,1)],
"8m":[]}
var Move8 = {"name":"Dagger",#INNATE ability (male MC)
#"slot":ITEM,
"info":"",
"types":[NOREQUIREDMOVES,1,1,EIGHTDIR,PRECISION,rarity.COMMON],
"special":[[FAST],DID,SB,M2,SFXISNAME], #3 indicates a 'FAST' attack, does not end turn.
#"directiontype":[Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1)], #Move1,Attack1,Move2,Attack2
"debuff":[],
"1a":[Vector2(-1,-1)],
"1m":[],
"2a":[Vector2(0,-1)],
"2m":[],
"3a":[Vector2(1,-1)],
"3m":[],
"4a":[Vector2(1,0)],
"4m":[],
"5a":[Vector2(1,1)],
"5m":[],
"6a":[Vector2(0,1)],
"6m":[],
"7a":[Vector2(-1,1)],
"7m":[],
"8a":[Vector2(-1,0)],
"8m":[]}
var Move7 = {"name":"Axe",#INNATE ability (female MC)
#"slot":ITEM,
"info":"",
"types":[NOREQUIREDMOVES,3,2,TWODIR,FORCE,rarity.COMMON],
#"directiontype":[Vector2(0,3)],
"special":[[HEAVY],DID,SB,M2,SFXISNAME], #2 means the attack is SLOW, and will take place after the turn ends and enemies move
"debuff":[],
"1m":[],
"1a":[Vector2(1,-1),Vector2(1,0),Vector2(1,1),Vector2(2,1),Vector2(2,2)]}
var Move6 = {"name":"Pole",#INNATE ability
#"slot":ITEM,
"info":"",
"types":[NOREQUIREDMOVES,1,1,EIGHTDIR,PRECISION,rarity.COMMON], #1: moves. #2: attacks. #3: min move count. #4: reduced attacks if attack cut short. #5 is damage per hit.
#"directiontype":[Vector2(2,1)],
"special":[[],DID,SB,M2,SFXISNAME],
"debuff":[],
"1m":[Vector2(0,-1),Vector2(-1,-2),Vector2(-2,-3)],
"1a":[Vector2(-1,-2),Vector2(-2,-2),Vector2(-2,-3)],
"2m":[Vector2(0,1)],
"2a":[Vector2(0,-2)],
"3m":[Vector2(0,-1),Vector2(1,-2),Vector2(2,-3)],
"3a":[Vector2(1,-2),Vector2(2,-2),Vector2(2,-3)],
"4m":[Vector2(-1,0)],
"4a":[Vector2(2,0)],
"5m":[Vector2(-1,-1)],
"5a":[Vector2(2,2)],
"6m":[Vector2(0,-1)],
"6a":[Vector2(0,2)],
"7m":[Vector2(1,-1)],
"7a":[Vector2(-2,2)],
"8m":[Vector2(1,0)],
"8a":[Vector2(-2,0)]}
var Move5 = {"name":"Sweep",#INNATE ability (female MC)
#"slot":INNATE,
"info":"",
"types":[1,4,1,TWODIR,PRECISION,rarity.COMMON],
#"directiontype":[Vector2(3,4)],
"special":[[],DID,SB,WINDUP,SFXISNAME], #identifier for special cases. Right now, 1 means you are WARPING, not jumping. 0 means no specials.
"debuff":[],
"1a":[Vector2(1,-1),Vector2(2,0),Vector2(2,-1),Vector2(2,-2)],
"1m":[Vector2(1,-1),Vector2(0,-2),Vector2(-1,-1)]}
var Move4 = {"name":"Lance",#INNATE ability (female MC)
#"slot":ITEM,
"info":"",
"types":[1,3,1,2,FORCE,rarity.COMMON],
#"directiontype":[Vector2(1,3)],
"special":[[],DID,0,0,SFXISNAME],
#"blocknum":1,
#"block":[[MOUSE,FORCE,3]],
"debuff":[Vector2(KNOCKBACK,1)],
"1a":[Vector2(1,0),Vector2(2,0),Vector2(3,0),Vector2(4,0)],
"1m":[Vector2(1,0)]}
var Move1004 = {"name":"Lance-II",#INNATE ability (female MC)
#"slot":ITEM,
"info":"Old friend.",
"types":[0,5,2,2,FORCE,rarity.RARE],
#"directiontype":[Vector2(1,3)],
"special":[[BLOCK],DID,0,0,SFXISNAME],
#"blocknum":1,
"block":[[MOUSE,FORCE,2]],
"debuff":[Vector2(8,3),Vector2(-2,2)],
"1a":[Vector2(1,0),Vector2(2,1),Vector2(2,-1),Vector2(2,0),Vector2(3,0),Vector2(3,2),Vector2(3,-2),Vector2(4,0),Vector2(5,0)],
"1m":[Vector2(1,0),Vector2(2,0)]}
var Move3 = {"name":"Dash",#INNATE ability (male MC)
#"slot":INNATE,
"info":"",
"types":[1,1,0,TWODIR,PURE,rarity.COMMON],
#"directiontype":[Vector2(5,0)],
"special":[[],DID,SB,M2,SFXISNAME],
"debuff":[Vector2(FLINCH,1)],
"1m":[Vector2(1,0),Vector2(2,0),Vector2(3,0),Vector2(4,0),Vector2(5,0)],
"1a":[]}
var Move2 = {"name":"Warp",#ITEM ability
#"slot":INNATE,
"info":"How did you get this you're not supposed to have this.",
"types":[1,0,1,TWODIR,PURE,rarity.RARE],
#"directiontype":[Vector2(1,0],
"special":[[WARP],DID,0,0,NOSFX],
"debuff":[],
"1m":[Vector2(3,0)],
"1a":[]}

#SPECIAL -- Specialtype(#1), objecttype(#2), debuff type (#3)
#enemy moves start here
var Move100 = {"name":"Pounce",#MONSTER INNATE ability
#"slot":INNATE,
"info":"Shreds clothes.",
"types":[2,3,1,TWODIR,FORCE,rarity.COMMON], #okay, so this fourth cell shows how many of the attacks occur if movement is cut short. Pay attention!
#"directiontype":[Vector2(3,5)],
"special":[[],DID,SB,M2,SFXISNAME],
"debuff":[Vector2(MARKED,1)],
#"debuff":[Vector2(7,3)],
#"debuffe1":[Vector2(HEAT,2)],
#"debuffe2":[Vector2(6,1)],
#"debuff":[Vector2(6,1),Vector2(7,2)],
"1a":[Vector2(3,0),Vector2(3,-1),Vector2(4,0),Vector2(4,1),Vector2(3,1)],
"1m":[Vector2(1,-1),Vector2(2,-1),Vector2(3,0)]}
var Move101 = {"name":"Scratch",#MONSTER INNATE ability
#"slot":ITEM,
"info":"Shreds clothes.",
"types":[NOREQUIREDMOVES,2,1,TWODIR,FORCE,rarity.COMMON], 
#"directiontype":[Vector2(1,2)]
"special":[[],DID,SB,M2,SFXISNAME],
#"debuff":[Vector2(7,2)],
"debuff":[Vector2(MARKED,1)],
#"debuffe2":[Vector2(MARKED,2)],
#"debuff":[Vector2(6,1),Vector2(7,1)],
"1a":[Vector2(1,0),Vector2(1,-1)],
"1m":[Vector2(-1,-1)]}
var Move102 = {"name":"Heavenly Chord",#MONSTER INNATE ability
#"slot":INNATE,
"info":"Hypnosis prevents enemies from attacking.",
"types":[NOCRASH,1,0,TWODIR,SWEET,rarity.COMMON], 
#"directiontype":[Vector2(1,2)]
"special":[[HEAVY],DID,SB,M2,SFXISNAME], #third part of special indicates a debuff. of which, 1 is "DROWSY". Consider programming special attacks to lose movements when DROWSY? N - DrowsyDrowsyscore.
"debuff":[Vector2(HYPNOSIS,3)],
"debuffe2":[Vector2(DROWSY,2)],
#"debuffe2":[Vector2(MARKED,1)],
#"debuff":[Vector2(1,2),Vector2(2,1),Vector2(6,1)],
"1a":[Vector2(5,0),Vector2(4,0),Vector2(3,0),Vector2(2,0),Vector2(1,0),Vector2(4,-1),Vector2(3,-1),Vector2(2,-1),Vector2(1,-1),Vector2(4,1),Vector2(3,1),Vector2(2,1),Vector2(1,1),Vector2(3,-2),Vector2(2,-2),Vector2(1,-2)],
"1m":[]}
var Move103 = {"name":"Sheep Song",#MONSTER INNATE ability
#"slot":INNATE,
"info":"Hypnosis prevents enemies from attacking.",
"types":[NOCRASH,1,0,ONEDIR,SWEET,rarity.COMMON], 
#"directiontype":[Vector2(1,2)]
"special":[[],DID,SB,M2,SFXISNAME], #third part of special indicates a debuff. of which, 1 is "DROWSY". Consider programming special attacks to lose movements when DROWSY? N - DrowsyDrowsyscore.
"debuff":[Vector2(HYPNOSIS,2)],
#"debuffe1":[Vector2(DROWSY,1)],
#"debuff":[Vector2(1,2)],
"1a":[Vector2(2,0),Vector2(1,0),Vector2(-1,0),Vector2(-2,0),Vector2(1,-1),Vector2(0,-1),Vector2(-1,-1),Vector2(0,0)],
"1m":[]}
var Move104 = {"name":"Remove Seal",
"slot":CURSE,
"info":"Remove fox's lewd seals. Self-inflicts Flinch.",
"types":[NOREQUIREDMOVES,0,0,ONEDIR,SPIRIT,rarity.COMMON],
"special":[[HEAVY],DID,SB,REMOVESEAL,SFXISNAME],
"selfdebuff":[Vector2(FLINCH,1)],
"debuff":[],
"1m":[],
"1a":[]}
var Move105 = {"name":"Resonance",#MONSTER INNATE ability
#"slot":INNATE,
"info":"",
"types":[NOCRASH,1,1,ONEDIR,SPIRIT,rarity.COMMON], #why is it TWODIR...Just because it's easier?
"special":[[ECHO],DID,SB,M2,SFXISNAME],
#"debuff":[Vector2(POSSESSION,2)],
#"debuffe1":[Vector2(DROWSY,1),Vector2(SWEATY,2)],
#"debuffe2":[Vector2(SWEATY,2)],
#"debuff":[Vector2(1,1),Vector2(3,2)],
"1a":[Vector2(2,0),Vector2(1,-1),Vector2(0,-2),Vector2(-1,-1),Vector2(-2,0),Vector2(-1,1),Vector2(0,2),Vector2(1,1),Vector2(0,0),Vector2(-2,-2),Vector2(2,2),Vector2(-2,2),Vector2(2,-2)],
"1m":[Vector2(0,-1)]}
var Move106 = {"name":"Warpath",#MONSTER INNATE ability -- pitch's ability, it's a better version of pounce
#"slot":INNATE,
"info":"",
"types":[3,5,2,TWODIR,FORCE,rarity.RARE], #okay, so this fourth????? cell shows how many of the attacks occur if movement is cut short. Pay attention!
#"directiontype":[Vector2(3,5)],
"special":[[HEAVY],DID,SB,M2,SFXISNAME], #you should set it to HEAVY though.
#"blocknum":1,
#"block":[[ALL,1,2],[ALL,2,2],[ALL,4,2]],
"debuff":[],#[Vector2(IMPACT,3)],
"1a":[Vector2(3,0),Vector2(3,-1),Vector2(4,0),Vector2(4,1),Vector2(4,0),Vector2(4,1),Vector2(5,0),Vector2(5,1),Vector2(6,0),Vector2(6,1),Vector2(6,-1)],
"1m":[Vector2(1,-1),Vector2(2,-1),Vector2(3,0),Vector2(4,0),Vector2(5,0)]}
var Move107 = {"name":"Sense",#INNATE ability. One of pitch's extra abilities.
#"slot":INNATE,
"info":"",
"types":[NOCRASH,0,0,ONEDIR,PURE,rarity.RARE],
"special":[[RECOVERY,HEAVY],DID,SENSE,M2,SFXISNAME],
"debuff":[],
"1m":[],
"1a":[]}
var Move108 = {"name":"Hypnosis",#MONSTER INNATE ability
#"slot":INNATE,
"info":"Hypnosis prevents enemies from attacking.",
"types":[NOCRASH,1,0,TWODIR,SWEET,rarity.RARE], 
#"directiontype":[Vector2(1,2)]
"special":[[],DID,SB,M2,SFXISNAME], #third part of special indicates a debuff. of which, 1 is "DROWSY". Consider programming special attacks to lose movements when DROWSY? N - DrowsyDrowsyscore.
"debuff":[Vector2(HYPNOSIS,3)],
#"debuffe1":[Vector2(HYPNOSIS,1)],
#"debuffe2":[Vector2(HYPNOSIS,1)],
"1a":[Vector2(0,0),Vector2(0,-1),Vector2(0,-2),Vector2(1,-2),Vector2(1,1),Vector2(1,0),Vector2(1,-1),Vector2(2,1),Vector2(2,0),Vector2(2,-1),Vector2(3,1),Vector2(3,0),Vector2(3,-1)],
"1m":[]}
var Move109 = {"name":"Empower",#MONSTER INNATE ability
#"slot":INNATE,
"info":"", #POSSESSION/seals can't stick without SWEATY/CUM
"types":[NOCRASH,4,1,TWODIR,SPIRIT,rarity.COMMON], 
"special":[[FAST,RECOVERY],DID,DBNEXTATTACK,M2,SFXISNAME], #FAST!
"debuffnextattack":[Vector2(KNOCKBACK,2)],
#"debuffe1":[Vector2(POSSESSION,1)],
#"debuffe2":[Vector2(SWEATY,1)],
#"debuff":[Vector2(3,1)],
"1a":[Vector2(0,1),Vector2(1,0),Vector2(-1,0),Vector2(0,-1)],
"1m":[]}
var Move110 = {"name":"Ruin",#MONSTER INNATE ability -- pitch's ability, it's a better version of pounce
#"slot":INNATE,
"info":"",
"types":[NOCRASH,13,1,TWODIR,FORCE,rarity.RARE], #why is this TWODIR. okay, so this fourth????? cell shows how many of the attacks occur if movement is cut short. Pay attention!
#"directiontype":[Vector2(3,5)],
"special":[[FAST],DID,SB,M2,SFXISNAME],
"debuff":[],#[Vector2(IMPACT,2)],
"1a":[Vector2(0,0),Vector2(0,1),Vector2(1,1),Vector2(1,0),Vector2(0,-1),Vector2(-1,-1),Vector2(-1,0),Vector2(-1,1),Vector2(1,-1),Vector2(2,0),Vector2(-2,0),Vector2(0,-2),Vector2(0,2)],
"1m":[Vector2(0,1)]}
var Move111 = {"name":"Juggernaut",#INNATE ability. One of pitch's extra abilities.
#"slot":INNATE,
"info":"Map-spanning vertical jump.",
"types":[1,0,0,FOURDIAGONALDIR,PURE,rarity.SUPER], #5 means 4 directions but diagonal. consists of 2m and 2a but flippable.
"special":[[WARP],DID,WINDSTEP,WINDUP,SFXISNAME],
"debuff":[],
"1m":[Vector2(1,-3),Vector2(2,-6),Vector2(3,-9),Vector2(4,-12),Vector2(5,-15),Vector2(6,-18),Vector2(7,-21),Vector2(8,-24),Vector2(9,-27),Vector2(10,-30),Vector2(11,-33)],
"1a":[],
"2m":[Vector2(1,3),Vector2(2,6),Vector2(3,9),Vector2(4,12),Vector2(5,15),Vector2(6,18),Vector2(7,21),Vector2(8,24),Vector2(9,27),Vector2(10,30),Vector2(11,33)],
"2a":[]}
var Move112 = {"name":"Shockwave",#INNATE ability. One of pitch's extra abilities.
#"slot":INNATE,
"info":"Inflicts Flinch.",
"types":[1,0,1,FOURDIR,FORCE,rarity.RARE], #5 means 4 directions but diagonal. consists of 2m and 2a but flippable.
"special":[[],DID,SB,M2,SFXISNAME],
#"debuff":[Vector2(8,1)],
"debuff":[Vector2(KNOCKBACK,2),Vector2(FLINCH,1)],#,Vector2(IMPACT,1)],
"1m":[Vector2(1,0)],
"1a":[Vector2(2,0),Vector2(3,0),Vector2(4,0),Vector2(3,-1),Vector2(3,1),Vector2(4,-1),Vector2(4,1),Vector2(4,-2),Vector2(4,2),Vector2(5,0)],
"2m":[Vector2(0,1)],
"2a":[Vector2(0,2),Vector2(0,3),Vector2(0,4),Vector2(-1,3),Vector2(1,3),Vector2(-1,4),Vector2(1,4),Vector2(-2,4),Vector2(2,4),Vector2(0,5)],
"3m":[Vector2(-1,0)],
"3a":[Vector2(-2,0),Vector2(-3,0),Vector2(-4,0),Vector2(-3,-1),Vector2(-3,1),Vector2(-4,-1),Vector2(-4,1),Vector2(-4,-2),Vector2(-4,2),Vector2(-5,0)],
"4m":[Vector2(0,-1)],
"4a":[Vector2(0,-2),Vector2(0,-3),Vector2(0,-4),Vector2(-1,-3),Vector2(1,-3),Vector2(-1,-4),Vector2(1,-4),Vector2(-2,-4),Vector2(2,-4),Vector2(0,-5)]}
var Move113 = {"name":"Ghost Bell",#ITEM ability
"info":"Summons Ghost-Foxes.",
"types":[NOREQUIREDMOVES,1,0,EIGHTDIR,SPIRIT,rarity.RARE],
"special":[[DEPLOYABLE,ECHO],3,SB,ATTACKWINDUP,SFXISNAME], #DEPLOYABLE 3 is the identity of the 'foxfire' object, now a ghost spirit
"debuff":[],
#"directiontype":[Vector2(0,3),Vector2(0,2),Vector2(0,3),Vector2(0,2),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,2)], #X = move, Y = attack, 1 starts at top-LEFT
"1a":[Vector2(-1,-1),Vector2(-2,-2)],
"1m":[],
"2a":[Vector2(0,-1),Vector2(0,-2)],
"2m":[],
"3a":[Vector2(1,-1),Vector2(2,-2)],
"3m":[],
"4a":[Vector2(1,0),Vector2(2,0)],
"4m":[],
"5a":[Vector2(1,1),Vector2(2,2)],
"5m":[],
"6a":[Vector2(0,1),Vector2(0,2)],
"6m":[],
"7a":[Vector2(-1,1),Vector2(-2,2)],
"7m":[],
"8a":[Vector2(-1,0),Vector2(-2,0)],
"8m":[]}
var Move114 = {"name":"Plunge",#MONSTER INNATE ability -- pitch's ability, it's a better version of pounce
#"slot":INNATE,
"info":"Must be used from the air.",
"types":[3,5,3,TWODIR,PRECISION,rarity.RARE], #okay, so this fourth????? cell shows how many of the attacks occur if movement is cut short. Pay attention!
#"directiontype":[Vector2(3,5)],
"special":[[],DID,SB,M2,SFXISNAME], #you should set it to HEAVY though.
#"blocknum":1,
"debuff":[Vector2(MARKED,2)],
"1a":[Vector2(2,3),Vector2(0,3),Vector2(3,3),Vector2(1,3),Vector2(2,4),Vector2(1,5),Vector2(2,5),Vector2(3,4),Vector2(1,4),Vector2(0,4)],
"1m":[Vector2(0,1),Vector2(0,2),Vector2(1,3),Vector2(1,4)]}

var Move115 = {"name":"Knight Shift",
#"slot":ITEM,
"info":"",
"types":[1,0,2,EIGHTDIAGONALDIR,PRECISION,rarity.RARE],
"special":[[],DID,SB,M2,SFXMULTI], #3 indicates a 'FAST' attack, does not end turn.
#"directiontype":[Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1)], #Move1,Attack1,Move2,Attack2
"debuff":[],
"1a":[Vector2(-1,-2)],
"1m":[Vector2(-1,-2)],
"2a":[Vector2(1,-2)],
"2m":[Vector2(1,-2)],
"3a":[Vector2(2,-1)],
"3m":[Vector2(2,-1)],
"4a":[Vector2(2,1)],
"4m":[Vector2(2,1)],
"5a":[Vector2(1,2)],
"5m":[Vector2(1,2)],
"6a":[Vector2(-1,2)],
"6m":[Vector2(-1,2)],
"7a":[Vector2(-2,1)],
"7m":[Vector2(-2,1)],
"8a":[Vector2(-2,-1)],
"8m":[Vector2(-2,-1)]}
var Move116 = {"name":"Pulse",
#"slot":ITEM,
"info":"",
"types":[NOREQUIREDMOVES,0,2,ONEDIR,SWEET,rarity.RARE],
"special":[[],DID,SB,M2,SFXISNAME], #3 indicates a 'FAST' attack, does not end turn.
#"directiontype":[Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1)], #Move1,Attack1,Move2,Attack2
"debuff":[],
"1a":[Vector2(1,0),Vector2(-1,0),Vector2(0,1),Vector2(0,-1),Vector2(2,0),Vector2(-2,0),Vector2(0,2),Vector2(0,-2)],
"1m":[]}
var Move117 = {"name":"Shine",
#"slot":ITEM,
"info":"",
"types":[NOREQUIREDMOVES,0,2,ONEDIR,ENERGY,rarity.RARE],
"special":[[],DID,SB,M2,SFXISNAME], #3 indicates a 'FAST' attack, does not end turn.
#"directiontype":[Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1)], #Move1,Attack1,Move2,Attack2
"debuff":[Vector2(DROWSY,2)],
"1a":[Vector2(1,1),Vector2(-1,-1),Vector2(-1,1),Vector2(1,-1),Vector2(2,2),Vector2(-2,-2),Vector2(-2,2),Vector2(2,-2)],
"1m":[]}

var Move118 = {"name":"Sweet Smoke",
"info":"Dazes targets to slow them.",
"types":[NOREQUIREDMOVES,25,0,TWODIR,SWEET,rarity.COMMON],
"special":[[],DID,WINDSTEP,M2,SFXISNAME],
"debuff":[Vector2(DROWSY,4)],
"1a":[Vector2(1,0),Vector2(2,1),Vector2(0,1),Vector2(3,1),Vector2(1,1),Vector2(2,2),Vector2(1,3),Vector2(2,3),Vector2(3,2),Vector2(1,2),Vector2(3,3),Vector2(3,4),Vector2(4,3),Vector2(4,4),Vector2(4,2),Vector2(2,4),Vector2(5,3),Vector2(5,4)],
"1m":[Vector2(0,-1)]}
var Move119 = {"name":"Smoke Plume",
"info":"",
"types":[NOREQUIREDMOVES,25,1,TWODIR,ENERGY,rarity.COMMON],
"special":[[],DID,SB,M2,SFXISNAME],
"debuff":[Vector2(DROWSY,1)],
"1a":[Vector2(1,0),Vector2(1,1),Vector2(0,1),Vector2(2,0),Vector2(2,1),Vector2(2,-1),Vector2(3,0)],
"1m":[Vector2(-1,0)]}


var Move200 = {"name":"Milky Drain",#INNATE ability (female MC)
#"slot":INNATE,
"info":"Generates milk from hits.",
"types":[NOREQUIREDMOVES,3,0,TWODIR,SWEET,rarity.COMMON], 
#"directiontype":[Vector2(0,3)],
"special":[[],DID,DRAIN,M2,SFXISNAME], #2 means the attack is SLOW, and will take place after the turn ends and enemies move
"debuff":[Vector2(DROWSY,1),Vector2(HYPNOSIS,3)],
"drainselfdebuff":[Vector2(MILK,3)],
"1m":[],
"1a":[Vector2(-1,0),Vector2(-1,-1),Vector2(0,1),Vector2(0,0),Vector2(0,-2),Vector2(0,-1),Vector2(1,-2),Vector2(1,1),Vector2(1,0),Vector2(1,-1),Vector2(2,1),Vector2(2,0),Vector2(2,-1),Vector2(3,0)]}
var Move201 = {"name":"Overflow",#INNATE ability (female MC)
"slot":CURSE,
"info":"Deals with the milk-laden weight of one's chest.",
"types":[NOREQUIREDMOVES,0,0,ONEDIR,SWEET,rarity.COMMON], 
#"directiontype":[Vector2(0,3)],
"special":[[],DID,SB,SCENETRIGGER,SFXISNAME], #2 means the attack is SLOW, and will take place after the turn ends and enemies move
"debuff":[],#Vector2(KNOCKBACK,-1)],
"1m":[],
"1a":[Vector2(0,0)]}#Vector2(0,0),Vector2(0,1),Vector2(1,1),Vector2(1,0),Vector2(0,-1),Vector2(-1,-1),Vector2(-1,0),Vector2(-1,1),Vector2(1,-1)]}
var Move202 = {"name":"Productivity",#INNATE ability (female MC)
#"slot":INNATE,
"info":"Inflict milk on self.",
"types":[NOREQUIREDMOVES,3,0,ONEDIR,SWEET,rarity.COMMON], 
#"directiontype":[Vector2(0,3)],
"special":[[],DID,SB,M2,NOSFX], #2 means the attack is SLOW, and will take place after the turn ends and enemies move
"debuff":[],
"selfdebuff":[Vector2(MILK,5)],
"1m":[],
"1a":[]}
var Move203 = {"name":"Draining Kiss",#INNATE ability (female MC)
"slot":CURSE,
"info":"",
"types":[NOREQUIREDMOVES,0,3,ONEDIR,SWEET,rarity.RARE], 
"special":[[],DID,DRAINHEAL,M2,SFXISNAME], #2 means the attack is SLOW, and will take place after the turn ends and enemies move
#"corruption":Vector2(raceRAM,20),
"debuff":[Vector2(HYPNOSIS,1)],
"1m":[],
"1a":[Vector2(0,0)]}
var Move204 = {"name":"Cum Seal",
"slot":CURSE,
"info":"Draw energy from the fox-cum in you. Causes drowsy.",
"types":[NOREQUIREDMOVES,0,0,ONEDIR,BITTER,rarity.RARE],
"special":[[],DID,SB,M2,SFXISNAME],
"selfdebuff":[Vector2(DROWSY,2),Vector2(SWEATY,2)],
"corruption":[Vector2(raceKITSUNE,20)],
"debuff":[],
"1m":[],
"1a":[]}
var Move205 = {"name":"Fox Seal",
"slot":CURSE,
"info":"Draw energy from residual fox corruption. Causes drowsy.",
"types":[NOREQUIREDMOVES,0,0,ONEDIR,BITTER,rarity.RARE],
"special":[[],DID,SB,M2,SFXISNAME],
"selfdebuff":[Vector2(DROWSY,2),Vector2(SWEATY,2)],
"corruption":[Vector2(raceKITSUNE,20)],
"debuff":[],
"1m":[],
"1a":[]}
var Move206 = {"name":"Egg Bomb",#INNATE ability (female MC)
"slot":CURSE,
"info":"Form and hold a dangerous object, deals more damage as it falls.",
"types":[NOREQUIREDMOVES,0,0,ONEDIR,SWEET,rarity.RARE], #Types currently are: [REQUIREDMOVES,REDUCEDATTACKCOUNT,DAMAGE,DIRECTIONS,DAMAGETYPE,RUNNING] currently 2 and 8 directions supported
"special":[[DEPLOYABLE],4,SB,M2,NOSFX], #2 means the attack is SLOW, and will take place after the turn ends and enemies move
#"corruption":Vector2(raceRAM,20),
"selfdebuff":[Vector2(DROWSY,2),Vector2(SWEATY,1)],
"1m":[],
"1a":[]}
var Move207 = {"name":"Masturbate",#INNATE ability (female MC)
"slot":CURSE,
"info":"Let out stress. Sates ghost-foxes.",
"types":[NOREQUIREDMOVES,0,0,ONEDIR,BITTER,rarity.COMMON], 
#"directiontype":[Vector2(0,3)],
"special":[[],DID,SB,SCENETRIGGER,NOSFX], #2 means the attack is SLOW, and will take place after the turn ends and enemies move
"debuff":[],#Vector2(KNOCKBACK,-1)],
"1m":[],
"1a":[]}#Vector2(0,0),Vector2(0,1),Vector2(1,1),Vector2(1,0),Vector2(0,-1),Vector2(-1,-1),Vector2(-1,0),Vector2(-1,1),Vector2(1,-1)]}
var Move208 = {"name":"Suck",#make it activate twice against unclothed targets?
"slot":CURSE,
"info":"Suck upon whatever the target has. Varying results.",
"types":[NOREQUIREDMOVES,0,2,TWODIR,BITTER,rarity.RARE], 
#"directiontype":[Vector2(0,3)],
"special":[[],DID,SB,M2,NOSFX], #2 means the attack is SLOW, and will take place after the turn ends and enemies move
"debuff":[Vector2(FLINCH,1),Vector2(DROWSY,2)],#Vector2(KNOCKBACK,-1)],
"1a":[Vector2(1,0)],
"1m":[]}
var Move209 = {"name":"Stumble",#INNATE ability (female MC)
"info":"",
"types":[1,1,0,EIGHTDIR,FORCE,rarity.COMMON],
"special":[[],DID,SB,M2,NOSFX], #7 indicates 'eats run' which means run is set to 0
#"directiontype":[Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1),Vector2(0,1)], #Move1,Attack1,Move2,Attack2
"debuff":[Vector2(KNOCKBACK,-1)],
"1a":[Vector2(-1,-1),Vector2(-2,-2)],
"1m":[Vector2(-1,-1),Vector2(-2,-2)],#,Vector2(-2,-2)],
"2a":[Vector2(0,-1),Vector2(0,-2)],
"2m":[Vector2(0,-1),Vector2(0,-2)],#,Vector2(0,-2)],
"3a":[Vector2(1,-1),Vector2(2,-2)],
"3m":[Vector2(1,-1),Vector2(2,-2)],#,Vector2(2,-2)],
"4a":[Vector2(1,0),Vector2(2,0)],
"4m":[Vector2(1,0),Vector2(2,0)],#,Vector2(2,0)],
"5a":[Vector2(1,1),Vector2(2,2)],
"5m":[Vector2(1,1),Vector2(2,2)],#,Vector2(2,2)],
"6a":[Vector2(0,1),Vector2(0,2)],
"6m":[Vector2(0,1),Vector2(0,2)],#,Vector2(0,2)],
"7a":[Vector2(-1,1),Vector2(-2,2)],
"7m":[Vector2(-1,1),Vector2(-2,2)],#,Vector2(-2,2)],
"8a":[Vector2(-1,0),Vector2(-2,0)],
"8m":[Vector2(-1,0),Vector2(-2,0)]}#,Vector2(-2,0)]}
var Move210 = {"name":"Beg",#INNATE ability (female MC)
"info":"Try to convince nearby monster-girls to 'help'.",
"types":[NOREQUIREDMOVES,0,0,ONEDIR,SWEET,rarity.COMMON], 
#"directiontype":[Vector2(0,3)],
"special":[[],DID,SB,SCENETRIGGER,NOSFX], #2 means the attack is SLOW, and will take place after the turn ends and enemies move
"debuff":[],#Vector2(KNOCKBACK,-1)],
"1m":[],
"1a":[Vector2(0,0),Vector2(0,1),Vector2(1,1),Vector2(1,0),Vector2(0,-1),Vector2(-1,-1),Vector2(-1,0),Vector2(-1,1),Vector2(1,-1),Vector2(2,0),Vector2(-2,0),Vector2(0,-2),Vector2(0,2),Vector2(-1,-2),Vector2(1,-2),Vector2(2,-1),Vector2(2,1),Vector2(1,2),Vector2(-1,2),Vector2(-2,1),Vector2(-2,-1)]}

#var Move205 = Move204.duplicate()

#0 = PURE, 1 = FORCE, 2 = PRECISION, 3 = SPIRIT, 4 = SWEET, 5 = BITTER, 6 = ENERGY

#maximum scores currently: fox 200+150, catgirl 350+150, ramgirl 250+150
#const scorefifty = PoolStringArray(["eyes","top","pants"])
#const scorehundred = PoolStringArray(["armright","horns","ears","tail"])
#func evaluate_class(lostrace):
##	var testcorruptiondict = corruptiondict.duplicate()
#	var scorearray = racecorruptionarray.duplicate()
#	scorearray[lostrace] += 150
#	scorearray[raceNAMAN] += playerbustsize * 50
#	if tempcorruptiondict.has("beads") and tempcorruptiondict["beads"] >= tempcorruptionbreakpoints["beads"][0]:
#		scorearray[raceNEKO] += 50
#	for checkstring in scorehundred:
#		if corruptiondict.has(checkstring):
#			scorearray[corruptiondict[checkstring]] += 100
#	for checkstring in scorefifty:
#		if corruptiondict.has(checkstring):
#			scorearray[corruptiondict[checkstring]] += 50

#enum{raceHUMAN=0,raceNAMAN=1,raceCUPID=2,raceNEKO=3,raceKITSUNE=4,raceRAM=5,raceWOLF=6,raceHARPY=7,raceIMP=8}
#var racearray = ["Human","Naman","Cupid","Neko","Kitsune","Ram","Wolf"]
enum classdefine{PRESERVEALL = -1, PRESERVESELF = -2,PRESERVEWOLF = -3,PRESERVEIMP = -4,PRESERVENEKO = -5}
const baseconsumables = [[10,0,CONSUMABLE,10,10],[9,0,CONSUMABLE,5,5],[16,0,CONSUMABLE,1]]
const classarray0 = [[4,0,INNATE],[7,0,INNATE],[8,0,INNATE],[13,0,INNATE]] #HUMAN
const classcorruption0 = {}#{"bust":-2}
const classarray1 = [[17,0,INNATE],[200,0,INNATE],[110,0,INNATE],[15,0,INNATE]] #NAMAN
const classcorruption1 = {"bust":+4,"ears":classdefine.PRESERVEWOLF,"tail":classdefine.PRESERVEWOLF}
const classarray2 = [[12,0,INNATE],[102,0,INNATE],[206,0,CURSE,2],[206,0,CURSE,1]] #CUPID
const classcorruption2 = {}
const classarray3 = [[101,0,INNATE],[100,0,INNATE],[101,0,INNATE],[100,0,INNATE]] #NEKO
const classcorruption3 = {"ears":3,"tail":3,"armright":classdefine.PRESERVENEKO}#,"armright":3,"armleft":3}
const classarray4 = [[105,0,INNATE],[109,0,INNATE],[11,0,INNATE],[113,0,INNATE],[13,0,INNATE]] #KITSUNE
const classcorruption4 = {"ears":4,"tail":4,"hair":4}
const classarray5 = [[108,0,INNATE],[102,0,INNATE],[203,0,INNATE]] #RAM
const classcorruption5 = {"horns":classdefine.PRESERVEALL,"top":5,"pants":5,"eyes":5,"bust":+2,"leg":-4}
const classarray5alt0 = [[103,0,INNATE]]
const classarray5alt1 = [[3,0,INNATE]]
const classarray6 = [[106,0,INNATE],[110,0,INNATE],[112,0,INNATE],[111,0,INNATE]] #WOLF
const classcorruption6 = {"bust":+3,"ears":6,"tail":6,"top":6,"armright":3,"leg":6}
const classarray7 = [[112,0,INNATE],[114,0,INNATE]] #HARPY
const classcorruption7 = {"leg":7,"armright":7,"bust":+2,"backhorns":classdefine.PRESERVESELF,"tail":classdefine.PRESERVESELF}
const classarray7alt0 = [[101,0,INNATE],[11,0,INNATE]]
const classarray7alt1 = [[118,0,INNATE],[119,0,INNATE]]
const classarray8 = [[115,0,INNATE],[115,0,INNATE]] #IMP
const classarray8alt0 = [[116,0,INNATE],[117,0,INNATE]]
const classarray8alt1 = [[117,0,INNATE],[117,0,INNATE]]
const classarray8alt2 = [[116,0,INNATE],[116,0,INNATE]]
const classarray8alt3 = [[116,0,INNATE],[117,0,INNATE]]
const classcorruption8 = {"tail":8,"wings":8,"leg":8,"body":8,"backhorns":8,"face":8,"pants":5}

const HumanPool = [9,10,18]#remove these from lootpools if you're too corrupt.
const LootPool0 = [4,5,7,8,9,14,17]
const LootPool1 = [3,6,12,13,15,12,18]
const LootPool2 = [12,13,1004,15,3,6,111]
const LootPool3 = [12,13,15,3,6]
const LootPool4 = [12,13,15,3,6]

const extrapoolsdict0 = {
#	"eyes5":[],
	"horns5":[102,103],
	"top5":[102,103],
	"ears3":[101,101],
	"tail3":[101,100],
	"armleft3":[101,100],
	"ears4":[13,11],
	"tail4":[105,11],
	"hair4":[13,109],
	"armleft7":[11,101],
	"leg7":[11,5],
	"leg6":[106],
	"ears6":[112],
	"tail6":[112],
	"leg8":[115,115],
	"wings8":[11],
	"tail8":[115],
	"backhorns7":[118],
	"backhorns8":[115],
	"body8":[115],
	"face8":[117],
	"tail7":[118,119]
}
const extrapoolsdict1 = {
	"eyes5":[203],
	"horns5":[102,103,108,3],
	"top5":[102,103,108],
	"ears3":[101,100],
	"tail3":[100,100],
	"armleft3":[5,5],
	"ears4":[109,113],
	"tail4":[113,105],
	"hair4":[113,105],
	"armleft7":[112,11],
	"leg7":[206,114],
	"leg6":[111,106],
	"ears6":[110,112],
	"tail6":[106,110],
	"leg8":[115,115],
	"wings8":[117],
	"tail8":[116],
	"backhorns7":[119],
	"backhorns8":[117],
	"body8":[116],
	"face8":[116],
	"tail7":[114,118]
}
const extrapoolsdict2 = {}
const extrapoolsdict3 = {}
const extrapoolsdict4 = {}


func load_external_font(path):
	var newfont = DynamicFont.new()
	var data = DynamicFontData.new()
	data.font_path = path
	newfont.font_data = data
	return newfont

const actual_font_dict = {
	"font_speech_default":"res://font/osaka-regular-mono/Osaka Regular-Mono.otf",
	"font_speech_choices":"res://font/simonetta/Simonetta-Regular.ttf",
	"font_move_names":"res://font/simonetta/Simonetta-BlackItalic.ttf",
	"font_event_messages":"res://font/liberation-mono/LiberationMono-Bold.ttf",
	"font_attack_record":"res://font/Audiowide-Regular.ttf",
	"font_attack_announce":"res://font/OpenSans-ExtraBold.ttf",
	"font_debuff_description":"res://font/Verily-Serif-Mono/VerilySerifMono.otf",
}

var default_font_dict = {
	"font_speech_default":null,
	"font_speech_default_size":1.0,
	"font_speech_default_conversation_size":1.0,
	"if_characters_overflow_textbox_increase_this":1.0,
	"font_speech_default_EXAMPLE_VALUE_NOT_USED":"Osaka Regular-Mono.otf",
	
	"font_speech_choices":null,
	"font_speech_choices_size":1.0,
	"font_speech_choices_EXAMPLE_VALUE_NOT_USED":"Simonetta-Regular.ttf",
	
	"font_move_names":null,
	"font_move_names_size":1.0,
	"font_move_names_EXAMPLE_VALUE_NOT_USED":"Simonetta-BlackItalic.ttf",
	
	"font_event_messages":null,
	"font_event_messages_size":1.0,
	"font_event_messages_EXAMPLE_VALUE_NOT_USED":"LiberationMono-Bold.ttf",
	
	"font_attack_record":null,
	"font_attack_record_size":1.0,
	"font_attack_record_EXAMPLE_VALUE_NOT_USED":"Audiowide-Regular.ttf",
	
	"font_attack_announce":null,
	"font_attack_announce_size":1.0,
	"font_attack_announce_EXAMPLE_VALUE_NOT_USED":"OpenSans-ExtraBold.ttf",
	
	"font_debuff_description":null,
	"font_debuff_description_size":1.0,
	"font_debuff_description_EXAMPLE_VALUE_NOT_USED":"VerilySerifMono.otf",
	}


var queuetllog = []
var tlerrorlog = []
var errorlog = []
func log_error(message,translation = false):
	if translation == true:
		if queuetllog.find(message) == -1:
			queuetllog.append(message)
	else:
		if errorlog.find(message) == -1:
			errorlog.append(message)

var transdict = {}
var spectransdict = {}
#var fontdict = {}
var basefontpath = ""
func process_speechdict(filestring,external = false):
	var speechdatafile = File.new()
	if external == false:
		speechdatafile.open("res://Conversations/"+filestring+".json", File.READ)
		if modarray[mods.TRANSLATION] == true and transdict.has(filestring+".json"):
			spectransdict = transdict[filestring+".json"]
			if typeof(spectransdict) != TYPE_DICTIONARY:
				spectransdict = {}
		else:
			spectransdict = {}
	else:
		if check_if_file_exists(filestring) == false:
			print("File does not exist: "+filestring)
			return {}
		speechdatafile.open(filestring, File.READ)
	var jsonparse = JSON.parse(speechdatafile.get_as_text())
#	JSON.close()
	if jsonparse.error == OK:
		return jsonparse.result
	else:
		var errormessage = ("JSON Parse Error: "+ str(jsonparse.error_string) + " in " + str(filestring) + " at line " + str(jsonparse.error_line))
#		print(errormessage)
		log_error(errormessage,external)
#		print(errorlog)
		return {}

func check_if_file_exists(filepath): 
	return (File.new()).file_exists(filepath)


#var activate_mods = false
enum mods{TRANSLATION}
var modarray = [false]
var orderdictionary = {}
func find_mods():
#	var test_timer = OS.get_ticks_msec()
	var path = OS.get_executable_path().get_base_dir()
	var dir = Directory.new()
	dir.open(path)
	var activate_mods = dir.dir_exists("mods")
	if activate_mods == false:
		return
	dir = Directory.new()
	dir.open(path+"/mods")
	dir.list_dir_begin()
	var testdirectories = [""]
	var testdirectoriesintegers = [-1]
	var modkeys = mods.keys()
	while true:
		var file = dir.get_next()
		if file == "":
			break
		else:
			if modkeys.has(file.to_upper()):
				testdirectories.append(file)
				testdirectoriesintegers.append(modkeys.find(file.to_upper()))
			elif dir.current_is_dir() and file.begins_with(".") == false:
				testdirectories.append(file)
				testdirectoriesintegers.append(-1)
	dir.list_dir_end()
	activate_mods = false
#	var modify_fonts = false
	
	for i in range(testdirectories.size()):
		dir.change_dir(path+"/mods/"+testdirectories[i])
#		print(path+"/mods/"+testdirectories[i])
		dir.list_dir_begin()
		var orderarray = []
		while true:
			var file = dir.get_next()
			if file == "":
				break
			elif file != ".":
				if testdirectories[i] == "translation" or testdirectoriesintegers[i] == -1:
					if file.begins_with("ef.") or file.begins_with("Translatable"):
						orderarray.append(str(file))
				elif testdirectoriesintegers[i] > -1:
					modarray[testdirectoriesintegers[i]] = true
					activate_mods = true
					break
		dir.list_dir_end()
		order_by_date(orderarray)
		if orderarray.size() > 0:
			orderdictionary["mods/"+testdirectories[i]] = orderarray[0]
#	if orderdictionary.size() > 0:
#		for entry in orderdictionary:
#			order_by_date(orderdictionary[entry])
#		if orderarray.size() > 1:
#			order_by_date(orderarray)

func find_internal_mods():
#	var test_timer = OS.get_ticks_msec()
	var path = "res://internalmods"
	var dir = Directory.new()
	dir.open(path)
	dir.list_dir_begin()
	var testdirectories = [""]
	while true:
		var file = dir.get_next()
		if file == "":
			break
		else:
			if dir.current_is_dir() and file.begins_with(".") == false:
				testdirectories.append(file)
	dir.list_dir_end()
	for i in range(testdirectories.size()):
		dir.open("res://internalmods/"+testdirectories[i])
#		print(path+"/mods/"+testdirectories[i])
		dir.list_dir_begin()
		var orderarray = []
		while true:
			var file = dir.get_next()
			if file == "":
				break
			elif file != ".":
				if file.begins_with("ef.") or file.begins_with("Translatable"):
					orderarray.append(str(file))
		dir.list_dir_end()
		order_by_date(orderarray)
		if orderarray.size() > 0:
			orderdictionary["internalmods/"+testdirectories[i]] = orderarray[0]
#	if orderdictionary.size() > 0:
#		for entry in orderdictionary:
#			order_by_date(orderdictionary[entry])
#		if orderarray.size() > 1:
#			order_by_date(orderarray)

func translate_game(orderkey):
	var modpath
	var internal = false
	if orderkey.find("internalmods") > -1:
		modpath = "res://" + orderkey + "/"
		internal = true
	else:
		modpath = OS.get_executable_path().get_base_dir() + "/" + orderkey + "/"
	if orderdictionary.has(orderkey):
		transdict = process_speechdict(modpath+orderdictionary[orderkey],true)
	else:
		log_error("Issue finding the translation .json at func 'translate_game'.",true)
		print("Check translate_game, something went wrong.")
		return
	if typeof(transdict) != TYPE_DICTIONARY or transdict.size() == 0:
		log_error("Translation did not parse.",true)
		print("transdict not a dictionary or missing. Its data is provided below:")
		print(transdict)
		return
	var modify_fonts = false
	var fontdict
	if check_if_file_exists(modpath+"font_parameters.json") == true:
		fontdict = process_speechdict(modpath+"font_parameters.json",true)
	if typeof(fontdict) != TYPE_DICTIONARY:
		fontdict = {}
	if fontdict.size() > 0:
		var dir = Directory.new()
		dir.open(modpath)
		for key in fontdict:
			if key.find("EXAMPLE") == -1:
				if default_font_dict.has(key) and fontdict[key] != null:
					if default_font_dict[key] == null:# and fontdict[key]:
						if fontdict[key].ends_with(".ttf") or fontdict[key].ends_with(".otf"):
							if dir.file_exists(fontdict[key]):
								default_font_dict[key] = load_external_font(modpath+fontdict[key])
								modify_fonts = true
							else:
								print("Couldn't find font: "+str(fontdict[key]))
								log_error("This font couldn't be found in the mods/translation folder, "+str(key) +"'s value of: "+str(fontdict[key]),true)
						else:
							print("Invalid font extension: "+str(fontdict[key]))
							log_error("Font name does not include extension .ttf or .otf, "+str(key) +"'s value of: "+str(fontdict[key]),true)
					else:
						if (typeof(fontdict[key]) in [TYPE_INT,TYPE_REAL]) == true:
							default_font_dict[key] = fontdict[key]
						else:
							print("Unknown data: "+str(fontdict[key]))
							log_error("Invalid font dict data type: "+str(key) +"'s value of: "+str(fontdict[key]),true)
			elif default_font_dict.has(key) and default_font_dict[key] != fontdict[key]:
				if internal == false:
					log_error("Don't change "+str(key) +". It's the font that already exists. To remove this error, replace it with: "+default_font_dict[key],true)
				else:
					print("you changed example??? why - "+str(fontdict[key]))
	
	if typeof(transdict) != TYPE_DICTIONARY:
		transdict = {}
	if transdict.size() > 0:
		modarray[mods.TRANSLATION] = true

		trans_from_dict(transdict,"icondescribearray",icondescribearray)
		trans_from_dict(transdict,"describecorruptiondict",describecorruptiondict)
		trans_from_dict(transdict,"preview_words",eswordarray)
		trans_from_dict(transdict,"curseditemdescribedict",curseditemdescribedict,true)
		trans_from_dict(transdict,"curseditemname",curseditemstransname,true)
		trans_from_dict(transdict,"settingstextdict",settingstextdict)
		trans_from_dict(transdict,"racearray",racearray)
		trans_from_dict(transdict,"raceclassarray",raceclassarray)
		trans_from_dict(transdict,"bodypartdict",bodypartdict)
		trans_from_dict(transdict,"debuffdescriptionarray",debuffdescriptionarray)
		trans_from_dict(transdict,"debuffidentifyarray",debuffidentifyarray)
		trans_from_dict(transdict,"debuffidentifynegativearray",debuffidentifynegativearray)
		trans_from_dict(transdict,"monster_display_names",monster_display_names,true)
		
		trans_from_dict(transdict,"describe_charm_dict",describe_charm_dict,true)
		trans_from_dict(transdict,"muffled_text_array",muffle_array)
		
		if transdict.has("mainmenu"):
			trans_from_dict(transdict["mainmenu"],"veiltextdict",veiltextdict)

		trans_from_dict(transdict,"customizationtextarray",customizationtextarray)

		trans_from_dict(transdict,"tabinfodict",tabinfodict)
		if transdict.has("specialmenu"):
			if transdict["specialmenu"].has("consentmessage"):
				consentmessage = transdict["specialmenu"]["consentmessage"]
		
		if transdict.has("maptext"):
			trans_from_dict(transdict["maptext"],"areastringdict",areastringdict,true)
			trans_from_dict(transdict["maptext"],"shortareastringdict",shortareastringdict,true)
			trans_from_dict(transdict["maptext"],"queststringarray",queststringarray)
			trans_from_dict(transdict["maptext"],"namemissiondict",namemissiondict)
			trans_from_dict(transdict["maptext"],"chooselabelsarray",chooselabelsarray)

		if transdict.has("misc_ui"):
			trans_from_dict(transdict["misc_ui"],"petalmenutext",petalmenutext)
			trans_from_dict(transdict["misc_ui"],"spec_enemy_text_array",spec_enemy_text_array)
#			if transdict["misc_ui"].has("petalmenutext"):
#				petalmenutext = transdict["misc_ui"]["petalmenutext"]
		if modify_fonts == true:
			get_node("/root/Master").call("modify_fonts")
		get_node("/root/Master").call("translate_main_menu_via_master")
#	if activate_mods == true:
#		print("Activated some manner of mods.")
#		print("mods loaded after this many milliseconds: "+str(float(OS.get_ticks_msec() - test_timer)/1000))
	current_translation_code = orderkey
	savevalue("Translation","Lasttranslation",current_translation_code)
	return

func trans_from_dict(dict,key,addtovar,integers = false):
	if dict.has(key):
		if typeof(dict[key]) == TYPE_DICTIONARY:
			if typeof(addtovar) == TYPE_DICTIONARY:
				for usekey in dict[key]:
					if integers == true and usekey.is_valid_integer():
						addtovar[usekey.to_int()] = dict[key][usekey]
					else:
						addtovar[usekey] = dict[key][usekey]
			else:
				print("error in trans-from-dict. putting a DICT into a non-DICT:"+str(dict[key]))
		elif typeof(dict[key]) == TYPE_ARRAY:
			if typeof(addtovar) == TYPE_ARRAY:
				for i4 in range(min(dict[key].size(),addtovar.size())):
					addtovar[i4] = dict[key][i4]
			else:
				print("error in trans-from-dict. putting aN ARRAY into a non-ARRAY:"+str(dict[key]))
		else:
			print("Could not find typeof:"+str(dict[key]))

func order_by_date(usearray):
	var clear = false
	while clear == false:
		clear = true
		for i3 in range(usearray.size()-1):
			if compare_date(usearray[i3],usearray[i3+1]) == false:#orderedresults[i3] < orderedresults[i3+1]:
				var value = usearray[i3]
				usearray.remove(i3)
				usearray.append(value)
				clear = false
	return usearray
func compare_date(left,right)->bool:
	var leftarray = []
	var rightarray = []
	while left.find(".") > -1:
		leftarray.append(left.to_int())
		left = left.substr(left.find(".")+1,-1)
	while right.find(".") > -1:
		rightarray.append(right.to_int())
		right = right.substr(right.find(".")+1,-1)
	for i in range(leftarray.size()):
		if i > (rightarray.size()-1):
			return true
		if leftarray[i] == rightarray[i]:
			pass
		elif leftarray[i] > rightarray[i]:
			return true
		else:
			return false
	if rightarray.size() > leftarray.size():
		return false
	else:
		return true

func set_inventory_to_class(force=false):
	if StagesCleared[StageRef.TUTORIAL] > 5 or force == true:
		playerinventory2darray = get("classarray"+str(CurrentClass)).duplicate(true)
		if "classarray"+str(CurrentClass)+"alt"+str(AltClass) in self:
			playerinventory2darray = playerinventory2darray + get("classarray"+str(CurrentClass)+"alt"+str(AltClass)).duplicate()
		playerconsumables2darray = baseconsumables.duplicate(true)
		if christmas_quest == true:
			playerinventory2darray.append([203,0,2])
		if force == true:
			identify_inventory()
	else:
		playerinventory2darray = []
		playerconsumables2darray = []

var mobile_ads = true

func set_base_colour():
	baseplayercolourarray = truecolourarray.duplicate()
	alter_material_array = [false,false,false,false,false]
	match get_corruption("colour"):
		raceHUMAN:pass
#			baseplayercolourarray = truecolourarray.duplicate()
#			set_eye_materials()
#			set_rank_materials()
#			set_hair_materials()
#			set_skin_materials()
#			set_clothes_materials()
		raceRAM:
#			if makeuprank == true:
#				corrupt_colour(pcol.HAIR,24)
#				corrupt_colour(pcol.SKIN,10)
#			else:
			corrupt_colour(pcol.HAIR,23)
			corrupt_colour(pcol.SKIN,9)
		raceNEKO:
#			if CurrentClass == raceNEKO and AltClass == 1:
#				corrupt_colour(pcol.HAIR,22)
#			else:
			corrupt_colour(pcol.HAIR,21)
			corrupt_colour(pcol.SKIN,8)
			corrupt_colour(pcol.CLOTHES,6)
			corrupt_colour(pcol.PANTS,6)
		raceKITSUNE:
			corrupt_colour(pcol.SKIN,6)
			corrupt_colour(pcol.HAIR,16)
			corrupt_colour(pcol.EYES,11)
		raceHARPY:
			if get_corruption("backhorns") == raceHARPY or get_corruption("tail") == raceHARPY:
				corrupt_colour(pcol.HAIR,18)#,true)
				corrupt_colour(pcol.SKIN,12)
				harpy_variants = true
			else:
				corrupt_colour(pcol.SKIN,11)
				corrupt_colour(pcol.HAIR,17)#,true)
				harpy_variants = false
		raceWOLF:
			corrupt_colour(pcol.HAIR,20)
			corrupt_colour(pcol.SKIN,10)
		raceIMP:
			corrupt_colour(pcol.SKIN,7)
			corrupt_colour(pcol.HAIR,19)
#			set_rank_materials()
#			set_clothes_materials()
	set_hair_materials(true)
	set_eye_materials()
	set_rank_materials()
	set_skin_materials()
	set_clothes_materials()
	set_horn_materials()

var alter_material_array = [false,false,false,false,false]
func corrupt_colour(part,colnum):#,override = false):
	baseplayercolourarray[part] = colnum
	alter_material_array[part] = true
#	match part:
#		pcol.EYES:set_eye_materials()
#		pcol.HAIR:
#			set_hair_materials()
#			if get_corruption("top") == raceWOLF:
#				set_clothes_materials()
#		pcol.SKIN:set_skin_materials()
#		pcol.CLOTHES:set_clothes_materials()
#		pcol.PANTS:set_clothes_materials()

func get_materials(num)->Vector3:
	var materialcase
	match num:
		pcol.RANK:materialcase = get_node("/root/Master/global_materials/global_rankshift").material
		pcol.EYES:materialcase = get_node("/root/Master/global_materials/global_eyeshift").material
		pcol.HAIR:materialcase = get_node("/root/Master/global_materials/global_hairshift").material
		pcol.SKIN:materialcase = get_node("/root/Master/global_materials/global_skinshift").material
		pcol.CLOTHES:materialcase = get_node("/root/Master/global_materials/global_clothesshift").material
		pcol.PANTS:materialcase = get_node("/root/Master/global_materials/global_pantsshift").material
		_:
			print("ERROR in get_materials: invalid num:"+str(num))
			return Vector3(1.0,1.0,1.0)
	return Vector3(materialcase.get_shader_param("hue_shift"),materialcase.get_shader_param("sat_mul"),materialcase.get_shader_param("val_mul"))

var hypno = false
func set_eye_materials():
	var eye_material = get_node("/root/Master/global_materials/global_eyeshift").material
	var eyecolHSV
	if hypno == true:
		eyecolHSV = newcolorarrayeye[1]
	elif get_corruption("eyes") == raceRAM:
		if makeuprank == true:
			eyecolHSV = RANKhsv[RAMGIRL]
			eyecolHSV.y -= 1
			eyecolHSV.z -= 1
		else:
			eyecolHSV = newcolorarrayeye[1]
	else:
		eyecolHSV = newcolorarrayeye[baseplayercolourarray[0]]
	if alter_material_array[pcol.EYES] == true:
		eyecolHSV = elite_alter(eyecolHSV,true)
	eye_material.set_shader_param("hue_shift",eyecolHSV.x)
	eye_material.set_shader_param("sat_mul",eyecolHSV.y)
	eye_material.set_shader_param("val_mul",eyecolHSV.z)

func set_rank_materials():
	var rank_material = get_node("/root/Master/global_materials/global_rankshift").material
	var rank_hsv
	if CurrentClass == raceIMP and get_corruption("colour") == raceIMP:
#		rank_hsv = Vector3(0.86,0.83,0.9)
		rank_hsv = Vector3(0.86,1.0,0.8)
		rank_hsv.x += IMPRANKhsv[AltClass].x
	elif AltClass > 0:
		match CurrentClass:
			raceNEKO:rank_hsv = RANKhsv[CATGIRL]
			raceRAM:rank_hsv = RANKhsv[RAMGIRL]
#			raceIMP:rank_hsv = IMPRANKhsv[AltClass]
#			raceKITSUNE:rank_hsv = RANKhsv[FOXGIRL]
			_:rank_hsv = Vector3(0.0,1.0,1.0)
	else:
		rank_hsv = Vector3(0.0,1.0,1.0)
	rank_material.set_shader_param("hue_shift",rank_hsv.x)
	rank_material.set_shader_param("sat_mul",rank_hsv.y)
	rank_material.set_shader_param("val_mul",rank_hsv.z)

func player_elite_state()->bool:
	match get_corruption("colour"):
		raceRAM:
			return makeuprank
#		raceKITSUNE:
#			return possessionrank
		raceIMP,raceNEKO,raceKITSUNE:
			return AltClass > 0
		_:return false

func elite_alter(use_hsv,minuscase)->Vector3:
	if player_elite_state() == true:
		var casecolor = get_corruption("colour")
		match casecolor:
			raceIMP:
				use_hsv.x = use_hsv.x + IMPRANKhsv[AltClass].x
				if use_hsv.x > 1:
					use_hsv.x -= 1
			raceHUMAN:pass
			_:
				var monsternum = enemyracearray.find(casecolor)
				if monsternum > -1:
					use_hsv.x = use_hsv.x + RANKhsv[monsternum].x
					
					if minuscase == true:
						use_hsv.y = (use_hsv.y+1 * (0.33 + 0.666*RANKhsv[monsternum].y))-1
						use_hsv.z = (use_hsv.z+1 * (0.33 + 0.666*RANKhsv[monsternum].z))-1
					else:
						use_hsv.y = use_hsv.y * (0.33 + 0.666*RANKhsv[monsternum].y)
						use_hsv.z = use_hsv.z * (0.33 + 0.666*RANKhsv[monsternum].z)
					
#					use_hsv.y = use_hsv.y * (0.33 + 0.666*RANKhsv[monsternum].y)
#					use_hsv.z = use_hsv.z * (0.33 + 0.666*RANKhsv[monsternum].z)

					if use_hsv.x > 1:
						use_hsv.x -= 1
	return use_hsv

var possessed = false
func set_hair_materials(sweep=false):
	var specialhair
	if possessed == true:
		if possessionrank == true:
			specialhair = 15
		else:
			specialhair = 14
	else:
		specialhair = baseplayercolourarray[1]
	var haircolHSV = newcolorarrayhair[specialhair]
	if alter_material_array[pcol.HAIR] == true:
		haircolHSV = elite_alter(haircolHSV,false)
	var hair_material = get_node("/root/Master/global_materials/global_hairshift").material
	hair_material.set_shader_param("hue_shift",haircolHSV.x)
	hair_material.set_shader_param("sat_mul",haircolHSV.y)
	hair_material.set_shader_param("val_mul",haircolHSV.z)
	if sweep == false and get_corruption("top") == raceWOLF:
		set_clothes_materials()

func set_skin_materials():
	var skincolHSV = newcolorarrayskin[baseplayercolourarray[2]]
	if CurrentClass == raceIMP and get_corruption("colour") == raceIMP:
		skincolHSV.x = skincolHSV.x + IMPRANKhsv[AltClass].x
		if skincolHSV.x > 1:
			skincolHSV.x -= 1
	if alter_material_array[pcol.SKIN] == true:
		skincolHSV = elite_alter(skincolHSV,true)
	var skin_material = get_node("/root/Master/global_materials/global_skinshift").material
	skin_material.set_shader_param("hue_shift",skincolHSV.x)
	skin_material.set_shader_param("sat_mul",skincolHSV.y)
	skin_material.set_shader_param("val_mul",skincolHSV.z)
	skin_material = get_node("/root/Master/global_materials/global_skinshift_rules_face").material
	skin_material.set_shader_param("hue_shift",skincolHSV.x)
	skin_material.set_shader_param("sat_mul",skincolHSV.y)
	skin_material.set_shader_param("val_mul",skincolHSV.z)


func set_clothes_materials():
	var clothescolHSV = newcolorarrayclothes[baseplayercolourarray[3]]
	var pantscolHSV =  newcolorarrayclothes[baseplayercolourarray[4]]
	var topnum = get_corruption("top")
	if topnum == raceWOLF:
		if get_corruption("colour") == raceIMP:
			clothescolHSV = Vector3(0.35,1.0,1.0)
			if CurrentClass == raceIMP:
				clothescolHSV.x += IMPRANKhsv[AltClass].x
				if clothescolHSV.x > 1:
					clothescolHSV.x -= 1
		elif wolf_variants == true:
#			clothescolHSV = newcolorarrayhair[baseplayercolourarray[1]]
			clothescolHSV = get_materials(pcol.HAIR)
		else:
			clothescolHSV = Vector3(0,1,1)
	elif get_corruption("colour") == raceIMP:
		clothescolHSV.x = 0
		clothescolHSV.x  += IMPRANKhsv[AltClass].x
		if topnum == raceRAM:
			clothescolHSV.x -= 0.1
		if clothescolHSV.x < 0:
			clothescolHSV.x += 1
	elif topnum == raceRAM:
		if get_corruption("colour") == raceRAM:
			if AltClass == 1:
				clothescolHSV = RANKhsv[RAMGIRL]
			else:
				clothescolHSV = newcolorarrayclothes[0]
		else:
			if alter_material_array[pcol.CLOTHES] == true:
				clothescolHSV = elite_alter(clothescolHSV,false)
			if topnum == raceRAM and baseplayercolourarray[3] != 0:
				clothescolHSV.x -= 0.1
				if clothescolHSV.x < 0:
					clothescolHSV.x += 1
	elif alter_material_array[pcol.CLOTHES] == true:
		clothescolHSV = elite_alter(clothescolHSV,false)
	
	if get_corruption("colour") == raceIMP:
		pantscolHSV = newcolorarrayclothes[0]
		pantscolHSV.x = pantscolHSV.x + IMPRANKhsv[AltClass].x
		if pantscolHSV.x > 1:
			pantscolHSV.x -= 1
	elif get_corruption("pants") == raceRAM:
		if get_corruption("colour") == raceRAM:
			if AltClass == 1:
				pantscolHSV = RANKhsv[RAMGIRL]
			else:
				pantscolHSV = newcolorarrayclothes[0]
		elif baseplayercolourarray[4] != 0:
			if alter_material_array[pcol.PANTS] == true:
				pantscolHSV = elite_alter(pantscolHSV,false)
			pantscolHSV.x -= 0.1
			if pantscolHSV.x < 0:
				pantscolHSV.x += 1
	elif alter_material_array[pcol.PANTS] == true:
		pantscolHSV = elite_alter(pantscolHSV,false)

	var clothes_material = get_node("/root/Master/global_materials/global_clothesshift").material
	clothes_material.set_shader_param("hue_shift",clothescolHSV.x)
	clothes_material.set_shader_param("sat_mul",clothescolHSV.y)
	clothes_material.set_shader_param("val_mul",clothescolHSV.z)
	
	var pants_material = get_node("/root/Master/global_materials/global_pantsshift").material
	pants_material.set_shader_param("hue_shift",pantscolHSV.x)
	pants_material.set_shader_param("sat_mul",pantscolHSV.y)
	pants_material.set_shader_param("val_mul",pantscolHSV.z)


func set_horn_materials():
	var horncolHSV = newcolorarrayhorns[0]
	if harpy_variants == true:#get_corruption("tail") == raceHARPY:
		horncolHSV = newcolorarrayhorns[1]
	else:
		var usecolor = get_corruption("colour")
		match usecolor:
			raceRAM:pass
			raceHARPY:
				if get_corruption("backhorns") == raceHARPY:
					horncolHSV = newcolorarrayhorns[1]
				else:
					horncolHSV = newcolorarrayhorns[2]
			raceIMP:horncolHSV = newcolorarrayhorns[3]
			raceHUMAN,_:
				if CurrentClass == raceRAM:
					pass
				elif get_corruption("backhorns") == raceHARPY:
					horncolHSV = newcolorarrayhorns[1]
				elif CurrentClass == raceHARPY:
					horncolHSV = newcolorarrayhorns[2]
				elif CurrentClass == raceIMP or curseditemdict[PIXIEDUST] == true or get_corruption("backhorns") == raceIMP:
					horncolHSV = newcolorarrayhorns[3]
	var horn_material = get_node("/root/Master/global_materials/global_hornshift").material
	horn_material.set_shader_param("hue_shift",horncolHSV.x)
	horn_material.set_shader_param("sat_mul",horncolHSV.y)
	horn_material.set_shader_param("val_mul",horncolHSV.z)


func get_corruption(string)->int:
	if corruptiondict.has(string):
		return corruptiondict[string]
	else:
		if debugmodeon == true:
			if bodypartdict.has(string) == false:
				print("ERROR IN GET_CORRUPTION: corruption does not exist: "+str(string))
		return 0
