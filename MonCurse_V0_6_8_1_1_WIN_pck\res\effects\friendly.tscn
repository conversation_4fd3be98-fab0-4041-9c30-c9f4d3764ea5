[gd_scene load_steps=4 format=2]

[ext_resource path="res://Assets/enemyfriendly.png" type="Texture" id=1]

[sub_resource type="Shader" id=67]
code = "shader_type canvas_item;

uniform vec4 shadow_color : hint_color;

uniform float sine_time_scale = 0.03;
uniform vec2 sine_offset_scale = vec2(0.4, 0.4);

vec2 calculate_sine_wave(float time, float multiplier, vec2 uv, vec2 offset_scale) {
	float time_multiplied = time * multiplier;
	float unique_offset = -uv.x + uv.y*3.0;
	return vec2(
		clamp(sin(time_multiplied + unique_offset * offset_scale.x),0,1),
		clamp(cos(time_multiplied + unique_offset * offset_scale.y),0,1)
	);
}

void fragment() {
	vec2 sine_offset = calculate_sine_wave(TIME, sine_time_scale, UV, sine_offset_scale);

	vec4 diffuse_color = texture(TEXTURE, UV);
	vec4 newshadow_color = vec4(shadow_color.rgb, diffuse_color.a);
	vec4 finalcolor = mix(diffuse_color, newshadow_color,sine_offset.y);
//	finalcolor = mix(diffuse_color, finalcolor,sine_offset.y);
	COLOR = finalcolor;
}"

[sub_resource type="ShaderMaterial" id=68]
shader = SubResource( 67 )
shader_param/shadow_color = Color( 0.929412, 0.74902, 0.92549, 1 )
shader_param/sine_time_scale = 2.0
shader_param/sine_offset_scale = Vector2( 1.5, 1.5 )

[node name="friendly" type="Sprite"]
self_modulate = Color( 1, 1, 1, 0.588235 )
material = SubResource( 68 )
z_index = 8
texture = ExtResource( 1 )
offset = Vector2( 0, -120 )

[node name="super1" type="Sprite" parent="."]
visible = false
self_modulate = Color( 1, 1, 1, 0.588235 )
show_behind_parent = true
use_parent_material = true
position = Vector2( 15, -144 )
scale = Vector2( 0.7, 0.7 )
texture = ExtResource( 1 )

[node name="super2" type="Sprite" parent="."]
visible = false
self_modulate = Color( 1, 1, 1, 0.588235 )
show_behind_parent = true
use_parent_material = true
position = Vector2( -15, -144 )
scale = Vector2( 0.7, 0.7 )
texture = ExtResource( 1 )
