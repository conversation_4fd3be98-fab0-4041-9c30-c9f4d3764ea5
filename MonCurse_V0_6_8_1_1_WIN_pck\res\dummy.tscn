[gd_scene load_steps=6 format=2]

[ext_resource path="res://Enemy/dummy.png" type="Texture" id=1]
[ext_resource path="res://dummy.gd" type="Script" id=2]

[sub_resource type="AtlasTexture" id=1]
atlas = ExtResource( 1 )
region = Rect2( 0, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=2]
atlas = ExtResource( 1 )
region = Rect2( 128, 0, 128, 128 )

[sub_resource type="SpriteFrames" id=3]
animations = [ {
"frames": [ SubResource( 1 ), SubResource( 2 ) ],
"loop": true,
"name": "default",
"speed": 6.0
} ]

[node name="dummy" type="AnimatedSprite"]
z_index = 9
frames = SubResource( 3 )
script = ExtResource( 2 )

[node name="enemyturn" type="Timer" parent="."]
