extends Control

func _ready():
	if Playervariables.current_translation_code == Playervariables.TL_DEFAULT or Playervariables.current_translation_code.find("internalmods") == -1:
		$new2/VBoxContainer/CheckBox.disabled = true
		$new2/VBoxContainer/CheckBox.pressed = false
	else:
		$new2/VBoxContainer/CheckBox/Label.set_text(Playervariables.current_translation_code)
	$TextureRect/Label.set_text("Export Official\nEnglish Ver.")
	_on_CheckBox_pressed()
	var selfpath = OS.get_executable_path() #may not work well in editor. Sorry! Try it out in the exported version.
	selfpath = selfpath.substr(selfpath.rfind("/"),-1)
	selfpath = selfpath.substr(selfpath.find("V"),-1)
	selfpath = selfpath.substr(0,selfpath.rfind("_"))
	if selfpath.length() > 4:
		$reject.set_text(selfpath)
		_on_reject_text_changed()

func process_speechdict(filestring):
	var speechdatafile = File.new()
	speechdatafile.open(filestring, File.READ) #special version takes full filepaths
	var speechdatajson = JSON.parse(speechdatafile.get_as_text())
	speechdatafile.close()
	return speechdatajson.result

func write_json(filepath,dict):
	var savedatafile = File.new()
	savedatafile.open(filepath, File.WRITE) #special version takes full filepaths
#	savedatafile.store_line(to_json(finaldictionary))
#	if trans() == false:
#	for key in dict:
#		savedatafile.store_line(to_json(str(key)+":"+str(dict[key])))
#	else:
	savedatafile.store_string(JSON.print(dict,"\t"))
#	savedatafile = JSON.new().stringify(finaldictionary)
	savedatafile.close()

var finaldictionary = {}

#func trans() -> bool: return true#return $VBoxContainer/Translation.pressed

func copy_folder():
	if create_mods_directory() == false:
		return null
	var dir = Directory.new()
	dir.open("res://")
	if dir.dir_exists(Playervariables.current_translation_code):
		var path = OS.get_executable_path().get_base_dir()
		var nointernalstring = Playervariables.current_translation_code.substr("internalmods/".length(),-1)
		var otherdir = Directory.new()
		otherdir.open(path+"/mods/")
		if otherdir.dir_exists(nointernalstring) == false:
			otherdir.make_dir(nointernalstring)
			if otherdir.dir_exists(nointernalstring) == false:
				$TextureRect/Label.set_text("ERROR: Could not make folder titled 'mods'.\nPlease manually create a folder named 'mods' next to the .exe.")
				return false
		else:
			$TextureRect/Label.set_text("ERROR: Export folder already exists.\n Rename or move the folder named: "+str(nointernalstring))
			return false
		var filearray = list_files_in_directory("res://"+Playervariables.current_translation_code+"/",false,true)
		for file in filearray:
			dir.copy(file,path+"/mods/"+nointernalstring+"/"+file.substr(file.rfind("/"),-1))
		$TextureRect/Label.set_text("in mods/translation, there\n should now be a new folder: "+nointernalstring)
	else:
		$TextureRect/Label.set_text("ERROR: Could not find internal directory: "+str(Playervariables.current_translation_code))
		return null

onready var startstringlength = groups[0].length()
var groups = ["res://Conversations/","res://Conversations/badend/","res://Conversations/load/","res://Conversations/quip/"]
func generate_file(exported = true):
# warning-ignore:return_value_discarded
#	connect("continu",self,"placeholder")
	$new2/VBoxContainer/CheckBox.disabled = true
	_on_CheckBox_pressed()
	for gr in groups:
		var nextfiles = list_files_in_directory(gr)
		$TextureRect/sub.set_text(gr)
		for i in range(nextfiles.size()):
			$TextureRect/Label.set_text(nextfiles[i].substr(startstringlength,-1))
#			yield(self,"continu")
#			if accept == true:
			addedlist.append(nextfiles[i].substr(startstringlength,-1)+".....")
			transkey.append(nextfiles[i].substr(startstringlength,-1))
			parselist.append(nextfiles[i])
			$all.set_text(str(addedlist))
	if addedlist.size() > 0:
		addedlist[-1] = addedlist[-1].substr(0,(addedlist[-1].length())-5)
	$TextureRect/sub.set_text("Now parsing.")
	$TextureRect/Label.set_text("Please wait.")
	var inc = 0
	for file in parselist:
		var result = process_speechdict(file)
		var chararray = ["Narration"]
#		var newentry = {}
		for key in result.keys():
			if key == "scenario":
#				if trans() == true:
				var i = 1
				while result[key].has("char"+str(i)):
					chararray.append(result[key]["char"+str(i)])
					i += 1
#					newentry = {"Char1+Char2":result["scenario"].get("char1")+"+"+result["scenario"].get("char2")}
#					finaldictionary[transkey[inc]] = {"Char1+Char2":result["scenario"].get("char1")+"+"+result["scenario"].get("char2")}
				finaldictionary[transkey[inc]] = {}
#				else:
#					finaldictionary[addedlist[inc].substr(0,addedlist[inc].length()-5)] = inc
			else:
#				if trans() == true:
				if optiontext == true and result[key]["options"].size() > 0:
#						newentry[str(inc)+"."+key+"-CHOICE"] = result[key]["options"]
					finaldictionary[transkey[inc]][key+".CHOICE"] = result[key]["options"]
				if maintext == true:
#						newentry[str(inc)+"."+key] = result[key]["speech"]
					var altconditiontext = ""
					if result[key]["altconditions"].size() > 0:
						altconditiontext = "... alt under conditions: "
						for text in result[key]["altconditions"]:
							text = text.replace("Playervariables.prefdict[2] == false","Futa enemies toggled: OFF")
							text = text.replace("Playervariables.prefdict[2] == true","Futa enemies toggled: ON")
							text = text.replace("Playervariables.prefdict[3] == false","Extreme breast expansion toggled: OFF")
							text = text.replace("Playervariables.prefdict[3] == true","Extreme breast expansion toggled: ON")
							text = text.replace("Playervariables.prefdict[4] == false","Pregnancy toggled: OFF")
							text = text.replace("Playervariables.prefdict[4] == true","Pregnancy toggled: ON")
							text = text.replace("Playervariables.prefdict[5] == false","Pseudopregnancy/eggstuff toggled: OFF")
							text = text.replace("Playervariables.prefdict[5] == true","Pseudopregnancy/eggstuff toggled: ON")
							text = text.replace("Playervariables.prefdict[6] == false","Unbirth/softvore toggled: OFF")
							text = text.replace("Playervariables.prefdict[6] == true","Unbirth/softvore toggled: ON")
							text = text.replace("5eyes","Player has ram-makeup")
							text = text.replace("5horns","Player has ram-horns")
							text = text.replace("4ears","Player has fox-ears")
							text = text.replace("3ears","Player has cat-ears")
							text = text.replace("6ears","Player has wolf-ears")
							text = text.replace("3tail","Player has cat-tail")
							text = text.replace("4tail","Player has fox-tail")
							text = text.replace("6tail","Player has wolf-tail")
							text = text.replace("Playervraiables.CurrentClass == 0","Player is class: Human")
							text = text.replace("Playervraiables.CurrentClass == 1","Player is class: Cow")
							text = text.replace("Playervraiables.CurrentClass == 3","Player is class: Cat-Girl")
							text = text.replace("Playervraiables.CurrentClass == 4","Player is class: Fox-Girl")
							text = text.replace("Playervraiables.CurrentClass == 5","Player is class: Ram")
							text = text.replace("Playervraiables.CurrentClass == 6","Player is class: Werewolf")
							text = text.replace("Playervraiables.CurrentClass == 7","Player is class: Harpy")
							text = text.replace("7armright","Player has harpy wings")
							text = text.replace("7leg","Player has harpy legs")
							text = text.replace("3armright","Player has cat-paws")
							text = text.replace("5top","Player has ram clothes top")
							text = text.replace("6top","Player has wolf tar pasties top")
							text = text.replace("5pants","Player has ram clothes pants")
							text = text.replace("endRamGirl0","Player has seen Ram Ending 0")
							text = text.replace("endRamGirl1","Player has seen Ram Ending 1")
							text = text.replace("endCatKnight0","Player has seen Cat Ending 0")
							text = text.replace("endFoxGirl0","Player has seen Fox Ending 0")
							text = text.replace("Playervariables.playerbustsize","Player's bust size is")
							text = text.replace("Playervariables.sealsarray[0]","Player has SLUT fox-seal")
							text = text.replace("Playervariables.sealsarray[1]","Player has SEAL fox-seal")
							text = text.replace("Playervariables.sealsarray[2]","Player has OBEY fox-seal")
							text = text.replace("mainscene.playerdebuffarray[0] > 0","Player has DROWSY debuff")
							text = text.replace("mainscene.playerdebuffarray[1] > 0","Player has HYPNOSIS debuff")
							text = text.replace("mainscene.playerdebuffarray[2] > 0","Player has SWEATY debuff")
							text = text.replace("mainscene.playerdebuffarray[3] > 0","Player has CUM debuff")
							text = text.replace("mainscene.playerdebuffarray[4] > 0","Player has POSSESSION debuff")
							text = text.replace("mainscene.playerdebuffarray[5] > 0","Player has MARKEDorSTRIPPED debuff")
							text = text.replace("mainscene.playerdebuffarray[6] > 0","Player has HEAT debuff")
							text = text.replace("mainscene.playerdebuffarray[7] > 0","Player has IMPACT debuff")
							text = text.replace("mainscene.playerdebuffarray[8] > 0","Player has MILK debuff")
							text = text.replace("Playervariables.touchscreenmode == true","Player is using a touchscreen")
							text = text.replace("Playervariables.touchscreenmode == false","Player is NOT using a touchscreen")
							text = text.replace("mainscene.capture_state == 2","Player is trapped in a harpy's egg.")
							altconditiontext += text + ", "
					var debugtext = ""
					if result[key].has("debug"):
						debugtext = "- tl_note (no_translation_needed):"
						for text in result[key]["debug"]:
							debugtext += text
					finaldictionary[transkey[inc]][key+".char"] = chararray[result[key]["charswitch"]] + debugtext + altconditiontext
					finaldictionary[transkey[inc]][key] = result[key]["speech"]
#				else:
#					if optiontext == true and result[key]["options"].size() > 0:
#						finaldictionary[str(inc)+"."+key+"-CHOICE"] = result[key]["options"]
#					if maintext == true:
#						finaldictionary[str(inc)+"."+key] = result[key]["speech"]
#		if trans() == true:
#			finaldictionary[transkey[inc]] = stringify(newentry,"")
		inc += 1
#	if trans() == true:# and $VBoxContainer/Translation2.pressed == true:
	
	finaldictionary["meta"] = {"Icon_Path":null,
		"Translator_Comment":"No comment",
		"Game_Version":"V0.6.???"
	}
	if versionvalid == true:
		var text = $reject.get_text()
		var numbify = str(text.replace(".","").to_int())
		var startnum = min(text.find(numbify.substr(0,1)),text.find("0."))
		var endnum = text.find(numbify.substr(numbify.length()-1,1))
		finaldictionary["meta"]["Game_Version"] = "V" + text.substr(startnum,endnum)
	finaldictionary["misc_ui"] = {
	"DISCARD":"DISCARD",
	"REMOVE":"REMOVE"}
	
	var playervariablescopy = load("playervariables.gd")
	var newnode = TextureRect.new()
	newnode.set_script(playervariablescopy)
	add_child(newnode)
	
	finaldictionary["misc_ui"]["petalmenutext"] = newnode.petalmenutext
	finaldictionary["misc_ui"]["spec_enemy_text_array"] = newnode.spec_enemy_text_array
	
	finaldictionary["mainmenu"] = {}
	finaldictionary["mainmenu"]["progress"] = "Class:VAR1\n\nProgress:\n\nStages Cleared: VAR2 / VAR3\nTransformations: VAR4 / VAR5\nCGs: VAR6 / VAR7\nEndings: VAR8 / VAR9"
	finaldictionary["mainmenu"]["patreonbutton"] = "Support \nthe Game"
	finaldictionary["mainmenu"]["discordbutton"] = "Join the\n Community"
	finaldictionary["mainmenu"]["veiltextdict"] = newnode.veiltextdict
	finaldictionary["mainmenu"]["yes"] = "Yes"
	finaldictionary["mainmenu"]["cancel"] = "Cancel"
	
	finaldictionary["specialmenu"] = {}
	finaldictionary["specialmenu"]["fullscreenconfirm"] = "Please press this to confirm,\nelse it will revert back in:"
	finaldictionary["specialmenu"]["fullscreencancel"] = "Revert to Windowed"
	finaldictionary["specialmenu"]["consentmessage"] = newnode.consentmessage
	
	finaldictionary["maptext"] = {}
	finaldictionary["maptext"]["areastringdict"] = newnode.areastringdict
	finaldictionary["maptext"]["shortareastringdict"] = newnode.shortareastringdict
	finaldictionary["maptext"]["queststringarray"] = newnode.queststringarray
	finaldictionary["maptext"]["namemissiondict"] = newnode.namemissiondict
	finaldictionary["maptext"]["chooselabelsarray"] = newnode.chooselabelsarray
	
	finaldictionary["settingstextdict"] = newnode.settingstextdict
	
	finaldictionary["attacknames"] = {}
#{"soft_attack_name":Playervariables.soft_attack_name,
#		"basic_attack_name":Playervariables.basic_attack_name,
#		"swipe_attack_name":Playervariables.swipe_attack_name}
#		var movearray = []
	for i in range(2000):
		if "Move"+str(i) in newnode:
			finaldictionary["attacknames"][newnode.get("Move"+str(i)).get("name")] = newnode.get("Move"+str(i)).get("name")
			if newnode.get("Move"+str(i)).get("info") != "":
				finaldictionary["attacknames"][newnode.get("Move"+str(i)).get("name")+"INFO"] = newnode.get("Move"+str(i)).get("info")
#				movearray.append(i)
#		finaldictionary["attacknames"]["Move_Array"] = movearray
	finaldictionary["describecorruptiondict"] = newnode.describecorruptiondict
	finaldictionary["icondescribearray"] = newnode.icondescribearray
	finaldictionary["curseditemdescribedict"] = newnode.curseditemdescribedict
	finaldictionary["curseditemname"] = newnode.curseditemstransname
	finaldictionary["bodypartdict"] = newnode.bodypartdict
	finaldictionary["debuffdescriptionarray"] = newnode.debuffdescriptionarray
	finaldictionary["debuffidentifyarray"] = newnode.debuffidentifyarray
	finaldictionary["debuffidentifynegativearray"] = newnode.debuffidentifynegativearray
	finaldictionary["preview_words"] = newnode.eswordarray
	finaldictionary["customizationtextarray"] = newnode.customizationtextarray
	finaldictionary["tabinfodict"] = newnode.tabinfodict
	
	finaldictionary["racearray"] = newnode.racearray
	finaldictionary["raceclassarray"] = newnode.raceclassarray
	
	finaldictionary["muffled_text_array"] = newnode.muffle_array
	
	finaldictionary["monster_display_names"] = newnode.monster_display_names
	
	finaldictionary["describe_charm_dict"] = newnode.describe_charm_dict

	finaldictionary["chapter_text"] = {}
	finaldictionary["event_messages"] = {}
	var alsotryfiles = list_files_in_directory("res://",true,false)
	for filepath in alsotryfiles:
		var f = File.new()
		var err = f.open(filepath, File.READ)
		if err == OK:
			var text = f.get_as_text()
			var findnum = 0
			while findnum > -1:
				findnum = text.find("register_event",findnum+16)
				if findnum > -1:
					var hashtest = text.rfind("#",findnum)
					var breaktest = text.rfind("\n",findnum)
					if breaktest <= hashtest:
						pass
#							print("Error: case commented out")
					else:
						var findcomma = text.find("\"",findnum)
						var findstring = text.substr(findnum,1+(findcomma-findnum))
						if findstring.find("register_event(\"") > -1 or findstring.find("register_event_via_main(\"") > -1 or findstring.find("register_event_via_master(\"") > -1:
#						if (findstring in ["register_event(\"","register_event_via_main(\"","register_event_via_master(\""]) == true:
	#						var begintext = text.find(",",findnum)
#							if text.substr(findnum-15,15).find("has_method") > -1 or text.substr(findnum-15,15).find("func") > -1:
							if text.substr(findnum-15,15).find("func") > -1:
								pass
#									print("has func exception.")
							else:
								var endtext = text.find("\"",findcomma+1)-1
								var finaltext = text.substr(findcomma+1,(endtext-findcomma))
								if finaldictionary["event_messages"].has(finaltext) == true or finaltext.to_lower().find("debug") > -1:
									pass
#										print("found dupe event")
								else:
									finaldictionary["event_messages"][finaltext] = finaltext
						else:
							pass
#								print("Couldn't find: "+findstring)
#							yield(get_tree(),"idle_frame")
			
			findnum = 0
			while findnum > -1:
				findnum = text.find("messagetiledict[Vector2",findnum+16)
				if findnum > -1:
					var hashtest = text.rfind("#",findnum)
					var breaktest = text.rfind("\n",findnum)
					if breaktest <= hashtest:
						pass
					else:
						var findcomma = text.find("\"",findnum)
#							var findstring = text.substr(findnum,1+(findcomma-findnum))
						var endtext = text.find("\"",findcomma+1)-1
						var finaltext = text.substr(findcomma+1,(endtext-findcomma))
						if finaldictionary["event_messages"].has(finaltext) == true:
							pass
						else:
							finaldictionary["event_messages"][finaltext] = finaltext
			
			findnum = 0
			
			while findnum > -1:
				findnum = text.find("attacking",findnum+12)
				if findnum > -1:
					var hashtest = text.rfind("#",findnum)
					var breaktest = text.rfind("\n",findnum)
					if breaktest <= hashtest:
						pass
#							print("Error: case commented out")
					else:
						var findstring = text.substr(findnum-6,20)
						if findstring.find("call(\"attacking\"") > -1 or findstring.find("attacking(") > -1:
#						if (findstring in ["call(\"attacking\",","attacking("]) == true:
							var findend = text.find("\n",findnum) -3
							var findcomma = text.rfind("\"",findend-1)
	#						var begintext = text.find(",",findnum)
#							if text.substr(findnum-15,15).find("has_method") > -1 or text.substr(findnum-15,15).find("func") > -1:
							if text.substr(findnum-15,15).find("func") > -1 or text.substr(findcomma-6,6).find("get(") > -1:
								pass
#									print("has func exception.")
							else:
								var finaltext = text.substr(findcomma+1,(findend-findcomma))
								if finaltext.find("\n") > -1 or finaltext.find("\t") > -1 or finaltext == "'s Urn" or finaltext.to_lower().find("debug") > -1:
									pass
#										print("error, attempted to put escape characters in output.")
								elif finaldictionary["attacknames"].has(finaltext) == true:
									pass
#										print("found dupe")
								else:
									finaldictionary["attacknames"][finaltext] = finaltext
#							else:
#								print("Couldn't find: "+findstring)
#							yield(get_tree(),"idle_frame")
				
			

			
			findnum = 0
			
			while findnum > -1:
				findnum = text.find("chapter_text(",findnum+18)
				if findnum > -1:
					var hashtest = text.rfind("#",findnum)
					var breaktest = text.rfind("\n",findnum)
					if breaktest <= hashtest:
						pass
#							print("Error: case commented out")
					else:
						var findstring = text.substr(findnum-17,20)
						if findstring.find("transdict") == -1:
							var findend = text.find("\n",findnum) -3
							var findcomma = text.rfind("\"",findend-1)
							var linestart = findnum - breaktest
							if text.substr(findnum-linestart,linestart).find("func") > -1:
								pass
							else:
								var finaltext = text.substr(findcomma+1,(findend-findcomma))
#									print(text.substr(findcomma+1,(findend-findcomma)))
								if finaltext.find("\n") > -1 or finaltext.find("\t") > -1 or finaltext == "'s Urn":
									pass
								elif finaldictionary["chapter_text"].has(finaltext) == true:
									pass
								else:
									finaldictionary["chapter_text"][finaltext] = finaltext
				
			
			
			f.close()
	var useful_font_dict = newnode.default_font_dict.duplicate()
	newnode.queue_free()
#			else:
#				print("error integer:"+str(err))
#	return
	if exported == false:
		return finaldictionary
	else:
		var timeDict = OS.get_datetime()
		var year = timeDict.year;
		var month = timeDict.month;
		var day = timeDict.day;
		var hour = timeDict.hour;
		var minute = timeDict.minute;
		var _seconds = timeDict.second;
		var uniquestring = str(year)+"."+str(month)+"."+str(day)+"."+str(hour)+"."+str(minute)
		if versionvalid == true:
			var startfind = $reject.get_text().to_float()
			var findnum = $reject.get_text().find(startfind)
#			print(startfind)
			var usetext = $reject.get_text().substr(findnum,-1)
#			print(usetext)
			var lastnumber = -1
			for i in range(usetext.length()):
				if usetext.substr(i,1) in ["0","1","2","3","4","5","6","7","8","9","."]:
					lastnumber = i+1
				else:
					break
	#		for i in range(10):
	#			if usetext.rfind(i) > lastnumber:
	#				lastnumber = usetext.rfind(i)
			uniquestring = usetext.substr(0,lastnumber)
		if addedlist.size() > 0:
			
			var path = OS.get_executable_path().get_base_dir()
			var dir = Directory.new()
			if create_mods_directory() == false:
				return null
			dir.open(path+"/mods/translation")
			if dir.get_current_dir().ends_with("translation") == false:
				$accept.visible = false
				$TextureRect/Label.set_text("ERROR: Failed to open the mods/translation/directory.")
				return null
			var teststring = uniquestring
			var i = 1
			while dir.file_exists("Translatable."+str(teststring)+".json") or dir.file_exists("OriginalDoNotAlter."+str(teststring)+".json"):
				teststring = uniquestring + "." + str(i)
				i += 1
			uniquestring = teststring
	#		write_json("res://donotexport/export jsons/ef."+str(uniquestring)+".json")
	#		var path = OS.get_executable_path().get_base_dir() +"/mods/translation"
			write_json(path+"/mods/translation/Translatable."+str(uniquestring)+".json",finaldictionary)
			write_json(path+"/mods/translation/OriginalDoNotAlter."+str(uniquestring)+".json",finaldictionary)
	#		yield(get_tree(),"idle_frame")
	#		yield(get_tree(),"idle_frame")
			dir = Directory.new()
			dir.open(path+"/mods/translation")
			if dir.file_exists("Translatable."+str(uniquestring)+".json") == false:
				$TextureRect/Label.set_text("ERROR: The mods/translation directory was found,\n but the ef.json file failed to write.")
			elif dir.file_exists("OriginalDoNotAlter."+str(uniquestring)+".json") == false:
				$TextureRect/Label.set_text("ERROR: The mods/translation directory was found,\n but the OriginalDoNotAlter.json version failed to write.")
			elif dir.file_exists("font_parameters.json") == false:
#				if Playervariables.current_translation_code != Playervariables.TL_DEFAULT:
#					write_json(path+"/mods/translation/font_parameters.json",Playervariables.font_dict)
#				else:
				write_json(path+"/mods/translation/font_parameters.json",useful_font_dict)
				if dir.file_exists("font_parameters.json") == true:
					$TextureRect/sub.set_text("Done! Also created font_parameters.json.")
				else:
					$TextureRect/sub.set_text("Done! However, had an issue creating font_parameters.json. This is bad if you need non-roman script.")
			else:
				$TextureRect/sub.set_text("Done! Check inside mods/translation.") #Files included: "+str(addedlist.size()))
				$TextureRect/Label.set_text("Your filename is: " +str("Translatable."+str(uniquestring)+".json\nThere is also an OriginalDoNotAlter copy provided."))
			$accept.visible = false
		else:
			$TextureRect/sub.set_text("My dude, you did not add any files.")
			$TextureRect/Label.set_text("You fool, you buffoon.")
#	$quit.visible = true
var transkey = []
var addedlist = []
var parselist = []

func list_files_in_directory(path,searchgd=false,any=false) -> Array:
	var files = []
	var dir = Directory.new()
	dir.open(path)
	dir.list_dir_begin()

	while true:
		var file = dir.get_next()
		if file == "":
			break
		else:
			if not file.begins_with("."):
				if any == true:
					files.append(path+file)
				elif searchgd == false:
					if file.ends_with(".json"):
						files.append(path+file)
				else:
					if file.ends_with(".gd"):# or file.ends_with(".gdc"):
						files.append(path+file)
					elif dir.current_is_dir() and file != "donotexport" and file != "exportedtools":
						var extrastrings = list_files_in_directory(path+file+"/",true)
						if typeof(extrastrings) == TYPE_ARRAY and extrastrings.size() > 0:
							files += extrastrings
	dir.list_dir_end()
	return files


func create_mods_directory() -> bool:
	var path = OS.get_executable_path().get_base_dir()
	var dir = Directory.new()
	dir.open(path)
	if dir.dir_exists("mods") == false:
		dir.make_dir("mods")
		if dir.dir_exists("mods") == false:
			$TextureRect/Label.set_text("ERROR: Could not make folder titled 'mods'.\nPlease manually create a folder named 'mods' next to the .exe.")
			return false
#			yield(get_tree(),"idle_frame")
#		dir.open("mods")

	dir.copy("res://internalmods/README for Translators.txt",path+"/mods/README for Translators.txt")
	dir.copy("res://internalmods/MoncurseJSONViewer.zip",path+"/mods/MoncurseJSONViewer.zip")
	dir = Directory.new()
	dir.open(path+str("/mods"))

	if dir.dir_exists("translation") == false:
		dir.make_dir("translation")
		if dir.dir_exists("translation") == false:
			$accept.visible = false
			$TextureRect/Label.set_text("ERROR: Could not make folder titled 'translation' inside of 'mods'.\nPlease manually create a folder named 'translation' inside the 'mods' folder.")
			return false
	return true

#var accept
#signal continu
func _on_accept_pressed():
	if $new2/VBoxContainer/CheckBox.pressed == true:
		copy_folder()
	else:
		generate_file()
#	accept = true
#	emit_signal("continu")

#func _on_reject_pressed():
#	return
#	accept = false
#	emit_signal("continu")

#func placeholder():
#	pass


func _on_quit_pressed():
	if get_parent().get_name() != "root":
		get_parent().visible = false
	queue_free()
#	get_tree().quit()

onready var maintext = $VBoxContainer/CheckBox2.pressed
onready var optiontext = $VBoxContainer/CheckBox.pressed

func _on_CheckBox2_toggled(button_pressed):
	maintext = button_pressed


func _on_CheckBox_toggled(button_pressed):
	optiontext = button_pressed

#
func _on_TextureButton_pressed():
	return
#	while $quit.visible == false:
#		accept = true
#		emit_signal("continu")
#		yield(get_tree(),"idle_frame")

var versionvalid = false
func _on_reject_text_changed():
	$reject.set_self_modulate(Color(1,0.5,0.5))
	versionvalid = false
	var rawnum = $reject.get_text().replace(".","").to_int()
	var findfirstnum = $reject.get_text().find($reject.get_text().to_int())
#	print(rawnum)
#	print($reject.get_text().find("."))
#	print($reject.get_text().substr(findfirstnum,-1).to_float())
	if rawnum > 60 and $reject.get_text().find(".") > -1 and $reject.get_text().substr(findfirstnum,-1).to_float() >= 0.6:
		$reject.set_self_modulate(Color(1,1,1))
		versionvalid = true


func _on_CheckBox_pressed():
	if $new2/VBoxContainer/CheckBox.disabled == true:
		$new2/VBoxContainer/CheckBox.disabled = false
		$new2/VBoxContainer/CheckBox.set_modulate(Color(0.9,0.6,0.5,0.7))
		$new2/VBoxContainer/CheckBox.disabled = true
	else:
		if $new2/VBoxContainer/CheckBox.pressed == true:
			$new2/VBoxContainer/CheckBox.set_modulate(Color(1,1,0.6,1))
			$TextureRect/Label.set_text("Export translated version:\n"+Playervariables.current_translation_code)
		else:
			$new2/VBoxContainer/CheckBox.set_modulate(Color(0.8,0.8,0.5,0.7))
			$TextureRect/Label.set_text("Export Official\nEnglish Ver.")
