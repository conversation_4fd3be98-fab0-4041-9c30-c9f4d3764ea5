[gd_scene load_steps=10 format=2]

[ext_resource path="res://newescalation.gd" type="Script" id=1]
[ext_resource path="res://Background/worldview.png" type="Texture" id=2]
[ext_resource path="res://Assets/selectorpathwhite.png" type="Texture" id=3]
[ext_resource path="res://Assets/selectorpath.png" type="Texture" id=4]
[ext_resource path="res://Assets/ui/gobackuipress.png" type="Texture" id=6]
[ext_resource path="res://Assets/ui/goback.png" type="Texture" id=7]
[ext_resource path="res://Assets/ui/gobackui.png" type="Texture" id=11]

[sub_resource type="Animation" id=3]
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="TileSet" id=4]
0/name = "selectorpath.png 0"
0/texture = ExtResource( 4 )
0/tex_offset = Vector2( 0, 0 )
0/modulate = Color( 1, 1, 1, 1 )
0/region = Rect2( 0, 0, 64, 64 )
0/tile_mode = 0
0/occluder_offset = Vector2( 0, 0 )
0/navigation_offset = Vector2( 0, 0 )
0/shape_offset = Vector2( 0, 0 )
0/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
0/shape_one_way = false
0/shape_one_way_margin = 0.0
0/shapes = [  ]
0/z_index = 0
1/name = "selectorpathwhite.png 1"
1/texture = ExtResource( 3 )
1/tex_offset = Vector2( 0, 0 )
1/modulate = Color( 1, 1, 1, 1 )
1/region = Rect2( 0, 0, 64, 64 )
1/tile_mode = 0
1/occluder_offset = Vector2( 0, 0 )
1/navigation_offset = Vector2( 0, 0 )
1/shape_offset = Vector2( 0, 0 )
1/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
1/shape_one_way = false
1/shape_one_way_margin = 0.0
1/shapes = [  ]
1/z_index = 0

[node name="EscalationWindow" type="Control"]
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource( 1 )

[node name="veilmap" type="TextureRect" parent="."]
visible = false
self_modulate = Color( 0.156863, 0.156863, 0.156863, 0.862745 )
anchor_left = -0.05
anchor_top = -0.05
anchor_right = 1.05
anchor_bottom = 1.05
margin_left = -7.11893
margin_top = -3.94052
margin_right = 2.88098
margin_bottom = 6.05945
texture = ExtResource( 2 )
expand = true
stretch_mode = 1

[node name="appear" type="AnimationPlayer" parent="."]
playback_speed = 2.0
anims/appearescalation = SubResource( 3 )

[node name="TextureButton" type="TextureButton" parent="."]
self_modulate = Color( 1, 1, 1, 0.745098 )
anchor_top = 0.8
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 0
size_flags_horizontal = 3
size_flags_vertical = 3
texture_normal = ExtResource( 7 )
texture_pressed = ExtResource( 6 )
texture_hover = ExtResource( 11 )
expand = true

[node name="TileMap" type="TileMap" parent="."]
position = Vector2( -58.3189, -33.9405 )
tile_set = SubResource( 4 )
format = 1
tile_data = PoolIntArray( 8, 1, 0, 131074, 0, 0, 131076, 0, 0, 131078, 0, 0, 131080, 0, 0, 131082, 0, 0, 131084, 0, 0, 131086, 0, 0, 262146, 0, 0, 262148, 0, 0, 262150, 0, 0, 262152, 0, 0, 262154, 0, 0, 262156, 0, 0, 262158, 0, 0, 393218, 0, 0, 393220, 0, 0, 393222, 0, 0, 393224, 0, 0, 393226, 0, 0, 393228, 0, 0, 393230, 0, 0, 524290, 0, 0, 524292, 0, 0, 524294, 0, 0, 524296, 0, 0, 524298, 0, 0, 524300, 0, 0, 524302, 0, 0 )

[connection signal="pressed" from="TextureButton" to="." method="_on_TextureButton_pressed"]
