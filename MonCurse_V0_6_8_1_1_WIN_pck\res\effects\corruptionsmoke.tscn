[gd_scene load_steps=16 format=2]

[ext_resource path="res://effects/corruptionsmoke.png" type="Texture" id=1]
[ext_resource path="res://effects/corruptionsmoke.gd" type="Script" id=2]
[ext_resource path="res://effects/openeffect.png" type="Texture" id=3]

[sub_resource type="AtlasTexture" id=10]
atlas = ExtResource( 3 )
region = Rect2( 0, 0, 300, 400 )

[sub_resource type="AtlasTexture" id=11]
atlas = ExtResource( 3 )
region = Rect2( 300, 0, 300, 400 )

[sub_resource type="AtlasTexture" id=12]
atlas = ExtResource( 3 )
region = Rect2( 600, 0, 300, 400 )

[sub_resource type="AtlasTexture" id=1]
atlas = ExtResource( 1 )
region = Rect2( 0, 0, 256, 256 )

[sub_resource type="AtlasTexture" id=2]
atlas = ExtResource( 1 )
region = Rect2( 256, 0, 256, 256 )

[sub_resource type="AtlasTexture" id=3]
atlas = ExtResource( 1 )
region = Rect2( 512, 0, 256, 256 )

[sub_resource type="AtlasTexture" id=4]
atlas = ExtResource( 1 )
region = Rect2( 768, 0, 256, 256 )

[sub_resource type="AtlasTexture" id=5]
atlas = ExtResource( 1 )
region = Rect2( 0, 256, 256, 256 )

[sub_resource type="AtlasTexture" id=6]
atlas = ExtResource( 1 )
region = Rect2( 256, 256, 256, 256 )

[sub_resource type="AtlasTexture" id=7]
atlas = ExtResource( 1 )
region = Rect2( 512, 256, 256, 256 )

[sub_resource type="AtlasTexture" id=8]
atlas = ExtResource( 1 )
region = Rect2( 768, 256, 256, 256 )

[sub_resource type="SpriteFrames" id=9]
animations = [ {
"frames": [ SubResource( 10 ), SubResource( 10 ), SubResource( 11 ), SubResource( 12 ), null ],
"loop": false,
"name": "open",
"speed": 10.0
}, {
"frames": [ SubResource( 1 ), SubResource( 1 ), SubResource( 2 ), SubResource( 3 ), SubResource( 4 ), SubResource( 5 ), SubResource( 6 ), SubResource( 7 ), SubResource( 8 ), null ],
"loop": false,
"name": "smoke",
"speed": 12.0
} ]

[node name="Node2D" type="AnimatedSprite"]
frames = SubResource( 9 )
animation = "open"
script = ExtResource( 2 )

[connection signal="animation_finished" from="." to="." method="_on_Node2D_animation_finished"]
