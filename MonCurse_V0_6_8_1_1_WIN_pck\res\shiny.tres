[gd_resource type="Animation" format=2]

[resource]
resource_name = "shiny"
length = 2.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:anchor_left")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 2 ),
"transitions": PoolRealArray( 0.2, 1, 1 ),
"update": 0,
"values": [ -1.0, 1.0, 1.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.2, 2 ),
"transitions": PoolRealArray( 0.2, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 0.203 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:margin_left")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.001, 0.1, 0.2 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 1,
"values": [ 256, 256.0, 128, 0.0 ]
}
