[gd_scene load_steps=50 format=2]

[ext_resource path="res://font/Audiowide-Regular.ttf" type="DynamicFontData" id=1]
[ext_resource path="res://DialogueArt/CG/monmusuforest.tscn" type="PackedScene" id=2]
[ext_resource path="res://Assets/moncurse icon actualfinal.png" type="Texture" id=3]
[ext_resource path="res://Assets/ui/mainmenu/Fullscreenfullscreen.png" type="Texture" id=4]
[ext_resource path="res://Mainmenu.gd" type="Script" id=5]
[ext_resource path="res://zapsplat/zapsplat_multimedia_pop_up_tone_short_005_78857.ogg" type="AudioStream" id=6]
[ext_resource path="res://zapsplat/zapsplat_multimedia_pop_up_close_003_78851.ogg" type="AudioStream" id=7]
[ext_resource path="res://Assets/ui/mainmenu/exitmenuhover.png" type="Texture" id=8]
[ext_resource path="res://Assets/moncurse icon actualfinalhover.png" type="Texture" id=9]
[ext_resource path="res://Assets/ui/mainmenu/Fullscreenfullscreenhovered.png" type="Texture" id=10]
[ext_resource path="res://Assets/ui/qaruleslowerui.png" type="Texture" id=11]
[ext_resource path="res://Assets/ui/subscribestarlink2.png" type="Texture" id=12]
[ext_resource path="res://Assets/uibutton.png" type="Texture" id=13]
[ext_resource path="res://button.gd" type="Script" id=14]
[ext_resource path="res://Assets/ui/mainmenu/titleev3.png" type="Texture" id=15]
[ext_resource path="res://Assets/ui/mainmenu/disclaimerbox.png" type="Texture" id=16]
[ext_resource path="res://Assets/moncurse icon actualfinalpressed.png" type="Texture" id=17]
[ext_resource path="res://Assets/ui/mainmenu/saveremoving.png" type="Texture" id=18]
[ext_resource path="res://Assets/ui/mainmenu/bitmapgear.png" type="BitMap" id=19]
[ext_resource path="res://Assets/ui/mainmenu/bitmapdoor.png" type="BitMap" id=20]
[ext_resource path="res://Assets/ui/mainmenu/bitmapapple.png" type="BitMap" id=21]
[ext_resource path="res://Assets/ui/mainmenu/settingsmenuhover.png" type="Texture" id=22]
[ext_resource path="res://Assets/ui/mainmenu/startmenuhover.png" type="Texture" id=23]
[ext_resource path="res://Assets/ui/mainmenu/menu buttons.png" type="Texture" id=24]
[ext_resource path="res://Assets/ui/mainmenu/translation.png" type="Texture" id=25]
[ext_resource path="res://Assets/ui/mainmenu/blank.png" type="Texture" id=26]
[ext_resource path="res://Assets/ui/mainmenu/savemenuhover.png" type="Texture" id=27]
[ext_resource path="res://Assets/ui/mainmenu/bitmapsave.png" type="BitMap" id=28]
[ext_resource path="res://Assets/blacksquare.png" type="Texture" id=29]
[ext_resource path="res://Assets/endpointbluebig.png" type="Texture" id=30]
[ext_resource path="res://Assets/endpointbig.png" type="Texture" id=31]
[ext_resource path="res://Assets/ui/mainmenu/saveremover.png" type="Texture" id=32]
[ext_resource path="res://Assets/ui/mainmenu/saveremoverhovered.png" type="Texture" id=33]
[ext_resource path="res://Assets/ui/discordlink4.png" type="Texture" id=34]
[ext_resource path="res://Background/blacksquare.png" type="Texture" id=35]
[ext_resource path="res://Assets/ui/patreonlink3.png" type="Texture" id=36]
[ext_resource path="res://Assets/ui/mainmenu/translationhovered.png" type="Texture" id=37]

[sub_resource type="Animation" id=15]
resource_name = "logo"
length = 4.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:anchor_top")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1, 2, 3, 4 ),
"transitions": PoolRealArray( 0.5, 2, 0.5, 2, 1 ),
"update": 0,
"values": [ 0.03, 0.05, 0.03, 0.01, 0.03 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:anchor_bottom")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1, 2, 3, 4 ),
"transitions": PoolRealArray( 0.5, 2, 0.5, 2, 1 ),
"update": 0,
"values": [ 0.37, 0.39, 0.37, 0.35, 0.37 ]
}

[sub_resource type="DynamicFont" id=8]
size = 20
font_data = ExtResource( 1 )

[sub_resource type="Animation" id=16]
resource_name = "rollcredits"
length = 4.7
loop = true
tracks/0/type = "method"
tracks/0/path = NodePath("../..")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 4.7 ),
"transitions": PoolRealArray( 1 ),
"values": [ {
"args": [  ],
"method": "next_credits"
} ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1, 4, 4.7 ),
"transitions": PoolRealArray( 0.5, 1, 2, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}

[sub_resource type="DynamicFont" id=2]
size = 34
font_data = ExtResource( 1 )

[sub_resource type="DynamicFont" id=10]
size = 24
font_data = ExtResource( 1 )

[sub_resource type="DynamicFont" id=11]
size = 12
font_data = ExtResource( 1 )

[sub_resource type="Animation" id=18]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath("subpatbuttons/supportlabel:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.470588 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("subpatbuttons/SubscribestarButton:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.470588 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("subpatbuttons/PatreonButton:self_modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.470588 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("DiscordButton:modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.470588 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("LinkBacker:self_modulate")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.392157 ) ]
}
tracks/5/type = "value"
tracks/5/path = NodePath(".:modulate")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=17]
resource_name = "shilling"
length = 2.3
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = false
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3, 1.8, 2.3 ),
"transitions": PoolRealArray( 1.5, 1.5, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 1 ), Color( 0.733333, 0.862745, 1, 0.666667 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("LinkBacker:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.3, 1, 1.6, 2.3 ),
"transitions": PoolRealArray( 1.5, 1.5, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.392157 ), Color( 1, 1, 1, 0.392157 ), Color( 1, 1, 1, 0.784314 ), Color( 1, 1, 1, 0.784314 ), Color( 1, 1, 1, 0.392157 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("DiscordButton:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.3, 0.8, 1.3, 2.3 ),
"transitions": PoolRealArray( 1.5, 1.5, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.470588 ), Color( 1, 1, 1, 0.470588 ), Color( 0.784314, 1, 0.862745, 1 ), Color( 1, 1, 1, 0.470588 ), Color( 1, 1, 1, 0.470588 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("subpatbuttons/PatreonButton:self_modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.3, 0.8, 1.3, 1.8, 2.3 ),
"transitions": PoolRealArray( 1.5, 1.5, 1.5, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.470588 ), Color( 1, 1, 1, 0.470588 ), Color( 1, 1, 1, 0.470588 ), Color( 0.784314, 1, 0.862745, 1 ), Color( 1, 1, 1, 0.470588 ), Color( 1, 1, 1, 0.470588 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("subpatbuttons/SubscribestarButton:self_modulate")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 0.3, 1.3, 1.8, 2.3 ),
"transitions": PoolRealArray( 1.5, 1.5, 1.5, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.470588 ), Color( 1, 1, 1, 0.470588 ), Color( 1, 1, 1, 0.470588 ), Color( 0.784314, 1, 0.862745, 1 ), Color( 1, 1, 1, 0.470588 ) ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("subpatbuttons/supportlabel:self_modulate")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0, 0.3, 0.8, 1.3, 1.8, 2.3 ),
"transitions": PoolRealArray( 1.5, 1.5, 1.5, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.470588 ), Color( 1, 1, 1, 0.470588 ), Color( 1, 1, 1, 0.470588 ), Color( 0.784314, 1, 0.862745, 1 ), Color( 0.784314, 1, 0.862745, 1 ), Color( 1, 1, 1, 0.470588 ) ]
}

[sub_resource type="DynamicFont" id=12]
size = 12
font_data = ExtResource( 1 )

[sub_resource type="Animation" id=13]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath(".:rect_scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray(  ),
"transitions": PoolRealArray(  ),
"update": 0,
"values": [  ]
}

[sub_resource type="Animation" id=14]
resource_name = "firstplay"
length = 0.4
tracks/0/type = "value"
tracks/0/path = NodePath(".:rect_scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.4 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ Vector2( 2, 2 ), Vector2( 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.4 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 0.313726, 0.313726, 0.313726, 1 ), Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="DynamicFont" id=9]
size = 24
font_data = ExtResource( 1 )

[node name="Mainmenu" type="Control"]
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource( 5 )

[node name="background" type="Control" parent="."]
anchor_right = 1.0
anchor_bottom = 1.0

[node name="monmusuforest" parent="background" instance=ExtResource( 2 )]

[node name="TextureRect" type="TextureRect" parent="."]
self_modulate = Color( 1, 1, 1, 0.666667 )
anchor_left = 0.05
anchor_right = 0.4
anchor_bottom = 1.0
texture = ExtResource( 29 )
expand = true
stretch_mode = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="TextureRect3" type="TextureRect" parent="."]
self_modulate = Color( 1, 1, 1, 0.392157 )
anchor_left = 0.6
anchor_right = 0.95
anchor_bottom = 1.0
texture = ExtResource( 29 )
expand = true
stretch_mode = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="TextureRect2" type="TextureRect" parent="."]
anchor_top = -0.05
anchor_right = 0.55
anchor_bottom = 1.05
margin_right = -101.056
texture = ExtResource( 24 )
expand = true
stretch_mode = 6
__meta__ = {
"_edit_use_anchors_": false
}

[node name="start" type="TextureButton" parent="TextureRect2"]
anchor_right = 1.0
anchor_bottom = 1.0
texture_normal = ExtResource( 26 )
texture_hover = ExtResource( 23 )
texture_click_mask = ExtResource( 21 )
expand = true
stretch_mode = 5
__meta__ = {
"_edit_use_anchors_": false
}

[node name="setting" type="TextureButton" parent="TextureRect2"]
anchor_right = 1.0
anchor_bottom = 1.0
texture_normal = ExtResource( 26 )
texture_hover = ExtResource( 22 )
texture_click_mask = ExtResource( 19 )
expand = true
stretch_mode = 5
__meta__ = {
"_edit_use_anchors_": false
}

[node name="exit" type="TextureButton" parent="TextureRect2"]
anchor_right = 1.0
anchor_bottom = 1.0
texture_normal = ExtResource( 26 )
texture_hover = ExtResource( 8 )
texture_click_mask = ExtResource( 20 )
expand = true
stretch_mode = 5
__meta__ = {
"_edit_use_anchors_": false
}

[node name="save" type="TextureButton" parent="TextureRect2"]
anchor_right = 1.0
anchor_bottom = 1.0
texture_normal = ExtResource( 26 )
texture_hover = ExtResource( 27 )
texture_click_mask = ExtResource( 28 )
expand = true
stretch_mode = 5
__meta__ = {
"_edit_use_anchors_": false
}

[node name="savenum" type="TextureRect" parent="TextureRect2"]
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 2
expand = true
stretch_mode = 6
__meta__ = {
"_edit_use_anchors_": false
}

[node name="logo" type="TextureButton" parent="."]
self_modulate = Color( 0.843137, 0.843137, 0.901961, 1 )
anchor_left = 0.4
anchor_top = 0.03
anchor_right = 0.6
anchor_bottom = 0.37
texture_normal = ExtResource( 3 )
texture_pressed = ExtResource( 17 )
texture_hover = ExtResource( 9 )
expand = true
stretch_mode = 5

[node name="logo2" type="TextureRect" parent="logo"]
self_modulate = Color( 0, 0, 0, 0.509804 )
show_behind_parent = true
anchor_left = -0.02
anchor_right = 1.02
anchor_bottom = 1.0
margin_top = 2.0
margin_bottom = 4.0
texture = ExtResource( 3 )
expand = true
stretch_mode = 6

[node name="logohover" type="AnimationPlayer" parent="logo"]
playback_speed = 0.4
anims/logo = SubResource( 15 )

[node name="credits" type="Label" parent="logo"]
self_modulate = Color( 1, 1, 1, 0 )
anchor_top = 0.85
anchor_right = 1.0
anchor_bottom = 1.0
custom_fonts/font = SubResource( 8 )
text = "Game credits will go here."
align = 1
valign = 2
autowrap = true

[node name="creditsanim" type="AnimationPlayer" parent="logo/credits"]
anims/rollcredits = SubResource( 16 )

[node name="savename" type="Label" parent="."]
anchor_left = 0.6
anchor_top = 0.08
anchor_right = 0.95
anchor_bottom = 0.2
grow_horizontal = 2
custom_colors/font_color = Color( 0.862745, 0.784314, 0.941176, 1 )
custom_fonts/font = SubResource( 2 )
text = "New Save?"
align = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="progressdata" type="Label" parent="."]
anchor_left = 0.6
anchor_top = 0.2
anchor_right = 0.95
anchor_bottom = 0.75
margin_top = 3.0
grow_horizontal = 2
custom_colors/font_color = Color( 0.862745, 0.784314, 0.941176, 1 )
custom_fonts/font = SubResource( 10 )
text = "Progress:

Levels Cleared:
Transformations:
CGs:

Caon:"
align = 1

[node name="errorlog" type="RichTextLabel" parent="."]
visible = false
anchor_left = 0.405
anchor_top = 0.02
anchor_right = 0.595
anchor_bottom = 0.64
grow_horizontal = 2
rect_clip_content = false
mouse_filter = 2
custom_colors/default_color = Color( 1, 0.843137, 0.505882, 1 )
text = "ERROR LOG
ERROR: THIS SHOULDN'T BE SHOWING THERE ARE NO ERRORS
I FORGOT TO MAKE IT INVISIBLE, I'M DUMB
- The Developer"
scroll_active = false

[node name="TextureRect" type="TextureRect" parent="errorlog"]
self_modulate = Color( 1, 1, 1, 0.666667 )
show_behind_parent = true
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource( 29 )
expand = true

[node name="shilling" type="Control" parent="."]
anchor_left = 0.44
anchor_top = 0.62
anchor_right = 0.56
anchor_bottom = 1.0
margin_left = -0.559998
margin_right = -0.440002
mouse_filter = 2

[node name="LinkBacker" type="TextureRect" parent="shilling"]
self_modulate = Color( 1, 1, 1, 0.392157 )
anchor_right = 1.0
anchor_bottom = 1.0
margin_right = -0.880005
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource( 35 )
expand = true

[node name="DiscordButton" type="TextureRect" parent="shilling"]
modulate = Color( 1, 1, 1, 0.470588 )
anchor_left = 0.275
anchor_top = 0.2
anchor_right = 0.725
anchor_bottom = 0.5
margin_left = 0.207996
margin_right = -0.0880051
grow_horizontal = 2
texture = ExtResource( 34 )
expand = true
stretch_mode = 6

[node name="Label" type="Label" parent="shilling/DiscordButton"]
anchor_right = 1.0
margin_left = -10.0
margin_top = -35.0
margin_right = 9.946
grow_horizontal = 2
grow_vertical = 0
custom_fonts/font = SubResource( 11 )
text = "Join the
 Community"
align = 1
valign = 1

[node name="DiscordInvis" type="Button" parent="shilling/DiscordButton"]
modulate = Color( 1, 1, 1, 0 )
anchor_left = -0.1
anchor_top = -0.3
anchor_right = 1.1
anchor_bottom = 1.0
margin_left = -0.4946
margin_top = -0.4
margin_right = -0.559402
grow_horizontal = 2

[node name="subpatbuttons" type="Control" parent="shilling"]
anchor_left = 0.03
anchor_top = 0.5
anchor_right = 0.97
anchor_bottom = 0.8

[node name="PatreonButton" type="TextureRect" parent="shilling/subpatbuttons"]
self_modulate = Color( 1, 1, 1, 0.470588 )
anchor_right = 0.48
anchor_bottom = 1.0
grow_horizontal = 2
texture = ExtResource( 36 )
expand = true
stretch_mode = 6

[node name="PatreonInvis" type="Button" parent="shilling/subpatbuttons/PatreonButton"]
modulate = Color( 1, 1, 1, 0 )
anchor_left = -0.042
anchor_right = 1.042
anchor_bottom = 1.3
grow_horizontal = 2

[node name="SubscribestarButton" type="TextureRect" parent="shilling/subpatbuttons"]
self_modulate = Color( 1, 1, 1, 0.470588 )
anchor_left = 0.52
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = -1.0
margin_right = -1.0
grow_horizontal = 2
texture = ExtResource( 12 )
expand = true
stretch_mode = 6

[node name="SubscribestarInvis" type="Button" parent="shilling/subpatbuttons/SubscribestarButton"]
modulate = Color( 1, 1, 1, 0 )
anchor_left = -0.042
anchor_right = 1.042
anchor_bottom = 1.3
margin_left = 1.0
margin_right = 1.0
grow_horizontal = 2

[node name="supportlabel" type="Label" parent="shilling/subpatbuttons"]
self_modulate = Color( 1, 1, 1, 0.470588 )
anchor_left = 0.45
anchor_top = 1.0
anchor_right = 0.55
anchor_bottom = 1.0
grow_horizontal = 2
custom_fonts/font = SubResource( 11 )
text = "Support 
the Game"
align = 1
valign = 2

[node name="shillinganim" type="AnimationPlayer" parent="shilling"]
playback_speed = 0.85
anims/RESET = SubResource( 18 )
anims/shilling = SubResource( 17 )

[node name="debugbutton" type="TextureButton" parent="."]
visible = false
anchor_left = 0.45
anchor_top = 0.5
anchor_right = 0.55
anchor_bottom = 0.57
grow_horizontal = 2
texture_normal = ExtResource( 13 )
expand = true

[node name="Label" type="Label" parent="debugbutton"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_fonts/font = SubResource( 12 )
text = "DEBUG START:
OFF"
align = 1
valign = 1

[node name="debugbutton2" type="TextureButton" parent="."]
visible = false
anchor_left = 0.45
anchor_top = 0.43
anchor_right = 0.55
anchor_bottom = 0.5
grow_horizontal = 2
texture_normal = ExtResource( 13 )
expand = true

[node name="Label" type="Label" parent="debugbutton2"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_fonts/font = SubResource( 12 )
text = "DEBUG: Class"
align = 1
valign = 1

[node name="Title" type="TextureRect" parent="."]
visible = false
anchor_left = 0.65
anchor_top = 0.05
anchor_right = 0.95
anchor_bottom = 0.3
margin_left = -0.599976
margin_right = 0.200012
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource( 15 )
expand = true
stretch_mode = 6
__meta__ = {
"_edit_use_anchors_": false
}

[node name="buttonsfx" type="AudioStreamPlayer" parent="."]
stream = ExtResource( 6 )
volume_db = -8.0
bus = "SFX"

[node name="quitbuttonsfx" type="AudioStreamPlayer" parent="."]
stream = ExtResource( 7 )
volume_db = -8.0
bus = "SFX"

[node name="saveremover" type="TextureButton" parent="."]
self_modulate = Color( 0.784314, 0.784314, 1, 1 )
anchor_left = 0.83
anchor_top = 0.76
anchor_right = 0.93
anchor_bottom = 0.96
texture_normal = ExtResource( 32 )
texture_hover = ExtResource( 33 )
texture_disabled = ExtResource( 18 )
expand = true
stretch_mode = 5

[node name="fullscreenbutton" type="TextureButton" parent="."]
self_modulate = Color( 0.745098, 0.745098, 1, 1 )
anchor_left = 0.725
anchor_top = 0.76
anchor_right = 0.825
anchor_bottom = 0.96
texture_normal = ExtResource( 4 )
texture_hover = ExtResource( 10 )
expand = true
stretch_mode = 5
script = ExtResource( 14 )

[node name="translationbutton" type="TextureButton" parent="."]
self_modulate = Color( 0.941176, 0.941176, 1, 1 )
anchor_left = 0.62
anchor_top = 0.76
anchor_right = 0.72
anchor_bottom = 0.96
rect_pivot_offset = Vector2( 102.4, 120 )
texture_normal = ExtResource( 25 )
texture_hover = ExtResource( 37 )
expand = true
stretch_mode = 5
script = ExtResource( 14 )

[node name="firstzoom" type="AnimationPlayer" parent="translationbutton"]
anims/RESET = SubResource( 13 )
anims/firstplay = SubResource( 14 )

[node name="Label" type="TextureRect" parent="."]
visible = false
anchor_right = 0.2
anchor_bottom = 0.3
texture = ExtResource( 16 )
expand = true
stretch_mode = 5
__meta__ = {
"_edit_use_anchors_": false
}

[node name="veil" type="TextureRect" parent="."]
visible = false
modulate = Color( 0.894118, 0.788235, 0.960784, 1 )
self_modulate = Color( 1, 1, 1, 0.921569 )
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 0
texture = ExtResource( 29 )
expand = true
__meta__ = {
"_edit_use_anchors_": false
}

[node name="veilbutton1" type="TextureButton" parent="veil"]
visible = false
modulate = Color( 0.784314, 1, 0.784314, 1 )
anchor_left = 0.3
anchor_top = 0.6
anchor_right = 0.45
anchor_bottom = 0.8
texture_normal = ExtResource( 31 )
texture_hover = ExtResource( 30 )
expand = true
stretch_mode = 5
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label" type="Label" parent="veil/veilbutton1"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_fonts/font = SubResource( 8 )
text = "Yes"
align = 1
valign = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="veilbutton2" type="TextureButton" parent="veil"]
visible = false
modulate = Color( 1, 0.784314, 0.784314, 1 )
anchor_left = 0.55
anchor_top = 0.6
anchor_right = 0.7
anchor_bottom = 0.8
texture_normal = ExtResource( 31 )
texture_hover = ExtResource( 30 )
expand = true
stretch_mode = 5
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label" type="Label" parent="veil/veilbutton2"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_fonts/font = SubResource( 8 )
text = "Cancel"
align = 1
valign = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label" type="Label" parent="veil"]
anchor_left = 0.3
anchor_top = 0.3
anchor_right = 0.7
anchor_bottom = 0.6
grow_horizontal = 2
grow_vertical = 0
custom_fonts/font = SubResource( 9 )
text = "This save file has no data on it yet. Would you like to start a new adventure?"
align = 1
valign = 1
autowrap = true

[node name="Settings" type="NinePatchRect" parent="."]
visible = false
self_modulate = Color( 0.705882, 0.705882, 0.823529, 1 )
anchor_left = 0.2
anchor_top = 0.2
anchor_right = 0.8
anchor_bottom = 0.8
margin_left = 0.199997
texture = ExtResource( 11 )
patch_margin_left = 100
patch_margin_top = 80
patch_margin_right = 100
patch_margin_bottom = 80
axis_stretch_horizontal = 2
axis_stretch_vertical = 2

[connection signal="mouse_entered" from="TextureRect2/start" to="." method="_on_start_mouse_entered"]
[connection signal="pressed" from="TextureRect2/start" to="." method="_on_start_pressed"]
[connection signal="mouse_entered" from="TextureRect2/setting" to="." method="_on_setting_mouse_entered"]
[connection signal="pressed" from="TextureRect2/setting" to="." method="_on_setting_pressed"]
[connection signal="mouse_entered" from="TextureRect2/exit" to="." method="_on_exit_mouse_entered"]
[connection signal="pressed" from="TextureRect2/exit" to="." method="_on_exit_pressed"]
[connection signal="mouse_entered" from="TextureRect2/save" to="." method="_on_save_mouse_entered"]
[connection signal="mouse_exited" from="TextureRect2/save" to="." method="_on_save_mouse_exited"]
[connection signal="pressed" from="TextureRect2/save" to="." method="_on_save_pressed"]
[connection signal="mouse_entered" from="TextureRect2/savenum" to="." method="_on_save_mouse_entered"]
[connection signal="pressed" from="logo" to="." method="_on_logo_pressed"]
[connection signal="mouse_entered" from="shilling/DiscordButton/DiscordInvis" to="." method="_on_DiscordInvis_mouse_entered"]
[connection signal="mouse_exited" from="shilling/DiscordButton/DiscordInvis" to="." method="_on_DiscordInvis_mouse_exited"]
[connection signal="pressed" from="shilling/DiscordButton/DiscordInvis" to="." method="_on_DiscordInvis_pressed"]
[connection signal="mouse_entered" from="shilling/subpatbuttons/PatreonButton/PatreonInvis" to="." method="_on_PatreonInvis_mouse_entered"]
[connection signal="mouse_exited" from="shilling/subpatbuttons/PatreonButton/PatreonInvis" to="." method="_on_PatreonInvis_mouse_exited"]
[connection signal="pressed" from="shilling/subpatbuttons/PatreonButton/PatreonInvis" to="." method="_on_PatreonInvis_pressed"]
[connection signal="mouse_entered" from="shilling/subpatbuttons/SubscribestarButton/SubscribestarInvis" to="." method="_on_SubscribestarInvis_mouse_entered"]
[connection signal="mouse_exited" from="shilling/subpatbuttons/SubscribestarButton/SubscribestarInvis" to="." method="_on_SubscribestarInvis_mouse_exited"]
[connection signal="pressed" from="shilling/subpatbuttons/SubscribestarButton/SubscribestarInvis" to="." method="_on_SubscribestarInvis_pressed"]
[connection signal="pressed" from="debugbutton" to="." method="_on_debugbutton_pressed"]
[connection signal="pressed" from="debugbutton2" to="." method="_on_debugbutton2_pressed"]
[connection signal="mouse_entered" from="saveremover" to="." method="_on_saveremover_mouse_entered"]
[connection signal="pressed" from="saveremover" to="." method="_on_saveremover_pressed"]
[connection signal="pressed" from="fullscreenbutton" to="." method="_on_fullscreenbutton_pressed"]
[connection signal="pressed" from="translationbutton" to="." method="_on_translationbutton_pressed"]
[connection signal="pressed" from="veil/veilbutton1" to="." method="_on_veilbutton1_pressed"]
[connection signal="pressed" from="veil/veilbutton2" to="." method="_on_veilbutton2_pressed"]
