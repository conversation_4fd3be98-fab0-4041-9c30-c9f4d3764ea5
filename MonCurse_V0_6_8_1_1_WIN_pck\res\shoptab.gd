extends Control


# Declare member variables here. Examples:
# var a = 2
# var b = "text"


# Called when the node enters the scene tree for the first time.
var corruptionref = ""

func _ready():
	$shoptab/AnimationPlayer.play("disable")
	$shoptab.visible = true


# Called every frame. 'delta' is the elapsed time since the previous frame.
#func _process(delta):
#	pass

var changeshards = false
var valid = false
func _on_shoptab_mouse_entered():
	if $shoptab/TextureButton/Label.get_text() != "":
		var cost = int($shoptab/TextureButton/Label.get_text())
		if cost >= 0 and corruptionref != "":# and Playervariables.corruptiondict.has(corruptionref):
			changeshards = true
			get_parent().get_parent().get_parent().get_parent().get_parent().set_shards(cost)
			if removing == true:
				get_node("/root/Master/SFX/uib").play()
				removing = false
			if Playervariables.playershards >= cost and corruption_valid_check() == true:
				valid = true
				$shoptab/AnimationPlayer.play("enable")
			else:
				valid = false
				$shoptab/AnimationPlayer.play("invalid")
	else:
		valid = false
		$shoptab/AnimationPlayer.play("invalid")

func corruption_valid_check():
	match corruptionref:
#		"leg":
#			if Playervariables.get_corruption("leg") == Playervariables.raceIMP and Playervariables.get_corruption("body") == Playervariables.raceIMP:
#				return false
#			else:
#				return true
		"ears":
			if Playervariables.corruptiondict.has("tail") and Playervariables.corruptiondict["tail"] == Playervariables.corruptiondict["ears"]:
				return false
			else:
				return true
		"pants":
			if Playervariables.corruptiondict.has("top") and Playervariables.corruptiondict["top"] == Playervariables.corruptiondict["pants"]:
				return false
			else:
				return true
#		"armleft":
#			if Playervariables.corruptiondict.has("armright") and Playervariables.corruptiondict["armright"] == Playervariables.corruptiondict["armleft"]:
#				return false
#			else:
#				return true
	return true

func _on_shoptab_mouse_exited():
	valid = false
	if changeshards == true:
		get_parent().get_parent().get_parent().get_parent().get_parent().set_shards()
		changeshards = false
	$shoptab/AnimationPlayer.play("disable")

var removing = false
func _on_TextureButton_pressed():
	if valid == false:
		_on_shoptab_mouse_entered()
		if int($shoptab/TextureButton/Label.get_text()) >= 0 and corruptionref != "":
			get_node("/root/Master/SFX/uib").play()
	else:
		var cost = int($shoptab/TextureButton/Label.get_text())
		if cost == 0:
			$shoptab/AnimationPlayer.playback_speed = 2.3
		else:
			$shoptab/AnimationPlayer.playback_speed = 1
		$shoptab/AnimationPlayer.play("removing")
		$AudioStreamPlayer.play()
		get_node("/root/Master/SFX/uif").play()
		removing = true

func new_set(gallery=false):
	match int(Playervariables.playerbustsize):
		1:
			return
		2:
			get_node("Board/Info").set_text("A larger bust retains milk.")
			get_node("Board/TitleBar/Title").set_text("Bust: Big")
			if gallery == false:
				get_node("shoptab/TextureButton/Label").set_text("2")
			else:
				get_node("shoptab/TextureButton/Label").set_text("0")
		3:
			get_node("Board/Info").set_text("An even larger bust retains more milk.")
			get_node("Board/TitleBar/Title").set_text("Bust: Oversized")
			if gallery == false:
				get_node("shoptab/TextureButton/Label").set_text("3")
			else:
				get_node("shoptab/TextureButton/Label").set_text("0")
		4:
			get_node("Board/Info").set_text("An absurdly huge bust retains milk.")
			get_node("Board/TitleBar/Title").set_text("Bust: Huge")
			if gallery == false:
				get_node("shoptab/TextureButton/Label").set_text("5")
			else:
				get_node("shoptab/TextureButton/Label").set_text("0")
	get_node("Board/Icon").texture = load("res://Assets/ui/debufficons/bustsize"+str(Playervariables.playerbustsize)+".png")
	corruptionref = "bust"
	$Board.visible = true
	$shoptab.visible = true

func disabled():
	var cost = int($shoptab/TextureButton/Label.get_text())
	$center/disappear.set_self_modulate(Color(1,1,1,0.7))
	$center/disappear.play("smoke")
	$center/disappear.frame = 0
	$shoptab/AnimationPlayer.play("disable")
	$center/disappear.visible = true
	$Board.visible = false
	$shoptab.visible = false
	Playervariables.playershards -= cost
#	if get_parent().get_parent().get_parent().get_parent().get_parent().has_method("finish_corruption"):
	var mainnode = get_parent().get_parent().get_parent().get_parent().get_parent()
	mainnode.mainscene.removed_corruption_effect(true)
	mainnode.set_shards()
	$shoptab/TextureButton/Label.set_text("-1")
	if corruptionref == "bust":
		if cost > 0:
			Playervariables.convert_to_bit(Playervariables.StoredTransformations,Playervariables.raceNAMAN,Playervariables.playerbustsize-2,false)
			if Playervariables.playerbustsize >= 3:
				Playervariables.convert_to_bit(Playervariables.StoredTransformations,Playervariables.raceNAMAN,Playervariables.playerbustsize-3,true)
			if Playervariables.playerbustsize < 4 and Playervariables.CurrentClass == Playervariables.raceNAMAN:
				Playervariables.CurrentClass = Playervariables.raceHUMAN
				mainnode.register_event("Class status has been cured.")
		Playervariables.playerbustsize = clamp(Playervariables.playerbustsize-1,1,4)
		mainnode.remove_specific_curses(201,true)
		mainnode.stat_recalc()
		mainnode.mainscene.updateplayersprite()
#		mainnode.mainscene.reset_hotsprings(true)
		mainnode.get_node("ConversationV1").rulestalker.assignplayercolour()
		new_set(cost == 0)
	else:
		mainnode.finish_corruption(Playervariables.raceHUMAN,corruptionref,cost > 0)
	get_node("/root/Master").call("cure_map_sprite_via_master")
	if cost > 0:
		mainnode.register_event("Corruption absorbed by shards.")
		
#		if mainnode.ui_indicator != null:
#			mainnode.handle_ui_indicator(self,0,null,false)
#		if Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] >= 8: #only disables the tutorial if you've reached route A11
#			Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] = 9
#		if Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] < 5:
#			Playervariables.tutorialscleared = max(Playervariables.tutorialscleared,6)
#			Playervariables.savevalue("Tutorial","Tutorialscleared",Playervariables.tutorialscleared)
		Playervariables.saveprogress()


func _on_TextureButton_button_up():
	$AudioStreamPlayer.stop()
	_on_shoptab_mouse_entered()


func _on_AnimationPlayer_animation_finished(anim_name):
	if anim_name == "removing":
		if Playervariables.corruptiondict.has(corruptionref) or corruptionref == "bust":
			disabled()
		else:
			$shoptab/AnimationPlayer.play("disable")
			$AudioStreamPlayer.stop()
	else:
		if removing == true:
			get_node("/root/Master/SFX/uib").play()
			removing = false
