extends TileMap

var bigtree = true
const Leafnode = preload("res://effects/fallingleaves.tscn")
const tilesize = 128

var occupied_tiles_array = PoolVector2Array([])

var gecko = false
enum{TREETOP,SECTION1,SECTION2,ROOTS,ROOTSOVER,SECTION1OVER,SECTION2OVER}
func just_like_make_bigger_tree(rootloc,size,_altcolors,leafchance,windspeed,use_backgroundmap):
	var rootsflip = randf() > 0.5
	self.set_cellv(rootloc,ROOTS,rootsflip)
	self.set_cellv(rootloc+Vector2(0,1),ROOTSOVER,rootsflip)
	var newgecko
	if randf() > 0.72:
		gecko = true
		newgecko = load(Mainpreload.Gecko).instance()
		add_child(newgecko)
		newgecko.possible_start_positions.append((rootloc+Vector2(2,0))*tilesize)
		newgecko.start_pos_flips.append(rootsflip)
	for x in range(4):
		for y in range(3):
			var localloc = Vector2(rootloc.x+x,rootloc.y+y)
			use_backgroundmap[localloc.y][localloc.x] = 2
			occupied_tiles_array.append(localloc)
	var offset = randi()%2
	for i in range(size):
		var useflip = randf() > 0.5
		if (i+offset) % 2 == 0:
			self.set_cellv(rootloc-Vector2(0,(i+1)*2),SECTION1,useflip)
			if useflip == true:
				self.set_cellv(rootloc-Vector2(-2,1+((i+1)*2)),SECTION1OVER,useflip)
			else:
				self.set_cellv(rootloc-Vector2(0,1+((i+1)*2)),SECTION1OVER,useflip)
		else:
			self.set_cellv(rootloc-Vector2(0,(i+1)*2),SECTION2,useflip)
			if useflip == true:
				self.set_cellv(rootloc-Vector2(0,1+((i+1)*2)),SECTION2OVER,useflip)
			else:
				self.set_cellv(rootloc-Vector2(-2,1+((i+1)*2)),SECTION2OVER,useflip)
		if gecko == true:
			newgecko.possible_start_positions.append((rootloc - Vector2(-2,(i+1)*2))*tilesize)
			newgecko.start_pos_flips.append(useflip)
		for x in range(2):
			for y in range(2):
				var localloc = Vector2(rootloc.x+x+1,rootloc.y+y-((i+1)*2))
				use_backgroundmap[localloc.y][localloc.x] = 2
				occupied_tiles_array.append(localloc)
	if gecko == true:
		newgecko.new_start()
	self.set_cellv(rootloc-Vector2(0,(size*2)+3),TREETOP,randf() > 0.5)
	for x in range(4):
		for y in range(3):
#			if y != 0 or (x == 1 or x == 2):
			var localloc = Vector2(rootloc.x+x,rootloc.y+y-((size*2)+3))
			use_backgroundmap[localloc.y][localloc.x] = 2
			occupied_tiles_array.append(localloc)
	
	if leafchance > randf()*2:
		var newleaf = Leafnode.instance()
		add_child(newleaf)
		newleaf.position = (rootloc+Vector2(2,1.5-((size*2)+3)))*tilesize
		newleaf.emitting = true
		newleaf.speed_scale = windspeed
		newleaf.lifetime = (12+12*windspeed)/2
		newleaf.preprocess = randf()*newleaf.lifetime
#		newleaf.use_parent_material = true
#		if altcolors > 0:
#		newleaf.texture = load("res://effects/treeleafalt.png")
		if leafchance < 1:
			newleaf.amount = 2
		elif leafchance > 2:
			newleaf.amount = 5
#			newleaf.lifetime = 15
