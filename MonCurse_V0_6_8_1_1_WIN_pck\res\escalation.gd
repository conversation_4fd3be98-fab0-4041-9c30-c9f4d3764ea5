extends Control

const totalpoolsnum = 3
const totalroutesnum = 3

var optionarray1 = ["Blank","Blank","Blank"]
var optionarray2 = ["Blank","Blank","Blank"]
var optionarray3 = ["Blank","Blank","Blank"]
#const pool1 = ["Map Width","Map Depth"]
#const pool2 = ["Map Width","Map Depth"]
#const pool3 = ["Map Width","Map Depth"]

var foodallowed = false
var mapallowed = false
var currentdefaultbacker = load("res://Assets/ui/escalationcards/BackerForest.png")

var temporarydisable = false

var excludedpools = []
var excludedoptions = []
var completedpoolarray = [false,false,false]
var firstrun = true
func generatepaths(food,map):
	food = true
	map = true
#	$Foodcounter.set_text("Food Count: "+str(Playervariables.playerforage)+" will halved to "+str(int(Playervariables.playerforage/2)))
	self.visible = true
	$appear.play("appearescalation")
	if firstrun == true:
		firstrun = false
		if Playervariables.stagenum > 5:
			if Playervariables.nextlocation == 1:
				if Playervariables.stagenum > 10:
					currentdefaultbacker = load("res://Assets/ui/escalationcards/BackerNight.png")
				else:
					currentdefaultbacker = load("res://Assets/ui/escalationcards/BackerSunset.png")
		var usedpoolsnum = clamp(ceil(Playervariables.stagenum/3),1,3)
		if usedpoolsnum < 3:
			if usedpoolsnum == 2:
				excludedpools.append(3)
			else:
				excludedpools.append(3)
				if Playervariables.stagenum == 1 or Playervariables.stagenum == 3:
					excludedpools.append(1)
				else:
					excludedpools.append(2)
		$backer2.set_modulate(Color(0.8,0.8,0.8,1))
		$backer2.set_texture(currentdefaultbacker)
	if food == true and completedpoolarray[0] == false:
		foodallowed = true
		$foodwarn.visible = false
		$backer1.set_modulate(Color(0.8,0.8,0.8,1))
		$backer1.set_texture(currentdefaultbacker)
	if map == true and completedpoolarray[2] == false:
		Playervariables.stagemapheld = false
		mapallowed = true
		$mapwarn.visible = false
		$backer3.set_modulate(Color(0.8,0.8,0.8,1))
		$backer3.set_texture(currentdefaultbacker)
	for i2 in range(totalroutesnum):
		if completedpoolarray[i2] == false:
			if (i2 == 1) or (i2 == 0 and food == true) or (i2 == 2 and map == true):
				var randchoices = PoolIntArray([0,0,0])
				for pool in range(totalpoolsnum):
					if excludedpools.find(pool+1) == -1:
						var ballotarray = PoolIntArray([])
						var increment = -1
						for choiceoption in Playervariables.get("mappool"+str(pool+1)):
							increment += 1
							var excluded = false
							for excludearray in excludedoptions:
								if excludearray[pool] == increment:
									excluded = true
									break
							if excluded == false:
								for _i in range(Playervariables.escalationweight.get(choiceoption)):
									ballotarray.append(increment)
						if ballotarray.size() > 0:
							var randID = ballotarray[randi()%ballotarray.size()]
							randchoices[pool] = randID
			#				excludedoptions[i2].append(randID)
						else:
#							print("no valid pools, choosing randomly. Pool:" +str(Playervariables.get("mappool"+str(pool+1))))
							randchoices[pool] = randi()%(Playervariables.get("mappool"+str(pool+1)).size())
					else:
						randchoices[pool] = -1
			#				excludedoptions[i2].append(randi()%(Playervariables.get("mappool"+str(pool+1)).size()))
				excludedoptions.append(randchoices)
				for i in range(totalpoolsnum):
					var card = get_node("backer"+str(i2+1)+"/TextureRect"+str(i+1))
					var poolused = Playervariables.get("mappool"+str(i+1))
					if poolused.size() > 0 and excludedpools.find(i+1) == -1:
						var randchoice = poolused[randchoices[i]]
	#					if randchoice.substr(randchoice.length()-4,-1) == "Rank" and Playervariables.escalationdict[randchoice] > 0:
	##						if Playervariables.escalationdict[randchoice] > 1:
	##							print("error with too high a rank being passed through, check map escalation pools.")
	##							print(poolused)
	##							print("the issue was: "+str(randchoice))
	##							card.set_texture(load("res://Assets/ui/escalationcards/Blank.png"))
	##						else:
	#						card.set_texture(load("res://Assets/ui/escalationcards/"+randchoice+"2.png"))
	#					else:
						if randchoices[i] > -1:
							card.set_texture(load("res://Assets/ui/escalationcards/"+randchoice+".png"))
							card.visible = true
							get("optionarray"+str(i2+1))[i] = randchoice
						else:
							card.set_texture(load("res://Assets/ui/escalationcards/Blank.png"))
							card.visible = true
					else:
						card.set_texture(load("res://Assets/ui/escalationcards/Blank.png"))
						card.visible = true
					if Playervariables.escalationdict.has(get("optionarray"+str(i2+1))[i]):
						card.get_node("Label").set_text("Rank "+romannumerals(Playervariables.escalationdict.get(get("optionarray"+str(i2+1))[i])))
					else:
						card.get_node("Label").set_text("")
				completedpoolarray[i2] = true

func romannumerals(number): #please only put integers in here thank you very much
	if number == 0:
		return "0 (NEW)"
	elif number < 4:
		var tempstring = "I"
		for _i in range(number-1):
			tempstring = tempstring+"I"
		return tempstring
	elif number == 4:
		return "IV"
	else:
		var tempstring = "V"
		if number < 9:
			for _i in range(number-5):
				tempstring = tempstring+"I"
			return tempstring
		elif number == 9:
			return "IX"
		elif number == 10:
			return "X"
		else:
			tempstring = "X"
			if number < 100:
				return "X" + romannumerals(number-10)
			else:
				return tempstring + str(number)
#	return "errornum:"+str(number)

var selectonclick = 0
func _input(event):
	if self.visible == true and temporarydisable == false:
		if $appear.is_playing() == false:
			var mouseposy = get_local_mouse_position().y / get_viewport().size.y
			if mouseposy < 0.8:
				if event is InputEventMouseMotion:
					var mouseposx = get_local_mouse_position().x / get_viewport().size.x
					if selectonclick == 0:
						if mouseposx < 0.325:
							if foodallowed == true:
								$whiteveil1.visible = true
								$backer1.set_modulate(Color(1,1,1,1))
							$whiteveil2.visible = false
							$backer2.set_modulate(Color(0.8,0.8,0.8,1))
							if mapallowed == true:
								$whiteveil3.visible = false
								$backer3.set_modulate(Color(0.8,0.8,0.8,1))
						elif mouseposx < 0.675:
							if foodallowed == true:
								$whiteveil1.visible = false
								$backer1.set_modulate(Color(0.8,0.8,0.8,1))
							$whiteveil2.visible = true
							$backer2.set_modulate(Color(1,1,1,1))
							if mapallowed == true:
								$whiteveil3.visible = false
								$backer3.set_modulate(Color(0.8,0.8,0.8,1))
						else:
							if foodallowed == true:
								$whiteveil1.visible = false
								$backer1.set_modulate(Color(0.8,0.8,0.8,1))
							$whiteveil2.visible = false
							$backer2.set_modulate(Color(0.8,0.8,0.8,1))
							if mapallowed == true:
								$whiteveil3.visible = true
								$backer3.set_modulate(Color(1,1,1,1))
				elif event.is_action_pressed("ui_click"):
					var mouseposx = get_local_mouse_position().x / get_viewport().size.x
					$whiteveil1.visible = false
					$whiteveil2.visible = false
					$whiteveil3.visible = false
					if mouseposx < 0.325:
						selectonclick = 1
					elif mouseposx < 0.675:
						selectonclick = 2
					else:
						selectonclick = 3
				elif event.is_action_released("ui_click"):
					var mouseposx = get_local_mouse_position().x / get_viewport().size.x
					if mouseposx < 0.325:
						if selectonclick == 1 and foodallowed == true:
							registerkeys(selectonclick)
						else:
							selectonclick = 0
					elif mouseposx < 0.675:
						if selectonclick == 2:
							registerkeys(selectonclick)
						else:
							selectonclick = 0
					else:
						if selectonclick == 3 and mapallowed == true:
							registerkeys(selectonclick)
						else:
							selectonclick = 0
			else:
				if foodallowed == true:
					$whiteveil1.visible = false
					$backer1.set_modulate(Color(0.8,0.8,0.8,1))
				$whiteveil2.visible = false
				$backer2.set_modulate(Color(0.8,0.8,0.8,1))
				if mapallowed == true:
					$whiteveil3.visible = false
					$backer3.set_modulate(Color(0.8,0.8,0.8,1))
#
#	escalationweight["Cat Rank"] = 5
#	escalationweight["Fox Rank"] = 5
#	escalationweight["Ram Rank"] = 5
func registerkeys(direction):
	var increment = 0
	for key in get("optionarray"+str(direction)):
		increment += 1
		if Playervariables.escalationdict.has(key):
#			print("Found dictionary key: "+key)
			Playervariables.escalationdict[key] += 1
#			print(key.substr(key.length()-4,-1))
#			print(key.substr(0,5))
#			print(key.substr(5,-1))
			if key.substr(key.length()-4,-1) == "Rank":
				if Playervariables.escalationdict[key] >= Playervariables.escalationdict["Enemy "+key.rstrip(" Rank")]:
					var positionID = Playervariables.get("mappool"+str(increment)).find(key)
					if positionID == -1:
						print("you fucked up, key not found in map pool for removal, check escalation.gd")
					else:
						Playervariables.get("mappool"+str(increment)).remove(positionID) #PLEASE DO NOT FORGET: POOL+1 IN PLAYERVARIABLES
#						print("Removing rank from pool")
					Playervariables.unusedpool.append(key)
			elif key.substr(0,5) == "Enemy":
				Playervariables.escalationweight[key.substr(6,-1)+" Rank"] += 5
#		else:
#			print("SERIOUS ESCALATION SCRIPT ERROR, COULD NOT FIND: "+key)
	Playervariables.playerforage = int(Playervariables.playerforage/2)
	if get_parent().mainscene.mapgenlocation == Playervariables.Home:
		get_node("/root/Master").call("remove_map")
#		if Playervariables.debugallowbonus == true:
#			Playervariables.maxresistance = 35
#			Playervariables.playerresistance = 35
#		Playervariables.playerinventory2darray = [[3,0,2],[5,0,2],[11,0,2],[13,0,2]]
#		Playervariables.playerconsumables2darray = [[10,0,3,20],[9,0,3,10]]
#		Playervariables.identify_inventory()
#		Playervariables.playerinnate2darray = [[3,0,2],[5,0,2],[11,0,2],[13,0,2]]
#		Playervariables.playerinventory2darray = [[4,0,1],[6,0,1],[7,0,1],[8,0,1],[12,0,1],[3,0,2],[5,0,2],[11,0,2],[13,0,2]]
	get_parent().mainscene.endscene()
	self.visible = false

func _on_TextureButton_pressed():
	if $appear.is_playing() == false:
		get_parent().mainscene.refreshselector = true
		get_parent().mainscene.moveready = true
		self.visible = false
