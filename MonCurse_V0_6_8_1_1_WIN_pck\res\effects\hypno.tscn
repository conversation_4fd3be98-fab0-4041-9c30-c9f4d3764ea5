[gd_scene load_steps=8 format=2]

[ext_resource path="res://MC/debuff/hypno particle small.png" type="Texture" id=1]
[ext_resource path="res://effects/confusionparticle.png" type="Texture" id=2]

[sub_resource type="ParticlesMaterial" id=98]
flag_disable_z = true
gravity = Vector3( 0, 0, 0 )
initial_velocity = 100.0
angular_velocity = 100.0
orbit_velocity = 0.8
orbit_velocity_random = 0.0

[sub_resource type="CanvasItemMaterial" id=99]
particles_animation = true
particles_anim_h_frames = 4
particles_anim_v_frames = 1
particles_anim_loop = true

[sub_resource type="Gradient" id=100]
offsets = PoolRealArray( 0, 0.315789, 0.568421, 1 )
colors = PoolColorArray( 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=101]
gradient = SubResource( 100 )

[sub_resource type="ParticlesMaterial" id=102]
flag_disable_z = true
direction = Vector3( 1, -1, 0 )
spread = 60.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 50.0
orbit_velocity = 0.04
orbit_velocity_random = 1.0
damping = 15.0
color_ramp = SubResource( 101 )
anim_speed = 2.0

[node name="hypno" type="Particles2D"]
position = Vector2( 0, -40 )
emitting = false
amount = 2
lifetime = 1.2
speed_scale = 0.5
process_material = SubResource( 98 )
texture = ExtResource( 1 )

[node name="confusion" type="Particles2D" parent="."]
material = SubResource( 99 )
z_index = 6
emitting = false
amount = 3
lifetime = 5.0
process_material = SubResource( 102 )
texture = ExtResource( 2 )
