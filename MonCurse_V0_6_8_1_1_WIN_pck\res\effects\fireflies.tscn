[gd_scene load_steps=5 format=2]

[ext_resource path="res://Background/fireflies.png" type="Texture" id=1]
[ext_resource path="res://Background/fireflies2.png" type="Texture" id=2]
[ext_resource path="res://effects/fireflies.gd" type="Script" id=3]

[sub_resource type="Animation" id=1]
resource_name = "fireflies"
length = 4.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3, 1, 2.5, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 0.5, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("fireflies2:self_modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1.5, 2.4, 2.6, 3.5, 4 ),
"transitions": PoolRealArray( 2, 1, 1, 2, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:offset")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1.1, 2.8, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 13, -5 ), Vector2( -8, 3 ), Vector2( 0, 0 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("fireflies2:offset")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 1.3, 2.4, 2.5, 3.3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 25, 4 ), Vector2( 36, 14 ), Vector2( 0, 0 ), Vector2( 15, 8 ), Vector2( 0, 0 ) ]
}

[node name="fireflies" type="Sprite"]
self_modulate = Color( 1, 1, 1, 0 )
texture = ExtResource( 1 )
script = ExtResource( 3 )

[node name="fireflies2" type="Sprite" parent="."]
self_modulate = Color( 1, 1, 1, 0 )
texture = ExtResource( 2 )

[node name="firefliesanim" type="AnimationPlayer" parent="."]
autoplay = "fireflies"
playback_speed = 0.1
anims/fireflies = SubResource( 1 )

[connection signal="animation_finished" from="firefliesanim" to="." method="_on_firefliesanim_animation_finished"]
