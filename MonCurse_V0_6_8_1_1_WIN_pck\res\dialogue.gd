extends Control

#inefficient loading...
##var barq = load("res://Assets/ui/invcorbar.png")
##var baroverq = load("res://Assets/ui/barfill.png")
#var baroverst = load("res://Assets/ui/stonebarmiddle.png")
#var barst = load("res://Assets/ui/stonebarover.png")
#var barunderst = load("res://Assets/ui/stonebarunder.png")
##var buttonq = load("res://Assets/ui/qadesnametag.png")
##var closeq = load("res://Assets/ui/qadesclosebutton.png")
#var buttonst = load("res://Assets/ui/monmusubuttonhalf.png")
#var closest = load("res://Assets/ui/monmusuclose.png")

#var CG1 = load("res://DialogueArt/CG/shikacg2.png")
#var Rules0 = load("res://DialogueArt/rulesv2alpha.png")
#var Rules1 = load("res://DialogueArt/rulesv2testexpression.png")
#var Rulespartcount = 1
#var Rulespresets = [[0],[1]]
#var Qades0 = load("res://DialogueArt/qadesshop.png")
#var Qadespresets = [[0]]
#var Qadespartcount = 1
#var Shika0 = load("res://DialogueArt/shika/shikabase1.png")
#var Shika1 = load("res://DialogueArt/shika/shikabase2.png")
#var Shika2 = load("res://DialogueArt/shika/shikabase3.png")
#var Shika3 = load("res://DialogueArt/shika/shikabase4.png")
#var Shika4 = load("res://DialogueArt/shika/shikabase5.png")
#var Shika0b0 = load("res://DialogueArt/shika/shikaarm1.png")
#var Shika0b1 = load("res://DialogueArt/shika/shikaarm2.png")
#var Shika0b2 = load("res://DialogueArt/shika/shikaarm3.png")
#var Shika1b0 = load("res://DialogueArt/shika/shikaexpression1.png")
#var Shika1b1 = load("res://DialogueArt/shika/shikaexpression2.png")
#var Shika1b2 = load("res://DialogueArt/shika/shikaexpression3.png")
#var Shika1b3 = load("res://DialogueArt/shika/shikaexpression4.png")
#var Shika1b4 = load("res://DialogueArt/shika/shikaexpression5.png")
#var Shika1b5 = load("res://DialogueArt/shika/shikaexpression6.png")
#var Shika1b6 = load("res://DialogueArt/shika/shikaexpression7.png")
#var Shikapartcount = 3
#var Shikapresets = [[4,1,0],[4,0,1],[3,1,2],[4,1,3],[3,1,4],[2,1,5],[2,1,6],[2,0,6],[2,2,5],[3,2,4],[2,2,0],[2,2,6],[3,2,2]]
#currently: 'idle', 'idlebeckoning', 'surprised', 'glancing', 'angry', 'smug', 'lewd', 'lewdbeck', 'smugcw', 'angry cw', 'idlecw', 'lewdcw', 'surprisecw'
#and the IDs: 0			1				2			3			4		5		6			7		8			9			10		11			12
#var Health2 = preload("res://healthconnector.tscn")
#var Health3 = preload("res://healthend.tscn")
#var health
#var DebuffMeterSprites = [preload("res://Assets/ui/debuffmeter1.png"),preload("res://Assets/ui/debuffmeter2.png"),preload("res://Assets/ui/debuffmeter3.png"),preload("res://Assets/ui/debuffmeter4.png"),preload("res://Assets/ui/debuffmeter5.png"),preload("res://Assets/ui/debuffmeter6.png"),preload("res://Assets/ui/debuffmeter7.png")]
#var DebuffMeter = preload("res://debuffmeter.tscn")
#var DebuffMeterDescription = preload("res://debuffmeterdescription.tscn")

#var Speechbubble = preload("res://Conversations/speechbubble.tscn")
#var Pop = preload("res://alertlabel.tscn")
#var Passiveshow = preload("res://passivedeploy.tscn")
#var Backer = preload("res://backer.tscn")
#var Deployables = preload("res://Deployable.tscn")
var deployables
#var Eventtext = preload("res://eventtext.tscn")
#var maxhandsize = 5
var maxitemsize = 0
#var maxinnatesize = 2
onready var mainscene = self.get_parent().get_parent()
#var EnemyArt = preload("res://enemyart.tscn")
#var SpriteWereWolf = load("res://DialogueArt/pitchart.tscn")
#var SpriteQades = load("res://DialogueArt/qadesart.tscn")
#var SpriteFoxGirl = load("res://DialogueArt/shikaart.tscn")
#var SpriteRamGirl = load("res://DialogueArt/ramgirlart.tscn")
#var SpriteCatKnight = load("res://DialogueArt/catknightart.tscn")
#var $ConversationV1.spritetalker
#var spritetalkerdirection = 1 #1 is not flipped (right) -1 is flipped
#var rulestalkerdirection = 1 #1 is not flipped (left) -1 is flipped
#onready var $ConversationV1.rulestalker = $ConversationV1/dialogueart/playerart/rulesart
#var SpriteRules = preload("res://DialogueArt/rulesart.tscn")
#var SpriteRulesMale = load("res://DialogueArt/rulesmaleart.tscn")
#var healthmark = [] #whenever a character hurts the MC, they 'mark' that damage off. Health is healed from the oldest to newest.
#NOTE that health being marked does not mean damage has been taken. Health can be pre-marked by effects and deny other enemies a chance.
#signal done_shopping
var deployabledistancing = 164
var deployablepositionarray = []

#var screeneffect = null

#var spriteactive = false #for stuff like pitch's art
func _ready():
#	mainscene = self.get_parent().get_parent()
	$deployablecontainer/lowerui2/deckfade.playback_speed = -1
	$ConversationV1.ismainscene = true
	$inventoryveil/inventory/Settingscreen/CloseButton.visible = false
	$inventoryveil/inventory/Settingscreen.ismainscene = true
	if $conversationv2.visible == true:
		print("Dialogue.gd: Please remember to make conversationv2 not visible in editor, thanks.")
		$conversationv2.visible = false #JUST TO BE SURE because it'll cause bugs otherwise and it's hard to notice.
	var _c = get_tree().root.connect("size_changed", self, "_on_viewport_size_changed")
	$upperui.rect_position.y = clamp(Playervariables.upperuiposition,-150,0)
	if Playervariables.debugmodeon == false:
		$inventoryveil/inventory/skiplevelbutton.visible = false
	update_health()
	itemshuffle(true)
	$upperui/events.margin_top = 20
	$upperui/events.margin_left = 20
	$upperui/events.margin_right = -20
	$upperui/events.margin_bottom = -20
	if Playervariables.modarray[Playervariables.mods.TRANSLATION] == true:
		$inventoryveil/inventory/Status/Label.set_text(Playervariables.tabinfodict["status"])
		$inventoryveil/inventory/Ability/Label2.set_text(Playervariables.tabinfodict["skills"])
		$inventoryveil/inventory/Settings/Label3.set_text(Playervariables.tabinfodict["settings"])
		if Playervariables.default_font_dict["font_move_names"] != null:
			$inventoryveil/inventory/Status/Label.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_move_names"].font_data
		if Playervariables.default_font_dict["font_speech_choices"] != null:
			$inventoryveil/inventory/TabInformation.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_speech_choices"].font_data
	for string in Playervariables.laststagemessages:
		register_event(string,[],false,true)
	Playervariables.laststagemessages = PoolStringArray([])
#	if Playervariables.homefirstload == false:
#	$upperui/events/filler1.queue_free()
#	$upperui/events/filler2.queue_free()
#	else:
#		$upperui/events/filler1.raise()
#		$upperui/events/filler2.raise()
	$upperui/events/eventtext.raise()
	adjust_hand_size(Playervariables.handsize,true)
	stack_deck(true)
	mainscene.deployablemargintop = $deployablecontainer.margin_top
	if Playervariables.nextlocation == Playervariables.DarkRoom:
		$deployablecontainer/inventorybutton.visible = false
		$upperui.visible = false
	if Playervariables.nextlocation in [Playervariables.Tutorial1,Playervariables.Tutorial2,Playervariables.DarkRoom]:
		$deployablecontainer/lowerui2.visible = false
		$deployablecontainer/healthbox.visible = false
		$deployablecontainer/deploy.visible = false
		$deployablecontainer/backdrop.visible = false
		if Playervariables.nextlocation in [Playervariables.Tutorial1,Playervariables.Tutorial2] and Playervariables.StagesCleared[Playervariables.StageRef.TUTORIAL] > 2:
			$inventoryveil/inventory/Settingscreen/Scrollcontainer/settings/fcontrol.visible = true
	elif Playervariables.nextlocation in [Playervariables.Village,Playervariables.LevelSelect1,Playervariables.Gallery,Playervariables.Home]:
		if Playervariables.nextlocation in [Playervariables.Village,Playervariables.LevelSelect1]:
			$inventoryveil/inventory/Status.set_self_modulate(shard_shop_color)
	elif Playervariables.nextlocation == Playervariables.Springs:
		$deployablecontainer/healthbox.set_modulate(shard_shop_color)
		$inventoryveil/inventory/Status.set_self_modulate(shard_shop_color)
		$deployablecontainer/healthbox/AnimationPlayer.play("springs")
		$deployablecontainer/healthbox/shards2/resleft2.set_text(str(Playervariables.playershards))
		$deployablecontainer/healthbox/shards2.visible = true
	else:
		$inventoryveil/inventory/Settingscreen/Scrollcontainer/settings/fcontrol.visible = true
	if Playervariables.tempcorruptiondict.has("beads") and Playervariables.tempcorruptiondict["beads"] >= Playervariables.tempcorruptionbreakpoints["beads"][1] and Playervariables.tempcorruptiondict["beads"] < Playervariables.tempcorruptionbreakpoints["beads"][2]:
		screen_filter(Playervariables.HYPNOSIS)

var total_backers = 0
func adjust_hand_size(newhandsize,first=false):
	var backer_change = (newhandsize - maxitemsize)
	if first == true:
		maxitemsize = 0
		backer_change = newhandsize+1
	if backer_change > 0:
		if first == false: 
			total_backers = $deployablecontainer/deploy.get_children().size()
			if maxitemsize < $deployablecontainer/deploy.get_children().size():
				for i in range(min(newhandsize,total_backers-1)):
					$deployablecontainer/deploy.get_child(i+1).material = null
					$deployablecontainer/backdrop.get_child(i+1).material = null
			if newhandsize < $deployablecontainer/deploy.get_children().size():
				maxitemsize = newhandsize
				return
			else:
				total_backers = newhandsize+1
		else:
			total_backers = newhandsize+1
		for i in range(total_backers):
			if i >=  $deployablecontainer/deploy.get_children().size():
				var backer = load(Mainpreload.Backer).instance()
				$deployablecontainer/backdrop.add_child(backer)
				if first == false:
					if i == 0:
						backer.position = Vector2((maxitemsize-(float(total_backers)/2)+0.5)*deployabledistancing,0)
					else:
						backer.position = Vector2(((i-1)-((float(total_backers)/2)+0.5))*deployabledistancing,0)
				deployablepositionarray.append(backer.position.x)
				if i > 0:
					backer.get_child(0).set_text("")
					backer.get_child(0).set_self_modulate(Color(0.7,0.5,0.5))
				else:
					backer.get_child(0).set_text("")
					backer.get_child(0).set_self_modulate(Color(0.4,0.9,0.4))
				discardarray.append(false)
				deployables = load(Mainpreload.Deployables).instance()
				$deployablecontainer/deploy.add_child(deployables)
				var autodiscard = false
				deployables.sealnum = i+1
				if first == true and i == 0:
					deployables.add_to_group("consumableslot")
					if Playervariables.playerconsumables2darray.size() > 0:
						deployables.call("_identify",Playervariables.playerconsumables2darray[Playervariables.lastconsumableitem],false,autodiscard)
					else:
						deployables.visible = false
				else:
					deployables.add_to_group("itemslot")
					if first == true:
						if deckarray.size() > 0:
							var drawncard = deckarray.pop_front()
							if drawncard >= Playervariables.playerinventory2darray.size():
								deployables.visible = false
								print("SERIOUS DECK ERROR: Could not find drawncard number "+str(drawncard))
							else:
								if Playervariables.autodiscardarrayitem.find(Playervariables.playerinventory2darray[drawncard][1]) != -1:
									autodiscard = true
									discardarray[i] = true
								deployables.call("_identify",Playervariables.playerinventory2darray[drawncard],false,autodiscard)
						else:
							deployables.visible = false
					else:
						deployables.visible = false
	#			else:
	#				var backer = $deployablecontainer/backdrop.get_child(i)
	#				var deploy = $deployablecontainer/deploy.get_child(i)
	#				deploy.uilocation = i
	#				if i == 0:
	#					backer.position = Vector2(((total_backers-1)-(float(total_backers)/2)+0.5)*deployabledistancing,0)
	#				else:
	#					backer.position = Vector2(((i-1)-((float(total_backers)/2)+0.5))*deployabledistancing,0)
	#				deployablepositionarray[i] =  backer.position.x
	#				deploy.checkbaseposition(true)
	#				deploy.lerpitem = true
	#				deploy.set_physics_process(true)
		if first == false:
			resize_deployables()
	else:
		remove_last_deployables(abs(backer_change))
		for i in (total_backers - (newhandsize+1)):
			$deployablecontainer/deploy.get_child(total_backers-(i+1)).material = load(Mainpreload.deployable_over_handsize)
			$deployablecontainer/backdrop.get_child(total_backers-(i+1)).material = load(Mainpreload.deployable_over_handsize)
	maxitemsize = newhandsize

func remove_last_deployables(num=1,force=false):
#	var total_backers = $deployablecontainer/deploy.get_children().size()
	var success = 0
	for _i in range(num):
		if force == true or $deployablecontainer/deploy.get_child(total_backers-1).options[0] == 0:
			$deployablecontainer/backdrop.get_child(total_backers-1).free()
			var removedeploy = $deployablecontainer/deploy.get_child(total_backers-1)
			removedeploy.queue_free()
			$deployablecontainer/deploy.remove_child(removedeploy)
			deployablepositionarray.pop_back()
			discardarray.pop_back()
			success += 1
			total_backers -= 1
		else:
			break
	if success > 0:
		
		total_backers = $deployablecontainer/deploy.get_children().size()
		if focusroulette >= total_backers:
			focusroulette = total_backers-1
#		var increment = 0
		resize_deployables()
#		for i in range(total_backers-success):
#			var backer = $deployablecontainer/backdrop.get_child(i)
##			if i == 0:
##				backer.position = Vector2(((total_backers)-((float(total_backers)/2)+0.5))*deployabledistancing,0)
##			else:
#			if increment = 0
#			backer.position = Vector2(((i-1)-((float(total_backers)/2)+0.5))*deployabledistancing,0)
#			deployablepositionarray[i] = backer.position.x
#			var deploy = $deployablecontainer/deploy.get_child(i+success)
#			deploy.uilocation = i+success
#			deploy.checkbaseposition(true)
#			deploy.lerpitem = true
#			deploy.set_physics_process(true)
#			increment += 1
			

func reposition_deployables(which,towhere):
	#WHICH refers to the deployable's child number
	#TOWHERE refers to where the deployable will be moved to
	if which > 0 and towhere > 0 and which != towhere:
#		var total_backers = $deployablecontainer/deploy.get_children().size()
		$deployablecontainer/deploy.get_child(towhere).pickupsound(true)
		$deployablecontainer/deploy.get_child(which).raise()
			#total_backers - 1     - total_backers  + towhere + 2
		for _i in range(total_backers-(towhere+1)):
			$deployablecontainer/deploy.get_child(towhere).raise()
			deployablepositionarray.append(deployablepositionarray.pop_at(towhere))
			discardarray.append(discardarray.pop_at(towhere))
		var minimum_affected = min(towhere,which)
		for i in range(total_backers-minimum_affected):
			var backer = $deployablecontainer/backdrop.get_child(i+minimum_affected)
#			if i == 0:
#				backer.position = Vector2(((total_backers)-((float(total_backers)/2)+0.5))*deployabledistancing,0)
#			else:
#			backer.position = Vector2(((i+minimum_affected)-((float(total_backers)/2)+0.5))*deployabledistancing,0)
			deployablepositionarray[i+minimum_affected] = backer.position.x
			var deploy = $deployablecontainer/deploy.get_child(i+minimum_affected)
			deploy.uilocation = i+minimum_affected
			deploy.checkbaseposition(true)
			
			if i+minimum_affected > maxitemsize:
				$deployablecontainer/deploy.get_child(i+minimum_affected).material = load(Mainpreload.deployable_over_handsize)
				$deployablecontainer/backdrop.get_child(i+minimum_affected).material = load(Mainpreload.deployable_over_handsize)
			else:
				$deployablecontainer/deploy.get_child(i+minimum_affected).material = null
				$deployablecontainer/backdrop.get_child(i+minimum_affected).material = null
#			deploy.lerpitem = true
#			deploy.set_physics_process(true)
	

const shard_shop_color = Color(1, 0.80, 0.95)

var labelfontsize = 28
var firstrun = true
var recentsizechange = false
func _on_viewport_size_changed():
	if recentsizechange == false:
		recentsizechange = true #this makes it so it only updates the viewport every 4 frames
		if firstrun == true:
			pass
		else:
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			mainscene.fixbackgroundbyzoom()
		$lootchest.rect_pivot_offset = $lootchest.rect_size/2
		var screenratio = (Playervariables.basescreensize.y/clamp(get_viewport_rect().size.y,1,100000))
		var yratio = 1/screenratio
		mainscene.get_node("watermaptops").material.set_shader_param("y_zoom",0.8*screenratio*screenratio/mainscene.get_node("Camera2D").zoom.y)
		var xratio = get_viewport_rect().size.x/Playervariables.basescreensize.x #for event text purposes
		#code for scroll bars
		var minratio = min(yratio,xratio)
		var flinteger_ratio = floor(minratio-0.4)
		if flinteger_ratio < 1:
			flinteger_ratio = 1
#		if minratio > 4.0:
#			$deployablecontainer/lowerui2/playerdebuffs.rect_scale = Vector2(3,3)
#		if minratio > 2.5:
#			$deployablecontainer/lowerui2/playerdebuffs.rect_scale = Vector2(minratio-0.5,minratio-0.5)
#			$deployablecontainer/lowerui2/playerdebuffs.rect_scale = Vector2(2,2)
#		else:
		$deployablecontainer/lowerui2/playerdebuffs.rect_scale = Vector2(flinteger_ratio,flinteger_ratio)
		for child in ui_indicator_array:
#		if ui_indicator != null:
#			ui_indicator.get_child(0).scale = Vector2(minratio,minratio)
			child.rect_scale = Vector2(minratio,minratio)
		$deployablecontainer/healthbox/resleft.get("custom_fonts/font").set_size(60*xratio)
		$deployablecontainer/healthbox/resleft.get("custom_fonts/font").set_outline_size(ceil(1.5*xratio))
#		$deployablecontainer.margin_bottom = 112
#		if Playervariables.reducelowergui == true or get_viewport().size.y < 700:
		if get_viewport().size.y < 700:
			$deployablecontainer.margin_top = -60
			if mainscene.mapgenlocation != Playervariables.Springs:
				$deployablecontainer/healthbox/resleft.margin_top = -50
		elif get_viewport().size.y < 1000:
			$ConversationV1/dialogueart/VBoxContainer2.margin_bottom = -120
			$deployablecontainer.margin_top = -120
			$deployablecontainer/healthbox/resleft.margin_top = 0
		else:
			var extend = (get_viewport().size.y - 1000)/10
			$ConversationV1/dialogueart/VBoxContainer2.margin_bottom = -120 - extend
			$deployablecontainer.margin_top = -120 - extend
			$deployablecontainer.margin_bottom = 112 + extend
			$deployablecontainer/healthbox/resleft.margin_top = 0
		$cameramode.anchor_bottom = clamp(((get_viewport_rect().size.y+$deployablecontainer.margin_top)/clamp(get_viewport_rect().size.y,0,99999))-0.03,0,0.94)#0.77 #0.77 when margin bottom is -120...
		scrollupper(0) #a scrollupper 0 needs to occur if not a -1 or +1 to save position of upper
#		var rawxratio = get_viewport_rect().size.x/Playervariables.basescreensize.x #for event text purposes
		$conversationv2/controls/chatcycle.rect_pivot_offset = $conversationv2/controls/chatcycle.rect_size/2
		var xbarsize = 30*xratio
		var ybarsize = 30*yratio
		var rectbarsizex = $inventoryveil/inventory/Statusscreen.rect_size.x -(xbarsize + 10)
		var rectbarsizey = $inventoryveil/inventory/Abilityscreen.rect_size.y -(ybarsize + 10)
		$inventoryveil/inventory/Statusscreen.get_v_scrollbar().rect_min_size.x = clamp(xbarsize,20,9000)
#		$inventoryveil/inventory/Statusscreen.get_h_scrollbar().rect_min_size.y = clampbarsize
		$inventoryveil/inventory/Statusscreen.get_v_scrollbar().rect_position.x = rectbarsizex
#		$inventoryveil/inventory/Statusscreen.get_h_scrollbar().rect_position.y = rectbarsizey
#		$inventoryveil/inventory/Abilityscreen.get_v_scrollbar().rect_min_size.x = clampbarsize
		$inventoryveil/inventory/Abilityscreen.get_h_scrollbar().rect_min_size.y = clamp(ybarsize,20,9000)
#		$inventoryveil/inventory/Abilityscreen.get_v_scrollbar().rect_position.x = rectbarsizex
		$inventoryveil/inventory/Abilityscreen.get_h_scrollbar().rect_position.y = rectbarsizey
#		if xratio > 1:
#			xratio = (2+xratio)/3
		$deployablecontainer/lowerui2/decknum.get("custom_fonts/font").set_size(40*yratio)
		$deployablecontainer/lowerui2/decknum.get("custom_fonts/font").set_outline_size(ceil(0.8*yratio))
#		scrollupper(-1)
		if $conversationv2.visible == true:
			bubble_font_calc()
			for bubble in $conversationv2/VBoxContainer.get_children():
				bubble.get_node("NinePatchRect/RichTextLabel").get("custom_fonts/normal_font").size = textboxfontcalc
				bubble.get_node("NinePatchRect/RichTextLabel").get("custom_fonts/normal_font").outline_size = clamp(textboxfontcalc/16,2,16)
				fix_text_bubble_font(bubble.get_node("NinePatchRect/RichTextLabel"),textboxfontcalc)#bubble.get_node("NinePatchRect/RichTextLabel").get("custom_fonts/normal_font").get_size())
				if bubble.get_node_or_null("TextureRect") != null:
					bubble.get_node("TextureRect").rect_min_size.x = get_viewport().size.x/3
				var scale = 0.5 + xratio*0.5
				bubble.get_node("NinePatchRect/symbol").rect_scale = Vector2(scale,scale)
				if bubble.get_node("NinePatchRect/symbol").anchor_left > 0.5:
					bubble.get_node("NinePatchRect/symbol").margin_left = -126 * (scale)
					bubble.get_node("NinePatchRect/symbol").margin_top = -122*(scale)
				else:
					bubble.get_node("NinePatchRect/symbol").margin_left = -126 * (scale) * 0.2
					bubble.get_node("NinePatchRect/symbol").margin_right = 126 * (scale) * 0.8
					bubble.get_node("NinePatchRect/symbol").margin_top = -122*(scale) * 0.8
					bubble.get_node("NinePatchRect/symbol").margin_bottom = 122*(scale) * 0.2
#				bubble.get_node("NinePatchRect").rect_min_size.y = (2.2*textboxfontcalc*bubble.get_node("NinePatchRect/RichTextLabel").get_line_count())  + 30
#				bubble.get_node("NinePatchRect").rect_min_size = newdimensions
#				print("textbox check after:"+str(bubble.get_node("NinePatchRect/RichTextLabel").rect_size))
		mainscene.deployablemargintop = $deployablecontainer.margin_top
		deployablescale = (xratio*0.5)# + 0.5
		resize_deployables()
		if firstrun == true:
			for deployable in $deployablecontainer/deploy.get_children():
				deployable.rect_position = Vector2(deployable.uibaseposition,0)
			firstrun = false
		var bardeployablesize = itemsize*deployablescale
		for item in $deployablecontainer/lowerui2/deckbacking/playeritemdeck.get_children():
			item.rect_min_size = bardeployablesize
		var movenameadjust = 1.0
		var speechchoiceadjust = 1.0
		if Playervariables.modarray[Playervariables.mods.TRANSLATION] == true:
			if Playervariables.default_font_dict["font_move_names"] != null:
				movenameadjust = Playervariables.default_font_dict["font_move_names_size"]
			if Playervariables.default_font_dict["font_speech_choices"] != null:
				speechchoiceadjust = Playervariables.default_font_dict["font_speech_choices_size"]
		$inventoryveil/inventory/TabInformation.get("custom_fonts/font").set_size(18*xratio*speechchoiceadjust)
		$inventoryveil/inventory/Status/Label.get("custom_fonts/font").size = (32*xratio*movenameadjust)
		$inventoryveil/inventory/Status/Label.get("custom_fonts/font").outline_size = 2*sqrt(xratio*movenameadjust)
		var shardratio = (xratio + sqrt(xratio))*0.5
		$inventoryveil/inventory/shards/shardcount.get("custom_fonts/font").set_size(80*shardratio)
		$inventoryveil/inventory/shards/shardcount.get("custom_fonts/font").set_outline_size(8*shardratio)
		$inventoryveil/inventory/shards/maxshards.get("custom_fonts/font").set_size(40*shardratio)
		$inventoryveil/inventory/shards/maxshards.get("custom_fonts/font").set_outline_size(5*shardratio)
		if inventory == true:
			var finaldeployablesize = deployablesize * deployablescale
			adjust_status_grid()
#			for item in $inventoryveil/inventory/Abilityscreen/abilitygrid.get_children():
#				item.rect_min_size = finaldeployablesize
#			$inventoryveil/inventory/Statusscreen/status/GridContainer.columns = ceil($inventoryveil/inventory.rect_size.x/800)
			gridlength = clamp(floor(($inventoryveil/inventory.rect_size.y-(80*deployablescale))/finaldeployablesize.y),1,10000) #by the way, this is just straight up the number of rows
#			$inventoryveil/inventory/Abilityscreen/abilitygrid.columns = ceil((Playervariables.playerinnate2darray.size()+Playervariables.playerinventory2darray.size()+Playervariables.playerconsumables2darray.size())/gridlength)
			$inventoryveil/inventory/Abilityscreen/abilitygrid.columns = ceil((Playervariables.playerinventory2darray.size()+Playervariables.playerconsumables2darray.size())/gridlength)
			$inventoryveil/inventory/Abilityscreen.rect_scale = Vector2(1,1)*deployablescale
			$inventoryveil/inventory/Abilityscreen.rect_size = $inventoryveil/inventory/Statusscreen.rect_size / clamp(deployablescale,0.1,9999)
#			$inventoryveil/inventory/Abilityscreen/abilitygrid.rect_scale = Vector2(1,1)*clamp(deployablescale,0.5,2)
#			for child in $inventoryveil/inventory/Abilityscreen/abilitygrid.get_children():
#				child.rect_scale = Vector2(1,1)*clamp(deployablescale,0.5,2)
		var separation = -(itemsize.x*deployablescale) + (get_viewport_rect().size.x*0.1)#-178 + (get_viewport_rect().size.x*0.1)#-159#*0.125) #math! ## 5*(iconsize+separation) = viewportrectsize*anchorx
		$deployablecontainer/lowerui2/deckbacking/playeritemdeck.set("custom_constants/separation",separation)
		recentsizechange = false
#		$upperui/events.visible = false
		labelfontsize = 28*xratio
		if Playervariables.modarray[Playervariables.mods.TRANSLATION] == true:
			labelfontsize = labelfontsize*Playervariables.default_font_dict["font_event_messages_size"]
		$upperui/events.get_child(0).get("custom_fonts/font").set_size(labelfontsize)
		if $upperui/alerts.get_children().size() > 0:
			$upperui/alerts.get_child(0).get("custom_fonts/font").set_size(labelfontsize)
#		yield(get_tree(),"idle_frame") #wait for the size to update
#		$upperui/events.visible = true
		labelheight = (labelfontsize*1.6)#+3#+7#$upperui/events.get_children()[-1].rect_size.y+5
		var sizebefore = $upperui.rect_size.y
		$upperui.rect_size.y = clamp(($upperui/events.get_children().size()*labelheight)+88,170,99999)
		$upperui.rect_position.y -= ($upperui.rect_size.y-sizebefore)
		$upperui.margin_right = 0
		$upperui/events.margin_right = -20
#		$upperui/events.margin_top = 64
		$upperui/events.margin_bottom = -20#*yratio
#		for debufftile in debufftokenarray:
#		var inc = 0
#		for debufftile in $deployablecontainer/lowerui2/playerdebuffs.get_children():
#			if debufftile.get_node("debuffnum").z_index < 100:
#				if debufftokenarray.size() > inc:
##					if get_viewport().size.y < 830:
##						debufftile.set_texture(load("res://Assets/ui/debufficons/"+str(debufftokenarray[inc])+"d.png"))
##					else:
#					debufftile.set_texture(load("res://Assets/ui/debufficons/"+str(debufftokenarray[inc])+".png"))
#				else:
#					print("Issue changing debufftiles in dialogue.gd. Breaking.")
#					break
#			inc += 1

func resize_deployables():
		if get_viewport_rect().size.x > 1056:
			var itemcap = total_backers#maxitemsize+1
			var extras = int(clamp((get_viewport_rect().size.x - 1056)/(200+(get_viewport_rect().size.x/12)-125),0,99))
			itemcap = clamp(itemcap-extras,4,10)
			deployabledistancing = clamp((((get_viewport_rect().size.x+1056)/2)*0.76)/(itemcap),64,800)
#			deployabledistancing = clamp((((get_viewport_rect().size.x+3168)/4)*0.76)/(itemcap),64,800)
#			print("DEPLOYABLEDISTANCING.")
#			print(deployabledistancing)
#			print(((get_viewport_rect().size.x+1056)/2)*0.76)
		else:
			deployabledistancing = clamp((get_viewport_rect().size.x*0.76)/(total_backers),64,400)
#			deployabledistancing = clamp((((get_viewport_rect().size.x+3168)/4)-256)/(itemcap),64,400)
#		else:
#			deployabledistancing = clamp((get_viewport_rect().size.x-256)/(maxitemsize+1),64,400)
		var increment = 0
		for backer in $deployablecontainer/backdrop.get_children():
			if increment == 0:
				backer.position = Vector2((($deployablecontainer/backdrop.get_children().size())-((float((total_backers))/2)+0.5))*deployabledistancing,0)
			else:
				backer.position = Vector2(((increment)-((float((total_backers))/2)+0.5))*deployabledistancing,0)
			deployablepositionarray[increment] = backer.position.x
			increment += 1
		for deployable in $deployablecontainer/deploy.get_children():
			deployable.call("checkbaseposition",true)#,xratio)
#			deployable.lerpitem = true
#			deployable.set_physics_process(true)

var discardarray = []
func discard(onoff,slotnum):
	discardarray[slotnum] = onoff
#func autodiscard(_innateitem,ID,addremove): #true if innate, false if item. True if adding, false if removing.
#	if addremove == true:
##		if innateitem == true:
##			if Playervariables.autodiscardarrayinnate.find(ID) == -1:
##				Playervariables.autodiscardarrayinnate.append(ID)
##				for card in range(maxinnatesize):
##					if $deployablecontainer/deploy.get_child(card).get("options")[1] == ID:
##						discard(true,card)
##						$deployablecontainer/deploy.get_child(card).call("remotediscard",true)
##						break
##		else:
#		if Playervariables.autodiscardarrayitem.find(ID) == -1:
#			Playervariables.autodiscardarrayitem.append(ID)
#			for card in range(maxitemsize):
#				if $deployablecontainer/deploy.get_child(card).get("options")[1] == ID:
#					discard(true,card)
#					$deployablecontainer/deploy.get_child(card).call("remotediscard",true)
#					break
#	else:
##		if innateitem == true:
##			if Playervariables.autodiscardarrayinnate.find(ID) != -1:
##				Playervariables.autodiscardarrayinnate.remove(Playervariables.autodiscardarrayinnate.find(ID))
##				for card in range(maxinnatesize):
##					if $deployablecontainer/deploy.get_child(card).get("options")[1] == ID:
##						discard(false,card)
##						$deployablecontainer/deploy.get_child(card).call("remotediscard",false)
##						break
##		else:
#		if Playervariables.autodiscardarrayitem.find(ID) != -1:
#			Playervariables.autodiscardarrayitem.remove(Playervariables.autodiscardarrayitem.find(ID))
#			for card in range(maxitemsize):
#				if $deployablecontainer/deploy.get_child(card).get("options")[1] == ID:
#					discard(false,card)
#					$deployablecontainer/deploy.get_child(card).call("remotediscard",false)
#					break
#var delaymax = 0
#var delay = 0
func trash_effect(cardnum,_defunctinnate = false):
	var options = $deployablecontainer/deploy.get_child(cardnum).options.duplicate()
	var movenum = options[0]
	if movenum != null and movenum > 0:
		if mainscene.mapgenlocation == Playervariables.Tutorial2:
			get_tree().call_group("Refresh_Pedestals","refresh",movenum,true)
		var newtrash = load(Mainpreload.Trash).instance()
		mainscene.add_child(newtrash)
		newtrash.position = mainscene.playerlocv
		var move = Playervariables.get("Move"+str(movenum))
		var itemname = move["name"]
		var innate = false
		if options[2] == Playervariables.INNATE:#move["slot"] == Playervariables.INNATE:
			innate = true
		newtrash.trash(itemname,innate)
func draw_cards(discardnum = -1):
	var carddrawn = false
#	if innatedeckarray.size() > 0:
#		for card in maxinnatesize:
#			if $deployablecontainer/deploy.get_child(card).get("options")[0] == 0 or discardarray[card] == true:
#				if discardarray[card] == true:
#					trash_effect(card,true)
#				if innatedeckarray[0] >= Playervariables.playerinnate2darray.size():
#					innatedeckarray.remove(0)
#					draw_cards()
#					return
#				if Playervariables.autodiscardarrayinnate.find(innatedeckarray[0]) != -1:
#					discardarray[card] = true
#				else:
#					discardarray[card] = false
#				$deployablecontainer/deploy.get_child(card).call("_identify",Playervariables.playerinnate2darray[innatedeckarray.pop_front()],false,discardarray[card])
#				carddrawn = true
#				break
	if deckarray.size() > 0:#if carddrawn == false and deckarray.size() > 0:
		if discardnum == -1:
			if total_backers-1 <= maxitemsize:
				for card in maxitemsize:
					if $deployablecontainer/deploy.get_child(card+1).get("options")[0] == 0:
						if deckarray[0] >= Playervariables.playerinventory2darray.size():
							deckarray.remove(0)
							draw_cards()
							return
						if Playervariables.autodiscardarrayitem.find(deckarray[0]) != -1:
							discardarray[card+1] = true
						else:
							discardarray[card+1] = false
						$deployablecontainer/deploy.get_child(card+1).call("_identify",Playervariables.playerinventory2darray[deckarray.pop_front()],false,discardarray[card+1])
						carddrawn = true
						break
			else:
				for card in total_backers-1:
					var deployable = $deployablecontainer/deploy.get_child(card+1)
					if deployable.options[0] == 0:
						reposition_deployables(card+1,total_backers-1)
						remove_last_deployables(1)
						break#MUST break because otherwise total_backers-1 will be greater than the actual total backers
		if carddrawn == false:
			for card in total_backers-1:
				if discardarray[card+1] == true and (discardnum == -1 or discardnum == $deployablecontainer/deploy.get_child(card+1).options[1]):
					trash_effect(card+1)
					if deckarray[0] >= Playervariables.playerinventory2darray.size():
						deckarray.remove(0)
						draw_cards()
						return
					if Playervariables.autodiscardarrayitem.find(deckarray[0]) != -1 and Playervariables.curseditemdict[Playervariables.ROPEGAG] == false:
						discardarray[card+1] = true
					else:
						discardarray[card+1] = false
					if total_backers-1 <= maxitemsize:
						$deployablecontainer/deploy.get_child(card+1).call("_identify",Playervariables.playerinventory2darray[deckarray.pop_front()],false,discardarray[card+1])
						carddrawn = true
					else:
						reposition_deployables(card+1,total_backers-1)
						remove_last_deployables(1,true)
					break
	elif Playervariables.corruptiondict.has("body") and Playervariables.corruptiondict["body"] == Playervariables.raceIMP:
		force_stack_card(115,true)
	if carddrawn == true:
		stack_deck(false)
	elif warnonce == false and mainscene.mapgenlocation == Playervariables.Tutorial2 and deckarray.size() > 0:
		if $deployablecontainer/deploy.get_child(0).get("options")[0] != 12:
			$deployablecontainer/deploy.get_child(0).add_indicator(true)
		else:
			$deployablecontainer/deploy.get_child(1).add_indicator(true)
		register_event("Your item hotbar is full! Right-click to discard.",[],true)
		warnonce = true
	if warnonce == true and mainscene.mapgenlocation == Playervariables.Tutorial2 and deckarray.size() <= 0 and $deployablecontainer/deploy.get_children().size() >= 2:
		$deployablecontainer/deploy.get_child(0).add_indicator(false)
		$deployablecontainer/deploy.get_child(1).add_indicator(false)
#	if carddrawn == true:
#		delay = delaymax
#	$deployablecontainer/lowerui2/delaycounter/delaylabel.set_text(str(innatedeckarray.size()))
#	$deployablecontainer/lowerui2/delaycounter/delaylabel2.set_text(str(deckarray.size()))

var warnonce = false

var deckarray = []
#var innatedeckarray = []
#var invalidcards = []
#var currentcards = []
#var firstshuffle = true
#var firstshuffleinnate = true
#func innateshuffle(firstshuffle):
#	innatedeckarray = []
#	var increment = 0
#	for n in Playervariables.playerinnate2darray.size():
#		innatedeckarray.append(increment)
#		increment += 1
##		innatedeckarray.append(Playervariables.playerinnate2darray[n][1])
#	if firstshuffle == false:
#		var invalidcards = []
#		for card in range(maxinnatesize):
#			invalidcards.append($deployablecontainer/deploy.get_child(card).get("options")[1])
#		for n in invalidcards.size():
#			innatedeckarray.erase(invalidcards[n])
##	else:
##		firstshuffleinnate = false
#	randomize()
#	innatedeckarray.shuffle()
func itemshuffle(firstshuffle,noinvalid=false):
	randomize()
	deckarray = []
	if firstshuffle == true:
		if Playervariables.autodiscardarrayitem.size() > 0 or Playervariables.autodiscardarrayinnate.size() > 0:
			remove_items()
		for value in Playervariables.retain_deck_array:
			if value > Playervariables.playerinventory2darray.size():
				Playervariables.retain_deck_array.erase(value)
		deckarray = Playervariables.retain_deck_array
		var faux_inventory_array = Playervariables.playerinventory2darray.duplicate()
		reload = faux_inventory_array.size() - deckarray.size()
		faux_inventory_array.shuffle()
		for value in faux_inventory_array:
			if deckarray.find(value[1]) == -1:
				deckarray.append(value[1])
				if value[2] == Playervariables.CURSE:
					reload_curses += 1
	else:
		var increment = 0
		for n in Playervariables.playerinventory2darray.size():
			deckarray.append(increment)
			increment += 1
		if noinvalid == false:
			var invalidcards = []
			for card in range(maxitemsize):
				invalidcards.append($deployablecontainer/deploy.get_child(card).get("options")[1])
			for n in invalidcards.size():
				deckarray.erase(invalidcards[n])
		deckarray.shuffle()
	stack_deck(true)

func force_stack_card(num,no_duplicates=false):
	for card in deckarray:
		if Playervariables.playerinventory2darray[card][0] == num:
			return
	if no_duplicates == true:
		for card in $deployablecontainer/deploy.get_children():
			if card.options[0] == num:
				return
	for card_info in Playervariables.playerinventory2darray:
		if card_info[0] == num:
			deckarray.append(card_info[1])
			stack_deck(false)
			return
	mainscene.awardplayer(num)
	if num == 115:
		register_event("You can't escape your Impish nature.")
##var nowshopping = false
#func shopping():
#	var newconversation = Playervariables.process_speechdict("qadesshop")
#	$ConversationV1.converse(newconversation,0)

func demotest():
	var newconversation = Playervariables.process_speechdict("qadestestconvo")
	$ConversationV1.converse(newconversation,0)

var allintros = false
#var npctalkstat = 0
#var npcdefeatstat = 0
#func shrineencounter(instance):
#	if instance == 1:
#		var newconversation = Playervariables.process_speechdict("shikashrine")
#		$ConversationV1.converse(newconversation,0) #it was "return $ConversationV1.converse(newconversation)" before
#		npctalkstat = Playervariables.timestalkedtoshika
#		npcdefeatstat = Playervariables.timesdefeatedshika
#
#	if instance == 2:
#		return converse(Playervariables.speechdictshrineloss)

#var askconsent = false
#func consentconversation():
#	askconsent = Playervariables.askconsent
#	var newconversation = Playervariables.process_speechdict("qadesconsentconvo")
#	$ConversationV1.converse(newconversation,0) #it was "return $ConversationV1.converse(newconversation)" before

#func lossscene(instance):
#	match instance:
#		3: #ram-girl first loss scene
#			var newconversation = Playervariables.process_speechdict("lossramgirl")
#			$ConversationV1.converse(newconversation,0)
#
#func tfscene(ID,instance):
#	match int(ID):
#		3:
#			match int(instance):
#				0: #ram-girl horns -> small
#					var newconversation = Playervariables.process_speechdict("ramgirlhornstf")
#					$ConversationV1.converse(newconversation,0)
#				1: #ram-girl horns small - > big
#					var newconversation = Playervariables.process_speechdict("ramgirlhornstf")
#					$ConversationV1.converse(newconversation,1)

#func pitchencounter(instance):
#	match instance:
#		1:
#			converse(Playervariables.speechdictpitch)
#		2:
#			converse(Playervariables.speechdictpitch2)
#		_:
#			converse(Playervariables.speechdictpitch)

func start_conversation(speechdictstring,startpoint = 0):
	var newconversation = Playervariables.process_speechdict(speechdictstring)
	$ConversationV1.converse(newconversation,startpoint)

func fix_text_bubble_font(textbox,fontsize):
	#AREA = 1x * 0.7x
	#1x = XAXIS
	#0.7x = YAXIS
	#so 0.7x^2 = AREA
	#x = sqrt(AREA/0.7)
#	var text = textbox.get_text()
#	print("textbox check before:"+str(textbox.rect_size))
	var textarea = Playervariables.default_font_dict["if_characters_overflow_textbox_increase_this"]*fontsize*fontsize*1.3 * textbox.get_text().length() #1.3 is roughly the margin sizes
	var yratio = 0.5
	var xlimit = get_viewport_rect().size.x * 0.6
#	print("xlimit: "+str(xlimit))
	var x = sqrt(textarea/yratio)
	var xvalue = max(140,x)
#x must be equal to xlimit
#x = xlimit
#x^2 = (textboxrectsizex*rectsizey)/yratio
#xlimit^2 / (textboxx*textboyy) = 1/yratio
#(textboxx*textboyy)/xlimit^2 = yratio
	if xvalue > xlimit and xlimit > 0:
		yratio = textarea/(xlimit*xlimit)#yratio*(xlimit/clamp(xvalue,1,99999))
		x = sqrt(textarea/yratio)
		xvalue = max(140,x)
		xvalue = xlimit
	var yvalue = max(max(220,yratio*x),textbox.rect_size.y)
#	if textbox.rect_size.y > yvalue:
#		textbox.anchor_bottom = 0
#		yvalue = textbox.rect_size.y
#	var xvalue = clamp(50+((fontsize*fontsize)/((0.015)*clamp((yvalue-30),1,1000))),140,get_viewport_rect().size.x) #I have no idea what the fuck is going on here
#	print("x value: "+str(x)+"... xvalue yvalue:"+str(Vector2(xvalue,yvalue)))
	textbox.get_parent().rect_min_size = Vector2(xvalue,yvalue)
#	print("textbox check after:"+str(textbox.rect_size))
	textbox.margin_left = 25 * xvalue/220 #220/170 used on margins
	textbox.margin_right = -25 * xvalue/220
	textbox.margin_bottom = -15 * yvalue/140
	textbox.margin_top = 15 * yvalue/140
	return Vector2(xvalue,yvalue)
#	var fulllines = get_viewport_rect().size.x/(2*clamp((fontsize+4)*text.length(),1,10000))
#	print("fulllines before:" +str(get_viewport_rect().size.x/(2*clamp((fontsize+4)*text.length(),1,10000))))
#	print("fulllines now:" +str(fulllines))
#	print("fulllines future:" +str(get_viewport_rect().size.x/(2*clamp((fontsize+8)*text.length(),1,10000))))
#	var yvalue = max(220,40 + ((fontsize+3)*fulllines))
#	var xvalue = clamp(50+((fontsize*fontsize)/((0.015)*clamp((yvalue-30),1,1000))),140,get_viewport_rect().size.x) #I have no idea what the fuck is going on here
#	return Vector2(xvalue,yvalue)

#func exitramble(): #this might be necessary, but I commented it because it didn't seem to be. Watch out!
#	conversation = true
#	if $ConversationV1.spritetalking == true:
#		$ConversationV1.spritetalker.talking(false)


var focusroulette = 0
var lastmousepos
#signal bubble_talking
func _input(event):
#	if inventory == true:
#		if event.is_action_pressed("ui_click"):
#			print("Huh.")
#		if event is InputEventScreenTouch:
#			print(event.pressed,event.position)
	if event.is_action_pressed("ui_acceptright"):
#		for deployable in $deployablecontainer/deploy.get_children():
#			deployable.deselect()
		if focusroulette == total_backers:
			focusroulette = 0
			get_parent().get_parent().call("option1",1)
		else:
			get_tree().call_group("itemslot","synergy_effect",-1)
			focusroulette += 1
			if focusroulette > 0 and focusroulette <= $deployablecontainer/deploy.get_children().size():
				$deployablecontainer/deploy.get_child(focusroulette-1).focusroulettecall()
	if event.is_action_pressed("ui_acceptleft"):
#		for deployable in $deployablecontainer/deploy.get_children():
#			deployable.deselect()
		if focusroulette == 0:
			focusroulette = total_backers
			get_parent().get_parent().call("option1",1)
		else:
			get_tree().call_group("itemslot","synergy_effect",-1)
			focusroulette += -1
			if focusroulette > 0 and focusroulette <= $deployablecontainer/deploy.get_children().size():
				$deployablecontainer/deploy.get_child(focusroulette-1).focusroulettecall()

#var Shopitem = preload("res://shopitem.tscn")
#var shopitem #new item
#var shopinventory = []
#var shopsize = 3
#func shopsetinventory():
#	for n in shopsize:
#		shopitem = Shopitem).instance()
#		$inventoryveil/inventory/Shopscreen/items.add_child(shopitem)
#		shopinventory.append(shopitem)
#		var shoppickup = shopitem.get_child(1)
#		shoppickup.generateitem(false,2)
#		shopitem.info(n,str(shoppickup.get("cost")),str(shoppickup.get("itemname")),shoppickup.get("currentitem"))
#func purchaseitem(ID,item):
#	$ConversationV1/SFX/purchase.play()
#	mainscene.awardplayer(item)
#	shopinventory[ID].get_child(1).generateitem(false,-1)
#	countresources()

#func countresources():
#	$inventoryveil/inventory/shards/shardcount.set_text(str(Playervariables.playershards))
#	if Playervariables.playershards > 0:
#		if Playervariables.playershards > 5:
#			if Playervariables.playershards > 10:
#				$inventoryveil/inventory/shards/shardvis.texture = load("res://Assets/ui/shards3.png")
#			else:
#				$inventoryveil/inventory/shards/shardvis.texture = load("res://Assets/ui/shards2.png")
#		else:
#			$inventoryveil/inventory/shards/shardvis.texture = load("res://Assets/ui/shards1.png")
#	else:
#		$inventoryveil/inventory/shards/shardvis.texture = null
#	$inventoryveil/inventory/shards/foodcount.set_text(str(Playervariables.playerforage))
#	if Playervariables.playerforage > 0:
#		if Playervariables.playerforage > 5:
#			if Playervariables.playerforage > 10:
#				$inventoryveil/inventory/shards/foodvis.texture = load("res://Assets/ui/food3.png")
#			else:
#				$inventoryveil/inventory/shards/foodvis.texture = load("res://Assets/ui/food2.png")
#		else:
#			$inventoryveil/inventory/shards/foodvis.texture = load("res://Assets/ui/food1.png")
#	else:
#		$inventoryveil/inventory/shards/foodvis.texture = null

var optionID = 1 #the corresponding ID of the option, 0 is nothing 1 is basic
var pause #paused or not?
var inventory = false #inventory open or not?
var gridlength = 5
const deployablesize = Vector2(202,235)
const itemsize = Vector2(400,250)
var deployablescale = 0.5#((get_viewport_rect().size.x/Playervariables.basescreensize.x)/2) + 0.5

func set_shards(reduce = -1):
	if reduce > -1:
		$inventoryveil/inventory/shards/shardcount.set_text(str(Playervariables.playershards-reduce))
		$inventoryveil/inventory/shards/shardcount.set_self_modulate(Color(1,0.7,0.7))
		if Playervariables.playershards-reduce >= 0:
			$inventoryveil/inventory/shards/hologram.material.set_shader_param("speed",25)
			$inventoryveil/inventory/shards/hologram.set_self_modulate(Color(0.866898, 0.761719, 1))
	else:
		$inventoryveil/inventory/shards/hologram.material.set_shader_param("speed",5)
		$inventoryveil/inventory/shards/hologram.set_self_modulate(Color(0.560784, 0.313726, 0.647059))
		$inventoryveil/inventory/shards/shardcount.set_self_modulate(Color(1,1,1))
		$inventoryveil/inventory/shards/shardcount.set_text(str(Playervariables.playershards))
		$inventoryveil/inventory/shards/maxshards.set_text(" /"+str(Playervariables.playershardsmax))
		$deployablecontainer/healthbox/shards2/resleft2.set_text(str(Playervariables.playershards))

func activate_shards_shop(yesno):
	if yesno == true:
		$inventoryveil/inventory/shards/hologram.visible = true
		$inventoryveil/inventory.set_self_modulate(shard_shop_color)
		$inventoryveil/inventory/shards.set_modulate(Color(1,1,1,1))
	else:
		$inventoryveil/inventory/shards/hologram.visible = false
		$inventoryveil/inventory.set_self_modulate(Color(1,1,1))
		$inventoryveil/inventory/shards.set_modulate(Color(0.8,0.5,0.6,0.7))

var show_inventory_array = []
var removed_corruption = false
var healthboxbuttonused = false
func _on_healthbox_pressed():
	healthboxbuttonused = true
	inventoryengage()
func inventoryengage():
	if healthboxbuttonused == false and $conversationv2.visible == true:#$ConversationV1.enemyartarray.size() > 0:
#		if $conversationv2.visible == true:
		_on_closeconvo_pressed()
#		else:
#			$ConversationV1.unscroll(false,true)
#			get_node("/root/Master/SFX/uib").play()
		return #not necessary, put it here anyway
	elif mainscene.get("moveready") == true:
		$deployablecontainer/healthbox/shards2.visible = false
		set_shards()
		$ConversationV1.inventory = true
		Input.set_custom_mouse_cursor(load(Mainpreload.cursorpoint))
		Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)
#	if nowshopping == true or $ConversationV1.spritetalking == true or mainscene.get("moveready") == true:
		if $ConversationV1.spritetalking == true:
			$ConversationV1/lowerui.visible = false
			if $ConversationV1.waitingchoice == true:
				$ConversationV1/choice.visible = false
#		$inventoryveil/inventory/Statusscreen/status/GridContainer.columns = ceil($inventoryveil/inventory.rect_size.x/800)
#		for child in $inventoryveil/inventory/Statusscreen/status/GridContainer.get_children():
#			child.queue_free()
		for child in $inventoryveil/inventory/Statusscreen/newstatus.get_children():
			child.queue_free()
		for debuff in mainscene.playerdebuffarray.size():
			if mainscene.playerdebuffarray[debuff] > 0 and debuff != (Playervariables.SWEATY-1):
				var newdebuff = load(Mainpreload.NewDebuffDescriber).instance()
				$inventoryveil/inventory/Statusscreen/newstatus.add_child(newdebuff)
				newdebuff.get_node("Board/Icon").texture = load("res://Assets/ui/debufficons/"+str(debuff+1)+"d.png")
#				newdebuff.get_node("Board/Icon/border").texture = load("res://Assets/ui/debufficons/debuffborder.png")
				newdebuff.get_node("Board/Info").set_text(Playervariables.debuffdescriptionarray[debuff])
				newdebuff.get_node("Board/TitleBar/Title").set_text(Playervariables.curseditemstransname["Status"]+Playervariables.debuffidentifyarray[debuff])
		var enableshop = false
		activate_shards_shop(false)
		if (mainscene.mapgenlocation in [Playervariables.LevelSelect1,Playervariables.Village,Playervariables.Springs,Playervariables.Gallery]) == true:
#			if mainscene.mapgenlocation == Playervariables.Springs:
#				if ui_indicator != null:
#					if healthboxbuttonused == false:
#						handle_ui_indicator($inventoryveil/inventory/Status,indi.FEATURE,null,true)
#					else:
#						handle_ui_indicator(self,indi.FEATURE,null,false)
			enableshop = true
			if healthboxbuttonused == true:
				activate_shards_shop(true)
		if Playervariables.playerbustsize > 1:
			var newdebuff = load(Mainpreload.NewDebuffDescriber).instance()
			$inventoryveil/inventory/Statusscreen/newstatus.add_child(newdebuff)
			newdebuff.get_node("Board/Icon").texture = load("res://Assets/ui/debufficons/bustsize"+str(Playervariables.playerbustsize)+".png")
			newdebuff.get_node("Board/Icon/border").texture = load("res://Assets/ui/debufficons/corruptionborder.png")
			if enableshop == true:
				newdebuff.corruptionref = "bust"
			match int(Playervariables.playerbustsize):
				2:
					newdebuff.get_node("Board/Info").set_text(Playervariables.describecorruptiondict["bust2"])
					newdebuff.get_node("Board/TitleBar/Title").set_text(Playervariables.curseditemstransname["bust2"])
					if enableshop == true:
						newdebuff.get_node("shoptab/TextureButton/Label").set_text("2")
				3:
					newdebuff.get_node("Board/Info").set_text(Playervariables.describecorruptiondict["bust3"])
					newdebuff.get_node("Board/TitleBar/Title").set_text(Playervariables.curseditemstransname["bust3"])
					if enableshop == true:
						newdebuff.get_node("shoptab/TextureButton/Label").set_text("4")
				4:
					newdebuff.get_node("Board/Info").set_text(Playervariables.describecorruptiondict["bust4"])
					newdebuff.get_node("Board/TitleBar/Title").set_text(Playervariables.curseditemstransname["bust4"])
					if enableshop == true:
						newdebuff.get_node("shoptab/TextureButton/Label").set_text("6")
			if Playervariables.get_corruption("colour") == Playervariables.raceNAMAN:#Playervariables.racelockcorruptionarray[Playervariables.raceNAMAN] == true:
				if mainscene.mapgenlocation != Playervariables.Gallery and enableshop == true:
					newdebuff.get_node("shoptab/TextureButton").texture_normal = load(Mainpreload.LockedResource)
					newdebuff.get_node("shoptab/TextureButton").disabled = true
					newdebuff.get_node("shoptab/TextureButton/Label").set_text("")
				newdebuff.get_node("Board").texture = load(Mainpreload.LockedBoard)
		for corruptionkey in Playervariables.corruptiondict:
			var race = Playervariables.corruptiondict[corruptionkey]
			if corruptionkey == "colour":
				var icon = load("res://Assets/ui/debufficons/colour.png")
				var newdebuff = load(Mainpreload.NewDebuffDescriber).instance()
				var describekey = "colour"
				newdebuff.get_node("Board/Icon").texture = icon
				newdebuff.get_node("Board/Icon/border").texture = load("res://Assets/ui/debufficons/corruptionborder.png")
				newdebuff.get_node("Board/Icon").set_self_modulate(Playervariables.racecolorarray[race])
				$inventoryveil/inventory/Statusscreen/newstatus.add_child(newdebuff)
				newdebuff.get_node("Board/Info").set_text(Playervariables.describecorruptiondict[describekey])
				var capskey = Playervariables.bodypartdict["colour"]
				capskey = capskey.substr(0,1).to_upper() + capskey.substr(1,-1)
				newdebuff.get_node("Board/TitleBar/Title").set_text(capskey+": "+Playervariables.racearray[race])
				newdebuff.corruptionref = corruptionkey
				newdebuff.get_node("shoptab/TextureButton/Label").set_text("5")
			else:
				var describekey = corruptionkey + str(race)
				if Playervariables.describecorruptiondict.has(describekey) and (describekey != "armright3" or  Playervariables.curseditemdict[Playervariables.PAWS] == false):# and (corruptionkey != "pants" or Playervariables.cannot_wear_pants == false):
					var icon = load("res://Assets/ui/debufficons/"+describekey+".png")
					var newdebuff = load(Mainpreload.NewDebuffDescriber).instance()
					if icon != null:
						newdebuff.get_node("Board/Icon").texture = icon
						newdebuff.get_node("Board/Icon/border").texture = load("res://Assets/ui/debufficons/corruptionborder.png")
					else:
						newdebuff.get_node("Board/Icon").texture = null
						newdebuff.get_node("Board/Icon/border").texture = null
					$inventoryveil/inventory/Statusscreen/newstatus.add_child(newdebuff)
					var capskey = corruptionkey#.substr(1,-1)
					if Playervariables.bodypartdict.has(capskey):#Playervariables.modarray[Playervariables.mods.TRANSLATION] == true:
						capskey = Playervariables.bodypartdict[capskey]
					capskey = capskey.substr(0,1).to_upper() + capskey.substr(1,-1)
#					if describekey == "backhorns8" and Playervariables.harpy_variants == true:
#						newdebuff.get_node("Board/Icon").texture = load("res://Assets/ui/debufficons/backhorns7.png")
#						newdebuff.get_node("Board/Info").set_text(Playervariables.describecorruptiondict["backhorns7"])
#						newdebuff.get_node("Board/TitleBar/Title").set_text(capskey+": "+Playervariables.racearray[Playervariables.raceHARPY])
					if describekey == "armright3" and Playervariables.wolf_variants == true:
						newdebuff.get_node("Board/Icon").texture = load("res://Assets/ui/debufficons/armright6.png")
						newdebuff.get_node("Board/Info").set_text(Playervariables.describecorruptiondict["armright6"])
						newdebuff.get_node("Board/TitleBar/Title").set_text(capskey+": "+Playervariables.racearray[Playervariables.raceWOLF])
					else:
						newdebuff.get_node("Board/Info").set_text(Playervariables.describecorruptiondict[describekey])
						newdebuff.get_node("Board/TitleBar/Title").set_text(capskey+": "+Playervariables.racearray[race])
					if enableshop == true:
						newdebuff.corruptionref = corruptionkey
						if corruptionkey == "body" or corruptionkey == "face":
							newdebuff.get_node("shoptab/TextureButton/Label").set_text("2")
						elif corruptionkey == "armright" and Playervariables.hands == false:
							newdebuff.get_node("shoptab/TextureButton/Label").set_text("8")
						elif corruptionkey == "pants" or corruptionkey == "top":
							newdebuff.get_node("shoptab/TextureButton/Label").set_text("4")
						else:
							newdebuff.get_node("shoptab/TextureButton/Label").set_text("6")
					if Playervariables.get_corruption("colour") == race or (race == Playervariables.raceRAM and Playervariables.curseditemdict[Playervariables.ROPEGAG] == true):#Playervariables.racelockcorruptionarray[Playervariables.corruptiondict[corruptionkey]] == true:
						if mainscene.mapgenlocation != Playervariables.Gallery and enableshop == true:
							newdebuff.get_node("shoptab/TextureButton").texture_normal = load(Mainpreload.LockedResource)
							newdebuff.get_node("shoptab/TextureButton").disabled = true
							newdebuff.get_node("shoptab/TextureButton/Label").set_text("")
						newdebuff.get_node("Board").texture = load(Mainpreload.LockedBoard)
		if Playervariables.tempcorruptiondict.has("beads") and Playervariables.tempcorruptiondict["beads"] >= Playervariables.tempcorruptionbreakpoints["beads"][0]:
			Playervariables.curseditemdict[Playervariables.BEADS] = true
		else:
			Playervariables.curseditemdict[Playervariables.BEADS] = false
		for cursednum in Playervariables.curseditemdict:
			if cursednum != -13 and Playervariables.curseditemdict[cursednum] == true and Playervariables.curseditemfiledict[cursednum] != null and (cursednum != Playervariables.NORMALPAWS):
				var newdebuff = load(Mainpreload.NewDebuffDescriber).instance()
				$inventoryveil/inventory/Statusscreen/newstatus.add_child(newdebuff)
				newdebuff.get_node("Board/Icon").texture = load("res://Assets/ui/debufficons/"+Playervariables.curseditemfiledict[cursednum]+".png")
				newdebuff.get_node("Board/Icon/border").texture = load("res://Assets/ui/debufficons/curseborder.png")
				newdebuff.get_node("Board/Info").set_text(Playervariables.curseditemdescribedict[cursednum])
				newdebuff.get_node("Board/TitleBar/Title").set_text(Playervariables.curseditemstransname["Cursed Item"]+Playervariables.curseditemstransname[cursednum])
		if mainscene.mapgenlocation == Playervariables.Gallery:
			for corruption in $inventoryveil/inventory/Statusscreen/newstatus.get_children():
				if corruption.get_node_or_null("shoptab/TextureButton/Label") != null:
					corruption.get_node("shoptab/TextureButton/Label").set_text("0")
		adjust_status_grid()
		$inventoryveil/inventory/Abilityscreen.visible = false
		$inventoryveil/inventory/Settingscreen.visible = false
#		countresources()
		var finaldeployablesize = deployablesize * deployablescale
		gridlength = clamp(floor(($inventoryveil/inventory.rect_size.y-(80*deployablescale))/finaldeployablesize.y),1,10000)
		$inventoryveil/inventory/Abilityscreen/abilitygrid.columns = clamp(ceil((Playervariables.passivearray.size()+Playervariables.playerinventory2darray.size()+Playervariables.playerconsumables2darray.size())/gridlength),1,9999)
		pause = true
		$deployablecontainer/lowerui2/climbtoggle.visible = false
		$inventoryveil/inventory/Status.set_modulate(Color(0.3,0.8,0.8))
		$inventoryveil/inventory/Ability.set_modulate(Color(1,1,1))
		$inventoryveil/inventory/Settings.set_modulate(Color(1,1,1))
		$inventoryveil/inventory/Settings.show_behind_parent = true
		$inventoryveil/inventory/Status.show_behind_parent = false
		$inventoryveil/inventory/Ability.show_behind_parent = true
		$inventoryveil/inventory/Statusscreen.visible = true
		$inventoryveil/inventory/Ability.texture_normal = load(Mainpreload.buttonst)
		$inventoryveil/inventory/Settings.texture_normal = load(Mainpreload.buttonst)
		$inventoryveil/inventory/Status.texture_normal = load(Mainpreload.buttonst)
		$inventoryveil/inventory/Settingscreen/Scrollcontainer/settings/qcontrol/quittomainmenu.texture_normal = load(Mainpreload.buttonst)
		$inventoryveil/inventory/Settingscreen/Scrollcontainer/settings/qcontrol/quittomainmenu.texture_hover = load(Mainpreload.buttonsthover)
		$inventoryveil/inventory/Settingscreen/Scrollcontainer/settings/qcontrol/quittomainmenu.texture_pressed = load(Mainpreload.buttonsthoverpress)
		$inventoryveil/inventory/Settingscreen/Scrollcontainer/settings/fcontrol/forfeit.texture_normal = load(Mainpreload.buttonst)
		$inventoryveil/inventory/Settingscreen/Scrollcontainer/settings/fcontrol/forfeit.texture_hover = load(Mainpreload.buttonsthover)
		$inventoryveil/inventory/Settingscreen/Scrollcontainer/settings/cen1/Mastervolume.texture_under = load(Mainpreload.barunderst)
		$inventoryveil/inventory/Settingscreen/Scrollcontainer/settings/cen1/Mastervolume.texture_over = load(Mainpreload.baroverst)
		$inventoryveil/inventory/Settingscreen/Scrollcontainer/settings/cen1/Mastervolume.texture_progress = load(Mainpreload.barst)
		$inventoryveil/inventory/Settingscreen/Scrollcontainer/settings/cen2/Musicvolume.texture_under = load(Mainpreload.barunderst)
		$inventoryveil/inventory/Settingscreen/Scrollcontainer/settings/cen2/Musicvolume.texture_over = load(Mainpreload.baroverst)
		$inventoryveil/inventory/Settingscreen/Scrollcontainer/settings/cen2/Musicvolume.texture_progress = load(Mainpreload.barst)
		$inventoryveil/inventory/Settingscreen/Scrollcontainer/settings/cen3/SFXvolume.texture_under = load(Mainpreload.barunderst)
		$inventoryveil/inventory/Settingscreen/Scrollcontainer/settings/cen3/SFXvolume.texture_over = load(Mainpreload.baroverst)
		$inventoryveil/inventory/Settingscreen/Scrollcontainer/settings/cen3/SFXvolume.texture_progress = load(Mainpreload.barst)
		$inventoryveil/inventory/Settingscreen/Scrollcontainer/settings/cen4/Ambiencevolume.texture_under = load(Mainpreload.barunderst)
		$inventoryveil/inventory/Settingscreen/Scrollcontainer/settings/cen4/Ambiencevolume.texture_over = load(Mainpreload.baroverst)
		$inventoryveil/inventory/Settingscreen/Scrollcontainer/settings/cen4/Ambiencevolume.texture_progress = load(Mainpreload.barst)
		$inventoryveil/inventory/Settingscreen.load_settings()
		$inventoryveil/inventory/closebutton.texture_normal = load(Mainpreload.closest)
		get_node("/root/Master/SFX/ui").play()
		mainscene.call("pause",true)
		if healthboxbuttonused == false:
			_on_Ability_pressed(true)
			$inventoryveil/inventory/TabInformation.set_text(Playervariables.tabinfodict["skillsinfo"])
		else:
			healthboxbuttonused = false
			$inventoryveil/inventory/TabInformation.set_text(Playervariables.tabinfodict["statusinfo"])
		inventory = true
		$inventoryveil.visible = true
		if mainscene.mapgenlocation != Playervariables.Tutorial1:
			if show_inventory_array.size() != Playervariables.playerinventory2darray.size():
				show_inventory_array = Playervariables.playerinventory2darray.duplicate(true)
				for iy in range(show_inventory_array.size()):
					for ix in range(show_inventory_array.size()-1,0+iy,-1):
						if (show_inventory_array[ix][0]) < (show_inventory_array[ix-1][0]): #if the statement is true, swap the values
							var temporaryStore = show_inventory_array[ix-1]
							show_inventory_array[ix-1] = show_inventory_array[ix]
							show_inventory_array[ix] = temporaryStore
			else:
				for item in range(show_inventory_array.size()):
					if show_inventory_array[item].size() > 3:
						show_inventory_array[item][3] = Playervariables.playerinventory2darray[show_inventory_array[item][1]][3]
			for n in show_inventory_array.size():
				deployables = load(Mainpreload.Deployables).instance()
				$inventoryveil/inventory/Abilityscreen/abilitygrid.add_child(deployables)
				if show_inventory_array[n][2] == Playervariables.INNATE and Playervariables.autodiscardarrayinnate.find(show_inventory_array[n][1]) != -1:
					deployables.call("_identify",show_inventory_array[n],true,true)
				elif Playervariables.autodiscardarrayitem.find(show_inventory_array[n][1]) != -1:
					deployables.call("_identify",show_inventory_array[n],true,true)
				else:
					deployables.call("_identify",show_inventory_array[n],true,false)
				deployables.add_to_group("displayslot")
			for n in Playervariables.playerconsumables2darray.size():
				deployables = load(Mainpreload.Deployables).instance()
				$inventoryveil/inventory/Abilityscreen/abilitygrid.add_child(deployables)
				deployables.call("_identify",Playervariables.playerconsumables2darray[n],true,false)
			for n in Playervariables.passivearray.size():
				var passiveshow = load(Mainpreload.Passiveshow).instance()
				$inventoryveil/inventory/Abilityscreen/abilitygrid.add_child(passiveshow)
				passiveshow.get_child(0).call("inventoryload",Playervariables.passivearray[n])
			$inventoryveil/inventory/Abilityscreen.rect_scale = Vector2(1,1)*deployablescale
			$inventoryveil/inventory/Abilityscreen.rect_size = $inventoryveil/inventory/Statusscreen.rect_size / clamp(deployablescale,0.1,99)
#			$inventoryveil/inventory/Abilityscreen.rect_scale = Vector2(1,1)*deployablescale#clamp(deployablescale,0.5,2)
#			for child in $inventoryveil/inventory/Abilityscreen/abilitygrid.get_children():
#				child.rect_scale = Vector2(1,1)*clamp(deployablescale,0.5,2)

func adjust_status_grid():
	if $inventoryveil/inventory/Statusscreen/newstatus.get_children().size() > 0:
		var totalsize = ($inventoryveil/inventory.rect_size * Vector2(0.9,0.85)) - Vector2($inventoryveil/inventory/Statusscreen.get_v_scrollbar().rect_min_size.x,20)
		var averagesize = Vector2(totalsize.x,totalsize.x*0.4)
		if totalsize.x < 500:
			$inventoryveil/inventory/Statusscreen/newstatus.columns = 1
			for child in $inventoryveil/inventory/Statusscreen/newstatus.get_children():
				child.rect_min_size = averagesize
		elif totalsize.x < 1000:
			$inventoryveil/inventory/Statusscreen/newstatus.columns = 2
			averagesize = averagesize*0.5
			for child in $inventoryveil/inventory/Statusscreen/newstatus.get_children():
				child.rect_min_size = averagesize
		else:
			averagesize = averagesize*0.33
			$inventoryveil/inventory/Statusscreen/newstatus.columns = 3
#			averagesize = Vector2((400+totalsize.x)/2,(400+totalsize.x)/5)
			for child in $inventoryveil/inventory/Statusscreen/newstatus.get_children():
				child.rect_min_size = averagesize
		if totalsize.x > 0:
			var fontcalc = averagesize.x/23
			if Playervariables.modarray[Playervariables.mods.TRANSLATION] == true and Playervariables.default_font_dict["font_debuff_description"] != null:
				fontcalc = fontcalc * Playervariables.default_font_dict["font_debuff_description_size"]
			if $inventoryveil/inventory/Statusscreen/newstatus.get_children().size() > 0:
				var usenode = $inventoryveil/inventory/Statusscreen/newstatus.get_child(0)
				usenode.get_node("Board/Info").get("custom_fonts/font").set_size(fontcalc)
				usenode.get_node("Board/Info").get("custom_fonts/font").set_outline_size(ceil(fontcalc*0.12))
				usenode.get_node("Board/TitleBar/Title").get("custom_fonts/font").set_size(fontcalc*1.2)
				usenode.get_node("Board/TitleBar/Title").get("custom_fonts/font").set_outline_size(ceil(fontcalc*0.14))
				usenode.get_node("shoptab/TextureButton/Label").get("custom_fonts/font").set_size(ceil(fontcalc*2.5))
				usenode.get_node("shoptab/TextureButton/Label").get("custom_fonts/font").set_outline_size(ceil(fontcalc*0.35))
				usenode.get_node("shoptab/Particles2D").position = averagesize*0.5
				usenode.get_node("shoptab/Particles2D").process_material.emission_box_extents = Vector3(averagesize.x*0.5,averagesize.y*0.6,1)
				usenode.get_node("center/disappear").scale = Vector2(averagesize.x*0.00166,averagesize.y*0.00415)

func _on_closebutton_pressed():
	get_node("/root/Master/SFX/uib").play()
	$inventoryveil.visible = false
	if $ConversationV1.spritetalking == false:
		mainscene.call("pause",false)
	else:
		$ConversationV1/lowerui.visible = true
		if $ConversationV1.waitingchoice == true:
			$ConversationV1/choice.visible = true
	inventory = false
	if mainscene.mapgenlocation == Playervariables.Springs:
		$deployablecontainer/healthbox/shards2.visible = true
	$ConversationV1.inventory = false
#	if mainscene.allowclimb == false: #removing ALLOWCLIMB mechanic
#		$deployablecontainer/lowerui2/climbtoggle/AnimationPlayer.playback_speed = 1 #removing ALLOWCLIMB mechanic
#		$deployablecontainer/lowerui2/climbtoggle/AnimationPlayer.play("showclimb") #removing ALLOWCLIMB mechanic
	$inventoryveil/inventory/Status.set_modulate(Color(1,1,1))
	$inventoryveil/inventory/Ability.set_modulate(Color(1,1,1))
	$inventoryveil/inventory/Settings.set_modulate(Color(1,1,1))
#	$inventoryveil/inventory/shards/shopbutton.set_modulate(Color(1,1,1))
	for child in $inventoryveil/inventory/Abilityscreen/abilitygrid.get_children():
		child.queue_free()
	if removed_corruption == true:
		removed_corruption = false
		mainscene.removed_corruption_effect(false)
#	if nowshopping == true:
#		$inventoryveil/inventory/shards/shopbutton.visible = false
#		emit_signal("done_shopping")

func _on_Status_pressed():
#	if mainscene.mapgenlocation == Playervariables.Springs:
#		handle_ui_indicator(self,indi.FEATURE,null,false)
	$inventoryveil/inventory/TabInformation.set_text(Playervariables.tabinfodict["statusinfo"])
	get_node("/root/Master/SFX/uif").play()
	$inventoryveil/inventory/Status.set_modulate(Color(0.3,0.8,0.8))
	$inventoryveil/inventory/Ability.set_modulate(Color(1,1,1))
	$inventoryveil/inventory/Settings.set_modulate(Color(1,1,1))
	$inventoryveil/inventory/Settings.show_behind_parent = true
	$inventoryveil/inventory/Status.show_behind_parent = false
	$inventoryveil/inventory/Ability.show_behind_parent = true
#	$inventoryveil/inventory/shards/shopbutton.set_modulate(Color(1,1,1))
	$inventoryveil/inventory/Statusscreen.visible = true
	$inventoryveil/inventory/Abilityscreen.visible = false
	$inventoryveil/inventory/Settingscreen.visible = false
	if (mainscene.mapgenlocation in [Playervariables.LevelSelect1,Playervariables.Village,Playervariables.Springs]) == true:
		activate_shards_shop(true)
#	$inventoryveil/inventory/Shopscreen.visible = false

func _on_Ability_pressed(callfrominventory = false):
	$inventoryveil/inventory/TabInformation.set_text(Playervariables.tabinfodict["skillsinfo"])
	if callfrominventory == false:
		get_node("/root/Master/SFX/uif").play()
	$inventoryveil/inventory/Ability.set_modulate(Color(0.3,0.8,0.8))
	$inventoryveil/inventory/Status.set_modulate(Color(1,1,1))
	$inventoryveil/inventory/Settings.set_modulate(Color(1,1,1))
	$inventoryveil/inventory/Settings.show_behind_parent = true
	$inventoryveil/inventory/Status.show_behind_parent = true
	$inventoryveil/inventory/Ability.show_behind_parent = false
#	$inventoryveil/inventory/shards/shopbutton.set_modulate(Color(1,1,1))
	$inventoryveil/inventory/Statusscreen.visible = false
	$inventoryveil/inventory/Abilityscreen.visible = true
	$inventoryveil/inventory/Settingscreen.visible = false
	activate_shards_shop(false)
#	$inventoryveil/inventory/Shopscreen.visible = false

func _on_Settings_pressed():
	$inventoryveil/inventory/TabInformation.set_text(Playervariables.tabinfodict["settingsinfo"])
	get_node("/root/Master/SFX/uif").play()
	$inventoryveil/inventory/Settings.set_modulate(Color(0.3,0.8,0.8))
	$inventoryveil/inventory/Status.set_modulate(Color(1,1,1))
	$inventoryveil/inventory/Ability.set_modulate(Color(1,1,1))
	$inventoryveil/inventory/Settings.show_behind_parent = false
	$inventoryveil/inventory/Status.show_behind_parent = true
	$inventoryveil/inventory/Ability.show_behind_parent = true
#	$inventoryveil/inventory/shards/shopbutton.set_modulate(Color(1,1,1))
	$inventoryveil/inventory/Statusscreen.visible = false
	$inventoryveil/inventory/Abilityscreen.visible = false
	$inventoryveil/inventory/Settingscreen.visible = true
	activate_shards_shop(false)
#	$inventoryveil/inventory/Shopscreen.visible = false
#
#func _on_shopbutton_pressed():
#	pass


#onready var labelheight = $upperui/events.get_children()[-1].rect_size.y+5
var labelheight = 45#$upperui/events.get_children()[-1].rect_size.y+5
func scrollupper(direction):
#	var labelheight = $upperui/events.get_children()[-1].rect_size.y+5
	var startpos = $upperui.rect_position.y
	if direction != 0:
#		if Playervariables.reducelowergui == true or get_viewport().size.y < 800:
#		$upperui.rect_position.y = clamp($upperui.rect_position.y + labelheight*direction,-$upperui.rect_size.y + (labelheight) -24,0)
		$upperui.rect_position.y = clamp($upperui.rect_position.y + labelheight*direction,-$upperui.rect_size.y + 24,0)
#		else:
#			$upperui.rect_position.y = clamp($upperui.rect_position.y + labelheight*direction,-$upperui.rect_size.y + (labelheight),0)
#	if $upperui.rect_position.y > 0:
#		$upperui.rect_position.y = 0
#	elif $upperui.rect_position.y < -$upperui.rect_size.y + (labelheight):
#		$upperui.rect_position.y = -$upperui.rect_size.y + (labelheight)
##	if $upperui.rect_position.y < -$upperui.rect_size.y + (labelheight*3): #kinda redundant now that I used CLAMP?
	Playervariables.upperuiposition = $upperui.rect_position.y
	return $upperui.rect_position.y != startpos
#	else:
#		Playervariables.upperuiposition = 0

var alternation = false
var last_event = ""
#var buffernum = 0
const MAXTEXTNUM = 60
func register_event(eventtext,var_array = [],noinstantrepeats = false,donotannounce = false,leftalign = false,recursive = false):
	if recursive == false and Playervariables.modarray[Playervariables.mods.TRANSLATION] == true:
		if Playervariables.transdict["event_messages"].has(eventtext):
			eventtext = Playervariables.transdict["event_messages"][eventtext]
	if var_array.size() > 0:
		for i in var_array.size():
			eventtext = eventtext.replace("VAR"+str(i+1),var_array[i])
	if recursive == false:
		if noinstantrepeats == true:
			if eventtext == last_event:
				return
		last_event = eventtext
#	if noinstantrepeats == true and $upperui/alerts.get_children().size() > 0:
#		var testtext = last_event#$upperui/alerts.get_child($upperui/alerts.get_children().size()-1).get_text()
#		if testtext.length() > 7:
#			if testtext.length() <= eventtext.length():
#				var eventtest = eventtext.substr(0,testtext.length()-3)
#				testtext = testtext.substr(0,testtext.length()-3)
#				if eventtest == testtext:
#					return
#		elif eventtext.length() <= 7:
#			if testtext == eventtext:
#				return
#		if eventtext.length() >= MAXTEXTNUM:
#			var eventtest = eventtext.substr(0,MAXTEXTNUM-10)
#			if testtext.length() >= MAXTEXTNUM-14:
#				testtext = testtext.substr(0,MAXTEXTNUM-10)
#				if testtext == eventtest:
#					return
#		elif testtext == eventtext:
#			return
	if $upperui/scrollindicator/blink.is_playing() == false:
		$upperui/scrollindicator/blink.play("blink")
	if donotannounce == false:
		var maxtext = ((get_viewport().size.x-30)/clamp($upperui/events.get_child(0).get("custom_fonts/font").get_size()*0.75,1,999))
#		if eventtext.length() > maxtext: #previously 32 with substr 29 if overflow
		var alerteventtext = eventtext
		while alerteventtext.length() > maxtext:
			queue.append(alerteventtext.substr(0,maxtext-3)+"...")
			alerteventtext = alerteventtext.substr(maxtext-3,-1)
		queue.append(alerteventtext)
#		else:
#			queue.append(eventtext)
		if $queue.is_stopped() == true:
			queueposition = 0
			$queue.start(0.05)
	var newtext = load(Mainpreload.Eventtext).instance()
	var looptext = ""
	if eventtext.length() > MAXTEXTNUM:
		looptext = "-" + eventtext.substr(MAXTEXTNUM,-1)
		eventtext = eventtext.substr(0,MAXTEXTNUM) + "-"
	var eventsnum = $upperui/events.get_children().size()
	if eventsnum > 20:
		$upperui/events.get_child(0).free()
		$upperui.rect_size.y -= labelheight
		$upperui.rect_position.y -= -labelheight
	if leftalign == true:
		newtext.align = 0
	$upperui/events.add_child(newtext)
	newtext.set_text(eventtext)
	if alternation == true:
		alternation = false
		newtext.set_modulate(Color(0.07,0,0))
	else:
		alternation = true
		newtext.set_modulate(Color(0,0.07,0))
#	var labelheight = newtext.rect_size.y+5
#	buffernum += 1
#	if buffernum > 0:#2:
#	if eventsnum * labelheight > 100:
	$upperui.rect_size.y += labelheight
	$upperui.rect_position.y += -labelheight
#	else:
#		$upperui/events.rect_position.y += -labelheight
#		var previousheight = $upperui/scrollup.get_animation("newtextscroll").track_get_key_value(0,1)
#		$upperui/scrollup.get_animation("newtextscroll").track_set_key_value(0,0,previousheight)
#		$upperui/scrollup.get_animation("newtextscroll").track_set_key_value(0,1,previousheight-Vector2(0,labelheight))
#		$upperui/events.margin_top += -labelheight
#		$upperui/events.margin_bottom += -labelheight
	$upperui.margin_right = 0
	newtext.get_child(0).play("newtext")
	$upperui/scrollup.play("newtextscroll")
	if looptext.length() > 0:
		register_event(looptext,[],false,true,leftalign,true)
	$upperui/events.margin_bottom = -20
	$upperui/events.margin_top = 20
var queue = []
var queueposition = 0
func _on_queue_timeout():
	if queueposition < queue.size():
		var pop = load(Mainpreload.Pop).instance()
		$upperui/alerts.add_child(pop)
		pop.set_text(queue[queueposition])
		pop.get("custom_fonts/font").set_size(labelfontsize)
#		if queue.size() - queueposition > 1:
#			$upperui/alerts/Label2.set_text("("+str((queue.size() - queueposition)-1)+" more messages)")
		queueposition += 1
		$queue.start(0.35/clamp(queue.size(),1,1000))
	else:
		queue = []
#		$upperui/alerts/Label2.set_text("")
func savemessages():
	var maxmessages = $upperui/events.get_children().size()
	var storage = PoolStringArray ([])
	for i in range(clamp(maxmessages,0,10)):
		var getmessage = $upperui/events.get_child(maxmessages-1-i).get_text()
		if getmessage.length() > 3:
			storage.append(getmessage)
	storage.invert()
	Playervariables.laststagemessages = storage

var warningsarray = [null,null,null]
enum{warningFOOD,warningMAP,warningCONSENT}
func tempwarning(newwarning):
	var warningused = -1
	for i in range(warningsarray.size()):
		if warningsarray[i] == null:
			warningused = i
			break
	if warningused == -1:
		warningsarray[0].get_node("AnimationPlayer").stop()
		warningsarray[0].queue_free()
		warningused = 0
	warningsarray[warningused] = load(Mainpreload.warning).instance()
	add_child(warningsarray[warningused])
	warningsarray[warningused].position = Vector2(70,get_viewport().size.y-(20+150*(warningused+1)))
	warningsarray[warningused].num = warningused
	match newwarning:
		warningFOOD:
			warningsarray[warningused].texture = load(Mainpreload.meatwarning)
		warningMAP:
			warningsarray[warningused].texture = load(Mainpreload.mapwarning)
		warningCONSENT:
			if Playervariables.consent == false:
				warningsarray[warningused].texture = load(Mainpreload.sfwwarning)
			elif Playervariables.consent == true:
				warningsarray[warningused].texture = load(Mainpreload.nsfwwarning)

#var Newdebufftile = preload("res://debufftilefixed.tscn")
#var Energyorb = preload("res://energyorbeffect.tscn")
#var debufftokenarray = []
func generatedebufftoken(startpoint,debuffarray,reverse=false,conversation=false):
	var totalincrement = 0
	var totalimpact = 0
	var randrotateoffset = randf()*6.28
	if typeof(debuffarray) != TYPE_ARRAY:
		if typeof(debuffarray) == TYPE_VECTOR2:
			debuffarray = [debuffarray]
		else:
			return
	for debuffvector in debuffarray:
		totalimpact += debuffvector.y
	for debuffvector in debuffarray:
		if debuffvector.y > 0 and (int(debuffvector.x) in [Playervariables.KNOCKBACK,Playervariables.MARKED]) != true:
			var canvazoom = get_viewport().get_canvas_transform().x.x
			var debuffscale = clamp($deployablecontainer/lowerui2/playerdebuffs.rect_scale.x,0.1,999)
			if conversation == true or reverse == true:
				debuffscale = 1
			var canvasposition = Vector2(get_viewport().get_canvas_transform().origin.x,get_viewport().get_canvas_transform().origin.y)
			var containerposition = Vector2($deployablecontainer/lowerui2/playerdebuffs.get_global_transform().origin)
#			var specposition = (containerposition - canvasposition) + Vector2(128*(debufftokenarray.size()-1),0)
			var specposition = (containerposition - canvasposition) + Vector2($deployablecontainer/lowerui2/playerdebuffs.rect_size.x-128,0)
			var finalposition = ((startpoint*canvazoom)-specposition)/debuffscale
			for _i in debuffvector.y:
				var energy = load(Mainpreload.Energyorb).instance()
				if debuffvector.x >= 0:
					energy.set_modulate(Playervariables.debuffcolorarray[debuffvector.x-1])
				else:
					energy.set_modulate(Playervariables.debuffcolornegativearray[abs(debuffvector.x)-1])
				var radialposition = Vector2(100,0).rotated(randrotateoffset+((6.28*totalincrement)/totalimpact))
				var newanim = energy.get_node("AnimationPlayer").get_animation("flyin").duplicate()
				energy.get_node("AnimationPlayer").add_animation("scfl", newanim)
				totalincrement += 1
				energy.get_node("AnimationPlayer").playback_speed = 0.6+(totalincrement*0.1)
				var debufftile = get_debuff_tile(debuffvector.x)#generate_debuff_tile(debuffvector.x)
				if debufftile != null:
					if reverse == false and conversation == false:
	#					var debufftile = get_debuff_tile(debuffvector.x)#generate_debuff_tile(debuffvector.x)
#						if debufftile != null:
						energy.get_node("AnimationPlayer").get_animation("scfl").track_set_key_value(0,1,finalposition+radialposition)
						energy.get_node("AnimationPlayer").get_animation("scfl").track_set_key_value(0,0,finalposition)
						debufftile.add_child(energy)
						energy.get_node("AnimationPlayer").play("scfl")
					else:
						var debuffloc = debufftile.get_index()#clamp(debufftokenarray.find(int(debuffvector.x)),0,99)
						var fixedoffset = Vector2((debuffloc*128)+64,64)
						energy.get_node("AnimationPlayer").get_animation("scfl").track_set_key_value(0,2,(fixedoffset+containerposition-canvasposition)/canvazoom)
						energy.get_node("AnimationPlayer").get_animation("scfl").track_set_key_value(0,1,startpoint+radialposition)
						energy.get_node("AnimationPlayer").get_animation("scfl").track_set_key_value(0,0,startpoint)
						mainscene.get_node("CanvasLayer3").add_child(energy)
						if reverse == true:
							energy.get_node("AnimationPlayer").play_backwards("scfl")
						else:
							energy.get_node("AnimationPlayer").play("scfl")

func generate_life_orb(startpoint,size):
#	var totalimpact = 0
#	totalimpact = int(ceil(size*2))
	if size > 0:
		var canvazoom = get_viewport().get_canvas_transform().x.x
		var canvasposition = Vector2(get_viewport().get_canvas_transform().origin.x,get_viewport().get_canvas_transform().origin.y)
		var containerposition = Vector2($deployablecontainer/healthbox.get_global_transform().origin)
		var specposition = (containerposition - canvasposition)
		var finalposition = (startpoint*canvazoom)-specposition
		var health = load(Mainpreload.Healthorb).instance()
		$deployablecontainer/healthbox.add_child(health)
		health.amount = int(size)
		var yratio = (get_viewport_rect().size.y/Playervariables.basescreensize.y)*0.7
#		health" =  Vector2(yratio,yratio)
		var newanim = health.get_node("AnimationPlayer").get_animation("flyin").duplicate()
		health.get_node("AnimationPlayer").add_animation("scfl", newanim)
		health.get_node("AnimationPlayer").get_animation("scfl").track_set_key_value(0,0,finalposition)
		health.get_node("AnimationPlayer").get_animation("scfl").track_set_key_value(0,1,Vector2($deployablecontainer/healthbox.rect_size.x*0.5,$deployablecontainer/healthbox.rect_size.y*0.25))
		health.get_node("AnimationPlayer").get_animation("scfl").track_set_key_value(2,0,Vector2(yratio,yratio))
		health.get_node("AnimationPlayer").get_animation("scfl").track_set_key_value(2,1,Vector2(yratio,yratio)*0.6)
		health.get_node("AnimationPlayer").get_animation("scfl").track_set_key_value(2,2,Vector2(yratio,yratio)*0.1)
		health.get_node("AnimationPlayer").play("scfl")
		if mainscene.mapgenlocation == Playervariables.Springs:
			$deployablecontainer/healthbox/AnimationPlayer.play("healedsprings")
			health.set_self_modulate(Color(0.7,1,1))
		else:
			$deployablecontainer/healthbox/AnimationPlayer.play("healed")

func get_debuff_tile(debuff):
	debuff = int(debuff)
	for debufftile in $deployablecontainer/lowerui2/playerdebuffs.get_children():
		if debufftile.debuffnum == debuff:
			return debufftile
	return null
#	var debuffloc = debufftokenarray.find(int(debuff))
#	if debuffloc > -1:
#		var debufftile = $deployablecontainer/lowerui2/playerdebuffs.get_child(debuffloc)
#		return debufftile
#	else:
#		return null

func generate_debuff_attack(extradebuffarray):
	if mainscene.debuffnextattack.size() > 0:
		var buff = int(100+mainscene.buffs.DEBUFFATTACK)
		var debufftile = get_debuff_tile(buff)
#		var debuffloc = debufftokenarray.find(buff)
#		var debufftile
		if debufftile == null:
			debufftile = load(Mainpreload.Newdebufftile).instance()
			debufftile.visible = false
			$deployablecontainer/lowerui2/playerdebuffs.add_child(debufftile)
			debufftile.debuffnum = buff
			debufftile.get_node("debuffnum").z_index = buff
			debufftile.set_texture(load("res://Assets/ui/debufficons/debuffattack.png"))
			debufftile.get_node("Label").set_text("")
			debufftile.get_node("Label2").set_text("")
			debufftile.get_node("showhide").play("show")
#			debufftokenarray.append(buff)
#			debuffloc = debufftokenarray.size()-1
		else:
			debufftile = showremove_debuff_token(true,debufftile)
		debufftile.get_node("buffattack").visible = true
		for debuffvector in extradebuffarray:
			var newdebuff = TextureRect.new()
			debufftile.get_node("buffattack").add_child(newdebuff)
			newdebuff.texture = load("res://Assets/ui/debufficons/"+str(debuffvector.x)+"d.png")
		return debufftile
	return null

func clear_debuff_attack():
	if mainscene.debuffnextattack.size() > 0:
#		for debufftile in $deployablecontainer/lowerui2/playerdebuffs.get_children():
#			if debufftile.debuffnum == 100 + mainscene.buffs.DEBUFFATTACK:
		var debufftile = get_debuff_tile(100 + mainscene.buffs.DEBUFFATTACK)
		if debufftile != null:
			showremove_debuff_token(false,debufftile)
		else:
			print("Never made a debufftile for debuffnextattack?")
#				break
#		var tile = get_debuff_tile(100+mainscene.buffs.DEBUFFATTACK)
##		var tile = debufftokenarray.find(100+mainscene.buffs.DEBUFFATTACK)
#		if tile != null:#> -1:
#			tile.queue_free()
#		var debufftile = get_debuff_tile(100+mainscene.buffs.DEBUFFATTACK)
#		if debufftile != null:
##		if debufftokenarray.find(100+mainscene.buffs.DEBUFFATTACK) > -1:
#			debufftokenarray.erase(100+mainscene.buffs.DEBUFFATTACK)
#		else:
#			print("ERROR finding debufftoken array's entry for DEBUFFATTACK. What? How?")
		mainscene.debuffnextattack = []
func generate_debuff_tile(debuff):
	debuff = int(debuff)
	if debuff == Playervariables.SWEATY and mainscene.playerswimming == false:
		return
	var debufftile = get_debuff_tile(debuff)
#	var debuffloc = debufftokenarray.find(debuff)
#	var debufftile
	if debufftile == null:#debuffloc == -1:
		debufftile = load(Mainpreload.Newdebufftile).instance()
		debufftile.visible = false
		$deployablecontainer/lowerui2/playerdebuffs.add_child(debufftile)
		debufftile.debuffnum = debuff
		debufftile.get_node("showhide").play("show")
		debufftile.get_node("debuffnum").z_index = debuff
		if debuff >= 100:
			var buffstring = mainscene.buffstringarray[debuff-100]
			debufftile.set_texture(load("res://Assets/ui/debufficons/"+buffstring+".png"))
			debufftile.get_node("Label").set("custom_colors/font_color",Color(0.85,1,0.7))
			debufftile.get_node("Label").set("custom_colors/font_outline_modulate",Color(0.4,1,0))
			debufftile.get_node("Label2").set("custom_colors/font_color",Color(0.85,1,0.7))
			debufftile.get_node("Label2").set("custom_colors/font_outline_modulate",Color(0.4,1,0))
#			debufftile.get_node("Label/waited").set_text("")
		else:#if debuff < 0:
#			if get_viewport().size.y < 830:
#				debufftile.set_texture(load("res://Assets/ui/debufficons/"+str(debuff)+"d.png"))
#			else:
			debufftile.set_texture(load("res://Assets/ui/debufficons/"+str(debuff)+"d.png"))
			if debuff >= 1:
				if Playervariables.debufftriggerarray[debuff-1] > 0:
					debufftile.get_node("fillborder").visible = true
					debufftile.get_node("fillborder").max_value = Playervariables.debufftriggerarray[debuff-1]
					
				else:
					if Playervariables.debuff_capped_array.find(debuff) > -1:
						debufftile.get_node("fillborder").visible = true
						debufftile.get_node("fillborder").max_value = 8
						debufftile.get_node("fillborder").texture_under = load(Mainpreload.big_debuff_border)
						debufftile.get_node("fillborder").texture_progress = load(Mainpreload.big_debuff_border_fill)
						debufftile.get_node("fillborder").fill_mode = 3
					else:
						debufftile.get_node("border").visible = true
			else:
				debufftile.get_node("border").visible = true
#			debufftile.get_node("Label/waited").set_text("")
#		else:
#			if get_viewport().size.y < 830:
#				debufftile.set_texture(load("res://Assets/ui/debufficons/"+str(debuff)+"d.png"))
#			else:
#			debufftile.set_texture(load("res://Assets/ui/debufficons/"+str(debuff)+".png"))
#		debufftokenarray.append(debuff)
#		debuffloc = debufftokenarray.size()-1
	else:
		showremove_debuff_token(true,debufftile)
	#	if debufftile == null:
	#		print("Serious error with generate_Debuff_tile, could not find tile")
	#		return
	var newvalue
	if debuff >= 100:
		var buffnum = mainscene.playerbuffarray.find(debuff-100)
		if buffnum > -1:
			newvalue = mainscene.playerbuffturnsarray[buffnum]
		else:
			newvalue = 0
	elif debuff >= 0:
		newvalue = clamp(mainscene.playerdebuffarray[debuff-1],0,99)
	else:
		newvalue = clamp(mainscene.playernegativedebuffarray[abs(debuff)-1],0,99)
	debufftile.get_node("fillborder").value = newvalue
	if debuff in [Playervariables.MARKED,Playervariables.SWEATY]:
		debufftile.get_node("Label").visible = false
		debufftile.get_node("Label2").visible = false
	else:
		if newvalue <= 1:
			debufftile.get_node("Label").set_text("")
			debufftile.get_node("Label2").set_text("")
		else:
			debufftile.get_node("Label").set_text(str(newvalue))
			debufftile.get_node("Label2").set_text(str(newvalue))
		if newvalue > 0:
			debufftile.get_node("Label").get("custom_fonts/font").outline_size = clamp(sqrt(newvalue),1,15)
		debufftile.get_node("Label").get("custom_fonts/font").size = 40+6*clamp(newvalue,0,6)
#			print(get_viewport().size.y)
	if get_viewport().size.y < 830:
		debufftile.get_node("AnimationPlayer").get_animation("count").track_set_key_value(1,0,0)
		debufftile.get_node("AnimationPlayer").get_animation("count").track_set_key_value(1,1,64)
	else:
		debufftile.get_node("AnimationPlayer").get_animation("count").track_set_key_value(1,0,-64)
		debufftile.get_node("AnimationPlayer").get_animation("count").track_set_key_value(1,1,0)
	debufftile.get_node("AnimationPlayer").play("count")
	return debufftile

func updatedebufftiles():
#	var sizetotal = $deployablecontainer/lowerui2/playerdebuffs.get_children().size()#debufftokenarray.size()
#	var mockdebuffarray = mainscene.playerdebuffarray.duplicate()
#	var mocknegativedebuffarray = mainscene.playernegativedebuffarray.duplicate() #not actually used right now
#	mainscene.incrementplayerdebuffs(true,mockdebuffarray,mocknegativedebuffarray,true)
	for debufftile in $deployablecontainer/lowerui2/playerdebuffs.get_children():
#	for arraynum in range(sizetotal):
#		var debufftilenum = sizetotal-(arraynum+1)
		var debuffid = debufftile.debuffnum#debufftokenarray[debufftilenum]
		if debuffid == Playervariables.SWEATY:
			if mainscene.playerswimming == false:
				showremove_debuff_token(false,debufftile)
		elif debuffid != Playervariables.MARKED:
			var newvalue
#			var debufftile = $deployablecontainer/lowerui2/playerdebuffs.get_child(debufftilenum)
			var oldvalue = debufftile.get_node("Label").get_text()
			if oldvalue == "":
				oldvalue = "1"
			if debuffid >= 100:
#				newvalue = clamp(mainscene.playerbuffarray[debuffid-1]-1,0,99)
				if debuffid == (100+mainscene.buffs.DEBUFFATTACK):
					newvalue = int(oldvalue)
					if mainscene.debuffnextattack.size() <= 0:
						newvalue = 0
				else:
					var buffnum = mainscene.playerbuffarray.find(debuffid-100)
					if buffnum > -1:
						newvalue = mainscene.playerbuffturnsarray[buffnum]
					else:
						newvalue = 0
#				debufftile.get_node("Label/waited").set_text("")#set_text(str(clamp(newvalue-1,0,99)))
			elif debuffid >= 0:
				newvalue = clamp(mainscene.playerdebuffarray[debuffid-1],0,99)
#				debufftile.get_node("Label/waited").set_text(str(clamp(mockdebuffarray[debuffid-1],0,99)))
#				debufftile.get_node("Label/waited").set_text(str(clamp(mainscene.playerdebuffarray[debuffid-1],0,99)))
			else:
				newvalue = clamp(mainscene.playernegativedebuffarray[abs(debuffid)-1],0,99)
	#			debufftile.get_node("Label/waited").set_text(str(clamp(mocknegativedebuffarray[abs(debuffid)-1],0,99)))
#				debufftile.get_node("Label/waited").set_text("")#newvalue-1,0,99)#.set_text("")
#			if get_viewport().size.y < 830:
#				debufftile.get_node("Label").margin_bottom = 64
#				debufftile.get_node("countrev").get_animation("countrev").track_set_key_value(3,0,64)
#				debufftile.get_node("countrev").get_animation("countrev").track_set_key_value(3,1,0)
#			else:
#				debufftile.get_node("Label").margin_bottom = 0
#				debufftile.get_node("countrev").get_animation("countrev").track_set_key_value(3,0,0)
#				debufftile.get_node("countrev").get_animation("countrev").track_set_key_value(3,1,-64)
			if int(oldvalue) != int(newvalue):
				debufftile.get_node("fillborder").value = newvalue
				if debufftile.get_node("Label").get_text() == "" and newvalue <= 1:
					pass
				else:
					debufftile.get_node("Label2").set_text(oldvalue)
					debufftile.get_node("countrev").stop()
					debufftile.get_node("countrev").play("countrev")
					debufftile.get_node("Label").set_text(str(newvalue))
					debufftile.get_node("Label").get("custom_fonts/font").size = 40+6*clamp(newvalue,0,6)
					if newvalue > 0:
						debufftile.get_node("Label").get("custom_fonts/font").outline_size = clamp(sqrt(newvalue),1,15)
#				debufftile.get_node("Label").get("custom_fonts/font").outline_size = clamp(newvalue,0,15)
				if newvalue <= 0:
					debufftile.get_node("Label2").set_text("")
					debufftile.get_node("Label").set_text("")
					showremove_debuff_token(false,debufftile)
	#				print(debufftokenarray)
func activatedebuffaffectlight(debuffID):#,onoff):
#	var debufftilenum = debufftokenarray.find(debuffID)
	var debufftile = get_debuff_tile(debuffID)
	if debufftile != null:#num != -1:
#		$deployablecontainer/lowerui2/playerdebuffs.get_child(debufftilenum).get_node("affectlight").visible = onoff
		debufftile.get_node("affectlight").visible = true#onoff
	else:
		print("tried to activate this debuff's light??: "+str(debuffID))
func pitch_debuff_tile(onoff):
	var debufftile = get_debuff_tile(Playervariables.CUM)
	if onoff == true:
		debufftile.set_self_modulate(Color(0.55, 0.70, 0.90, 0.82))
	else:
		debufftile.set_self_modulate(Color(1.0, 1.0, 1.0, 1.0))

const SHOWHIDE_ANIM_LENGTH = 0.6#note if showhide's animation changes in length, list it here
func showremove_debuff_token(showiftrue,debufftile):
	if debufftile == null:
#	if $deployablecontainer/lowerui2/playerdebuffs.get_children().size() <= debufftilenum:
		print("Serious error with showremove debuff token, trying to handle a non-existent tile.")
		return null
	else:
#		var debufftile = $deployablecontainer/lowerui2/playerdebuffs.get_child(debufftilenum)
		if showiftrue == false:
#			debufftile.get_node("showhide").playback_speed = 0.5
			if debufftile.visible == false:
				debufftile.queue_free()
#				debufftokenarray.remove(debufftilenum)
			elif debufftile.get_node("showhide").is_playing() == true:
				if debufftile.get_node("showhide").get_current_animation() == "hide":
					pass
				else:
					var timeadvance = SHOWHIDE_ANIM_LENGTH - debufftile.get_node("showhide").current_animation_position
					debufftile.get_node("showhide").play("hide")
					debufftile.get_node("showhide").advance(timeadvance)
			else:
				debufftile.get_node("showhide").play("hide")
		else:
#			debufftile.get_node("showhide").playback_speed = 1
#			debufftile.raise()
#			debufftokenarray.append(debuff)
#			debufftokenarray.remove(debufftilenum)
			if debufftile.visible == false:
				debufftile.get_node("showhide").play("show")
			elif debufftile.get_node("showhide").is_playing() == true:
				if debufftile.get_node("showhide").get_current_animation() == "hide":
					var timeadvance = SHOWHIDE_ANIM_LENGTH - debufftile.get_node("showhide").current_animation_position
					debufftile.get_node("showhide").play("show")
					debufftile.get_node("showhide").advance(timeadvance)
		return debufftile

func _on_climbtoggle_pressed():
	pass
#	mainscene.allowclimb = !(mainscene.allowclimb) #removing ALLOWCLIMB mechanic
#	if mainscene.allowclimb == true: #removing ALLOWCLIMB mechanic
#		$deployablecontainer/lowerui2/climbtoggle.texture_normal = load("res://Assets/ui/climbtoggle.png") #removing ALLOWCLIMB mechanic
#		$deployablecontainer/lowerui2/climbtoggle/AnimationPlayer.play_backwards("showclimb") #removing ALLOWCLIMB mechanic
#	else: #removing ALLOWCLIMB mechanic
#		$deployablecontainer/lowerui2/climbtoggle.texture_normal = load("res://Assets/ui/climbtoggleoff.png") #removing ALLOWCLIMB mechanic

#var hovered = false
#func climbbutton_hovered():
#	print("hovered")
#	hovered = true
#func climbbutton_not_hovered():
#	print("not hovered")
#	hovered = false


func _on_skiplevelbutton_pressed():
	if $inventoryveil/inventory/skiplevelbutton/doubleclicksk.is_stopped():
		$inventoryveil/inventory/skiplevelbutton/Label3.visible = true
		$inventoryveil/inventory/skiplevelbutton.set_self_modulate(Color(0.82,0.82,0.82))
		$inventoryveil/inventory/skiplevelbutton/doubleclicksk.start(1)
	elif Playervariables.playerresistance > 20 or Playervariables.debugmodeon == true:
		if Playervariables.debugmodeon == true and Playervariables.playerresistance <= 20:
			register_event("Using debugmode to skip at <= 20 endurance.")
#		Playervariables.corruptplayer(1,20)
#		mainscene.levelcleared = true
		_on_closebutton_pressed()
#		mainscene._end_turn()
		mainscene.forceskip(true)
	else:
		register_event("Cannot skip level, endurance too low.")
		$inventoryveil/inventory/skiplevelbutton/Label3.visible = false
		$inventoryveil/inventory/skiplevelbutton.set_self_modulate(Color(1,0.82,0.82))
		$inventoryveil/inventory/skiplevelbutton/doubleclicksk.stop()

func _on_doubleclicksk_timeout():
	$inventoryveil/inventory/skiplevelbutton/Label3.visible = false
	$inventoryveil/inventory/skiplevelbutton.set_self_modulate(Color(1,0.82,0.82))

func bubble_font_calc():
#	textboxfontcalc = sqrt(clamp(((get_viewport_rect().size.x/3)-0)*((get_viewport_rect().size.y/4)-0),50,1000000))/(12)
#	textboxfontcalc = pow(clamp(((get_viewport_rect().size.x/3)-0)*((get_viewport_rect().size.y/4)-0),50,1000000),0.315)#/(1)
	textboxfontcalc = pow(clamp(((get_viewport_rect().size.x/3)+90)*((get_viewport_rect().size.y/3)+90),50,1000000),0.29)#/(1)
#	print(((get_viewport_rect().size.x/3)-0)*((get_viewport_rect().size.y/4)-0))
#	print(textboxfontcalc)
#	if textboxfontcalc > 28:
#	textboxfontcalc = 20 + (textboxfontcalc / 2)
	if Playervariables.modarray[Playervariables.mods.TRANSLATION] == true and Playervariables.default_font_dict["font_speech_default"] != null:
		textboxfontcalc = Playervariables.default_font_dict["font_speech_default_size"]*(13.3 + 2*(textboxfontcalc / 3))

var ready_player_two = true
var bubblequeue = 0
#var interruptconverse = false
var lastbeat = null
var lastspeechdict = null
var laststartpoint = "999"
var speechdictsavepoint = "999"
var savedconvowait = 0
var includerules = false
const conversationwaittime = 1.05
var convoclosekeepsrules = true
var textboxfontcalc = 20
func bubbleconverse(speechdict,ID,startpoint):
#	var timecheck = OS.get_ticks_msec()
	if speechdict != null and speechdict.size() > 0:
		bubblequeue = (bubblequeue + 1) % 65500
		var bubblequeueposition = bubblequeue
		pauseconvov2 = false
#		if $ConversationV1.spritetalker_array[1] == null:
#			print("Could not find spritetalker2?? Returning.")
#			return
		$conversationv2.visible = true
		set_process(true)
		$conversationv2/controls/chatcycle.visible = false
		$conversationv2/controls/chatcycle/cycle.stop()
#		var delayonce = false
		if mainscene.preview_state == 0 and "monsternum" in ID:
			if ID.has_method("hideunhidequip"):
				ID.hideunhidequip(false,ID.monsternum,false,startpoint) #CALL GROUP IS DEFERRED. So you have to do this.
		elif ID == mainscene:
			ID.hideunhidequip(false)
#			get_tree().call_group("Quippers","hideunhidequip",false,ID.monsternum,false,startpoint)
		$conversationv2/scrollconvo.stop()
		ready_player_two = true
		if speechdict.get("scenario").get("char2") == "None":
			ready_player_two = false
			convoclosekeepsrules = true
		elif Playervariables.keepplayerscrolled == true:
			convoclosekeepsrules = true
		if speechdict.get("scenario").get("char1") == "Rules":
			includerules = true
			if Playervariables.keepplayerscrolled == false:
				convoclosekeepsrules = false
				Playervariables.keepplayerscrolled = true
				$ConversationV1.playerscroll(-1)
				mainscene.reticule(null,true,true)
		else:
			includerules = false
		bubble_font_calc()
		if speechdict.get("scenario").has("char3"):
			match speechdict.get("scenario")["char3"]:
				"VillagerOnee":
					for NPC in mainscene.staticNPCarray:
						if "monsternum" in NPC and NPC.monsternum == -5:
							$ConversationV1.enemyscroll(NPC,1,true,3)
							break
		if lastspeechdict == null or (laststartpoint != startpoint or lastspeechdict.hash() != speechdict.hash()):
			for bubble in $conversationv2/VBoxContainer.get_children():
				bubble.queue_free()
			speechdictsavepoint = startpoint#"000"
#			$conversationv2/scrollconvo.stop()
			$conversationv2/VBoxContainer.margin_top = 0
			currentfocusposition = 0
			convov2focuspositionmax = 0
			savedconvowait = 0
			convopositionsarray = [0]
		else:
#			delayonce = true
			if $conversationv2/VBoxContainer.get_children().size() > 0:
				var lastbubble = $conversationv2/VBoxContainer.get_children()[-1]
				if lastbubble.get_node("NinePatchRect/RichTextLabel").visible_characters > -1:
					lastbubble.queue_free()
					convov2focuspositionmax -= 1
					convopositionsarray.remove(convopositionsarray.size()-1)
#					if currentfocusposition > convov2focuspositionmax:
#						adjust_convo_position(convov2focuspositionmax,2,true)
			$conversationv2/VBoxContainer.margin_top = 0
			var extendi = 0
			for bubble in $conversationv2/VBoxContainer.get_children():
				bubble.get_node("NinePatchRect/RichTextLabel").get("custom_fonts/normal_font").size = textboxfontcalc
				bubble.get_node("NinePatchRect/RichTextLabel").get("custom_fonts/normal_font").outline_size = clamp(textboxfontcalc/16,2,16)
				var newdimensions = fix_text_bubble_font(bubble.get_node("NinePatchRect/RichTextLabel"),textboxfontcalc)
				if bubble.get_node_or_null("TextureRect") != null:
					bubble.get_node("TextureRect").rect_min_size.x = get_viewport().size.x/3
				if extendi < currentfocusposition:
					$conversationv2/VBoxContainer.margin_top -= newdimensions.y
				extendi += 1
#			for bubble in $conversationv2/VBoxContainer.get_children():
#				var newdimensions = fix_text_bubble_font(bubble.get_node("NinePatchRect/RichTextLabel"),bubble.get_node("NinePatchRect/RichTextLabel").get("custom_fonts/normal_font").get_size())
#				bubble.get_node("TextureRect").rect_min_size.x = get_viewport().size.x/3
			if speechdict.has(speechdictsavepoint) == false and lastbeat != null:
				var currentbeat = lastbeat
#				if includerules == true:
#					$ConversationV1.rulestalker.emotioncall(currentbeat.char1emotion)
#				$ConversationV1.spritetalker2.emotioncall(currentbeat.char2emotion)
				if speechdict.get("scenario").has("char3") and $ConversationV1.spritetalker_array[2] != null:
					if currentbeat.has("char3emotion"):
						$ConversationV1.spritetalker_array[2].emotioncall(currentbeat.char3emotion)
					if currentbeat.has("char3effect"):
						$ConversationV1.spritetalker_array[2].get_node("positionnode/effects").effectcall(currentbeat.char3effect,currentbeat.charswitch == 3)
					else:
						$ConversationV1.spritetalker_array[2].get_node("positionnode/effects").effectcall("neutral",currentbeat.charswitch == 3)
				if includerules == true:
					$ConversationV1.rulestalker.emotioncall(currentbeat.char1emotion)
					$ConversationV1.rulestalker.get_node("positionnode/effects").effectcall(currentbeat.char1effect,currentbeat.charswitch == 1)
				if ready_player_two == true and $conversationv2.visible == true and $ConversationV1.spritetalker_array[1] != null:
					$ConversationV1.spritetalker_array[1].emotioncall(currentbeat.char2emotion)
					$ConversationV1.spritetalker_array[1].get_node("positionnode/effects").effectcall(currentbeat.char2effect,currentbeat.charswitch == 2)
#				match int(currentbeat.charswitch):
#					2:
#						if includerules == true:
#							$ConversationV1.rulestalker.get_node("positionnode/effects").effectcall(currentbeat.char1effect,false)
#						$ConversationV1.spritetalker2.get_node("positionnode/effects").effectcall(currentbeat.char2effect,true)
#					1:
#						if includerules == true:
#							$ConversationV1.rulestalker.get_node("positionnode/effects").effectcall(currentbeat.char1effect,true)
#						$ConversationV1.spritetalker2.get_node("positionnode/effects").effectcall(currentbeat.char2effect,false)
#					0:
#						if includerules == true:
#							$ConversationV1.rulestalker.get_node("positionnode/effects").effectcall(currentbeat.char1effect,false)
#						$ConversationV1.spritetalker2.get_node("positionnode/effects").effectcall(currentbeat.char2effect,false)
				#var newdimensions = fix_text_bubble_font(bubble.get_node("NinePatchRect/RichTextLabel"),bubble.get_node("NinePatchRect/RichTextLabel").get("custom_fonts/normal_font").get_size())
#				bubble.get_node("NinePatchRect").rect_min_size = newdimensions
		lastspeechdict = speechdict
		laststartpoint = startpoint
		if speechdict.get("scenario").get("char1") == "Rules":
			$ConversationV1.playerscroll(-1)
#		$conversationv2/VBoxContainer/ProgressBar.visible = true
		var speechbubbleref = null
		var newdimensions = Vector2(0,0)
		var nextnum = speechdictsavepoint
		var charswitch = 0
		$ConversationV1.handle_dialoguezoom(true)
#		if delayonce == true and $conversationv2/VBoxContainer.get_children().size() > 0:
#			$conversationv2/waittime.start(conversationwaittime/2)
#			yield($conversationv2/waittime,"timeout")
		if speechdict.has(nextnum):
			if savedconvowait > 0:
				$conversationv2/waittime.start(savedconvowait)
				yield($conversationv2/waittime,"timeout")
			while bubblequeue == bubblequeueposition:# and speechdict.has(nextnum):
#				timecheck = OS.get_ticks_msec()
				var unpausecount = 0
				while pauseconvov2 == true:
					yield(get_tree(),"idle_frame")
					if bubblequeue != bubblequeueposition:
						break
					if $conversationv2/scrollconvo.is_playing() == false and currentfocusposition == convov2focuspositionmax and mainscene.rightclickheld == false:
						unpausecount += 1
						if unpausecount > 5:
							savedconvowait = clamp(savedconvowait-0.02,0,999)
							if savedconvowait == 0:
								pauseconvov2 = false
					else: 
						unpausecount = 0
				if bubblequeue != bubblequeueposition:
					break
				savedconvowait = 0
#				$conversationv2/scrollconvo.playback_speed = 1
				convov2focuspositionmax += 1
#				currentfocusposition = convov2focuspositionmax
				#start find num with altconditions
				var currentbeat = speechdict.get(nextnum)
				var currentnum = nextnum
				var plannednextnum = $ConversationV1.checkaltconditions(Playervariables.corruptiondict,speechdict.get(currentnum),currentnum)#changestringarray,currentbeat,currentnum)
				if speechdict.has(plannednextnum):
					currentnum = plannednextnum
				else:
					print("Error, BUBBLECONVERSE altconditions returned invalid nextnum: "+plannednextnum)
				currentbeat = speechdict.get(currentnum)
				if currentnum != nextnum: #checks alt conditions on the alt conditioned case once!
					plannednextnum = $ConversationV1.checkaltconditions(Playervariables.corruptiondict,speechdict.get(currentnum),currentnum)
					if speechdict.has(plannednextnum):
						currentnum = plannednextnum
						currentbeat = speechdict.get(currentnum)
					else:
						print("Error, BUBBLECONVERSE altconditions returned invalid nextnum: "+str(plannednextnum))
				#end find num with altconditions
				var newspeechbubble = load(Mainpreload.Speechbubble).instance()
#				if $conversationv2/VBoxContainer.get_children().size() > 0:
#					$conversationv2/VBoxContainer.get_children()[-1].get_node("unfocus").play("unfocus") #prevents infinite self-loops
				$conversationv2/VBoxContainer.add_child(newspeechbubble)
	#			newspeechbubble.get_node("NinePatchRect/RichTextLabel").visible_characters = 0
				charswitch = int(currentbeat.charswitch)
				newspeechbubble.charswitch = charswitch
#				if includerules == true:
#					$ConversationV1.rulestalker.emotioncall(currentbeat.char1emotion)
#				$ConversationV1.spritetalker2.emotioncall(currentbeat.char2emotion)
				lastbeat = currentbeat.duplicate()
				if $ConversationV1.spritetalker_array[2] != null:
					if currentbeat.has("char3emotion"):
						$ConversationV1.spritetalker_array[2].emotioncall(currentbeat.char3emotion)
					if currentbeat.has("char3effect"):
						$ConversationV1.spritetalker_array[2].get_node("positionnode/effects").effectcall(currentbeat.char3effect,currentbeat.charswitch == 3)
					else:
						$ConversationV1.spritetalker_array[2].get_node("positionnode/effects").effectcall("neutral",currentbeat.charswitch == 3)
				if includerules == true:
					$ConversationV1.rulestalker.emotioncall(currentbeat.char1emotion)
					$ConversationV1.rulestalker.get_node("positionnode/effects").effectcall(currentbeat.char1effect,currentbeat.charswitch == 1)
				if ready_player_two == true and $conversationv2.visible == true and $ConversationV1.spritetalker_array[1] != null:
					$ConversationV1.spritetalker_array[1].emotioncall(currentbeat.char2emotion)
					$ConversationV1.spritetalker_array[1].get_node("positionnode/effects").effectcall(currentbeat.char2effect,currentbeat.charswitch == 2)
				match charswitch:
					3:
						newspeechbubble.get_node("NinePatchRect").set_texture(load(Mainpreload.get("Speech"+str((speechdict.get("scenario")).get("char3")))))
						newspeechbubble.get_node("NinePatchRect").raise()
						newspeechbubble.get_node("NinePatchRect/symbol").set_texture(load(Mainpreload.get("Symbol"+str((speechdict.get("scenario")).get("char3")))))
						newspeechbubble.get_node("NinePatchRect/symbol").anchor_left = 1
						newspeechbubble.get_node("NinePatchRect/symbol").margin_right = 0
						var scale = 0.5+deployablescale
						newspeechbubble.get_node("NinePatchRect/symbol").rect_scale = Vector2(scale,scale)
						newspeechbubble.get_node("NinePatchRect/symbol").margin_left = -126 * (scale)
						newspeechbubble.get_node("NinePatchRect/symbol").margin_top = -122*(scale)
						newspeechbubble.get_node("TextureRect").queue_free()
					2:
#						if includerules == true:
#							$ConversationV1.rulestalker.get_node("positionnode/effects").effectcall(currentbeat.char1effect,false)
#						$ConversationV1.spritetalker2.get_node("positionnode/effects").effectcall(currentbeat.char2effect,true)
						newspeechbubble.get_node("NinePatchRect").set_texture(load(Mainpreload.get("Speech"+str((speechdict.get("scenario")).get("char2")))))
#						newspeechbubble.get_node("NinePatchRect").set_texture(load("res://Conversations/speechbubblerect"+str((speechdict.get("scenario")).get("char2"))+".png"))
						newspeechbubble.get_node("NinePatchRect").raise()
						newspeechbubble.get_node("NinePatchRect/symbol").set_texture(load(Mainpreload.get("Symbol"+str((speechdict.get("scenario")).get("char2")))))
#						newspeechbubble.get_node("NinePatchRect/symbol").set_texture(load("res://Conversations/symbol"+str((speechdict.get("scenario")).get("char2"))+".png"))
#						newspeechbubble.get_node("NinePatchRect/symbol").grow_horizontal = 0
						newspeechbubble.get_node("NinePatchRect/symbol").anchor_left = 1
						newspeechbubble.get_node("NinePatchRect/symbol").margin_right = 0
						var scale = 0.5+deployablescale
						newspeechbubble.get_node("NinePatchRect/symbol").rect_scale = Vector2(scale,scale)
#						newspeechbubble.get_node("NinePatchRect/symbol").anchor_right = 1
						newspeechbubble.get_node("NinePatchRect/symbol").margin_left = -126 * (scale)
						newspeechbubble.get_node("NinePatchRect/symbol").margin_top = -122*(scale)
#						newspeechbubble.get_node("NinePatchRect/symbol").margin_left = -(newspeechbubble.get_node("NinePatchRect/symbol").rect_size.x)
#						newspeechbubble.get_node("NinePatchRect/symbol").margin_bottom += (newspeechbubble.get_node("NinePatchRect/symbol").rect_size.y)/2
#						newspeechbubble.get_node("NinePatchRect/symbol").margin_right = 0
					1:
#						if includerules == true:
#							$ConversationV1.rulestalker.get_node("positionnode/effects").effectcall(currentbeat.char1effect,true)
#						$ConversationV1.spritetalker2.get_node("positionnode/effects").effectcall(currentbeat.char2effect,false)
						newspeechbubble.get_node("NinePatchRect").set_texture(load("res://Conversations/speechbubblerect"+str((speechdict.get("scenario")).get("char1"))+".png"))
						newspeechbubble.get_node("NinePatchRect/symbol").set_texture(load("res://Conversations/symbol"+str((speechdict.get("scenario")).get("char1"))+".png"))
						var scale = 0.5+deployablescale
						newspeechbubble.get_node("NinePatchRect/symbol").rect_scale = Vector2(scale,scale)
#						newspeechbubble.get_node("NinePatchRect/symbol").anchor_right = 1
						newspeechbubble.get_node("NinePatchRect/symbol").margin_left = -126 * (scale)*0.2
						newspeechbubble.get_node("NinePatchRect/symbol").margin_right = 126 * (scale)*0.8
						newspeechbubble.get_node("NinePatchRect/symbol").margin_top = -122*(scale)*0.8
						newspeechbubble.get_node("NinePatchRect/symbol").margin_bottom = 122*(scale)*0.2
					0:
						newspeechbubble.get_node("NinePatchRect").set_texture(load("res://Conversations/speechbubblerect.png"))
#						if includerules == true:
#							$ConversationV1.rulestalker.get_node("positionnode/effects").effectcall(currentbeat.char1effect,false)
#						$ConversationV1.spritetalker2.get_node("positionnode/effects").effectcall(currentbeat.char2effect,false)
						newspeechbubble.get_node("TextureRect").queue_free()
				newspeechbubble.get_node("NinePatchRect/RichTextLabel").bbcode_text = ""
				var speechstring = ""
				var usespeech = currentbeat.get("speech")
				if Playervariables.modarray[Playervariables.mods.TRANSLATION] == true:
					if Playervariables.spectransdict.has(currentnum):
						usespeech = Playervariables.spectransdict[currentnum]
				if Playervariables.curseditemdict[Playervariables.ROPEGAG] == true and charswitch == 1:
					speechstring = $ConversationV1.gag_text(usespeech)
				else:
					for speechtext in usespeech:
						if speechtext is String:
							speechstring = speechstring + speechtext
						else:
							speechstring = speechstring + $ConversationV1.speech_name_grab(int(speechtext))
	#						match int(speechtext):
	#							0:
	#								speechstring = speechstring + Playervariables.tempname
				newspeechbubble.get_node("NinePatchRect/RichTextLabel").bbcode_text = "[center]"+speechstring+"[/center]"
				newspeechbubble.get_node("NinePatchRect/RichTextLabel").set_text(speechstring)
				newspeechbubble.get_node("NinePatchRect/RichTextLabel").get("custom_fonts/normal_font").size = textboxfontcalc
				newspeechbubble.get_node("NinePatchRect/RichTextLabel").get("custom_fonts/normal_font").outline_size = clamp(textboxfontcalc/16,2,24)
				newdimensions = fix_text_bubble_font(newspeechbubble.get_node("NinePatchRect/RichTextLabel"),textboxfontcalc)#newspeechbubble.get_node("NinePatchRect/RichTextLabel").get("custom_fonts/normal_font").get_size())
				if charswitch in [1,2]:
					newspeechbubble.get_node("TextureRect").rect_min_size.x = get_viewport().size.x/3
				convopositionsarray.append(convopositionsarray[-1]-newdimensions.y)
#				$conversationv2/scrollconvo.get_animation("scrollconvo").track_set_key_value(0,0,convopositionsarray[-2])
				adjust_convo_position(convov2focuspositionmax)
#				$conversationv2/scrollconvo.get_animation("scrollconvo").track_set_key_value(0,0,$conversationv2/VBoxContainer.margin_top)
#				$conversationv2/scrollconvo.get_animation("scrollconvo").track_set_key_value(0,1,$conversationv2/VBoxContainer.margin_top-newdimensions.y)
#				$conversationv2/scrollconvo.get_animation("scrollconvo").track_set_key_value(0,1,convopositionsarray[-1])
#				$conversationv2/scrollconvo.play("scrollconvo")
#				var newdimensions = fix_text_bubble_font(speechstring,newspeechbubble.get_node("NinePatchRect/RichTextLabel").get("custom_fonts/normal_font").get_size())
#				newspeechbubble.get_node("NinePatchRect").rect_min_size = newdimensions
#				newspeechbubble.get_node("AnimationPlayer").get_animation("textshow").track_set_key_value(0,1,speechstring.length())
#				newspeechbubble.get_node("AnimationPlayer").play("textshow")
#				newspeechbubble.get_node("NinePatchRect/RichTextLabel").bbcode_text = "[center]"+speechstring+"[/center]"
#				newspeechbubble.get_node("NinePatchRect/RichTextLabel").set_text(speechstring)
				if charswitch == 2:
					newspeechbubble.get_node("NinePatchRect/RichTextLabel").set("custom_colors/default_color",Color(0.95,0.85,0.9)) #Color(0.85,0.66,0.75)
					get_node("/root/Master/SFX/ramble").pitch_scale = 1.2
				elif charswitch == 1:
					newspeechbubble.get_node("NinePatchRect/RichTextLabel").set("custom_colors/default_color",Color(0.75,0.66,0.85))
					get_node("/root/Master/SFX/ramble").pitch_scale = 0.9
				else:
					newspeechbubble.get_node("NinePatchRect/RichTextLabel").set("custom_colors/default_color",Color(0.9,0.9,0.9))
					#yield for some amount of time on a timer
				speechdictsavepoint = nextnum
				if currentbeat.get("optionsjump").size() > 0:
					if float(currentbeat.get("optionsjump")[0]) != float(nextnum): #prevents infinite self-loops
						nextnum = float(currentbeat.get("optionsjump")[0])
				elif int(nextnum) < 10:
					nextnum = "00"+str(int(nextnum)+1)
				elif int(nextnum) < 100:
					nextnum = "0"+str(int(nextnum)+1)
				else:
					nextnum = str(int(nextnum)+1)
				if Playervariables.debugmodeon == true and speechdict.has(nextnum) == false and int(nextnum) >= 0:
					var testnum = str(int(nextnum)+1)
					var i = 1
					while i < 12:
						i += 1
						if speechdict.has(testnum):
							print("SLIGHT ERROR: QUIP VERSION Speechdict has a missing text bridge, so I bridged it over.")
							nextnum = testnum
							break
						else:
							testnum = str(int(nextnum)+i)
				if currentbeat.get("optionsconsequences").size() > 0 and $conversationv2.visible == true and bubblequeue == bubblequeueposition:
					match currentbeat.get("optionsconsequences")[0]:
						"enableweapon":
							if Playervariables.get_corruption("body") != Playervariables.raceIMP:
								mainscene.basedamage = 1
							mainscene.delete_indicator(Vector2(0,0),mainscene.indi.RIGHTCLICK)
							if mainscene.get_node("rulessprite/frames/weapon").visible == false:
								mainscene.get_node("sfx/collect").play()
								register_event("ATTACK now deals 1 damage instead of 0.")
							mainscene.get_node("rulessprite/frames/weapon").visible = true
							mainscene.get_node("rulessprite/frames/weaponback").visible = true
							if Playervariables.touchscreenmode == true:
								if Playervariables.touchscreenalt == true:
									get_node("/root/Master").call("touch_camera_alert",false)
								else:
									get_node("/root/Master").call("touch_camera_alert",true)
						"startquest":
							Playervariables.queststart = true
							get_node("/root/Master").call("update_quest",0,false)
							get_node("/root/Master").call("update_quest",1,true)
#						"condition2":
#							if Playervariables.questcondition2 < 1:
#								Playervariables.questcondition2 = 1
						"condition0":
							if Playervariables.questcondition2 > 0:
								mainscene.refreshstage = true
								Playervariables.questcondition = 0
								mainscene.get_node("triggerlevelfinish").start(2)
#								mainscene.nextsceneoverride = true
#								mainscene.levelcleared = true
								Playervariables.nextlocation = Playervariables.Shrine
								get_node("/root/Master").call("update_quest",0,false)
								get_node("/root/Master").call("update_quest",1,false)
								get_node("/root/Master").call("update_quest",2,true)
						"condition1":
							mainscene.get_node("triggerlevelfinish").start(2)
#							mainscene.nextsceneoverride = true
#							mainscene.levelcleared = true
							Playervariables.nextlocation = Playervariables.Shrine
						"startconvo":
							match mainscene.mapgenlocation:
								Playervariables.Shrine:
									mainscene.queue_convo("shikashrinetalk")
						null:pass
						_:
							var usestring = currentbeat.get("optionsconsequences")[0]
							if typeof(usestring) == TYPE_STRING and usestring.length() >= 4:
								if usestring.substr(0,3) == "OMT":
									if usestring.substr(usestring.length()-1,1).is_valid_integer():
										var num = int(usestring.substr(3,-1))
										Playervariables.convert_to_bit(Playervariables.StagesCleared,Playervariables.StageRef.ONETIMEMESSAGES,num,true)
										Playervariables.saveprogress()
								elif usestring.substr(0,6) == "secret" and usestring.length() >= 7:
									if int(usestring) > 0 or usestring.substr(6,1) == "0":
										Playervariables.convert_to_bit(Playervariables.StagesCleared,Playervariables.StageRef.SECRET,int(usestring),true)
										Playervariables.saveprogress()
									else:
										print("error with secretflag 0. it should be secret0.")
								elif usestring.substr(0,5) == "alter" and ID.has_method("alter_array"):
									var alter_num = int(usestring)
									if alter_num != 0:
										ID.alter_array(int(int(nextnum)*0.01) + alter_num)
									else:
										print("Error with alter in optionsconsequences in line: "+str(usespeech))
								elif usestring.substr(0,6) == "assign":
									var usenum = int(usestring.substr(6,-1))
									ID.assignquip(usenum)
								else:
									print("Error with optionsconsequences in line: "+str(usespeech))
							else:
								print("Error with optionsconsequences in line: "+str(usespeech)+", either too short or not a string: "+str(usestring))
				speechbubbleref = weakref(newspeechbubble)
				if speechdict.has(nextnum) == true:
					$conversationv2/controls/chatcycle.visible = true
					$conversationv2/controls/chatcycle/cycle.play("cycle")
				else:
					$conversationv2/controls/chatcycle.visible = false
					$conversationv2/controls/chatcycle/cycle.stop()
				yield($ConversationV1.ramble(currentbeat,speechstring,weakref(newspeechbubble.get_node("NinePatchRect/RichTextLabel"))),"completed")
#				yield(self,"bubble_talking")
				if (speechbubbleref.get_ref() != null):
					newspeechbubble.get_node("NinePatchRect/RichTextLabel").visible_characters = -1
				if bubblequeueposition == bubblequeue:
					$conversationv2/waittime.start(conversationwaittime+speechstring.length()*0.017)
					yield($conversationv2/waittime,"timeout")
				if bubblequeueposition == bubblequeue:
					if speechdict.has(nextnum) == false:
#						$conversationv2/waittime.start(conversationwaittime)
#					else:
	#					$conversationv2/VBoxContainer/ProgressBar.visible = false
						speechdictsavepoint = "-1"
						break
					else:
						speechdictsavepoint = nextnum
#					yield($conversationv2/waittime,"timeout")
				else:
					break
#				elif naturaltimeout == false:
##					$conversationv2/scrollconvo.advance(99)
##					$conversationv2/scrollconvo.stop()
#					$conversationv2/VBoxContainer.margin_top -= newdimensions.y#newspeechbubble.get_node("NinePatchRect").rect_size.y
#					if (speechbubbleref.get_ref() != null):
#						newspeechbubble.queue_free()
#						convov2focuspositionmax -= 1
#						if currentfocusposition > convov2focuspositionmax:
#							currentfocusposition = convov2focuspositionmax
		if bubblequeueposition == bubblequeue:
			pass
#			bubblequeue = 0
		elif savedconvowait == 0:
#					$conversationv2/scrollconvo.advance(99)
#					$conversationv2/scrollconvo.stop()
			#$conversationv2/VBoxContainer.margin_top -= newdimensions.y#newspeechbubble.get_node("NinePatchRect").rect_size.y
			if speechbubbleref != null and (speechbubbleref.get_ref() != null):
#				if $conversationv2/scrollconvo.is_playing() == true:
#					$conversationv2/scrollconvo.get_animation("scrollconvo").track_set_key_value(0,0,$conversationv2/scrollconvo.get_animation("scrollconvo").track_get_key_value(0,0) + newdimensions.y)
#					$conversationv2/scrollconvo.get_animation("scrollconvo").track_set_key_value(0,1,$conversationv2/scrollconvo.get_animation("scrollconvo").track_get_key_value(0,1) + newdimensions.y)
#				else:
#					$conversationv2/VBoxContainer.margin_top += newdimensions.y
				speechbubbleref.get_ref().queue_free()
				convov2focuspositionmax -= 1
				convopositionsarray.remove(convopositionsarray.size()-1)
				if currentfocusposition > convov2focuspositionmax:
					adjust_convo_position(convov2focuspositionmax,2,true)
#					currentfocusposition = convov2focuspositionmax
#		interruptconverse = false
#		if mainscene.previewing == false:
#			get_tree().call_group("Quippers","hideunhidequip",true,ID.monsternum)


func requestunscrollplayer():
	if mainscene.get_node("aimers/enemyreticule").visible == false and $ConversationV1.spritetalking == false and $conversationv2.visible == false:
		$ConversationV1.unscroll(true)

func _on_waittime_timeout():
	savedconvowait = 0
#	pass #placeholder connecting timer to main body

func save_corruption(race,specnum,animpart,inc = true):
	$ConversationV1.rulestalker.transformation_animation(animpart,race,animpart == "colour" or mainscene.mapgenlocation == Playervariables.Gallery or race == Playervariables.raceNAMAN)#,mainscene.mapgenlocation == Playervariables.Gallery)
	if mainscene.mapgenlocation != Playervariables.Gallery:
		mainscene.get_node("rulessprite/flash").play("transform")
#		if animpart != null:
#		$ConversationV1.rulestalker.transformation_animation(animpart,race)#Playervariables.racecolorarray[race])
		if animpart+"_competition" in self:
			for testrace in self.get(animpart+"_competition"):
				if testrace != race:
					Playervariables.convert_to_bit(Playervariables.StoredTransformations,testrace,Playervariables.get(Playervariables.racetfsdict[testrace])[animpart.to_upper()],false)
#		if animpart == "tail":
#			for testrace in tail_competition:
#				if testrace != race:
#					Playervariables.convert_to_bit(Playervariables.StoredTransformations,testrace,Playervariables.get(Playervariables.racetfsdict[testrace])["TAIL"],false)
#		elif animpart == "ears":
#			for testrace in ears_competition:
#				if testrace != race:
#					Playervariables.convert_to_bit(Playervariables.StoredTransformations,testrace,Playervariables.get(Playervariables.racetfsdict[testrace])["EARS"],false)
#		if race == Playervariables.raceNEKO:
#			if specnum == Playervariables.cattfs.EARS:
#				Playervariables.convert_to_bit(Playervariables.StoredTransformations,Playervariables.raceKITSUNE,Playervariables.foxtfs.EARS,false)
#				Playervariables.convert_to_bit(Playervariables.StoredTransformations,Playervariables.raceWOLF,Playervariables.wolftfs.EARS,false)
#			elif specnum == Playervariables.cattfs.TAIL:
#				Playervariables.convert_to_bit(Playervariables.StoredTransformations,Playervariables.raceKITSUNE,Playervariables.foxtfs.TAIL,false)
#				Playervariables.convert_to_bit(Playervariables.StoredTransformations,Playervariables.raceWOLF,Playervariables.wolftfs.TAIL,false)
#				Playervariables.convert_to_bit(Playervariables.StoredTransformations,Playervariables.raceIMP,Playervariables.imptfs.TAIL,false)
#		elif race == Playervariables.raceKITSUNE:
#			if specnum == Playervariables.foxtfs.EARS:
#				Playervariables.convert_to_bit(Playervariables.StoredTransformations,Playervariables.raceNEKO,Playervariables.cattfs.EARS,false)
#				Playervariables.convert_to_bit(Playervariables.StoredTransformations,Playervariables.raceWOLF,Playervariables.wolftfs.EARS,false)
#			elif specnum == Playervariables.foxtfs.TAIL:
#				Playervariables.convert_to_bit(Playervariables.StoredTransformations,Playervariables.raceNEKO,Playervariables.cattfs.TAIL,false)
#				Playervariables.convert_to_bit(Playervariables.StoredTransformations,Playervariables.raceWOLF,Playervariables.wolftfs.TAIL,false)
#				Playervariables.convert_to_bit(Playervariables.StoredTransformations,Playervariables.raceIMP,Playervariables.imptfs.TAIL,false)
#		elif race == Playervariables.raceWOLF:
#			if specnum == Playervariables.wolftfs.EARS:
#				Playervariables.convert_to_bit(Playervariables.StoredTransformations,Playervariables.raceNEKO,Playervariables.cattfs.EARS,false)
#				Playervariables.convert_to_bit(Playervariables.StoredTransformations,Playervariables.raceKITSUNE,Playervariables.foxtfs.EARS,false)
#			elif specnum == Playervariables.wolftfs.TAIL:
#				Playervariables.convert_to_bit(Playervariables.StoredTransformations,Playervariables.raceNEKO,Playervariables.cattfs.TAIL,false)
#				Playervariables.convert_to_bit(Playervariables.StoredTransformations,Playervariables.raceKITSUNE,Playervariables.foxtfs.TAIL,false)
#				Playervariables.convert_to_bit(Playervariables.StoredTransformations,Playervariables.raceIMP,Playervariables.imptfs.TAIL,false)
#		elif race == Playervariables.raceIMP:
#				Playervariables.convert_to_bit(Playervariables.StoredTransformations,Playervariables.raceNEKO,Playervariables.cattfs.TAIL,false)
#				Playervariables.convert_to_bit(Playervariables.StoredTransformations,Playervariables.raceKITSUNE,Playervariables.foxtfs.TAIL,false)
#				Playervariables.convert_to_bit(Playervariables.StoredTransformations,Playervariables.raceWOLF,Playervariables.wolftfs.TAIL,false)
		Playervariables.convert_to_bit(Playervariables.UniqueTransformations,race,specnum,true)
		Playervariables.convert_to_bit(Playervariables.StoredTransformations,race,specnum,true)
		if inc == true:
			Playervariables.MonsterGirlTransformations[race] += 1#= max(Playervariables.MonsterGirlTransformations[Playervariables.raceKITSUNE],1)
		Playervariables.saveprogress()

func finish_corruption(race,part,SaveToGallery = true): #savetogallery also toggles if it hard-cleanses a part
	if Playervariables.get_corruption(part) == race:#Playervariables.corruptiondict.has(part) and Playervariables.corruptiondict[part] == race:
#		print("Attempted to add a dupe corruption, returning.")
		return
#	elif mainscene.mapgenlocation != Playervariables.Gallery and Playervariables.corruptiondict.has(part) and Playervariables.racelockcorruptionarray.size() > Playervariables.corruptiondict[part] and Playervariables.racelockcorruptionarray[Playervariables.corruptiondict[part]] == true:
	elif Playervariables.gallery_mode == false and Playervariables.corruptiondict.has(part) and (part != "colour" or race != Playervariables.raceHUMAN) and Playervariables.get_corruption("colour") == Playervariables.get_corruption(part):#and Playervariables.racelockcorruptionarray.size() > Playervariables.corruptiondict[part] and Playervariables.racelockcorruptionarray[Playervariables.corruptiondict[part]] == true:
		get_parent().get_parent().register_event_via_main("VAR1's transformation couldn't take hold over VAR2's deeply bonded corruption.",[Playervariables.racearray[race],Playervariables.racearray[Playervariables.get_corruption(part)]])
#		print("Attempted to add a corruption locked to a race. returning.")
		return
	elif Playervariables.mobile_ads == false and race == Playervariables.raceIMP:
		return
#	elif mainscene.mapgenlocation == Playervariables.Gallery and race != Playervariables.raceHUMAN:
	elif Playervariables.gallery_mode == true and race != Playervariables.raceHUMAN:
		SaveToGallery = false
	elif race == Playervariables.raceHUMAN:
		if Playervariables.gallery_mode == true:
			SaveToGallery = false
		if Playervariables.corruptiondict.has(part) == false:
			return
		elif SaveToGallery == true:
			var removerace = Playervariables.corruptiondict[part]
			var useenum = Playervariables.get(Playervariables.racetfsdict[removerace])
			var upperkey = part.to_upper()
			if useenum.has(upperkey):
				var reference = useenum.get(upperkey)
				Playervariables.convert_to_bit(Playervariables.StoredTransformations,removerace,reference,false)
			else:
				print("Serious error, tried to find an enum code that doesn't exist? in FINISH CORRUPTION raceHUMAN")
	var test_class = false
	if race == Playervariables.raceHUMAN:
		Playervariables.corruptiondict.erase(part)
		test_class = true
	match part:
		"colour":
			match race:
				Playervariables.raceHUMAN:pass
				Playervariables.raceNEKO,Playervariables.raceRAM,Playervariables.raceKITSUNE,Playervariables.raceWOLF,Playervariables.raceHARPY,Playervariables.raceIMP:
					Playervariables.corruptiondict["colour"] = race
					save_corruption(race,7,"colour")
#					if SaveToGallery == true:
#						register_event("VAR1's corruption can no longer be removed. Appearance changed.",[Playervariables.racearray[race]])
			Playervariables.set_base_colour()
		"backhorns":
			match race:
				Playervariables.raceHUMAN:
					if Playervariables.CurrentClass == Playervariables.raceHARPY and Playervariables.get_corruption("tail") != Playervariables.raceHARPY:
						Playervariables.AltClass = 0
				Playervariables.raceHARPY:
					Playervariables.corruptiondict["backhorns"] = Playervariables.raceHARPY
					save_corruption(Playervariables.raceHARPY,Playervariables.harpytfs.BACKHORNS,"backhorns")
					if SaveToGallery == true:
						register_event("DragonHarpy-ness increased.")
				Playervariables.raceIMP:
					Playervariables.corruptiondict["backhorns"] = Playervariables.raceIMP
					save_corruption(Playervariables.raceIMP,Playervariables.imptfs.BACKHORNS,"backhorns")
		"face":
			match race:
				Playervariables.raceHUMAN:
					pass
				Playervariables.raceIMP:
					if Playervariables.get_corruption("tail") != Playervariables.get_corruption("ears"):
						finish_corruption(Playervariables.raceHUMAN,"ears")
					finish_corruption(Playervariables.raceHUMAN,"ears")
					Playervariables.corruptiondict["face"] = Playervariables.raceIMP
					Playervariables.curseditemdict[Playervariables.PIXIEDUST] = true
					save_corruption(Playervariables.raceIMP,Playervariables.imptfs.FACE,"face")
					
		"wings":
			match race:
				Playervariables.raceHUMAN:
					pass
				Playervariables.raceIMP:
					Playervariables.corruptiondict["wings"] = Playervariables.raceIMP
					if SaveToGallery == true:
						register_event("Feeling impish...")
					save_corruption(Playervariables.raceIMP,Playervariables.imptfs.WINGS,"wings")
#					mainscene.updateplayersprite()
		"body":
			match race:
				Playervariables.raceHUMAN:
#					Playervariables.corruptiondict.erase("body")
					$ConversationV1.altered_size = false
					mainscene.get_node("rulessprite/jump").play("jump")
				Playervariables.raceIMP:
					$ConversationV1.altered_size = true
					mainscene.get_node("rulessprite/impjump").play("jump")
					if SaveToGallery == true:
						$ConversationV1.rulestalker.emotioncall("shock")
						register_event("Everything looks huge now! Smaller body makes regular attacks ineffective.")
					Playervariables.corruptiondict["body"] = Playervariables.raceIMP
#					finish_corruption(Playervariables.raceIMP,"leg")
					save_corruption(Playervariables.raceIMP,Playervariables.imptfs.BODY,"body")
#					mainscene.updateplayersprite()
			$ConversationV1.rulestalker.change_player_body(race)
			$ConversationV1.correcttalkerposition($ConversationV1.rulestalker,1)
		"hair":
			match race:
				Playervariables.raceHUMAN:pass
#					Playervariables.corruptiondict.erase("hair")
				Playervariables.raceKITSUNE:
					Playervariables.corruptiondict["hair"] = Playervariables.raceKITSUNE
					if SaveToGallery == true:
						$ConversationV1.rulestalker.emotioncall("flinch")
						register_event("Fox-power. May find more skills to choose?")
#					else:
#						$ConversationV1.rulestalker.force_scroll(1.5)
					save_corruption(Playervariables.raceKITSUNE,Playervariables.foxtfs.HAIR,"hair")
#		"armleft":
#			pass
		"leg":
#			if race != Playervariables.raceIMP:
#				finish_corruption(Playervariables.raceHUMAN,"body")
			var was_human = Playervariables.get_corruption("leg") == Playervariables.raceHUMAN
			Playervariables.wolf_variants = (race == Playervariables.raceWOLF)
			match race:
				Playervariables.raceHUMAN:pass
#					Playervariables.corruptiondict.erase("leg")
				Playervariables.raceWOLF:
					Playervariables.corruptiondict["leg"] = Playervariables.raceWOLF
					if Playervariables.playeroutfit[1] == 1:
						Playervariables.playeroutfit[1] = 0
					save_corruption(Playervariables.raceWOLF,Playervariables.wolftfs.LEG,"leg")
					if SaveToGallery == true:
						mainscene.proximity_apply_quip(mainscene.playerloc,Playervariables.WEREWOLF,16,-1,7)
				Playervariables.raceIMP:
					Playervariables.corruptiondict["leg"] = Playervariables.raceIMP
					finish_corruption(Playervariables.raceIMP,"body")#MUST come after leg = imp
#					Playervariables.cannot_wear_pants = true
					if Playervariables.playeroutfit[1] == 1:
						Playervariables.playeroutfit[1] = 0
					if SaveToGallery == true:
						$ConversationV1.rulestalker.emotioncall("shock")
					save_corruption(Playervariables.raceIMP,Playervariables.imptfs.LEG,"leg")
				Playervariables.raceHARPY:
					Playervariables.corruptiondict["leg"] = Playervariables.raceHARPY
#					Playervariables.cannot_wear_pants = true
					if Playervariables.playeroutfit[1] == 1:
						Playervariables.playeroutfit[1] = 0
					if SaveToGallery == true:
						$ConversationV1.rulestalker.emotioncall("shock")
						if Playervariables.corruptiondict.has("armright") and Playervariables.corruptiondict["armright"] == Playervariables.raceHARPY:
							register_event("My legs match my wings...")
						else:
							register_event("Everything hips-down feels bulky. Talons...?")
						mainscene.proximity_apply_quip(mainscene.playerloc,Playervariables.DRAGONHARPY,14,-1,7)
#					else:
#						$ConversationV1.rulestalker.force_scroll(1.5)
					save_corruption(Playervariables.raceHARPY,Playervariables.harpytfs.LEG,"leg")
			if SaveToGallery == true and was_human == true and Playervariables.get_corruption("leg") > 0:
				mainscene.assignquip(2500,"normal",7)
		"armright":
			match race:
				Playervariables.raceHUMAN:
#					Playervariables.corruptiondict.erase("armright")
					Playervariables.curseditemdict[Playervariables.PAWS] = false
					Playervariables.curseditemdict[Playervariables.NORMALPAWS] = false
				Playervariables.raceNEKO:
	#				if Playervariables.corruptiondict.has("armright") == false or Playervariables.corruptiondict["armright"] == 0:
	#					Playervariables.corruptiondict["armright"] = Playervariables.raceNEKO
					Playervariables.corruptiondict["armright"] = Playervariables.raceNEKO
					if SaveToGallery == true:
						$ConversationV1.rulestalker.emotioncall("annoyed")
						if Playervariables.corruptiondict.has("armright") == false or Playervariables.corruptiondict["armright"] == 0:
							register_event("These are so awkward that they can't be undone?!")
						elif Playervariables.corruptiondict.has("armright") and Playervariables.corruptiondict["armright"] == Playervariables.raceHARPY:
							register_event("How did wings fit into these?!")
						mainscene.proximity_apply_quip(mainscene.playerloc,Playervariables.CATGIRL,16,-1,7)
#							save_corruption(Playervariables.raceNEKO,Playervariables.cattfs.ARMRIGHT,"armright")
#							if Playervariables.bit_to_array(Playervariables.UniqueTransformations,Playervariables.raceNEKO)[Playervariables.cattfs.ARMRIGHT] == false:
#								register_event("I'll hang them up in the gallery too.")
#					else:
#						$ConversationV1.rulestalker.force_scroll(1.5)
					save_corruption(Playervariables.raceNEKO,Playervariables.cattfs.ARMRIGHT,"armright")
	#				Playervariables.stat_calc()
#					get_tree().call_group("consumableslot","refresh")
	#				Playervariables.corruptiondict["armleft"] = Playervariables.raceNEKO
				Playervariables.raceHARPY:
					var hadwings = Playervariables.corruptiondict.has("armright")
					Playervariables.corruptiondict["armright"] = Playervariables.raceHARPY
#					get_tree().call_group("consumableslot","refresh")
					if SaveToGallery == true:
						$ConversationV1.rulestalker.emotioncall("annoyed")
						Playervariables.curseditemdict[Playervariables.PAWS] = false
						Playervariables.curseditemdict[Playervariables.NORMALPAWS] = false
						if Playervariables.curseditemdict[Playervariables.ROPEGAG] == true:
							register_event("As if being bound wasn't enough, now my arms feel weird.")
						elif hadwings == true:
							register_event("My arms are messed up again.")
						else:
							register_event("My hands...")
							register_event("They're gone...?")
						mainscene.proximity_apply_quip(mainscene.playerloc,Playervariables.DRAGONHARPY,15,-1,7)
#					else:
#						$ConversationV1.rulestalker.force_scroll(1.5)
					save_corruption(Playervariables.raceHARPY,Playervariables.harpytfs.ARMRIGHT,"armright")
		"tail":
			match race:
				Playervariables.raceHUMAN:
#					Playervariables.corruptiondict.erase("tail")
					if $ConversationV1.spritetalking == false:
						Playervariables.tempcorruptiondict.erase("beads")
					if Playervariables.CurrentClass == Playervariables.raceHARPY and Playervariables.get_corruption("backhorns") != Playervariables.raceHARPY:
						Playervariables.AltClass = 0
#					Playervariables.stat_calc()
				Playervariables.raceHARPY:
					if Playervariables.mobile_ads == true:
						Playervariables.corruptiondict["tail"] = Playervariables.raceHARPY
						if Playervariables.CurrentClass == Playervariables.raceHARPY:
							Playervariables.AltClass = 1
						Playervariables.harpy_variants = true
						if Playervariables.get_corruption("colour") == Playervariables.raceHARPY:
							Playervariables.set_base_colour()
	#					else:
	#						Playervariables.set_horn_materials()
						save_corruption(Playervariables.raceHARPY,Playervariables.harpytfs.TAIL,"tail")
						if Playervariables.get_corruption("backhorns") == Playervariables.raceIMP:
							finish_corruption(Playervariables.raceHARPY,"backhorns",false)
						if SaveToGallery == true:
							register_event("DragonHarpy-ness increased.")
					else:
						register_event("Error. Might want to tell the dev.")
				Playervariables.raceIMP:
					if SaveToGallery == true:
						if Playervariables.corruptiondict.has("tail") and Playervariables.corruptiondict["tail"] != Playervariables.raceIMP:
							register_event("Impish magic takes over the tail!")
						else:
							register_event("Nobody would trust a demon tail.")
#					if Playervariables.corruptiondict.has("ears"):
#						uncorruptplayerwithbar(Playervariables.raceKITSUNE,50)
#						uncorruptplayerwithbar(Playervariables.raceNEKO,50)
#						uncorruptplayerwithbar(Playervariables.raceWOLF,50)
					test_class = true
					Playervariables.corruptiondict["tail"] = Playervariables.raceIMP
					save_corruption(Playervariables.raceIMP,Playervariables.imptfs.TAIL,"tail")
				Playervariables.raceWOLF:
					Playervariables.corruptiondict["tail"] = Playervariables.raceWOLF
					if $ConversationV1.spritetalking == false:
						Playervariables.tempcorruptiondict.erase("beads")
					if SaveToGallery == true:
						$ConversationV1.rulestalker.emotioncall("shock")
						register_event("There's no way to hide this absurdly fluffy tail!")
						test_class = true
#						if Playervariables.CurrentClass == Playervariables.raceKITSUNE:
#							Playervariables.CurrentClass = Playervariables.raceHUMAN
#						elif Playervariables.CurrentClass == Playervariables.raceNEKO:
#							Playervariables.CurrentClass = Playervariables.raceHUMAN
#						uncorruptplayerwithbar(Playervariables.raceKITSUNE,30)
#						uncorruptplayerwithbar(Playervariables.raceNEKO,30)
						mainscene.proximity_apply_quip(mainscene.playerloc,Playervariables.WEREWOLF,15,-1,7)
					save_corruption(Playervariables.raceWOLF,Playervariables.wolftfs.TAIL,"tail")
				Playervariables.raceNEKO:
					Playervariables.corruptiondict["tail"] = Playervariables.raceNEKO
					if SaveToGallery == true:
						if (mainscene.mapgenlocation in [Playervariables.Tutorial3s1,Playervariables.Tutorial3s2,Playervariables.Tutorial3s3]) == true:
							mainscene.search_and_apply_quip(0,5)
						else:
							mainscene.proximity_apply_quip(mainscene.playerloc,Playervariables.CATGIRL,15,-1,7)
						$ConversationV1.rulestalker.emotioncall("smug")
						register_event("Nya? Became a full Cat-Girl.")
						register_event("Jump speed increased.")
#						Playervariables.convert_to_bit(Playervariables.UniqueTransformations,Playervariables.raceNEKO,Playervariables.cattfs.TAIL,true)
#						Playervariables.MonsterGirlTransformations[Playervariables.raceNEKO] += 1# max(Playervariables.MonsterGirlTransformations[Playervariables.raceNEKO],2)
#						Playervariables.saveprogress()
						test_class = true
#						if Playervariables.CurrentClass == Playervariables.raceKITSUNE:
#							Playervariables.CurrentClass = Playervariables.raceHUMAN
#						elif Playervariables.CurrentClass == Playervariables.raceWOLF:
#							Playervariables.CurrentClass = Playervariables.raceHUMAN
#						uncorruptplayerwithbar(Playervariables.raceKITSUNE,30)
#						uncorruptplayerwithbar(Playervariables.raceWOLF,20)
#					else:
#						$ConversationV1.rulestalker.force_scroll(1.5)
					save_corruption(Playervariables.raceNEKO,Playervariables.cattfs.TAIL,"tail")
				Playervariables.raceKITSUNE:
					var had_tail = Playervariables.corruptiondict.has("tail")
					Playervariables.corruptiondict["tail"] = Playervariables.raceKITSUNE
					if $ConversationV1.spritetalking == false:
						Playervariables.tempcorruptiondict.erase("beads")
#					Playervariables.stat_calc()
#					mainscene.playerspeed = Playervariables.playermaxspeed #fixes and cat-tail speed buffs
#						if Playervariables.MonsterGirlTransformations[Playervariables.raceKITSUNE] == 0:
					if SaveToGallery == true:
						$ConversationV1.rulestalker.emotioncall("glancing")
						if had_tail == false:
							register_event("There's something soft behind me!")#"Since when did I have a tail?!")
						else:
							register_event("My tail is changing!")
#						register_event("May now gather more skills.")
						test_class = true
#						if Playervariables.CurrentClass == Playervariables.raceNEKO:
#							Playervariables.CurrentClass = Playervariables.raceHUMAN
#						elif Playervariables.CurrentClass == Playervariables.raceWOLF:
#							Playervariables.CurrentClass = Playervariables.raceHUMAN
#						uncorruptplayerwithbar(Playervariables.raceNEKO,30)
#						uncorruptplayerwithbar(Playervariables.raceWOLF,20)
#						Playervariables.convert_to_bit(Playervariables.UniqueTransformations,Playervariables.raceKITSUNE,Playervariables.foxtfs.TAIL,true)
#						Playervariables.MonsterGirlTransformations[Playervariables.raceKITSUNE] += 1#= max(Playervariables.MonsterGirlTransformations[Playervariables.raceKITSUNE],1)
#						Playervariables.saveprogress()
#					else:
#						$ConversationV1.rulestalker.force_scroll(1.5)
					save_corruption(Playervariables.raceKITSUNE,Playervariables.foxtfs.TAIL,"tail")
				_:print("Could not find race corruption:"+str(race)+" in part:"+str(part))
			if race != Playervariables.raceHUMAN:
				uncorrupt_competition(race,"tail",40)
		"ears":
			match race:
				Playervariables.raceHUMAN:
					if Playervariables.get_corruption("tail") != Playervariables.raceIMP:
						finish_corruption(Playervariables.raceHUMAN,"tail")
#					Playervariables.wolf_variants = false
#					Playervariables.corruptiondict.erase("ears")
#					Playervariables.stat_calc()
#					mainscene.playerspeed = Playervariables.playermaxspeed
				Playervariables.raceWOLF:
					Playervariables.corruptiondict["ears"] = Playervariables.raceWOLF
#					if Playervariables.corruptiondict.has("tail") and Playervariables.corruptiondict["tail"] > 0 and Playervariables.corruptiondict["tail"] != Playervariables.raceWOLF:
					if SaveToGallery == true:
						mainscene.proximity_apply_quip(mainscene.playerloc,Playervariables.WEREWOLF,14,-1,7)
					if Playervariables.get_corruption("tail") in [Playervariables.raceKITSUNE,Playervariables.raceNEKO]:
						if SaveToGallery == true:
							$ConversationV1.rulestalker.emotioncall("flinch")
							register_event("Everything's becoming fluffier!")
						finish_corruption(Playervariables.raceWOLF,"tail")
					else:
						if SaveToGallery == true:
							$ConversationV1.rulestalker.emotioncall("flinch")
							register_event("I can hear everything?!")
					save_corruption(Playervariables.raceWOLF,Playervariables.wolftfs.EARS,"ears")
				Playervariables.raceNEKO:
					Playervariables.corruptiondict["ears"] = Playervariables.raceNEKO
#					if Playervariables.corruptiondict.has("tail") and Playervariables.corruptiondict["tail"] > 0 and Playervariables.corruptiondict["tail"] != Playervariables.raceNEKO:
					if Playervariables.get_corruption("tail") in [Playervariables.raceKITSUNE,Playervariables.raceWOLF]:
						if SaveToGallery == true:
							$ConversationV1.rulestalker.emotioncall("shock")
							register_event("Cat-girl influence is taking over the existing corruption!")
							register_event("Move speed increased.")
#						else:
#							$ConversationV1.rulestalker.force_scroll(1.5)
#						if Playervariables.corruptiondict.has("tail") and Playervariables.corruptiondict["tail"] > 0 and Playervariables.corruptiondict["tail"] != Playervariables.raceNEKO:
						finish_corruption(Playervariables.raceNEKO,"tail")
					else:
						if SaveToGallery == true:
							$ConversationV1.rulestalker.emotioncall("flinch")
							register_event("Turning into a Cat-girl?!")
							register_event("Move speed increased.")
#						else:
#							$ConversationV1.rulestalker.force_scroll(1.5)
					if SaveToGallery == true:
						if (mainscene.mapgenlocation in [Playervariables.Tutorial3s1,Playervariables.Tutorial3s2,Playervariables.Tutorial3s3]) == true:
							mainscene.search_and_apply_quip(0,3)
						else:
							mainscene.proximity_apply_quip(mainscene.playerloc,Playervariables.CATGIRL,14,-1,7)
#						elif mainscene.mapgenlocation != Playervariables.Gallery:
#							mainscene.awardplayer(101)
#						save_corruption(Playervariables.raceNEKO,Playervariables.cattfs.EARS,"ears")
#						Playervariables.convert_to_bit(Playervariables.UniqueTransformations,Playervariables.raceNEKO,Playervariables.cattfs.EARS,true)
#						Playervariables.MonsterGirlTransformations[Playervariables.raceNEKO] += 1# max(Playervariables.MonsterGirlTransformations[Playervariables.raceNEKO],1)
#						Playervariables.saveprogress()
#					else:
#						$ConversationV1.rulestalker.force_scroll(1.5)
					save_corruption(Playervariables.raceNEKO,Playervariables.cattfs.EARS,"ears")
				Playervariables.raceKITSUNE:
					Playervariables.corruptiondict["ears"] = Playervariables.raceKITSUNE
#					Playervariables.stat_calc()
#					mainscene.playerspeed = Playervariables.playermaxspeed #fixes and cat-ear speed buffs
#					if Playervariables.corruptiondict.has("tail") and Playervariables.corruptiondict["tail"] > 0 and Playervariables.corruptiondict["tail"] != Playervariables.raceKITSUNE:
					if Playervariables.get_corruption("tail") in [Playervariables.raceNEKO,Playervariables.raceWOLF]:
						if SaveToGallery == true:
							$ConversationV1.rulestalker.emotioncall("flinch")
							register_event("Kitsune energy is taking over the existing corruption!")
#						else:
#							$ConversationV1.rulestalker.force_scroll(1.5)
#						if Playervariables.corruptiondict.has("tail") and Playervariables.corruptiondict["tail"] > 0 and Playervariables.corruptiondict["tail"] != Playervariables.raceKITSUNE:
						finish_corruption(Playervariables.raceKITSUNE,"tail")
					else:
						if SaveToGallery == true:
							$ConversationV1.rulestalker.emotioncall("flinch")
							register_event("My ears are twitchy?")
#						else:
#							$ConversationV1.rulestalker.force_scroll(1.5)
#						if Playervariables.MonsterGirlTransformations[Playervariables.raceKITSUNE] == 0:
#					if SaveToGallery == true:
#						save_corruption(Playervariables.raceKITSUNE,Playervariables.foxtfs.EARS,"ears")
#						Playervariables.convert_to_bit(Playervariables.UniqueTransformations,Playervariables.raceKITSUNE,Playervariables.foxtfs.EARS,true)
#						Playervariables.MonsterGirlTransformations[Playervariables.raceKITSUNE] += 1#= max(Playervariables.MonsterGirlTransformations[Playervariables.raceKITSUNE],1)
#						Playervariables.saveprogress()
#					else:
#						$ConversationV1.rulestalker.force_scroll(1.5)
					save_corruption(Playervariables.raceKITSUNE,Playervariables.foxtfs.EARS,"ears")
				_:print("Could not find race corruption:"+str(race)+" in part:"+str(part))
		"horns":
			match race:
				Playervariables.raceHUMAN:pass
#					Playervariables.corruptiondict.erase("horns")
				Playervariables.raceRAM:
					Playervariables.corruptiondict["horns"] = Playervariables.raceRAM
#					Playervariables.Move3["special"][2] = Playervariables.CHARGE
#					Playervariables.Move3["types"][2] = 1
					if SaveToGallery == true:
						$ConversationV1.rulestalker.emotioncall("pensive")
						register_event("Head feels heavy... Movement skills now deal damage.")#"My head feels... Heavier?")
						if mainscene.mapgenlocation != Playervariables.Gallery:
							mainscene.awardplayer(102)
#						save_corruption(Playervariables.raceRAM,Playervariables.ramtfs.HORNS,"horns")
#					else:
#						$ConversationV1.rulestalker.force_scroll(1.5)
#						Playervariables.convert_to_bit(Playervariables.UniqueTransformations,Playervariables.raceRAM,Playervariables.ramtfs.HORNS,true)
#						Playervariables.MonsterGirlTransformations[Playervariables.raceRAM] += 1#Playervariables.MonsterGirlTransformations[Playervariables.raceRAM]
#						Playervariables.saveprogress()
					save_corruption(Playervariables.raceRAM,Playervariables.ramtfs.HORNS,"horns")
				_:print("Could not find race corruption:"+str(race)+" in part:"+str(part))
		"eyes":
			match race:
				Playervariables.raceHUMAN:
#					Playervariables.corruptiondict.erase("eyes")
					Playervariables.baseplayercolourarray[0] = Playervariables.truecolourarray[0]
					if $ConversationV1.spritetalking == true:
						$ConversationV1.rulestalker.debugcureplayercolour()
					$ConversationV1.rulestalker.force_emotion()
				Playervariables.raceRAM:
					Playervariables.corruptiondict["eyes"] = Playervariables.raceRAM
					mainscene.updateplayersprite()
#					$ConversationV1.rulestalker.assignplayercolour()
#					$ConversationV1.rulestalker.force_emotion()
					$ConversationV1.rulestalker.emotioncall("flinch")
					if SaveToGallery == true:
						if mainscene.mapgenlocation != Playervariables.Gallery:
#							mainscene.awardplayer(203)
							mainscene.awardcurse(203,2,true,true)
#					else:
#						$ConversationV1.rulestalker.force_scroll(1.5)
#						Playervariables.convert_to_bit(Playervariables.UniqueTransformations,Playervariables.raceRAM,Playervariables.ramtfs.EYES,true)
#						Playervariables.saveprogress()
					save_corruption(Playervariables.raceRAM,Playervariables.ramtfs.EYES,"eyes",false)
				_:print("Could not find race corruption:"+str(race)+" in part:"+str(part))
		"top":
			match race:
				Playervariables.raceHUMAN:
#					Playervariables.corruptiondict.erase("top")
					Playervariables.playeroutfit[0] = 1
					Playervariables.outfithealth = 4
#					if Playervariables.cannot_wear_pants == true and Playervariables.corruptiondict.has("pants") and Playervariables.corruptiondict["pants"] == Playervariables.raceRAM:
#					if Playervariables.corruptiondict.has("pants") and Playervariables.corruptiondict["pants"] == Playervariables.raceRAM:
#						finish_corruption(Playervariables.raceHUMAN,"pants",SaveToGallery)
					mainscene.updateplayersprite()
				Playervariables.raceRAM:
					Playervariables.playeroutfit[0] = Playervariables.raceRAM
					Playervariables.corruptiondict["top"] = Playervariables.raceRAM
					Playervariables.outfithealth = 4
					if SaveToGallery == true:
						register_event("Forced into new clothes...")
						if Playervariables.corruptiondict.has("horns") and Playervariables.corruptiondict["horns"] == Playervariables.raceRAM or mainscene.playerdebuffarray[Playervariables.HYPNOSIS-1] > 0:
							$ConversationV1.rulestalker.emotioncall("interested")
						else:
							$ConversationV1.rulestalker.emotioncall("pensive")
#						Playervariables.MonsterGirlTransformations[Playervariables.raceRAM] += 1
#						Playervariables.convert_to_bit(Playervariables.UniqueTransformations,Playervariables.raceRAM,Playervariables.ramtfs.TOP,true)
#						Playervariables.saveprogress()
#					else:
#						$ConversationV1.rulestalker.force_scroll(1.5)
					save_corruption(Playervariables.raceRAM,Playervariables.ramtfs.TOP,"top")
					mainscene.updateplayersprite()
				Playervariables.raceWOLF:
					Playervariables.playeroutfit[0] = Playervariables.raceWOLF
					Playervariables.corruptiondict["top"] = Playervariables.raceWOLF
					if SaveToGallery == true:
						register_event("What's with this tar?")
						$ConversationV1.rulestalker.emotioncall("flinch")
						$ConversationV1.rulestalker.get_node("positionnode/effects").effectcall("jump",true)
					save_corruption(Playervariables.raceWOLF,Playervariables.wolftfs.TOP,"top")
					mainscene.updateplayersprite()
				_:print("Could not find race corruption:"+str(race)+" in part:"+str(part))
#			$ConversationV1.rulestalker.assignplayercolour()
		"pants":
#			if Playervariables.cannot_wear_pants == false or race == Playervariables.raceHUMAN:
			match race:
				Playervariables.raceHUMAN:
#						Playervariables.corruptiondict.erase("pants")
					if Playervariables.corruptiondict.has("leg") == false:#Playervariables.cannot_wear_pants == false:
						Playervariables.playeroutfit[1] = 1
						mainscene.updateplayersprite()
					else:
						Playervariables.playeroutfit[1] = 0
				Playervariables.raceRAM:
					Playervariables.playeroutfit[1] = Playervariables.raceRAM
					Playervariables.corruptiondict["pants"] = Playervariables.raceRAM
					if SaveToGallery == true:
						register_event("Stuffed into fluffy stockings?")
#							Playervariables.MonsterGirlTransformations[Playervariables.raceRAM] += 1
#							Playervariables.convert_to_bit(Playervariables.UniqueTransformations,Playervariables.raceRAM,Playervariables.ramtfs.PANTS,true)
#							Playervariables.saveprogress()
#						else:
#							$ConversationV1.rulestalker.force_scroll(1.5)
					save_corruption(Playervariables.raceRAM,Playervariables.ramtfs.PANTS,"pants")
					mainscene.updateplayersprite()
				_:print("Could not find race corruption:"+str(race)+" in part:"+str(part))
#			$ConversationV1.rulestalker.assignplayercolour()
		_:print("Could not find part corruption:"+str(part)+" also, race was:"+str(race))
	if test_class == true:#race == Playervariables.raceHUMAN:
		if Playervariables.gallery_mode == false:# mainscene.mapgenlocation != Playervariables.Gallery:
			var class_test_array = []
			match Playervariables.CurrentClass:
				Playervariables.raceHUMAN:pass
				Playervariables.raceNAMAN:pass
				Playervariables.raceNEKO:class_test_array = ["tail","ears","armright"]
				Playervariables.raceRAM:class_test_array = ["eyes","horns","top","pants"]
				Playervariables.raceKITSUNE:class_test_array = ["tail","ears","hair"]
				Playervariables.raceWOLF:class_test_array = ["tail","ears","leg","top"]
				Playervariables.raceHARPY:class_test_array = ["armright","leg","tail"]#doesn't test backhorns, 'cuz
				Playervariables.raceIMP:class_test_array = ["leg","backhorns","body","face","tail","wings"]
			if test_class_corruptions(class_test_array) == false:
				if Playervariables.wolf_collar == true:#if Playervariables.CurrentClass == Playervariables.raceWOLF:
					Playervariables.tempname = Playervariables.playername
				Playervariables.CurrentClass = Playervariables.raceHUMAN
				Playervariables.set_rank_materials()
				register_event("Class status has been cured.")
		if $ConversationV1.spritetalking == false:#to stop corruption-removing/adding scenes breaking
			mainscene.purify_player_completely(false,false)
	stat_recalc()
	if part == "body":
		mainscene.basedamage = Playervariables.playermaxbasedamage
	elif part == "armright":
		if Playervariables.hands == false:
			get_tree().call_group("consumableslot","force_consumable_switch",16)
		else:
			get_tree().call_group("consumableslot","refresh")

func test_class_corruptions(stringarray):
	for corruption in stringarray:
		if Playervariables.get_corruption(corruption) == Playervariables.CurrentClass:
			return true
	return false

func stat_recalc():
	Playervariables.lewdness_calc()
	Playervariables.stat_calc()
	mainscene.playerjumpspeed = Playervariables.playermaxjumpspeed
	mainscene.playerspeed = Playervariables.playermaxspeed

func race_corruption_order(race,special=false)->String:
	match race:
		Playervariables.raceNAMAN:
			if Playervariables.playerbustsize < 4:
				return "bustsize" + str(Playervariables.playerbustsize+1)
		Playervariables.raceNEKO,Playervariables.raceKITSUNE:
			if Playervariables.get_corruption("ears") != race:
				return "ears"
			elif Playervariables.get_corruption("tail") != race:
				return "tail"
		Playervariables.raceWOLF:
			if Playervariables.get_corruption("leg") != race and Playervariables.get_corruption("ears") > 0 and Playervariables.get_corruption("tail") > 0 and mainscene.mapgenlocation != Playervariables.Gallery:
				return "leg"
			elif Playervariables.get_corruption("ears") != race:
				return "ears"
			elif Playervariables.get_corruption("tail") != race:
				return "tail"
		Playervariables.raceRAM:
			if Playervariables.get_corruption("horns") != race:
				return "horns"
		Playervariables.raceHARPY:
			if Playervariables.get_corruption("leg") != race:
				return "leg"
			elif Playervariables.get_corruption("armright") != race:
				return "armright"
			elif special == true and Playervariables.mobile_ads == true:
				if Playervariables.get_corruption("tail") != race:
					return "tail"
				elif Playervariables.get_corruption("backhorns") != race:
					return "backhorns"
		Playervariables.raceIMP:
			if Playervariables.get_corruption("wings") != race:
				return "wings"
			elif Playervariables.get_corruption("tail") == 0 or (mainscene.mapgenlocation == Playervariables.Gallery and Playervariables.get_corruption("tail") != Playervariables.raceIMP):
				return "tail"
			elif Playervariables.get_corruption("face") != race and Playervariables.get_corruption("tail") == race:
				return "face"
			elif Playervariables.get_corruption("leg") != race and Playervariables.get_corruption("leg") != Playervariables.get_corruption("colour"):# and mainscene.mapgenlocation != Playervariables.Gallery:
				return "leg"
			elif Playervariables.get_corruption("tail") != race and Playervariables.get_corruption("tail") != Playervariables.get_corruption("colour"):
				return "tail"
			elif Playervariables.get_corruption("backhorns") != race and Playervariables.get_corruption("backhorns") != Playervariables.get_corruption("colour"):#must come after tail!= race to ensure harpy tail isn't there
				return "backhorns"
			elif Playervariables.get_corruption("face") != race:
				return "face"
			elif Playervariables.get_corruption("body") != race:# and mainscene.mapgenlocation != Playervariables.Gallery:
				return "body"
	return "bonus"

const backhorns_competition = PoolIntArray([Playervariables.raceHARPY,Playervariables.raceIMP])
const leg_competition = PoolIntArray([Playervariables.raceHARPY,Playervariables.raceWOLF,Playervariables.raceIMP])
const ears_competition = PoolIntArray([Playervariables.raceWOLF,Playervariables.raceNEKO,Playervariables.raceKITSUNE])
const tail_competition = PoolIntArray([Playervariables.raceWOLF,Playervariables.raceNEKO,Playervariables.raceKITSUNE,Playervariables.raceIMP,Playervariables.raceHARPY])
func uncorrupt_competition(race,part,strength):
	if part != "backhorns" and part+"_competition" in self:
		var use_competition = self.get(part+"_competition")
		for test_race in use_competition:
			if test_race != race:
				if Playervariables.get_corruption(part) == test_race:
					uncorruptplayerwithbar(test_race,strength)
				else:
					var test_string = race_corruption_order(test_race)
					if test_string == part:
						uncorruptplayerwithbar(test_race,strength)

var corruption_tracker = 0
var corruption_triggers = 0
func corruptplayerwithbar(race,increase,special=false):
#	if Playervariables.racemultiplierarray[race] > 0:
#	if race in [Playervariables.raceKITSUNE,Playervariables.raceRAM,Playervariables.raceNEKO,Playervariables.raceNAMAN,Playervariables.raceHARPY,Playervariables.raceWOLF]:
#	if Playervariables.racelockcorruptionarray[race] == true:
#		if (mainscene.mapgenlocation in [Playervariables.Tutorial3s1,Playervariables.Tutorial3s2,Playervariables.Tutorial3s3]) == false:
#			return
	if Playervariables.mobile_ads == false and race == Playervariables.raceIMP:
		return
	elif mainscene.gameover == true:
		return
#	var bonus_corruption = false
	var target_part = race_corruption_order(race,special)
	if mainscene.mapgenlocation == Playervariables.Gallery and target_part == "bonus":
		return
	elif target_part != "bonus":
		if mainscene.mapgenlocation != Playervariables.Gallery and mainscene.mapgenlocation != Playervariables.ImpDebug:
			if target_part == "tail":
				if race == Playervariables.raceHARPY:
					if special == false:
						return
				elif Playervariables.corruptiondict.has(target_part):
					increase = increase*0.67
			elif (target_part == "horns" or target_part == "backhorns") and (Playervariables.corruptiondict.has("horns") or Playervariables.corruptiondict.has("backhorns")):
				increase = increase*0.5
			elif target_part == "ears" and Playervariables.get_corruption("face") > 0 and Playervariables.get_corruption("face") != race:
				increase = increase*0.34
			elif Playervariables.corruptiondict.has(target_part) and Playervariables.corruptiondict[target_part] != race:
				if target_part == "ears":
					increase = increase*0.67
				elif target_part == "leg":
					increase = increase*0.5
				else:
					increase = increase*0.34
	elif Playervariables.get_corruption("colour") > 0 and (mainscene.mapgenlocation in [Playervariables.Tutorial3s1,Playervariables.Tutorial3s2,Playervariables.Tutorial3s3]) == false:
		return
	else:
		increase = increase*0.67
	uncorrupt_competition(race,target_part,increase)
#	match race:
#		Playervariables.raceIMP:
#			if mainscene.mapgenlocation == Playervariables.Gallery:
#				if Playervariables.corruptiondict.has("tail") == true and Playervariables.corruptiondict["tail"] == Playervariables.raceIMP:
#					if Playervariables.corruptiondict.has("wings") == true and Playervariables.corruptiondict["wings"] == Playervariables.raceIMP:
#						return
#			elif Playervariables.corruptiondict.has("body") == true and Playervariables.corruptiondict["body"] == Playervariables.raceIMP:
#				if Playervariables.corruptiondict.has("tail") == true and Playervariables.corruptiondict["tail"] == Playervariables.raceIMP:
#					if Playervariables.corruptiondict.has("wings") == true and Playervariables.corruptiondict["wings"] == Playervariables.raceIMP:
#						bonus_corruption = true
#		Playervariables.raceWOLF:
##			if Playervariables.racelockcorruptionarray[Playervariables.raceNEKO] == true or Playervariables.racelockcorruptionarray[Playervariables.raceKITSUNE] == true or Playervariables.racelockcorruptionarray[Playervariables.raceIMP] == true:
##				return
#			if Playervariables.corruptiondict.has("tail") == true and (Playervariables.corruptiondict["tail"] != Playervariables.raceIMP or (Playervariables.corruptiondict.has("ears") and Playervariables.corruptiondict["ears"] == Playervariables.raceWOLF)):
#				increase = increase*0.5
#				if Playervariables.corruptiondict["tail"] == Playervariables.raceWOLF:
#					uncorruptplayerwithbar(Playervariables.raceKITSUNE,increase)
#					uncorruptplayerwithbar(Playervariables.raceNEKO,increase)
#					bonus_corruption = true
#					return
#		Playervariables.raceHARPY:
#			if Playervariables.corruptiondict.has("armright") and (Playervariables.corruptiondict.has("leg") and Playervariables.corruptiondict["leg"] == Playervariables.raceHARPY):
#				if Playervariables.corruptiondict["armright"] == Playervariables.raceHARPY:
#					bonus_corruption = true
#					return
#				else:
#					increase = increase * 0.34
#		Playervariables.raceNAMAN:
#			if Playervariables.racelockcorruptionarray[Playervariables.raceNAMAN] == true:
#				return
#			if Playervariables.playerbustsize >= 4:#and Playervariables.playerbustsize >= 3:
	#			if (Playervariables.curseditemdict[Playervariables.COWBELL] == false and mainscene.mapgenlocation != Playervariables.Gallery) or Playervariables.playerbustsize >= 4:
	#			if Playervariables.playerbustsize >= 4:
#				bonus_corruption = true
#				return
#		Playervariables.raceKITSUNE:
#			if Playervariables.racelockcorruptionarray[Playervariables.raceNEKO] == true or Playervariables.racelockcorruptionarray[Playervariables.raceWOLF] == true or Playervariables.racelockcorruptionarray[Playervariables.raceIMP] == true:
#				return
#			if Playervariables.corruptiondict.has("tail") == true and (Playervariables.corruptiondict["tail"] != Playervariables.raceIMP or (Playervariables.corruptiondict.has("ears") and Playervariables.corruptiondict["ears"] == Playervariables.raceKITSUNE)):
#				increase = increase * 0.34
#				if Playervariables.corruptiondict["tail"] == Playervariables.raceKITSUNE:
#					uncorruptplayerwithbar(Playervariables.raceWOLF,increase)
#					uncorruptplayerwithbar(Playervariables.raceNEKO,increase)
#					bonus_corruption = true
#					return
#		Playervariables.raceRAM:
#			if Playervariables.corruptiondict.has("horns") == true:
#				bonus_corruption = true
#				return
	if race == Playervariables.raceNEKO and target_part == "bonus":
#			if Playervariables.racelockcorruptionarray[Playervariables.raceKITSUNE] == true or Playervariables.racelockcorruptionarray[Playervariables.raceWOLF] == true or Playervariables.racelockcorruptionarray[Playervariables.raceIMP] == true:
#				return
#			if Playervariables.corruptiondict.has("tail") == true and (Playervariables.corruptiondict["tail"] != Playervariables.raceIMP or (Playervariables.corruptiondict.has("ears") and Playervariables.corruptiondict["ears"] == Playervariables.raceNEKO)):
#				increase = increase*0.34
#				if Playervariables.corruptiondict["tail"] == Playervariables.raceNEKO:
					if Playervariables.tempcorruptiondict.has("beads"):
						Playervariables.tempcorruptiondict["beads"] += 1
						if Playervariables.tempcorruptionbreakpoints["beads"].find(Playervariables.tempcorruptiondict["beads"]) != -1:
							$ConversationV1.rulestalker.emotioncall("flinch")
							$ConversationV1.rulestalker.get_node("positionnode/effects").effectcall("shaking",true)
							match Playervariables.tempcorruptionbreakpoints["beads"].find(Playervariables.tempcorruptiondict["beads"]):
								0:
									register_event("She stuffed some black beads in me?!")
									register_event("It's tied to my new tail..?")
	#								mainscene.get_node("sfx/abilitysfx/Sheep Song").position = mainscene.playerlocv
	#								mainscene.get_node("sfx/abilitysfx/Sheep Song").play()
									mainscene.search_and_apply_quip(0,4)
									mainscene.call("attacking",[mainscene.playerloc],Vector2(Playervariables.BITTER,0),mainscene,[Vector2(Playervariables.SWEATY,1)],[Playervariables.raceNEKO],-2,"Cursed Beads")
	#								$ConversationV1.rulestalker.force_scroll(3)
									$ConversationV1.rulestalker.transformation_animation("tail",Playervariables.raceNEKO)#Playervariables.racecolorarray[Playervariables.raceNEKO])
								1:
									register_event("My tail slipped! Head feels hazy.")
									mainscene.get_node("sfx/abilitysfx/Heavenly Chord").position = mainscene.playerlocv
									mainscene.get_node("sfx/abilitysfx/Heavenly Chord").play()
									mainscene.search_and_apply_quip(0,6)
									mainscene.call("attacking",[mainscene.playerloc],Vector2(Playervariables.BITTER,0),mainscene,[Vector2(Playervariables.SWEATY,2)],[Playervariables.raceNEKO],-2,"Cursed Beads")
									screen_filter(Playervariables.HYPNOSIS)
									$ConversationV1.rulestalker.force_scroll(3)
									$ConversationV1.rulestalker.assignplayercolour()
								2:
									register_event("My tail slipped again, it's over...")
									mainscene.get_node("sfx/abilitysfx/Hypnosis").position = mainscene.playerlocv
									mainscene.get_node("sfx/abilitysfx/Hypnosis").play()
									mainscene.call("attacking",[mainscene.playerloc],Vector2(Playervariables.BITTER,0),mainscene,[Vector2(Playervariables.SWEATY,4)],[Playervariables.raceNEKO],-2,"Cursed Beads")
									screen_filter(Playervariables.HYPNOSIS,false)
									$ConversationV1.rulestalker.assignplayercolour()
									mainscene.bad_end()
					else:
						if mainscene.mapgenlocation in [Playervariables.Tutorial3s1,Playervariables.Tutorial3s2,Playervariables.Tutorial3s3]:
							if Playervariables.playeroutfit[0] != 0:
								register_event("Too warm for clothes...")
							mainscene.solve_debuff(Vector2(Playervariables.MARKED,9))
							Playervariables.tempcorruptiondict["beads"] = 0
#					uncorruptplayerwithbar(Playervariables.raceWOLF,increase)
#					uncorruptplayerwithbar(Playervariables.raceKITSUNE,increase)
#					bonus_corruption = true
#					return
#		_:
#			return
	var oldvalue
	var newvalue
	if target_part == "bonus":
		if (mainscene.mapgenlocation in [Playervariables.Gallery,Playervariables.Tutorial3s1,Playervariables.Tutorial3s2,Playervariables.Tutorial3s3]) == true:
			return
		elif race == Playervariables.raceNAMAN:
			return
		oldvalue = Playervariables.bonusracecorruptionarray[race]
		newvalue = oldvalue + increase
		Playervariables.bonusracecorruptionarray[race] = newvalue
	else:#if target_part != "bonus":
		oldvalue = Playervariables.racecorruptionarray[race]
		newvalue = oldvalue + increase
		Playervariables.racecorruptionarray[race] = newvalue
		if race != Playervariables.raceNAMAN:
			if newvalue >= 100:
				finish_corruption(race,target_part)
				if race == Playervariables.raceHARPY:
					if mainscene.capture_id_weakref != null and mainscene.capture_id_weakref.get_ref() != null:
						mainscene.capture_id.SpawnHarpy = true
		else:
			$ConversationV1.rulestalker.assignplayercolour()
#			$corruptionbar/hbox/meter.value = newvalue
#	$corruptionbar/hidetime.start(2.5)
#	var partstring = ""
#	match race:
#		Playervariables.raceIMP:
#			if Playervariables.get_corruption("tail") == Playervariables.raceIMP:
#				partstring = "body"
#			elif Playervariables.get_corruption("wings") == Playervariables.raceIMP:
#				partstring = "tail"
#			else:
#				partstring = "wings"
#		Playervariables.raceHARPY:
#			if Playervariables.get_corruption("leg") == Playervariables.raceHARPY:
#				partstring = "armright"
#			else:
#				partstring = "leg"
#			$corruptionbar/hbox/icon.set_texture(load("res://Assets/ui/debufficons/armright7.png"))
#			if newvalue >= 100:
#				if Playervariables.corruptiondict.has("leg") and Playervariables.corruptiondict["leg"] == Playervariables.raceHARPY:
#					finish_corruption(Playervariables.raceHARPY,"armright")
#				else:
#					finish_corruption(Playervariables.raceHARPY,"leg")
#		Playervariables.raceNEKO:
#			if Playervariables.get_corruption("ears") == Playervariables.raceNEKO:
#				partstring = "tail"
#			else:
#				partstring = "ears"
#			$corruptionbar/hbox/icon.set_texture(load("res://Assets/ui/debufficons/ears3.png"))
#			if newvalue >= 100:
#				if Playervariables.corruptiondict.has("ears") and Playervariables.corruptiondict["ears"] == Playervariables.raceNEKO:
#					finish_corruption(Playervariables.raceNEKO,"tail")
#				else:
#					finish_corruption(Playervariables.raceNEKO,"ears")
#		Playervariables.raceRAM:
#			partstring = "horns"
#			$corruptionbar/hbox/icon.set_texture(load("res://Assets/ui/debufficons/horns5.png"))
#			if newvalue >= 100:
#				finish_corruption(Playervariables.raceRAM,"horns")
#		Playervariables.raceWOLF:
#			if Playervariables.get_corruption("ears") == Playervariables.raceWOLF:
#				partstring = "tail"
#				if newvalue >= 100:
#					finish_corruption(Playervariables.raceWOLF,"tail")
#			else:
#				if newvalue >= 100:
#				partstring = "ears"
#					finish_corruption(Playervariables.raceWOLF,"ears")
#			if newvalue >= 100:
#				if Playervariables.corruptiondict.has("ears") and Playervariables.corruptiondict["ears"] == Playervariables.raceWOLF:
#					finish_corruption(Playervariables.raceWOLF,"tail")
#				else:
#					finish_corruption(Playervariables.raceWOLF,"ears")
#			$corruptionbar/hbox/icon.set_texture(load("res://Assets/ui/debufficons/ears6.png"))
#		Playervariables.raceKITSUNE:
#			$corruptionbar/hbox/icon.set_texture(load("res://Assets/ui/debufficons/ears4.png"))
#			if Playervariables.get_corruption("ears") == Playervariables.raceKITSUNE:#Playervariables.corruptiondict.has("ears") and Playervariables.corruptiondict["ears"] == Playervariables.raceKITSUNE:
#				partstring = "tail"
#			else:
#				partstring = "ears"
	if race == Playervariables.raceKITSUNE and newvalue >= 100:# and target_part != "bonus":#bonus_corruption == false:
		if target_part == "tail" and Playervariables.get_corruption("tail") == Playervariables.raceKITSUNE:
#		if Playervariables.corruptiondict.has("ears") and Playervariables.corruptiondict["ears"] == Playervariables.raceKITSUNE:
#					finish_corruption(Playervariables.raceKITSUNE,"tail")
			var findenemy = mainscene.find_nearest_enemy(mainscene.playerloc,10,Playervariables.FOXGIRL)
			if findenemy != null:
				findenemy.assignquip(15*100,null,"normal",false,12)
		elif target_part == "ears" and Playervariables.get_corruption("ears") == Playervariables.raceKITSUNE:
#					finish_corruption(Playervariables.raceKITSUNE,"ears")
			var findenemy = mainscene.find_nearest_enemy(mainscene.playerloc,10,Playervariables.FOXGIRL)
			if findenemy != null:
				findenemy.assignquip(14*100,null,"normal",false,12)
#		Playervariables.raceNAMAN:
#			$corruptionbar/hbox/icon.set_texture(load("res://Assets/ui/debufficons/bustsize"+str(Playervariables.playerbustsize+1)+".png"))
#			partstring = "bustsize" + str(Playervariables.playerbustsize+1)
#			if newvalue >= 100 and target_part != "bonus":#bonus_corruption == false:
	elif race == Playervariables.raceNAMAN and newvalue >= 100 and target_part != "bonus":#bonus_corruption == false:
#					if Playervariables.playerbustsize < 3 or mainscene.mapgenlocation == Playervariables.Gallery or Playervariables.curseditemdict[Playervariables.COWBELL] == true:
		Playervariables.playerbustsize = clamp(Playervariables.playerbustsize+1,1,4)
		if mainscene.mapgenlocation != Playervariables.Gallery:
			if Playervariables.playerbustsize > 1:
				save_corruption(Playervariables.raceNAMAN,Playervariables.busttfs.get("size"+str(clamp(Playervariables.playerbustsize,2,4))),"bust")
		else:
			$ConversationV1.rulestalker.force_scroll(1.5)
#							Playervariables.convert_to_bit(Playervariables.StoredTransformations,Playervariables.raceNAMAN,Playervariables.playerbustsize-2,true)
		if Playervariables.consent == true:
			match (Playervariables.playerbustsize+10):# + (Playervariables.playeroutfit[0]*10)):
				12:register_event("Bust size increased.")#My chest is swelling!")
				13:register_event("Bust size increased even further.")#"This outfit wasn't made to fit a girl's proportions. It's starting to rip.")
				14:register_event("Bust size has become incredulous.")#My top is ruined. I'll make do with what I have.")
				2:register_event("There's too much extra weight on my chest...")
				3:register_event("My chest feels so heavy now.")
				4:register_event("I can barely see my feet?!")
				_: pass
#		_:
#			$corruptionbar/hbox/icon.set_texture(null)
#			newvalue = 0
#			print("error finding relevant corruption check dialogue.gd")
	if Playervariables.get_corruption("face") == Playervariables.raceIMP and mainscene.mapgenlocation != Playervariables.Gallery:
		var old_corruption_tracker = corruption_tracker
		corruption_tracker += increase
		if int(corruption_tracker) % 35 < int(old_corruption_tracker) % 35:
			if int(corruption_triggers) % 2 == 0:
				if corruption_triggers <= 4 or int(corruption_triggers) % 4 == 0:#triggers twice then gets twice as hard
					if corruption_triggers <= 16 or int(corruption_triggers) % 8 == 0:#triggers three times then gets twice as hard
						mainscene.awardcurse(207,-1,true)
			else:
				var imp_score = 0
				if corruption_triggers <= 2 or int(corruption_triggers) % 2 == 0:#triggers twice then gets twice as hard
					if corruption_triggers <= 8 or int(corruption_triggers) % 4 == 0:#triggers three times then gets twice as hard
						for key in Playervariables.corruptiondict:
							if Playervariables.corruptiondict[key] == Playervariables.raceIMP:
								imp_score += 1
								if imp_score >= 4:
									mainscene.awardcurse(207,-1,true)
									break
			corruption_triggers += 1
	mainscene.create_corruption_bar(race,oldvalue,newvalue,target_part)#,false)
#	$corruptionbar/hbox/drain.stop()
	mainscene.corruption_particles(race,increase,newvalue >= 100)
	if target_part != "bonus":
		if newvalue >= 100:
			mainscene.updateplayersprite()
			if race == Playervariables.raceNAMAN and mainscene.mapgenlocation == Playervariables.Gallery:
				$ConversationV1.rulestalker.assignplayercolour()
			Playervariables.racecorruptionarray[race] += -100
	#		$corruptionbar/hbox/drain.play("drainbar")
			if mainscene.has_node("CGframe") == true:
				mainscene.get_node("CGframe").checkcorruptions()
			if race == Playervariables.raceNAMAN:
				$ConversationV1.rulestalker.debuffcall(mainscene.playerdebuffarray)
			
	#		$corruptionbar/hbox/fill.pitch_scale = 0.35+(randf()*0.2)
	#		$corruptionbar/hbox/fill.volume_db = -15
	#		$corruptionbar/hbox/fill.play()
		elif increase > 0:
			$ConversationV1.rulestalker.corruption_part_effect(target_part,race)
	#		var animationtarget = $corruptionbar/hbox/drain.get_animation("fillbar")
	#		if $corruptionbar/hbox/drain.is_playing() == true:
	#			animationtarget.track_set_key_value(0,0,$corruptionbar/hbox/meter.value)
	#		else:
	#			animationtarget.track_set_key_value(0,0,oldvalue)
	#		animationtarget.track_set_key_value(0,1,newvalue)
	#		$corruptionbar/hbox/drain.play("fillbar")
	#		if newvalue > 0:
	#			$corruptionbar/hbox/fill.pitch_scale = clamp(0.4 + (pow(newvalue,1.35)*0.01),0.4,5.4)
	#		else:
	#			$corruptionbar/hbox/fill.pitch_scale = 0.4
	#		$corruptionbar/hbox/fill.volume_db = clamp((-32 + increase),-32,-15)
	#		$corruptionbar/hbox/fill.play()
	#	$corruptionbar.visible = true
	#		$ConversationV1.playerscroll(-1)
	elif newvalue >= 100 and Playervariables.get_corruption("colour") == race:#Playervariables.racelockcorruptionarray[race] == true:
		Playervariables.bonusracecorruptionarray[race] = 0#+= -100


func uncorruptplayerwithbar(race,reduce):
	reduce = -abs(reduce)
	var oldvalue = 0
	var oldbonusvalue = 0
	if race != Playervariables.raceHUMAN:
		oldvalue = Playervariables.racecorruptionarray[race]
		oldbonusvalue = Playervariables.bonusracecorruptionarray[race]
	else:
		for testrace in range(Playervariables.racecorruptionarray.size()):
			if max(Playervariables.bonusracecorruptionarray[testrace],Playervariables.racecorruptionarray[testrace]) >= max(oldvalue,oldbonusvalue):
				oldvalue = Playervariables.racecorruptionarray[testrace]
				oldbonusvalue = Playervariables.bonusracecorruptionarray[testrace]
				race = testrace
	var newvalue = oldvalue + reduce#*Playervariables.racemultiplierarray[race]
#			var barvalue = int(newvalue/10)
	var bonusnewvalue = oldbonusvalue + reduce
	Playervariables.racecorruptionarray[race] = clamp(newvalue,0,100)
	Playervariables.bonusracecorruptionarray[race] = clamp(bonusnewvalue,0,100)
	if Playervariables.racecorruptionarray[race] == oldvalue and Playervariables.bonusracecorruptionarray[race] == oldbonusvalue:
		return #don't show the bar if corruption is already 0
#			$corruptionbar/hbox/meter.value = newvalue
	$corruptionbar/hidetime.start(2.5)
	match race:
		Playervariables.raceWOLF:$corruptionbar/hbox/icon.set_texture(load("res://Assets/ui/debufficons/ears6.png"))
		Playervariables.raceNEKO:$corruptionbar/hbox/icon.set_texture(load("res://Assets/ui/debufficons/ears3.png"))
		Playervariables.raceKITSUNE:$corruptionbar/hbox/icon.set_texture(load("res://Assets/ui/debufficons/ears4.png"))
		Playervariables.raceRAM:$corruptionbar/hbox/icon.set_texture(load("res://Assets/ui/debufficons/horns5.png"))
		Playervariables.raceHARPY:$corruptionbar/hbox/icon.set_texture(load("res://Assets/ui/debufficons/armright7.png"))
		Playervariables.raceNAMAN:
			match Playervariables.playerbustsize:
				1:$corruptionbar/hbox/icon.set_texture(load("res://Assets/ui/debufficons/bustsize2.png"))
				2:$corruptionbar/hbox/icon.set_texture(load("res://Assets/ui/debufficons/bustsize3.png"))
				3:$corruptionbar/hbox/icon.set_texture(load("res://Assets/ui/debufficons/bustsize4.png"))
				_:$corruptionbar/hbox/icon.set_texture(load("res://Assets/ui/deployableicons/recover.png"))
		_:
			$corruptionbar/hbox/icon.set_texture(load("res://Assets/ui/deployableicons/recover.png"))
	$corruptionbar/hbox/drain.stop()
	if reduce < 0:
		var animationtarget = $corruptionbar/hbox/drain.get_animation("fillbar")
		if oldbonusvalue < oldvalue:
			$corruptionbar/hbox/meter.texture_progress = load("res://Assets/ui/corruptionbarv3fill.png")
			if $corruptionbar/hbox/drain.is_playing() == true:
				animationtarget.track_set_key_value(0,0,$corruptionbar/hbox/meter.value)
			else:
				animationtarget.track_set_key_value(0,0,oldvalue)
			animationtarget.track_set_key_value(0,1,newvalue)
			if newvalue > 0:
				$corruptionbar/hbox/fill.pitch_scale = clamp(0.4 + (pow(newvalue,1.35)*0.01),0.4,5.4)
			else:
				$corruptionbar/hbox/fill.pitch_scale = 0.4
		else:
			$corruptionbar/hbox/meter.texture_progress = load("res://Assets/ui/corruptionbarv3lock.png")
			if $corruptionbar/hbox/drain.is_playing() == true:
				animationtarget.track_set_key_value(0,0,$corruptionbar/hbox/meter.value)
			else:
				animationtarget.track_set_key_value(0,0,oldbonusvalue)
			animationtarget.track_set_key_value(0,1,bonusnewvalue)
			if bonusnewvalue > 0:
				$corruptionbar/hbox/fill.pitch_scale = clamp(0.4 + (pow(bonusnewvalue,1.35)*0.01),0.4,5.4)
			else:
				$corruptionbar/hbox/fill.pitch_scale = 0.4
		$corruptionbar/hbox/drain.play("fillbar")
		$corruptionbar/hbox/fill.volume_db = clamp((-32 - (reduce/2)),-32,-20)
		$corruptionbar/hbox/fill.play()
	$corruptionbar.visible = true


func _on_hidetime_timeout():
	$corruptionbar/hbox/drain.play("fade")

var screenfiltersarray = []
func screen_filter(num,onoff = true,use_color = null):
#	print("screen filter debug before:" + str(screenfiltersarray))
#	print("screen filter debug before:" + str($screenfilter.get_children()))
#	print("Screenfilter state:"+str(num)+str(onoff))
	if onoff == false:
		var childnum = screenfiltersarray.find(num)
		if childnum != -1 and $screenfilter.get_children().size() > childnum:
			$screenfilter.get_child(childnum).queue_free()
			screenfiltersarray.remove(childnum)
#			print("Successfully queuefreed a filter.")
#		print("screen filter debug after REMOVED:" + str(screenfiltersarray))
#		print("screen filter debug after REMOVED:" + str($screenfilter.get_children()))
	else:
		if screenfiltersarray.find(num) == -1:
			var screeneffect = null
			match num:
				Playervariables.HYPNOSIS:screeneffect = load(Mainpreload.HypnosisScreen).instance()
				Playervariables.CUM:screeneffect = load(Mainpreload.CumScreen).instance()
				Playervariables.POSSESSION:screeneffect = load(Mainpreload.PossessionScreen).instance()
			if screeneffect != null:
				$screenfilter.add_child(screeneffect)
				screenfiltersarray.append(num)
				if use_color != null:
					screeneffect.set_self_modulate(use_color)
#		print("screen filter debug after ADDED:" + str(screenfiltersarray))
#		print("screen filter debug after ADDED:" + str($screenfilter.get_children()))

var pauseconvov2 = false
var currentfocusposition = 0
#var convov2focusposition = 0
var convov2focuspositionmax = 0
var convopositionsarray = [0]
func _on_scrolldown_pressed():
	if currentfocusposition == convov2focuspositionmax:
		if $conversationv2/waittime.time_left > 0:
			savedconvowait = $conversationv2/waittime.get_time_left()
			$conversationv2/waittime.stop()
			$conversationv2/waittime.emit_signal("timeout")
		else:
			$ConversationV1.conversation = false
	else:
#		$conversationv2/scrollconvo.playback_speed = 2
		$ConversationV1.conversation = false
		pauseconvov2 = true
		var convov2focusposition = clamp(currentfocusposition+1,1,convov2focuspositionmax)
		adjust_convo_position(convov2focusposition,2,false,true)
#		$conversationv2/VBoxContainer.get_children()[currentfocusposition-1].get_node("unfocus").playback_speed = 2
#		$conversationv2/VBoxContainer.get_children()[currentfocusposition-1].get_node("unfocus").stop()
#		$conversationv2/VBoxContainer.get_children()[currentfocusposition-1].get_node("unfocus").play("unfocus")
#		var targetmargintop = convopositionsarray[convov2focusposition]#0
##		for intb in range(convov2focusposition-currentfocusposition):
##			targetmargintop += $conversationv2/VBoxContainer.get_children()[currentfocusposition-1+intb].rect_size.y
#		$conversationv2/scrollconvo.stop()
#		$conversationv2/scrollconvo.get_animation("scrollconvo").track_set_key_value(0,0,$conversationv2/VBoxContainer.margin_top)
##		targetmargintop = $conversationv2/scrollconvo.get_animation("scrollconvo").track_get_key_value(0,1)-targetmargintop
#		$conversationv2/scrollconvo.get_animation("scrollconvo").track_set_key_value(0,1,targetmargintop)
#		$conversationv2/scrollconvo.play("scrollconvo")
#		$conversationv2/VBoxContainer.get_children()[convov2focusposition-1].get_node("unfocus").playback_speed = 2
#		$conversationv2/VBoxContainer.get_children()[convov2focusposition-1].get_node("unfocus").stop()
#		$conversationv2/VBoxContainer.get_children()[convov2focusposition-1].get_node("unfocus").play_backwards("unfocus")
#		currentfocusposition = convov2focusposition

func _on_scrollup_pressed():
	if currentfocusposition != 1:
#		$conversationv2/scrollconvo.playback_speed = 2
		$ConversationV1.conversation = false
		pauseconvov2 = true
		var convov2focusposition = clamp(currentfocusposition-1,1,convov2focuspositionmax)
		adjust_convo_position(convov2focusposition,2,false,true)
#		$conversationv2/VBoxContainer.get_children()[currentfocusposition-1].get_node("unfocus").playback_speed = 2
#		$conversationv2/VBoxContainer.get_children()[currentfocusposition-1].get_node("unfocus").stop()
#		$conversationv2/VBoxContainer.get_children()[currentfocusposition-1].get_node("unfocus").play("unfocus")
#		var targetmargintop = convopositionsarray[convov2focusposition]#0
##		for intb in range(currentfocusposition-convov2focusposition):
##			targetmargintop -= $conversationv2/VBoxContainer.get_children()[currentfocusposition-1-intb].rect_size.y
#		$conversationv2/scrollconvo.stop()
#		$conversationv2/scrollconvo.get_animation("scrollconvo").track_set_key_value(0,0,$conversationv2/VBoxContainer.margin_top)
##		targetmargintop = $conversationv2/scrollconvo.get_animation("scrollconvo").track_get_key_value(0,1)-targetmargintop
#		$conversationv2/scrollconvo.get_animation("scrollconvo").track_set_key_value(0,1,targetmargintop)
#		$conversationv2/scrollconvo.play("scrollconvo")
#		$conversationv2/VBoxContainer.get_children()[convov2focusposition-1].get_node("unfocus").playback_speed = 2
#		$conversationv2/VBoxContainer.get_children()[convov2focusposition-1].get_node("unfocus").stop()
#		$conversationv2/VBoxContainer.get_children()[convov2focusposition-1].get_node("unfocus").play_backwards("unfocus")
#		currentfocusposition = convov2focusposition

func adjust_convo_position(convov2focusposition,speed=1,correction = false,scroll=false):
	if correction == false:
		$conversationv2/scrollconvo.playback_speed = speed
		if convov2focuspositionmax > 1:
			var use_node = $conversationv2/VBoxContainer.get_children()[currentfocusposition-1]
			fix_focus(use_node.get_node("unfocus"),false)
			if scroll == true:
				match use_node.charswitch:
					0:pass
					1:$ConversationV1.rulestalker.get_node("positionnode/effects").effectcall("",false)
					_:
						if use_node.charswitch <= $ConversationV1.spritetalker_array.size():
							$ConversationV1.spritetalker_array[use_node.charswitch-1].get_node("positionnode/effects").effectcall("",false)
	if convopositionsarray.size() <= convov2focusposition:
		print("Serious error, tried to adjust convo position but the text box it refers to doesn't exist!!")
		convov2focusposition = convopositionsarray.size() - 1
	if convopositionsarray.size() <= 1:
		if correction == false:
			print("Serious error, tried to adjust convo position but there's not at least 2 array values!!")
		return
	var targetmargintop = convopositionsarray[convov2focusposition]
	$conversationv2/scrollconvo.stop()
	$conversationv2/scrollconvo.get_animation("scrollconvo").track_set_key_value(0,0,$conversationv2/VBoxContainer.margin_top)
	$conversationv2/scrollconvo.get_animation("scrollconvo").track_set_key_value(0,1,targetmargintop)
	$conversationv2/scrollconvo.play("scrollconvo")
	if correction == false:
		var use_node = $conversationv2/VBoxContainer.get_children()[convov2focusposition-1]
		fix_focus(use_node.get_node("unfocus"),true)
		if scroll == true:
			match use_node.charswitch:
				0:pass
				1:$ConversationV1.rulestalker.get_node("positionnode/effects").effectcall("",false)
				_:
					if use_node.charswitch <= $ConversationV1.spritetalker_array.size():
						$ConversationV1.spritetalker_array[use_node.charswitch-1].get_node("positionnode/effects").effectcall("",true)
	currentfocusposition = convov2focusposition

#const unfocuslength = 1#warning! if you change unfocus' length in speechbubble.tscn this will break
func fix_focus(focusnode,backwards):
	var focusposition = 0#unfocuslength
	if focusnode.is_playing():
		focusposition = focusnode.current_animation_position
#	focusnode.playback_speed = speed
	focusnode.stop()
	if backwards == false:
		focusnode.play("unfocus")
		focusnode.seek(focusposition,true)
	else:
		focusnode.play_backwards("unfocus")
	if focusposition > 0:
		focusnode.seek(focusposition,true)

func _on_closeconvo_pressed(from_unscroll = false):
	if $conversationv2.visible == true:
		savedconvowait = $conversationv2/waittime.get_time_left()
	#	$conversationv2/waittime.stop()
		if ready_player_two == true and from_unscroll == false:
			$ConversationV1.unscroll(false,true)
		$ConversationV1.conversation = false
	#	bubblequeue += 1
		lastspeechdict = null
		get_node("/root/Master/SFX/uib").play()
		if ready_player_two == true and convoclosekeepsrules == false:
			Playervariables.keepplayerscrolled = false
		else:
			$ConversationV1.rulestalker.get_node("positionnode/effects").effectcall("reset",false)
		ready_player_two = true
	#		if includerules == true:
	#			$ConversationV1.unscroll(false)
		includerules = false
		$conversationv2/waittime.stop()
		$conversationv2/waittime.emit_signal("timeout")
		$ConversationV1.get_node("rambler").stop()
		$ConversationV1.get_node("rambler").emit_signal("timeout")
		$ConversationV1.handle_dialoguezoom(false)
		$conversationv2.visible = false
		get_node("/root/Master/SFX/ramble").volume_db = $ConversationV1.ramblevolume-20

var controlstate = false
func _process(_delta):
	if $conversationv2.is_visible() == true:
		var localmousepos = get_viewport().get_mouse_position()
#		var neutralanchortop = $conversationv2/controls/controlspeek.get_animation("peek").track_get_key_value(0,0)
		if localmousepos.y < $conversationv2/controls.rect_size.y:
			if controlstate == false:#$conversationv2/controls/controlspeek.is_playing() == true: #or ($conversationv2/controls/controlspeek.is_playing() == false and $conversationv2/controls.anchor_top <= neutralanchortop):
				$conversationv2/controls/controlspeek.play("peek",-1,1,false)
#			elif controlstate == false:
#				$conversationv2/controls/controlspeek.play("peek",-1,1,false)
				controlstate = true
#					elif $conversationv2/controls/controlspeek.is_playing() == true:
#						$conversationv2/controls/controlspeek.play("peek",-1,1,false)
		elif localmousepos.y > $conversationv2/controls.rect_size.y*1.7:
#					if $conversationv2/controls/controlspeek.is_playing() == true:
			if controlstate == true:
				if $conversationv2/controls/controlspeek.is_playing() == true:
					$conversationv2/controls/controlspeek.play("peek",-1,-1,false)
				else:#if controlstate == true:
					$conversationv2/controls/controlspeek.play("peek",-1,-1,true)
				controlstate = false
#			elif $conversationv2/controls.anchor_top != neutralanchortop:
#				$conversationv2/controls/controlspeek.play("peek",-1,-1,true)
	else:
		set_process(false)

func lower_ui_hovered(onoff):
	var speed = $deployablecontainer/lowerui2/deckfade.get_playing_speed()
	if speed == 0:
		if onoff == true:
			if $deployablecontainer/lowerui2/deckfade.playback_speed != 1:
				$deployablecontainer/lowerui2/deckfade.playback_speed = 1
				$deployablecontainer/lowerui2/deckfade.play("deckscroll")
		else:
			if $deployablecontainer/lowerui2/deckfade.playback_speed != -1:
				$deployablecontainer/lowerui2/deckfade.playback_speed = -1
				$deployablecontainer/lowerui2/deckfade.play("deckscroll",-1,1,true)
	elif speed < 0:
		if onoff == true:
			$deployablecontainer/lowerui2/deckfade.playback_speed = 1
	elif onoff == false:
		$deployablecontainer/lowerui2/deckfade.playback_speed = -1
	
	
	if onoff == true and mainscene.option > 1:
		for deployable in $deployablecontainer/deploy.get_children():
			if deployable.held == true:
				var num = deployable.uilocation
				var basepos = $deployablecontainer/deploy.get_global_transform().origin.x
				var localmousepos = get_viewport().get_mouse_position().x
#				print("basepos"+str(basepos)+"...mousepos"+str(localmousepos)+"..."+str(deployablepositionarray))
				for i1 in range(maxitemsize):
					if i1+1 < num:
						if localmousepos-basepos < deployablepositionarray[i1+1]:
							reposition_deployables(num,i1+1)
							return
					else:
						break
				for i2 in range(maxitemsize):
					if maxitemsize-i2 > num:
						if localmousepos-basepos > deployablepositionarray[maxitemsize-i2]:
							reposition_deployables(num,maxitemsize-i2)
							return
					else:
						break
				return

var error_array = []
var lastshow = []
func stack_deck(shuffled = false):
	if shuffled == true or lastshow.size() == 0:
		lastshow = deckarray.duplicate()
		for child in $deployablecontainer/lowerui2/deckbacking/playeritemdeck.get_children():
			child.queue_free()
		for i in range(min(lastshow.size(),5)):
			add_ability_icon(lastshow[i])
	elif deckarray.size() > 0:
		while lastshow[0] != deckarray[0]:
			var childrensize = clamp($deployablecontainer/lowerui2/deckbacking/playeritemdeck.get_children().size(),0,5)
			if childrensize > 0:
				$deployablecontainer/lowerui2/deckbacking/playeritemdeck.get_children()[-1].free()
				childrensize -= 1
			else:
				stack_deck(true)
				return
			lastshow.remove(0)
			if deckarray.size() > childrensize:
				add_ability_icon(deckarray[childrensize])
			if deckarray.size() == 0:
				break
			elif lastshow.size() == 0:
				lastshow = deckarray.duplicate()
		var diff = deckarray.size() - lastshow.size()
		if diff > 0:
			var startsize = lastshow.size()#-1
			for i in range (diff):
				lastshow.append(deckarray[startsize+i])
				if $deployablecontainer/lowerui2/deckbacking/playeritemdeck.get_children().size() < 5:
					add_ability_icon(lastshow[startsize+i])
	elif $deployablecontainer/lowerui2/deckbacking/playeritemdeck.get_children().size() > 0:
		for child in $deployablecontainer/lowerui2/deckbacking/playeritemdeck.get_children():
			child.queue_free()
	$deployablecontainer/lowerui2/deckbacking.anchor_right = 0.5 - (5-clamp($deployablecontainer/lowerui2/deckbacking/playeritemdeck.get_children().size(),1,5))*0.1
	if deckarray.size() == 0:
		$deployablecontainer/lowerui2/decknum.set_text("")
	else:
		$deployablecontainer/lowerui2/decknum.set_text(str(deckarray.size()-error_array.size()))
func add_ability_icon(id):
	var newimg = TextureRect.new()
	$deployablecontainer/lowerui2/deckbacking/playeritemdeck.add_child(newimg)
	$deployablecontainer/lowerui2/deckbacking/playeritemdeck.move_child(newimg,0)
	if Playervariables.playerinventory2darray.size() > id:
		if Playervariables.curseditemdict[Playervariables.ROPEGAG] == true:
			newimg.texture = load("res://Assets/abilityicons/Error.png")
		else:
			newimg.texture = load("res://Assets/abilityicons/"+Playervariables.get("Move"+str(Playervariables.playerinventory2darray[id][0]))["name"]+".png")
	else:
		print("Error at dialogue.gd. Passed an id of an item that isn't in the inventory.:" +str(id)+"...And inventoryarray:"+str(Playervariables.playerinventory2darray))
		if error_array.find(id) == -1:
			error_array.append(id)
#	newimg.expand = true
	newimg.rect_min_size = itemsize*deployablescale#deployablesize
	newimg.expand = true
#	var imgtxtratio = newimg.texture.get_size().x
#	print(imgtxtratio)
#	if imgtxtratio > 127:
#		newimg.rect_scale.x = 127/clamp(imgtxtratio,50,400)
#		print(newimg.rect_scale.x)
	

func forfeit(): #accessed by Settingscreen.gd 's forfeit button
	mainscene.bad_end(mainscene.badendspec.FORFEIT)

var alternate_beg_int = 0
func remove_specific_curses(num,all = true):
	var removearray = []
	for item in Playervariables.playerinventory2darray:
		if item[0] == num:
			removearray.append(item[1])
			if all == false:
				break
	removearray.sort()
	removearray.invert()
	for item in removearray:
		if deckarray.find(item) > -1:
			deckarray.erase(item)
		else:
			for deployable in $deployablecontainer/deploy.get_children():
				if deployable.options[1] == item:
					deployable.used()
		if Playervariables.playerinventory2darray[item].size() > 3:
			Playervariables.playerinventory2darray[item][3] = 0
	if removearray.size() > 0:
#		Playervariables.identify_inventory()
		stack_deck(true)

var curse_decay = false
func remove_curses():
	var removearray = []
	var helditemnums = []
	for deployable in $deployablecontainer/deploy.get_children():
		helditemnums.append(deployable.options[1])
	for item in Playervariables.playerinventory2darray:
		if item[2] == Playervariables.CURSE:
			if curse_decay == true and (deckarray.find(item[1]) > -1 or helditemnums.find(item[1])):
				if item.size() > 3:
					item[3] = item[3] - 1
			if item.size() < 4 or item[3] <= 0:
#				print("removing item with moveid: "+str(item[0]))
				removearray.append(item[1])
	removearray.sort()
	removearray.invert()
	if removearray.size() > 0:
		for i in removearray:
#			if Playervariables.playerinventory2darray[i].size() > 3:
#				if Playervariables.playerinventory2darray[i][3] > 1:
#					Playervariables.playerinventory2darray[i][3] = Playervariables.playerinventory2darray[i][3] - 1
#				else:
			match Playervariables.playerinventory2darray[i][0]:
#				104:
#					$ConversationV1.get("rulestalker").remove_seal(-1)
				203:
					if Playervariables.corruptiondict.has("eyes") and Playervariables.corruptiondict["eyes"] == Playervariables.raceRAM and Playervariables.CurrentClass != Playervariables.raceRAM:
						Playervariables.corruptiondict.erase("eyes")
						Playervariables.baseplayercolourarray[0] = Playervariables.truecolourarray[0]
#			Playervariables.retain_deck_array.erase(removearray[i][1])
			Playervariables.playerinventory2darray.remove(i)
		Playervariables.identify_inventory()
func remove_items():
	var combined_array = Playervariables.autodiscardarrayitem + Playervariables.autodiscardarrayinnate
	combined_array.sort()
	combined_array.invert()
	if combined_array.size() > 0:
		for i in combined_array:
#			Playervariables.retain_deck_array.erase(Playervariables.playerinventory2darray[i][1])
			Playervariables.playerinventory2darray.remove(i)
	else:
		print("ERROR: Tried to remove items but no array entries?")
		return
	Playervariables.autodiscardarrayitem = []
	Playervariables.autodiscardarrayinnate = []
	Playervariables.identify_inventory()

var last_updated_health = -1
func update_health():
	if Playervariables.playerresistance != last_updated_health:
		last_updated_health = Playervariables.playerresistance
		$deployablecontainer/healthbox/resleft.set_text(str(ceil(Playervariables.playerresistance)))
		$ConversationV1/dialogueart/healthnum.set_text(str(ceil(Playervariables.playerresistance)))
		var healthpercent = Playervariables.playerresistance/clamp(Playervariables.maxresistance,1,9999)
		if healthpercent >= 1.0:
			$deployablecontainer/healthbox/resleft.add_color_override("font_color",Color(0.6,0.6,1))
			$deployablecontainer/healthbox/resleft.use_parent_material = true
		elif healthpercent > 0.25:
			$deployablecontainer/healthbox/resleft.add_color_override("font_color",Color(1, 0.65098, 0.823529))
			$deployablecontainer/healthbox/resleft.use_parent_material = true
		else:
			$deployablecontainer/healthbox/resleft.add_color_override("font_color",Color(1, 0.65098, 0.823529))
			$deployablecontainer/healthbox/resleft.use_parent_material = false

var reload = 0
var reload_curses = 0
func check_reset():
	if Playervariables.awaitingconversation == "":
		if Playervariables.resetanimation == true:
			Playervariables.resetanimation = false
			var reseteffect = load(Mainpreload.ResetEffect).instance()
			if mainscene.mapgenlocation == Playervariables.DarkRoom:
				reseteffect.modulate = Color(0,0,0,1)
			reseteffect.get_child(0).rect_scale = Vector2(deployablescale,deployablescale)
			reseteffect.anchor_top = 0.8
			reseteffect.anchor_bottom = 0.8
			reseteffect.anchor_left = 0.2
			reseteffect.anchor_right = 0.2
			add_child(reseteffect)
			if Playervariables.CurrentClass == Playervariables.raceHARPY:
				get_tree().call_group("consumableslot","force_consumable_switch",16)
			register_event("Inventory reset to class defaults.",[],true)
		elif reload > 0:
			var reloadeffect = load(Mainpreload.ReloadEffect).instance()
			if mainscene.mapgenlocation == Playervariables.DarkRoom:
				reloadeffect.modulate = Color(0,0,0,1)
			reloadeffect.get_child(0).rect_scale = Vector2(deployablescale,deployablescale)
			$ConversationV1.add_child(reloadeffect)
			reloadeffect.load_tabs(reload,reload_curses)

enum indi{QADES,FEATURE,SUB,RIGHTCLICK}
var ui_indicator_array = []
func handle_ui_indicator(indicatornode,subtype = indi.QADES,color = null,makeiftrue = true):
#	if ui_indicator != null:
#		ui_indicator.queue_free()
#		ui_indicator = null
	if makeiftrue == true:
		var indicator = load(Mainpreload.Indicator).instance()
		var controlnode = Control.new()
		controlnode.anchor_left = 0.5
		controlnode.anchor_right = 0.5
		controlnode.margin_top = 50
		controlnode.add_child(indicator)
		indicator.z_index = 1
		indicatornode.add_child(controlnode)
		var ui_indicator = controlnode
		ui_indicator_array.append(ui_indicator)
		match subtype:
			indi.RIGHTCLICK:
				indicator.play("rightclick")
				if Playervariables.touchscreenmode == true:
					indicator.get_node("touchscreen").visible = true
					indicator.play("tap")
					indicator.get_child(0).playback_speed = 0.6
				indicator.get_child(0).play("float")
			indi.FEATURE,indi.SUB:
				indicator.position = Vector2(0,95)
				if subtype == indi.FEATURE:
					indicator.play("feature")
					indicator.get_child(0).play("indicator")
				else:
					indicator.play("sub")
			indi.QADES,_:
				indicator.play("default")
				indicator.get_child(0).play("indicator")
		if color != null:
			indicator.set_modulate(color)
		
		var yratio = get_viewport_rect().size.y/Playervariables.basescreensize.y
		var xratio = get_viewport_rect().size.x/Playervariables.basescreensize.x
		var minratio = min(yratio,xratio)
#		ui_indicator.get_child(0).scale = Vector2(minratio,minratio)
		ui_indicator.rect_scale = Vector2(minratio,minratio)

func save_deck():
	Playervariables.retain_deck_array = []
	for deployable in $deployablecontainer/deploy.get_children():
		if deployable.options[0] > 0 and deployable.consumableslot == false:
			Playervariables.retain_deck_array.append(deployable.options[1])
	Playervariables.retain_deck_array += deckarray


const semi_visible_nodes = ["deployablecontainer","upperui"]
var semivisibility = true
func some_visible(onoff):
	if onoff != semivisibility:
		for node in semi_visible_nodes:
			if get_node(node).visible == onoff:
				print("tried to some-visible but something was toggled to the wrong visible. IGNORING SOME_VISIBLE FUNCTION NOW. RETURNING.")
				return
		for node in semi_visible_nodes:
			get_node(node).visible = onoff
		semivisibility = onoff


func _on_AnimationPlayer_animation_finished(_anim_name):
	update_health()
