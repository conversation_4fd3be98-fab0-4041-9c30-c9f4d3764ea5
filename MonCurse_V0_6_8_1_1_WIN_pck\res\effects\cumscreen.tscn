[gd_scene load_steps=4 format=2]

[ext_resource path="res://effects/cum screen.png" type="Texture" id=1]
[ext_resource path="res://effects/cumscreen.gd" type="Script" id=2]

[sub_resource type="Animation" id=1]
resource_name = "screeneffect"
length = 6.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.4, 1, 6 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.392157 ), Color( 1, 1, 1, 0.196078 ), Color( 1, 1, 1, 0.392157 ), Color( 1, 1, 1, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:anchor_left")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1, 6 ),
"transitions": PoolRealArray( 0.5, 2, 1 ),
"update": 0,
"values": [ -0.3, 0.0, -0.15 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:anchor_right")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1, 6 ),
"transitions": PoolRealArray( 0.5, 2, 1 ),
"update": 0,
"values": [ 1.3, 1.0, 1.15 ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:anchor_top")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 1, 6 ),
"transitions": PoolRealArray( 0.5, 2, 1 ),
"update": 0,
"values": [ -0.3, 0.0, -0.15 ]
}
tracks/4/type = "value"
tracks/4/path = NodePath(".:anchor_bottom")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 1, 6 ),
"transitions": PoolRealArray( 0.5, 2, 1 ),
"update": 0,
"values": [ 1.3, 1.0, 1.15 ]
}

[node name="TextureRect" type="TextureRect"]
modulate = Color( 1, 1, 1, 0.392157 )
anchor_left = -0.3
anchor_top = -0.3
anchor_right = 1.3
anchor_bottom = 1.3
texture = ExtResource( 1 )
expand = true
script = ExtResource( 2 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
autoplay = "screeneffect"
anims/screeneffect = SubResource( 1 )

[connection signal="animation_finished" from="AnimationPlayer" to="." method="_on_AnimationPlayer_animation_finished"]
