[gd_scene load_steps=7 format=2]

[ext_resource path="res://Assets/enemyselector.png" type="Texture" id=1]
[ext_resource path="res://Assets/enemyselectortext.png" type="Texture" id=2]
[ext_resource path="res://font/alagard.ttf" type="DynamicFontData" id=3]
[ext_resource path="res://Assets/materials/enemyreticule.shader" type="Shader" id=4]

[sub_resource type="ShaderMaterial" id=2]
shader = ExtResource( 4 )
shader_param/shadow_color = Color( 0.537255, 0.27451, 0.552941, 1 )
shader_param/sine_time_scale = 2.0
shader_param/sine_offset_scale = Vector2( 0.7, 0.7 )

[sub_resource type="DynamicFont" id=3]
size = 28
use_filter = true
font_data = ExtResource( 3 )

[node name="enemyreticule" type="Sprite"]
modulate = Color( 1, 1, 1, 0.509804 )
material = SubResource( 2 )
texture = ExtResource( 1 )

[node name="Sprite" type="Sprite" parent="."]
use_parent_material = true
position = Vector2( 50, -63 )
texture = ExtResource( 2 )

[node name="Label" type="Label" parent="Sprite"]
use_parent_material = true
margin_left = -18.0
margin_top = -59.0
margin_right = 70.0
margin_bottom = -32.0
grow_horizontal = 2
grow_vertical = 0
custom_fonts/font = SubResource( 3 )
text = "ERROR"
align = 1

[node name="Sprite2" type="Sprite" parent="."]
use_parent_material = true
position = Vector2( -50, 63 )
texture = ExtResource( 2 )
flip_h = true
flip_v = true

[node name="Label2" type="Label" parent="Sprite2"]
use_parent_material = true
margin_left = -73.0
margin_top = 36.0
margin_right = 15.0
margin_bottom = 63.0
grow_horizontal = 2
grow_vertical = 0
custom_fonts/font = SubResource( 3 )
text = "HP: A"
align = 1
__meta__ = {
"_edit_use_anchors_": false
}
