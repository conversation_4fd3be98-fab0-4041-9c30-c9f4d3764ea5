[gd_scene load_steps=4 format=2]

[ext_resource path="res://font/Audiowide-Regular.ttf" type="DynamicFontData" id=1]
[ext_resource path="res://Assets/ui/blockmarker light.png" type="Texture" id=2]

[sub_resource type="DynamicFont" id=1]
size = 46
outline_size = 4
outline_color = Color( 0, 0, 0, 1 )
font_data = ExtResource( 1 )

[node name="TextureRect" type="TextureRect"]
margin_right = 64.0
margin_bottom = 64.0
texture = ExtResource( 2 )

[node name="Label" type="Label" parent="."]
anchor_right = 1.0
anchor_bottom = 1.0
custom_colors/font_color = Color( 1, 1, 1, 1 )
custom_fonts/font = SubResource( 1 )
text = "2"
align = 1
valign = 1
