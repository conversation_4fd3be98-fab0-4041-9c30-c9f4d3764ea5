[gd_scene load_steps=7 format=2]

[ext_resource path="res://effects/corruption particle.png" type="Texture" id=1]
[ext_resource path="res://effects/corruption_particle.gd" type="Script" id=2]

[sub_resource type="CanvasItemMaterial" id=12]
particles_animation = true
particles_anim_h_frames = 9
particles_anim_v_frames = 1
particles_anim_loop = true

[sub_resource type="Gradient" id=13]
offsets = PoolRealArray( 0, 0.326316, 0.631579, 1 )
colors = PoolColorArray( 0.191406, 0.160751, 0.188772, 1, 0.815812, 0.687805, 0.921875, 1, 0.638983, 0.43042, 0.640625, 0.756863, 0.85297, 0.762207, 0.875, 0 )

[sub_resource type="GradientTexture" id=14]
gradient = SubResource( 13 )

[sub_resource type="ParticlesMaterial" id=15]
emission_shape = 1
emission_sphere_radius = 100.0
flag_disable_z = true
spread = 180.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 80.0
initial_velocity_random = 0.6
orbit_velocity = -0.1
orbit_velocity_random = 0.0
color_ramp = SubResource( 14 )
anim_speed = 1.0
anim_speed_random = 1.0

[node name="Particles2D" type="Particles2D"]
material = SubResource( 12 )
emitting = false
amount = 12
lifetime = 5.0
one_shot = true
preprocess = 1.0
explosiveness = 0.7
process_material = SubResource( 15 )
texture = ExtResource( 1 )
script = ExtResource( 2 )

[node name="Timer" type="Timer" parent="."]
wait_time = 6.0
one_shot = true
autostart = true

[connection signal="timeout" from="Timer" to="." method="_on_Timer_timeout"]
