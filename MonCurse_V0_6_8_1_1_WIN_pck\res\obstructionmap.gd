extends TileMap

const tilesize = 128
var health = 3
var maxhealth = 3
var inactive = false
var dimension = 2 #2 for 2x2, 3 for 3x3, etc... 1 is 1x1.
var damagemaparray = PoolIntArray([0,6,7])
var specialID = 0
var loc = Vector2(0,0)
var healthsprite = null
enum{ALPHA=1,BETA=2,GAMMA=3,DELTA=4,TURBINEUP=5,TURBINEDOWN=6,TURBINERIGHT=7,TURBINELEFT=8}
func obstructiontype(dimensionnum = 2,ID = 0):
	dimension = dimensionnum
	if dimensionnum == 3:
		health = 9
		maxhealth = 10
		damagemaparray = PoolIntArray([5,9,10])
#		position.y += 24 #I made it so all the 3x3 textures are offset by 24 within the tilemap tileset itself.
	elif dimensionnum == 1:
		if ID > 0:
			specialID = ID
			add_to_group("Attackers")
#			timer = Timer.new()
#			add_child(timer)
#			timer.one_shot = true
			match ID:
				1,3:
					health = 2
					maxhealth = 2
				2: 
					health = 4
					maxhealth = 4
				4:
					health = 1
					maxhealth = 1
				5,6,7,8:
					add_to_group("Turnends")
					health = 5
					maxhealth = 5
					if ID == 5: #UP
						attackarray = [loc+Vector2(0,0),loc+Vector2(0,-1),loc+Vector2(0,-2),loc+Vector2(0,-3),loc+Vector2(0,-4)]
						hardcodeddirection = 2
					elif ID == 6: #DOWN
						attackarray = [loc+Vector2(0,0),loc+Vector2(0,1),loc+Vector2(0,2),loc+Vector2(0,3),loc+Vector2(0,4)]
						hardcodeddirection = 0
					elif ID == 7: #RIGHT
						attackarray = [loc+Vector2(0,0),loc+Vector2(1,0),loc+Vector2(2,0),loc+Vector2(3,0),loc+Vector2(4,0)]
						hardcodeddirection = 3
					elif ID == 8: #LEFT
						attackarray = [loc+Vector2(0,0),loc+Vector2(-1,0),loc+Vector2(-2,0),loc+Vector2(-3,0),loc+Vector2(-4,0)]
						hardcodeddirection = 1
			if healthsprite != null and health <= 4 and health >= 0:
				healthsprite.texture = load("res://Assets/ui/"+str(health)+".png")
				healthsprite.z_index = 1

#var timer = null
var attackarray = []
var hardcodeddirection = 0 #2 is upwards, 0 is downwards, 1 is probably right and 3 is probably left?
var totaldamagehealed = 0
func _get_ready():
#	if timer != null:
#		timer.start(0.3)
#		yield(timer,"timeout")
	if health <= 0:
		remove_from_group("Attackers")
		return
	match specialID:
		1:
			if health < maxhealth:
				totaldamagehealed -= health
				health = clamp(health+1,0,maxhealth) #At Start of End of Turn: Obstructing Crate Regenerated 1 Health
				totaldamagehealed += health
				get_parent().get_parent().create_sparkle((loc+Vector2(0.5,0.5))*tilesize,0,true)
				if healthsprite != null:
					if totaldamagehealed % 10 == 0:
						get_parent().get_parent().register_event_via_main("Hit Crate Alpha with Dagger, then a regular attack.")
#					else:
					var newhit = load(Mainpreload.Hitmarker).instance()
					self.add_child(newhit)
					newhit.position = (loc+Vector2(0.5,0.5))*tilesize
					newhit.blocked_effect(1,1)
			if healthsprite != null and health <= 9 and health >= 0:
				healthsprite.texture = load("res://Assets/ui/"+str(health)+".png")
		2: 
			if health < maxhealth:
				totaldamagehealed -= health
				health = clamp(health+1,0,maxhealth) #At Start of End of Turn: Obstructing Crate Regenerated 1 Health
				totaldamagehealed += health
				get_parent().get_parent().create_sparkle((loc+Vector2(0.5,0.5))*tilesize,0,true)
				if healthsprite != null:
					if totaldamagehealed % 16 == 0:
						get_parent().get_parent().register_event_via_main("Hit Crate Beta with Axe, then Dagger, then a regular attack.")
					var newhit = load(Mainpreload.Hitmarker).instance()
					self.add_child(newhit)
					newhit.position = (loc+Vector2(0.5,0.5))*tilesize
					newhit.blocked_effect(1,1)
			if healthsprite != null and health <= 9 and health >= 0:
				healthsprite.texture = load("res://Assets/ui/"+str(health)+".png")
		3,4:
			remove_from_group("Attackers")
		5,6,7,8:
			remove_from_group("Attackers")
#			get_parent().get_parent().call("attacking",attackarray,Vector2(Playervariables.FORCE,0),self,[Vector2(Playervariables.KNOCKBACK,4)],[],hardcodeddirection,"Turbine Blast")
func _end_turn():
	if health <= 0:
		remove_from_group("Turnends")
		return
	if specialID in [5,6,7,8]:
		get_parent().get_parent().call("attacking",attackarray,Vector2(Playervariables.FORCE,0),self,[Vector2(Playervariables.KNOCKBACK,4)],[],hardcodeddirection,"Turbine Blast")

func damagebush(localloc,direction,typedamage):
	if inactive == false:
#		if specialID > 4 and specialID < 9:
#			return []#turbines, immune to damage
		var damage = int(typedamage.y)
		$animations.stop()
		$animations.play("shake") #check animations, there's also a 'destroyed' animation that's unused
		if specialID == 3 and typedamage.x == Playervariables.FORCE:
			if typedamage.x == Playervariables.FORCE:
				damage = 0
				totaldamagehealed += 1
				get_parent().get_parent().create_sparkle((loc+Vector2(0.5,0.5))*tilesize,0,true)
				if totaldamagehealed % 20 == 0:
					get_parent().get_parent().register_event_via_main("Hit crate Delta with a Torch.")
				var newhit = load(Mainpreload.Hitmarker).instance()
				self.add_child(newhit)
				newhit.position = (loc+Vector2(0.5,0.5))*tilesize
				newhit.blocked_effect(0)
			elif typedamage.x == Playervariables.PRECISION:
				damage = 0
				totaldamagehealed += 1
				get_parent().get_parent().create_sparkle((loc+Vector2(0.5,0.5))*tilesize,0,true)
				if totaldamagehealed % 20 == 0:
					get_parent().get_parent().register_event_via_main("Hit crate Delta with a Torch.")
				var newhit = load(Mainpreload.Hitmarker).instance()
				self.add_child(newhit)
				newhit.position = (loc+Vector2(0.5,0.5))*tilesize
				newhit.blocked_effect(0)
		if specialID == 4:
			if abs(direction.x)+0.1 > abs(direction.y):
				damage = 0
				totaldamagehealed += 1
				get_parent().get_parent().create_sparkle((loc+Vector2(0.5,0.5))*tilesize,0,true)
				if totaldamagehealed % 20 == 0:
					get_parent().get_parent().register_event_via_main("Stand on the platform, then use the scimitar down-right.")
				elif totaldamagehealed % 2 == 1:
					get_parent().get_parent().register_event_via_main("Crate Delta resisted damage taken against its sides.")
			elif typedamage.x == Playervariables.ENERGY:
				damage = 0
				totaldamagehealed += 1
				get_parent().get_parent().create_sparkle((loc+Vector2(0.5,0.5))*tilesize,0,true)
				if totaldamagehealed % 20 == 0:
					get_parent().get_parent().register_event_via_main("Stand on the platform, then hit the bottom-right crate Delta.")
				elif totaldamagehealed % 4 == 1:
					get_parent().get_parent().register_event_via_main("Crate Delta resisted ENERGY damage.")
		if damage > 0:
			var locallocv = (localloc+Vector2(0.5,0.5))*tilesize
			for _i in range(damage):
				get_parent().get_parent().create_sparkle(locallocv,1,true)
			if specialID == 0:
				var newleaf = load(Mainpreload.LeafEffect).instance()
				add_child(newleaf)
				newleaf.position = locallocv
				newleaf.hitforce(direction,damage)
				newleaf.emitting = true
			var hitcell = localloc
			hitcell = correcthitcell(hitcell)
			if dimension == 3:
				health += -damage
				var healthfraction = float(health)/float(maxhealth)
				if healthfraction < 0.7:
					if healthfraction < 0.35:
						if healthfraction <= 0:
							self.set_cellv(hitcell,damagemaparray[2])
							$animations.play("destroyed")
							inactive = true
							var realhitcell = hitcell+Vector2(1,2)
							if get_parent().get_parent().features3darray[realhitcell.y][realhitcell.x].find(8) != -1: #only makes an indicator if there's a cursed item here.
								get_parent().get_parent().make_indicator(realhitcell)
							return bushdamagedimensions(hitcell,true)
						else:
							self.set_cellv(hitcell,damagemaparray[2])
							return bushdamagedimensions(hitcell,false)
					else:
						self.set_cellv(hitcell,damagemaparray[1])
						return bushdamagedimensions(hitcell,false)
			elif dimension == 2:
				match self.get_cellv(hitcell):
					0:
						if damage == 1: 
							self.set_cellv(hitcell,6)
						else: 
							self.set_cellv(hitcell,7)
							return bushdamagedimensions(hitcell,false)
					6:
						self.set_cellv(hitcell,7)
						return bushdamagedimensions(hitcell,false)
					_:
						print("again, obstructionmap.gd error, this time with get cell ID.")
			elif dimension == 1:
				health += -damage
				if health > 0:
#					return bushdamagedimensions(hitcell,false)
					if healthsprite != null and health <= 9 and health >= 0:
						healthsprite.texture = load("res://Assets/ui/"+str(health)+".png")
				else:
					if healthsprite != null:
						healthsprite.queue_free()
						healthsprite = null
					$animations.play("destroyed")
					inactive = true
					return bushdamagedimensions(hitcell,true)
					
		return []

func correcthitcell(hitcell):
	for i1 in range(dimension):
		for i2 in range(dimension):
			if self.get_cellv(hitcell-Vector2(i1,i2)) > -1:
				return hitcell-Vector2(i1,i2)
	print("obstructionmap.gd correcthitcell isn't working properly, apparently...")
	return hitcell

func bushdamagedimensions(hitcell,destroyed):
	var bushdamagearray = []
	if dimension != 3 or destroyed == true:
		for i2 in range(dimension):
			for i1 in range (dimension):
				bushdamagearray.append(hitcell+Vector2(i1,i2))
	else:
		match self.get_cellv(hitcell):
			5:
				pass
			9:
				for i1 in range (dimension):
					bushdamagearray.append(hitcell+Vector2(i1,0))
			10:
				for i2 in range(dimension):
					for i1 in range (dimension):
						if i1 != 1 or i2 != 2:
							bushdamagearray.append(hitcell+Vector2(i1,i2))
	return bushdamagearray
