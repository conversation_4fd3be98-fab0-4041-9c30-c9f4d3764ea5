[gd_scene load_steps=8 format=2]

[ext_resource path="res://font/OpenSans-ExtraBold.ttf" type="DynamicFontData" id=1]
[ext_resource path="res://Assets/blacksquare.png" type="Texture" id=2]
[ext_resource path="res://Assets/ui/qadesbutton.png" type="Texture" id=3]
[ext_resource path="res://Assets/ui/qadesnametag.png" type="Texture" id=4]
[ext_resource path="res://specialmenu.gd" type="Script" id=5]
[ext_resource path="res://button.gd" type="Script" id=6]

[sub_resource type="DynamicFont" id=1]
outline_size = 3
outline_color = Color( 0.294118, 0.294118, 0.294118, 1 )
extra_spacing_top = -3
extra_spacing_bottom = -3
font_data = ExtResource( 1 )

[node name="specialmenu" type="CanvasLayer"]
layer = 123

[node name="specialmenu" type="TextureRect" parent="."]
self_modulate = Color( 1, 1, 1, 0.901961 )
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = -5.0
margin_top = -5.0
margin_right = 5.0
margin_bottom = 5.0
texture = ExtResource( 2 )
expand = true
script = ExtResource( 5 )

[node name="TextureButton2" type="TextureButton" parent="specialmenu"]
self_modulate = Color( 0.411765, 0.45098, 0.54902, 1 )
anchor_left = 0.4
anchor_top = 0.3
anchor_right = 0.6
anchor_bottom = 0.4
texture_normal = ExtResource( 4 )
expand = true
script = ExtResource( 6 )

[node name="Label3" type="Label" parent="specialmenu/TextureButton2"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_fonts/font = SubResource( 1 )
text = "Revert to Windowed"
align = 1
valign = 1
autowrap = true

[node name="TextureButton" type="TextureButton" parent="specialmenu"]
self_modulate = Color( 0.411765, 0.45098, 0.54902, 1 )
anchor_left = 0.35
anchor_top = 0.4
anchor_right = 0.65
anchor_bottom = 0.6
texture_normal = ExtResource( 3 )
expand = true
script = ExtResource( 6 )
__meta__ = {
"_edit_use_anchors_": false
}

[node name="Label" type="Label" parent="specialmenu/TextureButton"]
anchor_left = 0.07
anchor_top = 0.15
anchor_right = 0.93
anchor_bottom = 0.6
custom_fonts/font = SubResource( 1 )
text = "Please press this to confirm,
else it will revert back in:"
align = 1
autowrap = true

[node name="Label2" type="Label" parent="specialmenu/TextureButton/Label"]
anchor_top = 1.05
anchor_right = 1.0
anchor_bottom = 1.05
custom_fonts/font = SubResource( 1 )
text = "10"
align = 1
valign = 1
autowrap = true

[node name="Timer" type="Timer" parent="specialmenu"]

[connection signal="pressed" from="specialmenu/TextureButton2" to="specialmenu" method="_on_TextureButton2_pressed"]
[connection signal="pressed" from="specialmenu/TextureButton" to="specialmenu" method="_on_TextureButton_pressed"]
[connection signal="timeout" from="specialmenu/Timer" to="specialmenu" method="_on_Timer_timeout"]
