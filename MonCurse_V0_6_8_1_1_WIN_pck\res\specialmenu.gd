extends TextureRect

var timer = 10
func _ready():
	if Playervariables.modarray[Playervariables.mods.TRANSLATION] == true:
		if Playervariables.default_font_dict["font_attack_announce"] != null:
			$TextureButton2/Label3.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_announce"].font_data
		if Playervariables.transdict.has("specialmenu"):
			if Playervariables.transdict["specialmenu"].has("fullscreencancel"):
				$TextureButton2/Label3.set_text(Playervariables.transdict["specialmenu"]["fullscreencancel"])
			if Playervariables.transdict["specialmenu"].has("fullscreenconfirm"):
				$TextureButton/Label.set_text(Playervariables.transdict["specialmenu"]["fullscreenconfirm"])
	elif Playervariables.touchscreenmode == true:
		$TextureButton2/Label3.set_text("Revert to Full-Res")
	$Timer.start(1)
	timer = 15
	$TextureButton/Label/Label2.set_text(str(timer))
	var _c = get_tree().root.connect("size_changed", self, "_on_viewport_size_changed")
	_on_viewport_size_changed()

var firstrun = true
var recentsizechange = false
func _on_viewport_size_changed():
	if recentsizechange == false:
		recentsizechange = true #this makes it so it only updates the viewport every 4 frames
		if firstrun == false:
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
		else:
			firstrun = false
		recentsizechange = false
		var newsize = get_viewport_rect().size
#		var proportiony = (newsize.length()/600)
#		var proportionfull = (newsize.length()/1024)
		var proportionx = (newsize.x/1024)
		if Playervariables.modarray[Playervariables.mods.TRANSLATION] == true and Playervariables.default_font_dict["font_attack_announce"] != null:
			proportionx = proportionx*Playervariables.default_font_dict["font_attack_announce_size"]
		$TextureButton2/Label3.get("custom_fonts/font").size = int(16*proportionx)
		$TextureButton2/Label3.get("custom_fonts/font").outline_size = int(3*proportionx)


func _on_TextureButton_pressed():
	$Timer.stop()
	get_parent().queue_free()
func _on_Timer_timeout():
	timer += -1
	if Playervariables.fullscreen == true or (Playervariables.touchscreenmode == true and Playervariables.batterysaver):
		if timer > 0:
			$TextureButton/Label/Label2.set_text(str(timer))
		else:
			$Timer.stop()
			if Playervariables.touchscreenmode == true:
				Playervariables.set_battery_saver_size(false)
			else:
				OS.set_window_fullscreen(false)
				Playervariables.fullscreen = false
			get_tree().call_group("fullscreencheck","_fullscreencheckboxcheck")
			get_parent().queue_free()
	else:
		$Timer.stop()
		get_parent().queue_free()
func _on_TextureButton2_pressed():
	$Timer.stop()
	if Playervariables.touchscreenmode == true:
		Playervariables.set_battery_saver_size(false)
	else:
		OS.set_window_fullscreen(false)
		Playervariables.fullscreen = false
	get_tree().call_group("fullscreencheck","_fullscreencheckboxcheck")
	get_parent().queue_free()
