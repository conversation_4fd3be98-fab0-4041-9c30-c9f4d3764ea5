extends AnimatedSprite


# Declare member variables here. Examples:
# var a = 2
# var b = "text"

#enum {NONE,MAIN,CONVO}
#var parentnode = null

# Called when the node enters the scene tree for the first time.
func _ready():
	add_to_group("Turnends")
	add_to_group("Attackers")
	add_to_group("Torches")
#	if "heavyattack" in get_parent().get_parent():
#		parentnode = MAIN

func _end_turn():
	self.frame = 1
	$enemyturn.start(0.3+Playervariables.gameslowness)
	yield($enemyturn,"timeout")
	if "heavyattack" in get_parent().get_parent() and get_parent().get_parent().heavyattack == true:
		if self.frame == 1:
			self.frame = 2

func _get_ready():
	if "heavyattack" in get_parent().get_parent() and get_parent().get_parent().heavyattack == true:
		if self.frame == 1:
			self.frame = 2

func torchburns():
#	yield(get_tree(),"idle_frame")
#	yield(get_tree(),"idle_frame")
#	yield(get_tree(),"idle_frame")
#	if self.frame == 2:
	self.frame = 0
