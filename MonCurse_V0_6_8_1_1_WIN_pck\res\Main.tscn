[gd_scene load_steps=223 format=2]

[ext_resource path="res://enemyreticule.tscn" type="PackedScene" id=1]
[ext_resource path="res://rulessprite.tscn" type="PackedScene" id=2]
[ext_resource path="res://Tilemaps/features.tres" type="TileSet" id=3]
[ext_resource path="res://Background/mainbackgroundforetest.png" type="Texture" id=4]
[ext_resource path="res://Background/backgroundscroller.png" type="Texture" id=5]
[ext_resource path="res://originalsfx/Bfxr/hurtdrainexp3.wav" type="AudioStream" id=6]
[ext_resource path="res://Main2Array.gd" type="Script" id=7]
[ext_resource path="res://Assets/blacksquare.png" type="Texture" id=8]
[ext_resource path="res://AmbienceLoader.tscn" type="PackedScene" id=9]
[ext_resource path="res://Assets/darksquare.png" type="Texture" id=10]
[ext_resource path="res://Assets/selector/attack2.png" type="Texture" id=11]
[ext_resource path="res://Assets/selector/jump2.png" type="Texture" id=12]
[ext_resource path="res://Assets/selector/run2.png" type="Texture" id=13]
[ext_resource path="res://Assets/selector/runorattack2.png" type="Texture" id=14]
[ext_resource path="res://Assets/selector/jumporattack2.png" type="Texture" id=15]
[ext_resource path="res://Assets/selector/wait2.png" type="Texture" id=16]
[ext_resource path="res://Assets/selector/swimorattack2.png" type="Texture" id=17]
[ext_resource path="res://Assets/selector/swim2.png" type="Texture" id=18]
[ext_resource path="res://Assets/selector/empty2.png" type="Texture" id=19]
[ext_resource path="res://effects/backgroundleaf.png" type="Texture" id=20]
[ext_resource path="res://Tilemaps/backgroundfeatures.tres" type="TileSet" id=21]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_fantasy_thud_hit_hard_74070.ogg" type="AudioStream" id=22]
[ext_resource path="res://zapsplat/zapsplat_foley_footstep_single_barefoot_on_small_stones_001_66856.ogg" type="AudioStream" id=23]
[ext_resource path="res://zapsplat/zapsplat_foley_footstep_single_barefoot_on_small_stones_006_66810.ogg" type="AudioStream" id=24]
[ext_resource path="res://Tilemaps/shards.tres" type="TileSet" id=25]
[ext_resource path="res://zapsplat/zapsplat_foley_footstep_single_barefoot_on_small_stones_005_66809.ogg" type="AudioStream" id=26]
[ext_resource path="res://zapsplat/zapsplat_foley_footstep_single_barefoot_on_small_stones_009_66813.ogg" type="AudioStream" id=27]
[ext_resource path="res://effects/hiteffect.png" type="Texture" id=28]
[ext_resource path="res://zapsplat/zapsplat_foley_footstep_single_barefoot_wood_balcony_007_37352.ogg" type="AudioStream" id=29]
[ext_resource path="res://dialogue.tscn" type="PackedScene" id=30]
[ext_resource path="res://originalsfx/Bfxr/hurtdrainexp.wav" type="AudioStream" id=31]
[ext_resource path="res://Tilemaps/map.tres" type="TileSet" id=32]
[ext_resource path="res://Background/undercliffbackground.png" type="Texture" id=33]
[ext_resource path="res://Background/bedrockhalf.png" type="Texture" id=34]
[ext_resource path="res://Assets/testlightinvert.png" type="Texture" id=35]
[ext_resource path="res://Enemy/dropshadow.png" type="Texture" id=36]
[ext_resource path="res://Tilemaps/foliage.tres" type="TileSet" id=37]
[ext_resource path="res://destructiblefeatures.tres" type="TileSet" id=38]
[ext_resource path="res://zapsplat/zapsplat_foley_chunk_of_stone_tile_very_short_scrape_ground_002_96046.ogg" type="AudioStream" id=39]
[ext_resource path="res://Background/fog2x2tiles.png" type="Texture" id=40]
[ext_resource path="res://zapsplat/zapsplat_warfare_axe_or_other_heavy_weapon_swing_002_25152.ogg" type="AudioStream" id=41]
[ext_resource path="res://zapsplat/zapsplat_foley_money_coins_small_x4_handling_short_003_56505.ogg" type="AudioStream" id=42]
[ext_resource path="res://zapsplat/zapsplat_sound_design_impact_hit_thump_punchy_into_a_pitch_descending_drop_tail_71727.ogg" type="AudioStream" id=43]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_building_blocks_bricks_collect_click_002_70220.ogg" type="AudioStream" id=44]
[ext_resource path="res://zapsplat/zapsplat_foley_footstep_single_barefoot_wood_balcony_012_37357.ogg" type="AudioStream" id=45]
[ext_resource path="res://zapsplat/zapsplat_foley_footstep_barefoot_single_wooden_step_005_19184.ogg" type="AudioStream" id=46]
[ext_resource path="res://Tilemaps/constructmap.tres" type="TileSet" id=47]
[ext_resource path="res://zapsplat/audio_hero_BirdFlying_DIGIC02-34.ogg" type="AudioStream" id=48]
[ext_resource path="res://Tilemaps/watertoponly.png" type="Texture" id=49]
[ext_resource path="res://Tilemaps/constructmapedges.tres" type="TileSet" id=50]
[ext_resource path="res://Tilemaps/constructbackground.tres" type="TileSet" id=51]
[ext_resource path="res://zapsplat/zapsplat_nature_fire_flames_burst_petrol_throw_onto_fire_89010.ogg" type="AudioStream" id=52]
[ext_resource path="res://zapsplat/newunused3/zapsplat_nature_fire_flame_short_burst_blow_002_90293.ogg" type="AudioStream" id=53]
[ext_resource path="res://Tilemaps/waterbotonly.png" type="Texture" id=54]
[ext_resource path="res://Tilemaps/roofmap.tres" type="TileSet" id=55]
[ext_resource path="res://Assets/materials/watertop.shader" type="Shader" id=56]
[ext_resource path="res://Assets/materials/water.shader" type="Shader" id=57]
[ext_resource path="res://zapsplat/zapsplat_foley_footstep_single_barefoot_wood_balcony_009_37354.ogg" type="AudioStream" id=58]
[ext_resource path="res://zapsplat/game_monopoly_game_card_turn_over_on_playing_board.ogg" type="AudioStream" id=59]
[ext_resource path="res://Assets/materials/crystalshards.gdshader" type="Shader" id=60]
[ext_resource path="res://zapsplat/zapsplat_foley_liquid_plastic_item_movements_in_plastic_container_of_water_plunges_004_53977.ogg" type="AudioStream" id=61]
[ext_resource path="res://zapsplat/aaj_0614_TapeRip01.ogg" type="AudioStream" id=62]
[ext_resource path="res://originalsfx/moderate bell.ogg" type="AudioStream" id=63]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_fantasy_magical_hit_positive_collect_item_73662.ogg" type="AudioStream" id=64]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_fantasy_hit_collect_open_level_73635.ogg" type="AudioStream" id=65]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_collect_coin_single_010_40830.ogg" type="AudioStream" id=66]
[ext_resource path="res://Tilemaps/cumtiles.tres" type="TileSet" id=67]
[ext_resource path="res://zapsplat/zapsplat_sound_design_hit_bright_yet_hard_hard_thud_73623.ogg" type="AudioStream" id=68]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_collect_coin_single_011_40831.ogg" type="AudioStream" id=69]
[ext_resource path="res://zapsplat/zapsplat_sound_design_hit_weird_fractured_74740.ogg" type="AudioStream" id=70]
[ext_resource path="res://zapsplat/zapsplat_bell_small_hand_ring_short_008_39325.ogg" type="AudioStream" id=71]
[ext_resource path="res://zapsplat/zapsplat_fantasy_magic_ping_wand_90s_style_dreamy_001_64944.ogg" type="AudioStream" id=72]
[ext_resource path="res://zapsplat/zapsplat_fantasy_magic_ping_wand_90s_style_dreamy_003_64946.ogg" type="AudioStream" id=73]
[ext_resource path="res://zapsplat/zapsplat_industrial_hammer_hit_metal_x1+_12828.ogg" type="AudioStream" id=74]
[ext_resource path="res://zapsplat/zapsplat_industrial_hammer_hit_wood_decking_plank_001_12829.ogg" type="AudioStream" id=75]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_fantasy_magical_hit_shot_target_fairy_bright_twinkle_73663.ogg" type="AudioStream" id=76]
[ext_resource path="res://zapsplat/zapsplat_warfare_axe_or_other_heavy_weapon_swing_001_25151.ogg" type="AudioStream" id=77]
[ext_resource path="res://zapsplat/zapsplat_warfare_knife_blades_x2_single_hit_together_and_scrape_003_78694.ogg" type="AudioStream" id=78]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_fantasy_magical_impact_plants_foliage_bright_glistening_74062.ogg" type="AudioStream" id=79]
[ext_resource path="res://zapsplat/zapsplat_impacts_body_hit_ground_heavy_thud_002_43760.ogg" type="AudioStream" id=80]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_fantasy_hit_smash_debris_rocks_gems_005_73647.ogg" type="AudioStream" id=81]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_fantasy_magical_impact_metallic_med_heavy_reverberant_74055.ogg" type="AudioStream" id=82]
[ext_resource path="res://zapsplat/zapsplat_foley_brown_paper_bag_rip_003_82187.ogg" type="AudioStream" id=83]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_designed_water_drip_onto_surface_009_26342.ogg" type="AudioStream" id=84]
[ext_resource path="res://zapsplat/zapsplat_sound_design_hit_thud_hard_bright_chiming_73628.ogg" type="AudioStream" id=85]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_fantasy_magical_impact_metal_gong_like_74050.ogg" type="AudioStream" id=86]
[ext_resource path="res://zapsplat/zapsplat_leisure_game_small_wood_balls_hit_each_other_strike_003_70591.ogg" type="AudioStream" id=87]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_magic_game_magical_burst_hit_lightly_dark_003_53789.ogg" type="AudioStream" id=88]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_sci_fi_game_hit_stutter_hard_78176.ogg" type="AudioStream" id=89]
[ext_resource path="res://zapsplat/zapsplat_foley_footsteps_barefoot_scuffs_movements_small_stones_shingle_001_55038.ogg" type="AudioStream" id=90]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_monster_hit_impact_growl_magical_shrill_sparkle_78159.ogg" type="AudioStream" id=91]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_hit_heavy_with_a_bright_sparkle_and_whoosh_tail_74071.ogg" type="AudioStream" id=92]
[ext_resource path="res://zapsplat/zapsplat_impacts_crockery_ceramic_plate_piece_broken_drop_smash_break_001_85249.ogg" type="AudioStream" id=93]
[ext_resource path="res://Assets/touchspeech.png" type="Texture" id=94]
[ext_resource path="res://Assets/touchspeechmacro.png" type="Texture" id=95]
[ext_resource path="res://zapsplat/zapsplat_impacts_crockery_ceramic_plate_piece_broken_drop_smash_break_010_85258.ogg" type="AudioStream" id=96]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_wooden_door_clunk_open_or_close_006_78406.ogg" type="AudioStream" id=97]
[ext_resource path="res://Background/bedrocktop.png" type="Texture" id=98]
[ext_resource path="res://Background/bedrocktopsmall.png" type="Texture" id=99]
[ext_resource path="res://lines.gd" type="Script" id=100]
[ext_resource path="res://Assets/ui/enemyhealth.png" type="Texture" id=101]
[ext_resource path="res://Assets/ui/kcursor2.png" type="Texture" id=102]
[ext_resource path="res://Tilemaps/paths.tres" type="TileSet" id=103]
[ext_resource path="res://Assets/ui/enemyhealthfill.png" type="Texture" id=104]
[ext_resource path="res://zapsplat/zapsplat_cartoon_bubble_single_short_low_pitched_94428.ogg" type="AudioStream" id=105]
[ext_resource path="res://zapsplat/zapsplat_sound_design_hit_thud_hard_chime_warm_negative_sounding_73629.ogg" type="AudioStream" id=106]
[ext_resource path="res://Tilemaps/mapedges.tres" type="TileSet" id=107]
[ext_resource path="res://Tilemaps/newsandstoneboth.png" type="Texture" id=108]
[ext_resource path="res://Tilemaps/testtilesalt.png" type="Texture" id=109]
[ext_resource path="res://zapsplat/household_door_wooden_internal_shut_013.ogg" type="AudioStream" id=110]
[ext_resource path="res://Assets/ui/quipbubble.png" type="Texture" id=111]
[ext_resource path="res://zapsplat/zapsplat_nature_water_underwater_whoosh_movement_pass_short_designed_001_59243.ogg" type="AudioStream" id=112]
[ext_resource path="res://Background/fogcloud2.png" type="Texture" id=113]
[ext_resource path="res://Background/fogcloud2grad.png" type="Texture" id=114]
[ext_resource path="res://zapsplat/zapsplat_impacts_crockery_ceramic_plate_piece_broken_drop_smash_break_011_85259.ogg" type="AudioStream" id=115]
[ext_resource path="res://zapsplat/zapsplat_industrial_pickaxe_metal_head_gently_hit_concrete_002_97657.ogg" type="AudioStream" id=116]
[ext_resource path="res://zapsplat/zapsplat_foley_chunk_of_stone_tile_scrape_ground_007_96030.ogg" type="AudioStream" id=117]
[ext_resource path="res://zapsplat/zapsplat_foley_chunk_of_stone_tile_scrape_ground_012_96035.ogg" type="AudioStream" id=118]
[ext_resource path="res://zapsplat/zapsplat_foley_wood_plank_1x4_drop_or_set_down_014_100258.ogg" type="AudioStream" id=119]
[ext_resource path="res://zapsplat/zapsplat_foley_wood_plank_1x4_drop_or_set_down_012_100256.ogg" type="AudioStream" id=120]
[ext_resource path="res://zapsplat/zapsplat_industrial_pickaxe_metal_head_gently_hit_concrete_001_97656.ogg" type="AudioStream" id=121]
[ext_resource path="res://zapsplat/zapsplat_foley_wood_plank_1x4_drop_or_set_down_013_100257.ogg" type="AudioStream" id=122]
[ext_resource path="res://zapsplat/zapsplat_foley_wood_large_board_lean_against_wall_001_24595.ogg" type="AudioStream" id=123]
[ext_resource path="res://zapsplat/zapsplat_foley_wood_large_board_lean_against_wall_002_24596.ogg" type="AudioStream" id=124]
[ext_resource path="res://originalsfx/Bfxr/minihurt2.wav" type="AudioStream" id=125]
[ext_resource path="res://originalsfx/Bfxr/minihurt3.wav" type="AudioStream" id=126]
[ext_resource path="res://zapsplat/zapsplat_science_fiction_robotic_machine_or_computer_short_blip_tone_activate_operate_001_79080.ogg" type="AudioStream" id=127]
[ext_resource path="res://zapsplat/zapsplat_sound_design_whoosh_dark_magical_wind_chimes_001_92991.wav" type="AudioStream" id=128]
[ext_resource path="res://originalsfx/Bfxr/hurtdrainexp2.wav" type="AudioStream" id=129]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_underwater_bubble_wet_collect_item_earn_point_bonus_001_78395.ogg" type="AudioStream" id=130]
[ext_resource path="res://Assets/touchshow.png" type="Texture" id=131]
[ext_resource path="res://Assets/touchspeechmini.png" type="Texture" id=132]
[ext_resource path="res://Background/sandcloud.png" type="Texture" id=133]
[ext_resource path="res://effects/possession arrow.png" type="Texture" id=134]
[ext_resource path="res://zapsplat/zapsplat_warfare_arrow_shoot_hit_target_002_14337.ogg" type="AudioStream" id=135]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_designed_water_hit_thump_005_26347.ogg" type="AudioStream" id=136]
[ext_resource path="res://zapsplat/zapsplat_fantasy_magic_ping_wand_90s_style_dreamy_002_64945.ogg" type="AudioStream" id=137]
[ext_resource path="res://zapsplat/zapsplat_foley_wall_tile_on_tile_set_down_001_58515.ogg" type="AudioStream" id=138]
[ext_resource path="res://zapsplat/zapsplat_foley_wall_tile_on_tile_set_down_003_58517.ogg" type="AudioStream" id=139]
[ext_resource path="res://zapsplat/zapsplat_fantasy_magic_ping_wand_90s_style_dreamy_004_64947.ogg" type="AudioStream" id=140]
[ext_resource path="res://zapsplat/zapsplat_foley_wall_tile_on_tile_set_down_004_58518.ogg" type="AudioStream" id=141]
[ext_resource path="res://zapsplat/zapsplat_foley_wall_tile_on_tile_set_down_005_58519_fix.ogg" type="AudioStream" id=142]
[ext_resource path="res://zapsplat/zapsplat_foley_wall_tile_on_tile_set_down_002_58516_fix.ogg" type="AudioStream" id=143]
[ext_resource path="res://Assets/materials/mapshader.tres" type="Material" id=144]
[ext_resource path="res://Background/desertundergroundbackground.png" type="Texture" id=145]
[ext_resource path="res://Background/desertundergroundbottom.png" type="Texture" id=146]

[sub_resource type="TileSet" id=1]
0/name = "backgroundscroller.png 0"
0/texture = ExtResource( 5 )
0/tex_offset = Vector2( 0, 0 )
0/modulate = Color( 1, 1, 1, 1 )
0/region = Rect2( 0, 0, 1920, 1080 )
0/tile_mode = 0
0/occluder_offset = Vector2( 0, 0 )
0/navigation_offset = Vector2( 0, 0 )
0/shape_offset = Vector2( 0, 0 )
0/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
0/shape_one_way = false
0/shape_one_way_margin = 0.0
0/shapes = [  ]
0/z_index = 0
1/name = "desertundergroundbottom.png 1"
1/texture = ExtResource( 146 )
1/tex_offset = Vector2( 0, 0 )
1/modulate = Color( 1, 1, 1, 1 )
1/region = Rect2( 0, 0, 1920, 1024 )
1/tile_mode = 0
1/occluder_offset = Vector2( 0, 0 )
1/navigation_offset = Vector2( 0, 0 )
1/shape_offset = Vector2( 0, 0 )
1/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
1/shape_one_way = false
1/shape_one_way_margin = 0.0
1/shapes = [  ]
1/z_index = 0

[sub_resource type="TileSet" id=2]
0/name = "mainbackgroundforetest.png 0"
0/texture = ExtResource( 4 )
0/tex_offset = Vector2( 0, 0 )
0/modulate = Color( 1, 1, 1, 1 )
0/region = Rect2( 0, 0, 1920, 1080 )
0/tile_mode = 0
0/occluder_offset = Vector2( 0, 0 )
0/navigation_offset = Vector2( 0, 0 )
0/shape_offset = Vector2( 0, 0 )
0/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
0/shape_one_way = false
0/shape_one_way_margin = 0.0
0/shapes = [  ]
0/z_index = 0
1/name = "undercliffbackground.png 1"
1/texture = ExtResource( 33 )
1/tex_offset = Vector2( 0, 0 )
1/modulate = Color( 1, 1, 1, 1 )
1/region = Rect2( 0, 0, 1920, 1080 )
1/tile_mode = 0
1/occluder_offset = Vector2( 0, 0 )
1/navigation_offset = Vector2( 0, 0 )
1/shape_offset = Vector2( 0, 0 )
1/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
1/shape_one_way = false
1/shape_one_way_margin = 0.0
1/shapes = [  ]
1/z_index = 0
2/name = "desertundergroundbackground.png 2"
2/texture = ExtResource( 145 )
2/tex_offset = Vector2( 0, 0 )
2/modulate = Color( 1, 1, 1, 1 )
2/region = Rect2( 0, 0, 1920, 1080 )
2/tile_mode = 0
2/occluder_offset = Vector2( 0, 0 )
2/navigation_offset = Vector2( 0, 0 )
2/shape_offset = Vector2( 0, 0 )
2/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
2/shape_one_way = false
2/shape_one_way_margin = 0.0
2/shapes = [  ]
2/z_index = 0

[sub_resource type="Animation" id=3]
length = 5.0
tracks/0/type = "value"
tracks/0/path = NodePath(".:color")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 5 ),
"transitions": PoolRealArray( 0.3, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.6, 0.4, 0.2, 1 ) ]
}

[sub_resource type="ShaderMaterial" id=5]
shader = ExtResource( 57 )
shader_param/blue_tint = Color( 0.392157, 0.54902, 1, 1 )
shader_param/sprite_scale = Vector2( 128, 128 )
shader_param/scale_x = 0.67
shader_param/amplitude = 360.0

[sub_resource type="TileSet" id=80]
0/name = "waterbotonly.png 0"
0/texture = ExtResource( 54 )
0/tex_offset = Vector2( 0, 0 )
0/modulate = Color( 1, 1, 1, 1 )
0/region = Rect2( 0, 0, 512, 512 )
0/tile_mode = 1
0/autotile/bitmask_mode = 0
0/autotile/bitmask_flags = [ Vector2( 0, 0 ), 64, Vector2( 0, 1 ), 257, Vector2( 0, 2 ), 4, Vector2( 1, 0 ), 260, Vector2( 1, 1 ), 324, Vector2( 1, 2 ), 5, Vector2( 1, 3 ), 256, Vector2( 2, 0 ), 321, Vector2( 2, 1 ), 325, Vector2( 2, 2 ), 261, Vector2( 2, 3 ), 68, Vector2( 3, 0 ), 320, Vector2( 3, 1 ), 69, Vector2( 3, 2 ), 65, Vector2( 3, 3 ), 1 ]
0/autotile/icon_coordinate = Vector2( 0, 0 )
0/autotile/tile_size = Vector2( 128, 128 )
0/autotile/spacing = 0
0/autotile/occluder_map = [  ]
0/autotile/navpoly_map = [  ]
0/autotile/priority_map = [  ]
0/autotile/z_index_map = [  ]
0/occluder_offset = Vector2( 0, 0 )
0/navigation_offset = Vector2( 0, 0 )
0/shape_offset = Vector2( 0, 0 )
0/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
0/shape_one_way = false
0/shape_one_way_margin = 0.0
0/shapes = [  ]
0/z_index = 0

[sub_resource type="OpenSimplexNoise" id=7]

[sub_resource type="NoiseTexture" id=8]
seamless = true
noise = SubResource( 7 )

[sub_resource type="ShaderMaterial" id=9]
shader = ExtResource( 56 )
shader_param/blue_tint = Color( 0.196078, 0.27451, 1, 1 )
shader_param/y_zoom = 1.0
shader_param/distortion_scale = Vector2( 0.1, 1 )
shader_param/intensity = 0.1
shader_param/speed = 0.01
shader_param/noise = SubResource( 8 )

[sub_resource type="TileSet" id=10]
0/name = "watertoponly.png 0"
0/texture = ExtResource( 49 )
0/tex_offset = Vector2( 0, 0 )
0/modulate = Color( 1, 1, 1, 1 )
0/region = Rect2( 384, 0, 128, 128 )
0/tile_mode = 0
0/occluder_offset = Vector2( 0, 0 )
0/navigation_offset = Vector2( 0, 0 )
0/shape_offset = Vector2( 0, 0 )
0/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
0/shape_one_way = false
0/shape_one_way_margin = 0.0
0/shapes = [  ]
0/z_index = 0
1/name = "watertoponly.png 1"
1/texture = ExtResource( 49 )
1/tex_offset = Vector2( 0, 0 )
1/modulate = Color( 1, 1, 1, 1 )
1/region = Rect2( 256, 0, 128, 128 )
1/tile_mode = 0
1/occluder_offset = Vector2( 0, 0 )
1/navigation_offset = Vector2( 0, 0 )
1/shape_offset = Vector2( 0, 0 )
1/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
1/shape_one_way = false
1/shape_one_way_margin = 0.0
1/shapes = [  ]
1/z_index = 0
2/name = "watertoponly.png 2"
2/texture = ExtResource( 49 )
2/tex_offset = Vector2( 0, 0 )
2/modulate = Color( 1, 1, 1, 1 )
2/region = Rect2( 0, 0, 128, 128 )
2/tile_mode = 0
2/occluder_offset = Vector2( 0, 0 )
2/navigation_offset = Vector2( 0, 0 )
2/shape_offset = Vector2( 0, 0 )
2/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
2/shape_one_way = false
2/shape_one_way_margin = 0.0
2/shapes = [  ]
2/z_index = 0

[sub_resource type="TileSet" id=16]
1/name = "testtilesalt.png 1"
1/texture = ExtResource( 109 )
1/tex_offset = Vector2( 0, 0 )
1/modulate = Color( 1, 1, 1, 1 )
1/region = Rect2( 0, 0, 1408, 640 )
1/tile_mode = 1
1/autotile/bitmask_mode = 1
1/autotile/bitmask_flags = [ Vector2( 0, 0 ), 432, Vector2( 0, 1 ), 438, Vector2( 0, 2 ), 54, Vector2( 0, 3 ), 48, Vector2( 0, 4 ), 511, Vector2( 1, 0 ), 504, Vector2( 1, 1 ), 511, Vector2( 1, 2 ), 63, Vector2( 1, 3 ), 56, Vector2( 1, 4 ), 504, Vector2( 2, 0 ), 216, Vector2( 2, 1 ), 219, Vector2( 2, 2 ), 27, Vector2( 2, 3 ), 24, Vector2( 3, 0 ), 144, Vector2( 3, 1 ), 146, Vector2( 3, 2 ), 18, Vector2( 3, 3 ), 16, Vector2( 3, 4 ), 16, Vector2( 4, 0 ), 176, Vector2( 4, 1 ), 182, Vector2( 4, 2 ), 434, Vector2( 4, 3 ), 50, Vector2( 4, 4 ), 178, Vector2( 5, 0 ), 248, Vector2( 5, 1 ), 255, Vector2( 5, 2 ), 507, Vector2( 5, 3 ), 59, Vector2( 5, 4 ), 251, Vector2( 6, 0 ), 440, Vector2( 6, 1 ), 447, Vector2( 6, 2 ), 510, Vector2( 6, 3 ), 62, Vector2( 6, 4 ), 446, Vector2( 7, 0 ), 152, Vector2( 7, 1 ), 155, Vector2( 7, 2 ), 218, Vector2( 7, 3 ), 26, Vector2( 7, 4 ), 154, Vector2( 8, 0 ), 184, Vector2( 8, 1 ), 191, Vector2( 8, 2 ), 506, Vector2( 8, 3 ), 58, Vector2( 8, 4 ), 186, Vector2( 9, 0 ), 443, Vector2( 9, 1 ), 254, Vector2( 9, 2 ), 442, Vector2( 9, 3 ), 190, Vector2( 10, 2 ), 250, Vector2( 10, 3 ), 187 ]
1/autotile/icon_coordinate = Vector2( 0, 0 )
1/autotile/tile_size = Vector2( 128, 128 )
1/autotile/spacing = 0
1/autotile/occluder_map = [  ]
1/autotile/navpoly_map = [  ]
1/autotile/priority_map = [  ]
1/autotile/z_index_map = [  ]
1/occluder_offset = Vector2( 0, 0 )
1/navigation_offset = Vector2( 0, 0 )
1/shape_offset = Vector2( 0, 0 )
1/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
1/shape_one_way = false
1/shape_one_way_margin = 0.0
1/shapes = [  ]
1/z_index = 0
2/name = "newsandstoneboth.png 2"
2/texture = ExtResource( 108 )
2/tex_offset = Vector2( 0, 0 )
2/modulate = Color( 1, 1, 1, 1 )
2/region = Rect2( 0, 0, 1408, 640 )
2/tile_mode = 1
2/autotile/bitmask_mode = 1
2/autotile/bitmask_flags = [ Vector2( 0, 0 ), 432, Vector2( 0, 1 ), 438, Vector2( 0, 2 ), 54, Vector2( 0, 3 ), 48, Vector2( 1, 0 ), 504, Vector2( 1, 1 ), 511, Vector2( 1, 2 ), 63, Vector2( 1, 3 ), 56, Vector2( 2, 0 ), 216, Vector2( 2, 1 ), 219, Vector2( 2, 2 ), 27, Vector2( 2, 3 ), 24, Vector2( 3, 0 ), 144, Vector2( 3, 1 ), 146, Vector2( 3, 2 ), 18, Vector2( 3, 3 ), 16, Vector2( 4, 0 ), 176, Vector2( 4, 1 ), 182, Vector2( 4, 2 ), 434, Vector2( 4, 3 ), 50, Vector2( 4, 4 ), 178, Vector2( 5, 0 ), 248, Vector2( 5, 1 ), 255, Vector2( 5, 2 ), 507, Vector2( 5, 3 ), 59, Vector2( 5, 4 ), 251, Vector2( 6, 0 ), 440, Vector2( 6, 1 ), 447, Vector2( 6, 2 ), 510, Vector2( 6, 3 ), 62, Vector2( 6, 4 ), 446, Vector2( 7, 0 ), 152, Vector2( 7, 1 ), 155, Vector2( 7, 2 ), 218, Vector2( 7, 3 ), 26, Vector2( 7, 4 ), 154, Vector2( 8, 0 ), 184, Vector2( 8, 1 ), 191, Vector2( 8, 2 ), 506, Vector2( 8, 3 ), 58, Vector2( 8, 4 ), 186, Vector2( 9, 0 ), 443, Vector2( 9, 1 ), 254, Vector2( 9, 2 ), 442, Vector2( 9, 3 ), 190, Vector2( 10, 2 ), 250, Vector2( 10, 3 ), 187 ]
2/autotile/icon_coordinate = Vector2( 0, 0 )
2/autotile/tile_size = Vector2( 128, 128 )
2/autotile/spacing = 0
2/autotile/occluder_map = [  ]
2/autotile/navpoly_map = [  ]
2/autotile/priority_map = [  ]
2/autotile/z_index_map = [  ]
2/occluder_offset = Vector2( 0, 0 )
2/navigation_offset = Vector2( 0, 0 )
2/shape_offset = Vector2( 0, 0 )
2/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
2/shape_one_way = false
2/shape_one_way_margin = 0.0
2/shapes = [  ]
2/z_index = 0

[sub_resource type="TileSet" id=17]
0/name = "run2.png 0"
0/texture = ExtResource( 13 )
0/tex_offset = Vector2( 0, 0 )
0/modulate = Color( 1, 1, 1, 1 )
0/region = Rect2( 0, 0, 128, 128 )
0/tile_mode = 0
0/occluder_offset = Vector2( 0, 0 )
0/navigation_offset = Vector2( 0, 0 )
0/shape_offset = Vector2( 0, 0 )
0/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
0/shape_one_way = false
0/shape_one_way_margin = 0.0
0/shapes = [  ]
0/z_index = 0
1/name = "runorattack2.png 1"
1/texture = ExtResource( 14 )
1/tex_offset = Vector2( 0, 0 )
1/modulate = Color( 1, 1, 1, 1 )
1/region = Rect2( 0, 0, 128, 128 )
1/tile_mode = 0
1/occluder_offset = Vector2( 0, 0 )
1/navigation_offset = Vector2( 0, 0 )
1/shape_offset = Vector2( 0, 0 )
1/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
1/shape_one_way = false
1/shape_one_way_margin = 0.0
1/shapes = [  ]
1/z_index = 0
2/name = "swim2.png 2"
2/texture = ExtResource( 18 )
2/tex_offset = Vector2( 0, 0 )
2/modulate = Color( 1, 1, 1, 1 )
2/region = Rect2( 0, 0, 128, 128 )
2/tile_mode = 0
2/occluder_offset = Vector2( 0, 0 )
2/navigation_offset = Vector2( 0, 0 )
2/shape_offset = Vector2( 0, 0 )
2/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
2/shape_one_way = false
2/shape_one_way_margin = 0.0
2/shapes = [  ]
2/z_index = 0
3/name = "swimorattack2.png 3"
3/texture = ExtResource( 17 )
3/tex_offset = Vector2( 0, 0 )
3/modulate = Color( 1, 1, 1, 1 )
3/region = Rect2( 0, 0, 128, 128 )
3/tile_mode = 0
3/occluder_offset = Vector2( 0, 0 )
3/navigation_offset = Vector2( 0, 0 )
3/shape_offset = Vector2( 0, 0 )
3/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
3/shape_one_way = false
3/shape_one_way_margin = 0.0
3/shapes = [  ]
3/z_index = 0
4/name = "wait2.png 4"
4/texture = ExtResource( 16 )
4/tex_offset = Vector2( 0, 0 )
4/modulate = Color( 1, 1, 1, 1 )
4/region = Rect2( 0, 0, 128, 128 )
4/tile_mode = 0
4/occluder_offset = Vector2( 0, 0 )
4/navigation_offset = Vector2( 0, 0 )
4/shape_offset = Vector2( 0, 0 )
4/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
4/shape_one_way = false
4/shape_one_way_margin = 0.0
4/shapes = [  ]
4/z_index = 0
5/name = "attack2.png 5"
5/texture = ExtResource( 11 )
5/tex_offset = Vector2( 0, 0 )
5/modulate = Color( 1, 1, 1, 1 )
5/region = Rect2( 0, 0, 128, 128 )
5/tile_mode = 0
5/occluder_offset = Vector2( 0, 0 )
5/navigation_offset = Vector2( 0, 0 )
5/shape_offset = Vector2( 0, 0 )
5/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
5/shape_one_way = false
5/shape_one_way_margin = 0.0
5/shapes = [  ]
5/z_index = 0
6/name = "jump2.png 6"
6/texture = ExtResource( 12 )
6/tex_offset = Vector2( 0, 0 )
6/modulate = Color( 1, 1, 1, 1 )
6/region = Rect2( 0, 0, 128, 128 )
6/tile_mode = 0
6/occluder_offset = Vector2( 0, 0 )
6/navigation_offset = Vector2( 0, 0 )
6/shape_offset = Vector2( 0, 0 )
6/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
6/shape_one_way = false
6/shape_one_way_margin = 0.0
6/shapes = [  ]
6/z_index = 0
7/name = "jumporattack2.png 7"
7/texture = ExtResource( 15 )
7/tex_offset = Vector2( 0, 0 )
7/modulate = Color( 1, 1, 1, 1 )
7/region = Rect2( 0, 0, 128, 128 )
7/tile_mode = 0
7/occluder_offset = Vector2( 0, 0 )
7/navigation_offset = Vector2( 0, 0 )
7/shape_offset = Vector2( 0, 0 )
7/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
7/shape_one_way = false
7/shape_one_way_margin = 0.0
7/shapes = [  ]
7/z_index = 0
8/name = "empty2.png 8"
8/texture = ExtResource( 19 )
8/tex_offset = Vector2( 0, 0 )
8/modulate = Color( 1, 1, 1, 1 )
8/region = Rect2( 0, 0, 128, 128 )
8/tile_mode = 0
8/occluder_offset = Vector2( 0, 0 )
8/navigation_offset = Vector2( 0, 0 )
8/shape_offset = Vector2( 0, 0 )
8/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
8/shape_one_way = false
8/shape_one_way_margin = 0.0
8/shapes = [  ]
8/z_index = 0

[sub_resource type="AudioStreamOGGVorbis" id=87]
data = PoolByteArray( 79, 103, 103, 83, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 158, 75, 203, 236, 0, 0, 0, 0, 196, 13, 3, 111, 1, 30, 1, 118, 111, 114, 98, 105, 115, 0, 0, 0, 0, 2, 128, 187, 0, 0, 0, 0, 0, 0, 128, 181, 1, 0, 0, 0, 0, 0, 184, 1, 79, 103, 103, 83, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 158, 75, 203, 236, 1, 0, 0, 0, 144, 101, 104, 12, 17, 134, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 7, 3, 118, 111, 114, 98, 105, 115, 13, 0, 0, 0, 76, 97, 118, 102, 53, 56, 46, 55, 54, 46, 49, 48, 48, 3, 0, 0, 0, 32, 0, 0, 0, 101, 110, 99, 111, 100, 101, 114, 61, 76, 97, 118, 99, 53, 56, 46, 49, 51, 52, 46, 49, 48, 48, 32, 108, 105, 98, 118, 111, 114, 98, 105, 115, 28, 0, 0, 0, 101, 110, 99, 111, 100, 101, 100, 95, 98, 121, 61, 83, 111, 117, 110, 100, 32, 71, 114, 105, 110, 100, 101, 114, 32, 52, 46, 48, 33, 0, 0, 0, 99, 111, 112, 121, 114, 105, 103, 104, 116, 61, 67, 111, 112, 121, 114, 105, 103, 104, 116, 32, 65, 108, 97, 110, 32, 77, 99, 75, 105, 110, 110, 101, 121, 1, 5, 118, 111, 114, 98, 105, 115, 37, 66, 67, 86, 1, 0, 64, 0, 0, 36, 115, 24, 42, 70, 165, 115, 22, 132, 16, 26, 66, 80, 25, 227, 28, 66, 206, 107, 236, 25, 66, 76, 17, 130, 28, 50, 76, 91, 203, 37, 115, 144, 33, 164, 160, 66, 136, 91, 40, 129, 208, 144, 85, 0, 0, 64, 0, 0, 135, 65, 120, 20, 132, 138, 65, 8, 33, 132, 37, 61, 88, 146, 131, 39, 61, 8, 33, 132, 136, 57, 120, 20, 132, 105, 65, 8, 33, 132, 16, 66, 8, 33, 132, 16, 66, 8, 33, 132, 69, 57, 104, 146, 131, 39, 65, 8, 29, 132, 227, 48, 56, 12, 131, 229, 56, 248, 28, 132, 69, 57, 88, 16, 131, 39, 65, 232, 32, 132, 15, 66, 184, 154, 131, 172, 57, 8, 33, 132, 36, 53, 72, 80, 131, 6, 57, 232, 28, 132, 194, 44, 40, 138, 130, 196, 48, 184, 22, 132, 4, 53, 40, 140, 130, 228, 48, 200, 212, 131, 11, 66, 136, 154, 131, 73, 53, 248, 26, 132, 103, 65, 120, 22, 132, 105, 65, 8, 33, 132, 36, 65, 72, 144, 131, 6, 65, 200, 24, 132, 70, 65, 88, 146, 131, 6, 57, 184, 20, 132, 203, 65, 168, 26, 132, 42, 57, 8, 31, 132, 32, 52, 100, 21, 0, 144, 0, 0, 160, 162, 40, 138, 162, 40, 10, 16, 26, 178, 10, 0, 200, 0, 0, 16, 64, 81, 20, 199, 113, 28, 201, 145, 28, 201, 177, 28, 11, 8, 13, 89, 5, 0, 0, 1, 0, 8, 0, 0, 160, 72, 138, 164, 72, 142, 228, 72, 146, 36, 89, 146, 37, 89, 146, 37, 89, 146, 230, 137, 170, 44, 203, 178, 44, 203, 178, 44, 203, 50, 16, 26, 178, 10, 0, 72, 0, 0, 80, 81, 12, 69, 113, 20, 7, 8, 13, 89, 5, 0, 100, 0, 0, 8, 160, 56, 138, 165, 88, 138, 165, 104, 138, 231, 136, 142, 8, 132, 134, 172, 2, 0, 128, 0, 0, 4, 0, 0, 16, 52, 67, 83, 60, 71, 148, 68, 207, 84, 85, 215, 182, 109, 219, 182, 109, 219, 182, 109, 219, 182, 109, 219, 182, 109, 91, 150, 101, 25, 8, 13, 89, 5, 0, 64, 0, 0, 16, 210, 105, 102, 169, 6, 136, 48, 3, 25, 6, 66, 67, 86, 1, 0, 8, 0, 0, 128, 17, 138, 48, 196, 128, 208, 144, 85, 0, 0, 64, 0, 0, 128, 24, 74, 14, 162, 9, 173, 57, 223, 156, 227, 160, 89, 14, 154, 74, 177, 57, 29, 156, 72, 181, 121, 146, 155, 138, 185, 57, 231, 156, 115, 206, 201, 230, 156, 49, 206, 57, 231, 156, 162, 156, 89, 12, 154, 9, 173, 57, 231, 156, 196, 160, 89, 10, 154, 9, 173, 57, 231, 156, 39, 177, 121, 208, 154, 42, 173, 57, 231, 156, 113, 206, 233, 96, 156, 17, 198, 57, 231, 156, 38, 173, 121, 144, 154, 141, 181, 57, 231, 156, 5, 173, 105, 142, 154, 75, 177, 57, 231, 156, 72, 185, 121, 82, 155, 75, 181, 57, 231, 156, 115, 206, 57, 231, 156, 115, 206, 57, 231, 156, 234, 197, 233, 28, 156, 19, 206, 57, 231, 156, 168, 189, 185, 150, 155, 208, 197, 57, 231, 156, 79, 198, 233, 222, 156, 16, 206, 57, 231, 156, 115, 206, 57, 231, 156, 115, 206, 57, 231, 156, 32, 52, 100, 21, 0, 0, 4, 0, 64, 16, 134, 141, 97, 220, 41, 8, 210, 231, 104, 32, 70, 17, 98, 26, 50, 233, 65, 247, 232, 48, 9, 26, 131, 156, 66, 234, 209, 232, 104, 164, 148, 58, 8, 37, 149, 113, 82, 74, 39, 8, 13, 89, 5, 0, 0, 2, 0, 64, 8, 33, 133, 20, 82, 72, 33, 133, 20, 82, 72, 33, 133, 20, 98, 136, 33, 134, 24, 114, 202, 41, 167, 160, 130, 74, 42, 169, 168, 162, 140, 50, 203, 44, 179, 204, 50, 203, 44, 179, 204, 58, 236, 172, 179, 14, 59, 12, 49, 196, 16, 67, 43, 173, 196, 82, 83, 109, 53, 214, 88, 107, 238, 57, 231, 154, 131, 180, 86, 90, 107, 173, 181, 82, 74, 41, 165, 148, 82, 10, 66, 67, 86, 1, 0, 32, 0, 0, 4, 66, 6, 25, 100, 144, 81, 72, 33, 133, 20, 98, 136, 41, 167, 156, 114, 10, 42, 168, 128, 208, 144, 85, 0, 0, 32, 0, 128, 0, 0, 0, 0, 79, 242, 28, 209, 17, 29, 209, 17, 29, 209, 17, 29, 209, 17, 29, 209, 241, 28, 207, 17, 37, 81, 18, 37, 81, 18, 45, 211, 50, 53, 211, 83, 69, 85, 117, 101, 215, 150, 117, 89, 183, 125, 91, 216, 133, 93, 247, 125, 221, 247, 125, 221, 248, 117, 97, 88, 150, 101, 89, 150, 101, 89, 150, 101, 89, 150, 101, 89, 150, 101, 89, 150, 32, 52, 100, 21, 0, 0, 2, 0, 0, 32, 132, 16, 66, 72, 33, 133, 20, 82, 72, 41, 198, 24, 115, 204, 57, 232, 36, 148, 16, 8, 13, 89, 5, 0, 0, 2, 0, 8, 0, 0, 0, 112, 20, 71, 113, 28, 201, 145, 28, 73, 178, 36, 75, 210, 36, 205, 210, 44, 79, 243, 52, 79, 19, 61, 81, 20, 69, 211, 52, 85, 209, 21, 93, 81, 55, 109, 81, 54, 101, 211, 53, 93, 83, 54, 93, 85, 86, 109, 87, 150, 109, 91, 182, 117, 219, 151, 101, 219, 247, 125, 223, 247, 125, 223, 247, 125, 223, 247, 125, 223, 247, 125, 93, 7, 66, 67, 86, 1, 0, 18, 0, 0, 58, 146, 35, 41, 146, 34, 41, 146, 227, 56, 142, 36, 73, 64, 104, 200, 42, 0, 64, 6, 0, 64, 0, 0, 138, 226, 40, 142, 227, 56, 146, 36, 73, 146, 37, 105, 146, 103, 121, 150, 168, 153, 154, 233, 153, 158, 42, 170, 64, 104, 200, 42, 0, 0, 16, 0, 64, 0, 0, 0, 0, 0, 0, 138, 166, 120, 138, 169, 120, 138, 168, 120, 142, 232, 136, 146, 104, 153, 150, 168, 169, 154, 43, 202, 166, 236, 186, 174, 235, 186, 174, 235, 186, 174, 235, 186, 174, 235, 186, 174, 235, 186, 174, 235, 186, 174, 235, 186, 174, 235, 186, 174, 235, 186, 174, 235, 186, 174, 235, 186, 174, 11, 132, 134, 172, 2, 0, 36, 0, 0, 116, 36, 71, 114, 36, 71, 82, 36, 69, 82, 36, 71, 114, 128, 208, 144, 85, 0, 128, 12, 0, 128, 0, 0, 28, 195, 49, 36, 69, 114, 44, 203, 210, 52, 79, 243, 52, 79, 19, 61, 209, 19, 61, 211, 83, 69, 87, 116, 129, 208, 144, 85, 0, 0, 32, 0, 128, 0, 0, 0, 0, 0, 0, 12, 201, 176, 20, 203, 209, 28, 77, 18, 37, 213, 82, 45, 85, 83, 45, 213, 82, 69, 213, 83, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 77, 211, 52, 77, 19, 8, 13, 89, 9, 0, 144, 1, 0, 144, 16, 83, 45, 45, 198, 154, 9, 139, 36, 98, 210, 106, 171, 160, 99, 12, 82, 236, 165, 177, 72, 42, 103, 181, 183, 202, 49, 133, 24, 181, 94, 26, 135, 148, 81, 16, 123, 169, 36, 99, 138, 65, 204, 45, 164, 208, 41, 38, 173, 214, 84, 66, 133, 20, 164, 152, 99, 42, 21, 82, 14, 82, 32, 52, 100, 133, 0, 16, 154, 1, 224, 112, 28, 64, 178, 44, 64, 178, 44, 0, 0, 0, 0, 0, 0, 0, 144, 52, 13, 208, 60, 15, 176, 52, 15, 0, 0, 0, 0, 0, 0, 0, 36, 77, 3, 44, 79, 3, 52, 207, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 210, 52, 64, 243, 60, 64, 243, 60, 0, 0, 0, 0, 0, 0, 0, 208, 60, 15, 240, 60, 17, 240, 68, 17, 0, 0, 0, 0, 0, 0, 0, 44, 207, 3, 52, 209, 3, 60, 81, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64, 210, 52, 64, 243, 60, 64, 243, 60, 0, 0, 0, 0, 0, 0, 0, 176, 60, 15, 240, 68, 17, 208, 60, 17, 0, 0, 0, 0, 0, 0, 0, 44, 207, 3, 60, 81, 4, 60, 209, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 16, 224, 0, 0, 16, 96, 33, 20, 26, 178, 34, 0, 136, 19, 0, 112, 72, 18, 36, 9, 146, 4, 205, 3, 72, 150, 5, 77, 131, 166, 193, 52, 1, 146, 101, 65, 211, 160, 105, 48, 77, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 77, 131, 166, 65, 211, 32, 138, 0, 73, 211, 160, 105, 208, 52, 136, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 146, 166, 65, 211, 160, 105, 16, 69, 128, 164, 105, 208, 52, 104, 26, 68, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 207, 52, 33, 138, 16, 69, 152, 38, 192, 51, 77, 136, 34, 68, 17, 166, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 24, 112, 0, 0, 8, 48, 161, 12, 20, 26, 178, 34, 0, 136, 19, 0, 112, 56, 138, 101, 1, 0, 128, 227, 56, 150, 5, 0, 0, 142, 227, 88, 22, 0, 0, 88, 150, 37, 138, 0, 0, 96, 89, 154, 40, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 24, 112, 0, 0, 8, 48, 161, 12, 20, 26, 178, 18, 0, 136, 2, 0, 112, 40, 138, 101, 1, 199, 177, 44, 224, 56, 150, 5, 36, 201, 178, 0, 150, 5, 208, 60, 128, 166, 1, 68, 17, 0, 8, 0, 0, 40, 112, 0, 0, 8, 176, 65, 83, 98, 113, 128, 66, 67, 86, 2, 0, 81, 0, 0, 6, 197, 177, 44, 77, 19, 69, 146, 164, 105, 154, 39, 138, 36, 73, 211, 60, 79, 20, 105, 154, 231, 121, 158, 105, 194, 243, 60, 207, 52, 33, 138, 162, 104, 154, 16, 69, 81, 52, 77, 152, 166, 105, 170, 42, 48, 77, 85, 21, 0, 0, 80, 224, 0, 0, 16, 96, 131, 166, 196, 226, 0, 133, 134, 172, 4, 0, 66, 2, 0, 28, 138, 98, 89, 154, 230, 121, 158, 39, 138, 166, 169, 154, 36, 73, 211, 60, 79, 20, 69, 209, 52, 77, 83, 85, 73, 146, 166, 121, 158, 40, 138, 162, 105, 154, 166, 170, 178, 44, 77, 243, 60, 81, 20, 69, 211, 84, 85, 85, 133, 166, 121, 158, 40, 138, 162, 105, 170, 170, 234, 194, 243, 60, 79, 20, 69, 209, 52, 85, 213, 117, 225, 121, 158, 39, 138, 162, 104, 154, 170, 234, 186, 16, 69, 81, 52, 77, 211, 84, 77, 85, 117, 93, 32, 138, 166, 105, 154, 170, 170, 170, 174, 11, 68, 79, 20, 77, 83, 85, 93, 215, 117, 129, 231, 137, 162, 105, 170, 170, 171, 186, 46, 16, 77, 211, 84, 85, 85, 117, 93, 89, 6, 152, 166, 105, 170, 170, 235, 202, 50, 64, 85, 85, 213, 117, 93, 87, 150, 1, 170, 170, 170, 174, 235, 186, 178, 12, 80, 85, 215, 117, 93, 89, 150, 101, 0, 174, 235, 186, 178, 44, 203, 2, 0, 0, 14, 28, 0, 0, 2, 140, 160, 147, 140, 42, 139, 176, 209, 132, 11, 15, 64, 161, 33, 43, 2, 128, 40, 0, 0, 192, 24, 166, 20, 83, 202, 48, 38, 33, 164, 16, 26, 198, 36, 132, 20, 66, 38, 37, 165, 210, 82, 170, 32, 164, 82, 82, 41, 21, 132, 84, 74, 42, 37, 163, 148, 82, 106, 41, 85, 16, 82, 41, 169, 148, 10, 66, 42, 37, 149, 82, 0, 0, 216, 129, 3, 0, 216, 129, 133, 80, 104, 200, 74, 0, 32, 15, 0, 128, 48, 70, 41, 198, 24, 115, 78, 34, 164, 20, 99, 206, 57, 39, 17, 82, 138, 49, 231, 156, 147, 74, 49, 230, 156, 115, 206, 73, 41, 25, 115, 204, 57, 231, 164, 148, 206, 57, 231, 156, 115, 82, 74, 230, 156, 115, 206, 57, 41, 165, 115, 206, 57, 231, 156, 148, 82, 74, 231, 156, 115, 78, 74, 41, 37, 132, 206, 65, 39, 165, 148, 210, 57, 231, 156, 19, 0, 0, 84, 224, 0, 0, 16, 96, 163, 200, 230, 4, 35, 65, 133, 134, 172, 4, 0, 82, 1, 0, 12, 142, 99, 89, 154, 230, 121, 162, 104, 154, 150, 36, 105, 154, 231, 121, 158, 40, 154, 166, 38, 73, 154, 230, 121, 158, 39, 138, 170, 201, 243, 60, 79, 20, 69, 209, 52, 85, 149, 231, 121, 158, 40, 138, 162, 105, 170, 42, 215, 21, 69, 211, 52, 77, 85, 85, 93, 178, 44, 138, 166, 105, 154, 170, 234, 186, 48, 77, 211, 84, 85, 215, 117, 93, 152, 166, 105, 170, 170, 235, 186, 46, 108, 91, 85, 85, 213, 117, 101, 25, 182, 173, 170, 170, 234, 186, 178, 12, 92, 215, 117, 101, 217, 150, 129, 44, 187, 174, 236, 218, 178, 0, 0, 240, 4, 7, 0, 160, 2, 27, 86, 71, 56, 41, 26, 11, 44, 52, 100, 37, 0, 144, 1, 0, 64, 24, 131, 144, 66, 8, 33, 101, 16, 66, 10, 33, 132, 148, 82, 8, 9, 0, 0, 24, 112, 0, 0, 8, 48, 161, 12, 20, 26, 178, 18, 0, 72, 5, 0, 0, 140, 177, 214, 90, 107, 173, 181, 214, 64, 103, 173, 181, 214, 90, 107, 173, 128, 204, 90, 107, 173, 181, 214, 90, 107, 173, 181, 214, 90, 107, 173, 181, 214, 82, 107, 173, 181, 214, 90, 107, 173, 181, 214, 90, 107, 173, 181, 214, 90, 107, 173, 181, 214, 90, 107, 173, 181, 214, 90, 107, 173, 181, 214, 90, 107, 173, 181, 214, 90, 107, 173, 181, 214, 90, 107, 173, 181, 214, 90, 107, 173, 181, 214, 90, 107, 45, 165, 148, 82, 74, 41, 165, 148, 82, 74, 41, 165, 148, 82, 74, 41, 165, 148, 82, 74, 5, 0, 250, 85, 56, 0, 248, 63, 216, 176, 58, 194, 73, 209, 88, 96, 161, 33, 43, 1, 128, 112, 0, 0, 192, 24, 165, 24, 115, 12, 66, 41, 165, 84, 8, 49, 230, 156, 116, 84, 90, 139, 177, 66, 136, 49, 231, 36, 164, 212, 90, 108, 197, 115, 206, 65, 40, 33, 149, 214, 98, 44, 158, 115, 14, 66, 41, 41, 197, 86, 99, 81, 41, 132, 82, 82, 74, 45, 182, 88, 139, 74, 161, 163, 146, 82, 74, 173, 213, 88, 140, 49, 169, 164, 214, 90, 139, 173, 198, 98, 140, 73, 41, 180, 212, 90, 139, 49, 22, 35, 108, 77, 169, 181, 216, 106, 171, 177, 24, 99, 107, 42, 45, 180, 24, 99, 140, 197, 8, 95, 100, 108, 45, 166, 218, 106, 13, 198, 8, 35, 91, 44, 45, 213, 90, 107, 48, 198, 24, 221, 91, 139, 165, 182, 154, 139, 49, 62, 248, 218, 82, 44, 49, 214, 92, 0, 0, 119, 131, 3, 0, 68, 130, 141, 51, 172, 36, 157, 21, 142, 6, 23, 26, 178, 18, 0, 8, 9, 0, 32, 16, 82, 138, 49, 198, 24, 115, 206, 57, 231, 164, 82, 140, 57, 230, 156, 115, 14, 66, 8, 161, 84, 138, 49, 198, 156, 115, 14, 66, 8, 33, 148, 140, 49, 230, 156, 115, 16, 66, 8, 33, 132, 82, 74, 198, 156, 115, 16, 66, 8, 33, 132, 144, 82, 234, 156, 115, 16, 66, 8, 33, 132, 16, 74, 41, 157, 115, 14, 66, 8, 33, 132, 16, 66, 41, 165, 131, 16, 66, 8, 33, 132, 16, 74, 40, 165, 164, 20, 66, 8, 33, 132, 16, 66, 8, 169, 164, 148, 66, 8, 33, 132, 82, 66, 40, 33, 149, 148, 82, 8, 33, 132, 16, 66, 41, 37, 164, 148, 82, 10, 33, 132, 82, 66, 8, 161, 132, 148, 82, 74, 41, 133, 16, 66, 8, 165, 148, 146, 82, 74, 41, 165, 18, 74, 9, 37, 132, 18, 82, 41, 41, 165, 20, 74, 8, 33, 148, 82, 74, 74, 41, 165, 84, 74, 9, 161, 132, 18, 74, 41, 37, 165, 148, 82, 74, 33, 132, 16, 74, 41, 5, 0, 0, 28, 56, 0, 0, 4, 24, 65, 39, 25, 85, 22, 97, 163, 9, 23, 30, 128, 66, 67, 86, 2, 0, 100, 0, 0, 144, 162, 148, 82, 41, 45, 69, 130, 34, 165, 24, 164, 24, 75, 70, 21, 115, 80, 90, 138, 168, 114, 12, 82, 205, 169, 82, 206, 32, 230, 36, 150, 136, 49, 132, 148, 147, 84, 50, 230, 20, 66, 12, 66, 234, 28, 117, 76, 41, 6, 45, 149, 24, 66, 198, 24, 164, 216, 114, 75, 161, 115, 14, 0, 0, 0, 65, 0, 128, 128, 144, 0, 0, 3, 4, 5, 51, 0, 192, 224, 0, 225, 115, 16, 116, 2, 4, 71, 27, 0, 128, 32, 68, 102, 136, 68, 195, 66, 112, 120, 80, 9, 16, 17, 83, 1, 64, 98, 130, 66, 46, 0, 84, 88, 92, 164, 93, 92, 64, 151, 1, 46, 232, 226, 174, 3, 33, 4, 33, 8, 65, 44, 14, 160, 128, 4, 28, 156, 112, 195, 19, 111, 120, 194, 13, 78, 208, 41, 42, 117, 32, 0, 0, 0, 0, 0, 12, 0, 240, 0, 0, 144, 92, 0, 17, 17, 209, 204, 97, 100, 104, 108, 112, 116, 120, 124, 128, 132, 136, 140, 144, 8, 0, 0, 0, 0, 0, 23, 0, 124, 0, 0, 36, 37, 64, 68, 68, 52, 115, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 1, 0, 128, 0, 2, 0, 0, 0, 0, 32, 128, 0, 4, 4, 4, 0, 0, 0, 0, 0, 2, 0, 0, 0, 4, 4, 79, 103, 103, 83, 0, 4, 150, 143, 0, 0, 0, 0, 0, 0, 158, 75, 203, 236, 2, 0, 0, 0, 56, 69, 61, 107, 91, 49, 58, 55, 56, 58, 47, 45, 46, 54, 56, 62, 46, 48, 44, 46, 51, 56, 59, 54, 46, 54, 51, 255, 9, 254, 247, 252, 46, 46, 45, 45, 48, 46, 59, 53, 61, 255, 0, 250, 242, 46, 45, 44, 43, 46, 45, 47, 58, 55, 53, 246, 229, 45, 45, 46, 42, 46, 45, 45, 59, 52, 255, 2, 247, 59, 55, 54, 47, 45, 55, 51, 55, 240, 231, 212, 203, 216, 216, 216, 193, 174, 206, 199, 209, 204, 221, 189, 171, 167, 48, 1, 132, 78, 79, 94, 9, 113, 161, 211, 147, 211, 133, 191, 127, 233, 105, 93, 69, 223, 59, 82, 32, 0, 114, 221, 127, 204, 21, 221, 167, 219, 109, 191, 47, 209, 149, 125, 10, 152, 253, 247, 253, 245, 55, 97, 29, 173, 230, 49, 20, 188, 78, 43, 230, 191, 104, 235, 244, 230, 124, 240, 253, 47, 71, 153, 130, 38, 203, 0, 116, 78, 88, 57, 230, 246, 242, 176, 108, 246, 227, 231, 63, 127, 126, 191, 155, 251, 19, 142, 39, 163, 64, 85, 255, 111, 159, 126, 54, 181, 87, 206, 227, 149, 103, 236, 23, 64, 0, 76, 225, 7, 134, 251, 83, 248, 137, 221, 191, 45, 91, 52, 147, 193, 4, 224, 147, 179, 60, 74, 178, 164, 180, 117, 250, 203, 113, 234, 234, 98, 140, 135, 83, 73, 28, 174, 57, 249, 84, 149, 182, 213, 82, 79, 209, 52, 73, 190, 52, 42, 95, 196, 28, 6, 132, 237, 61, 15, 77, 13, 223, 7, 29, 166, 178, 77, 159, 104, 25, 32, 0, 107, 253, 23, 235, 13, 26, 114, 111, 45, 218, 90, 220, 59, 207, 82, 85, 227, 70, 138, 210, 161, 29, 39, 25, 156, 104, 159, 191, 86, 74, 164, 109, 255, 78, 150, 73, 82, 49, 0, 100, 239, 91, 28, 70, 229, 122, 223, 102, 159, 222, 7, 136, 150, 12, 4, 224, 209, 40, 160, 201, 141, 235, 143, 244, 68, 1, 204, 245, 242, 109, 60, 116, 151, 210, 38, 219, 94, 178, 162, 236, 220, 45, 86, 222, 22, 78, 213, 202, 90, 38, 229, 74, 85, 53, 209, 165, 163, 76, 229, 195, 183, 209, 215, 173, 55, 88, 7, 223, 113, 241, 175, 215, 207, 66, 36, 25, 0, 0, 174, 100, 66, 234, 56, 11, 131, 39, 114, 199, 205, 102, 102, 143, 57, 62, 230, 114, 46, 174, 122, 249, 37, 234, 72, 48, 108, 229, 45, 246, 134, 117, 231, 45, 206, 17, 221, 190, 158, 3, 199, 2, 4, 192, 47, 27, 78, 239, 237, 213, 123, 191, 69, 122, 178, 110, 205, 9, 242, 173, 158, 246, 58, 114, 115, 124, 188, 124, 152, 141, 0, 84, 105, 107, 134, 78, 218, 180, 94, 122, 220, 198, 15, 0, 14, 10, 4, 224, 86, 36, 152, 123, 179, 133, 79, 107, 144, 36, 81, 129, 39, 196, 168, 107, 72, 39, 119, 92, 19, 123, 234, 17, 101, 238, 71, 34, 2, 116, 231, 61, 207, 166, 235, 210, 7, 94, 3, 188, 117, 76, 112, 3, 5, 96, 63, 161, 178, 155, 86, 209, 245, 58, 203, 92, 170, 183, 104, 106, 178, 215, 241, 196, 116, 242, 82, 167, 105, 82, 31, 117, 117, 245, 92, 149, 232, 197, 149, 25, 231, 52, 5, 124, 239, 91, 214, 86, 180, 107, 223, 225, 24, 235, 91, 154, 216, 25, 244, 134, 4, 2, 48, 123, 156, 4, 141, 157, 233, 244, 176, 120, 62, 177, 134, 222, 101, 69, 246, 94, 74, 109, 143, 100, 245, 247, 166, 205, 215, 74, 61, 181, 137, 57, 170, 55, 168, 136, 0, 124, 239, 157, 142, 187, 252, 189, 247, 186, 154, 125, 238, 113, 231, 16, 68, 111, 119, 163, 128, 0, 248, 251, 74, 161, 249, 209, 252, 196, 96, 111, 85, 217, 130, 39, 180, 84, 218, 102, 255, 99, 207, 185, 100, 183, 114, 253, 173, 169, 10, 182, 158, 92, 14, 84, 147, 110, 165, 249, 143, 75, 0, 124, 233, 13, 206, 233, 117, 231, 45, 205, 209, 219, 122, 61, 162, 18, 8, 192, 104, 155, 145, 223, 85, 79, 103, 100, 212, 152, 47, 172, 15, 61, 80, 150, 113, 88, 85, 43, 18, 169, 234, 233, 77, 31, 89, 41, 42, 68, 235, 219, 91, 107, 190, 41, 125, 179, 206, 1, 221, 206, 30, 36, 130, 2, 1, 184, 176, 111, 56, 52, 213, 205, 170, 63, 120, 158, 181, 223, 43, 129, 160, 52, 243, 222, 151, 117, 57, 237, 17, 229, 119, 124, 141, 113, 0, 116, 237, 59, 28, 214, 244, 91, 239, 120, 156, 246, 90, 182, 137, 100, 32, 0, 55, 38, 55, 183, 13, 6, 102, 0, 75, 76, 46, 127, 79, 1, 104, 93, 146, 37, 160, 146, 126, 233, 87, 28, 148, 198, 0, 84, 225, 141, 207, 228, 119, 157, 55, 186, 53, 231, 1, 136, 100, 0, 0, 184, 18, 96, 211, 100, 231, 10, 157, 18, 161, 126, 42, 93, 233, 251, 68, 99, 152, 26, 76, 117, 84, 254, 163, 228, 61, 67, 101, 142, 2, 44, 219, 221, 168, 51, 119, 61, 246, 44, 187, 221, 30, 0, 28, 11, 2, 94, 110, 192, 100, 212, 61, 229, 6, 102, 132, 109, 107, 11, 71, 113, 180, 179, 108, 215, 101, 157, 173, 162, 253, 157, 157, 252, 252, 104, 222, 25, 176, 159, 45, 26, 60, 217, 188, 217, 250, 209, 55, 238, 253, 186, 183, 102, 162, 21, 17, 12, 165, 172, 83, 163, 4, 107, 204, 158, 139, 69, 111, 91, 117, 105, 175, 174, 218, 182, 74, 154, 38, 231, 227, 114, 186, 50, 181, 204, 236, 247, 235, 211, 228, 139, 28, 198, 198, 231, 147, 42, 76, 225, 90, 187, 225, 159, 112, 237, 115, 239, 254, 114, 127, 162, 45, 72, 195, 168, 218, 59, 94, 114, 175, 149, 167, 214, 89, 17, 114, 6, 203, 9, 115, 174, 43, 157, 139, 130, 171, 84, 42, 105, 243, 145, 248, 144, 54, 83, 211, 87, 209, 84, 254, 75, 73, 165, 60, 181, 30, 108, 233, 37, 240, 226, 219, 121, 13, 188, 252, 59, 121, 214, 78, 72, 164, 80, 239, 147, 183, 221, 228, 94, 167, 183, 51, 158, 78, 24, 20, 242, 53, 169, 225, 210, 190, 184, 14, 53, 157, 101, 34, 234, 229, 103, 234, 132, 223, 189, 90, 165, 234, 167, 2, 92, 231, 208, 147, 246, 183, 244, 146, 152, 184, 123, 121, 218, 142, 100, 69, 80, 129, 143, 55, 136, 97, 182, 168, 213, 51, 179, 150, 97, 69, 158, 169, 184, 169, 187, 235, 84, 185, 158, 228, 106, 50, 102, 106, 87, 41, 92, 235, 37, 148, 244, 179, 244, 6, 229, 254, 7, 104, 50, 20, 56, 223, 1, 188, 118, 61, 59, 43, 98, 117, 94, 119, 201, 213, 117, 111, 11, 53, 247, 237, 177, 211, 16, 178, 54, 154, 88, 77, 153, 243, 53, 35, 123, 87, 155, 253, 237, 184, 55, 0, 100, 105, 221, 123, 233, 89, 58, 196, 191, 233, 247, 211, 244, 84, 131, 136, 72, 25, 56, 214, 255, 223, 224, 76, 251, 170, 147, 18, 166, 44, 197, 104, 117, 46, 56, 160, 222, 127, 37, 109, 93, 171, 12, 159, 75, 157, 195, 53, 63, 45, 0, 58, 138, 37, 245, 17, 157, 147, 208, 161, 83, 77, 211, 84, 47, 117, 103, 162, 7, 179, 71, 145, 93, 168, 149, 124, 159, 95, 252, 219, 7, 70, 245, 41, 31, 103, 91, 229, 30, 125, 110, 28, 215, 120, 60, 43, 218, 73, 164, 24, 37, 51, 51, 57, 111, 170, 170, 42, 2, 121, 212, 121, 223, 59, 169, 107, 198, 123, 252, 231, 208, 231, 58, 81, 205, 184, 122, 142, 65, 223, 86, 62, 244, 152, 237, 237, 103, 141, 245, 66, 205, 206, 226, 114, 246, 190, 228, 76, 175, 105, 87, 180, 107, 203, 13, 0, 168, 180, 198, 227, 128, 232, 24, 149, 2, 86, 85, 54, 177, 238, 21, 77, 24, 144, 81, 229, 218, 67, 198, 81, 40, 90, 80, 196, 176, 76, 139, 236, 44, 78, 118, 84, 75, 209, 83, 104, 42, 203, 141, 134, 82, 204, 53, 75, 77, 79, 5, 197, 230, 158, 116, 78, 233, 141, 252, 76, 38, 78, 160, 232, 113, 51, 161, 38, 59, 139, 94, 13, 88, 192, 244, 16, 118, 51, 100, 141, 151, 130, 164, 231, 106, 32, 122, 155, 109, 64, 13, 170, 139, 248, 231, 176, 32, 52, 137, 122, 170, 236, 44, 33, 165, 70, 205, 97, 250, 193, 45, 66, 168, 95, 157, 83, 14, 19, 221, 233, 14, 240, 84, 232, 148, 66, 12, 52, 152, 118, 49, 146, 19, 115, 141, 128, 244, 93, 198, 15, 209, 97, 125, 147, 35, 125, 255, 33, 93, 92, 142, 176, 154, 64, 112, 212, 57, 7, 0, 126, 187, 45, 108, 155, 90, 193, 33, 9, 8, 213, 76, 106, 181, 85, 92, 211, 151, 115, 67, 130, 154, 146, 231, 88, 218, 1, 2, 59, 122, 41, 153, 13, 102, 153, 67, 85, 85, 17, 216, 126, 55, 105, 67, 237, 116, 63, 222, 68, 218, 175, 77, 142, 166, 116, 40, 158, 237, 75, 137, 245, 128, 17, 179, 173, 58, 204, 69, 115, 213, 121, 212, 94, 13, 172, 87, 46, 106, 81, 116, 71, 209, 63, 229, 12, 85, 43, 78, 17, 97, 8, 211, 165, 0, 85, 52, 166, 21, 79, 89, 21, 96, 130, 244, 66, 195, 101, 143, 61, 222, 92, 222, 107, 204, 203, 67, 149, 178, 166, 193, 238, 185, 149, 243, 12, 19, 185, 188, 159, 61, 185, 3, 171, 0, 196, 190, 217, 113, 18, 225, 78, 38, 163, 202, 206, 74, 119, 30, 22, 247, 58, 89, 128, 32, 111, 96, 52, 11, 160, 125, 114, 238, 100, 57, 119, 237, 48, 204, 180, 27, 191, 54, 43, 203, 249, 139, 128, 1, 33, 100, 30, 100, 111, 84, 35, 36, 87, 169, 183, 77, 5, 45, 64, 34, 2, 100, 35, 1, 224, 200, 194, 96, 47, 123, 43, 200, 59, 84, 34, 209, 232, 228, 179, 38, 141, 102, 55, 197, 158, 115, 80, 151, 60, 123, 11, 50, 228, 235, 221, 23, 185, 66, 214, 7, 241, 192, 235, 85, 68, 140, 189, 189, 185, 184, 61, 159, 150, 33, 199, 121, 84, 186, 218, 0, 222, 187, 157, 205, 35, 243, 22, 100, 76, 21, 106, 195, 217, 108, 21, 175, 156, 35, 204, 106, 104, 170, 169, 13, 159, 27, 11, 81, 145, 236, 210, 200, 82, 146, 85, 149, 171, 42, 2, 193, 161, 45, 198, 110, 131, 243, 236, 152, 42, 247, 116, 196, 234, 114, 57, 253, 162, 25, 143, 79, 210, 165, 49, 133, 225, 220, 42, 159, 84, 88, 74, 154, 222, 20, 86, 247, 213, 60, 222, 170, 127, 231, 162, 30, 211, 37, 0, 209, 140, 100, 0, 22, 203, 147, 218, 165, 78, 166, 152, 149, 139, 106, 186, 161, 70, 161, 110, 194, 114, 87, 127, 229, 206, 222, 117, 229, 201, 194, 84, 145, 181, 215, 91, 113, 67, 202, 80, 228, 20, 93, 211, 100, 83, 209, 233, 63, 155, 247, 241, 138, 179, 115, 47, 159, 180, 222, 166, 177, 208, 84, 141, 202, 168, 61, 46, 44, 235, 117, 108, 228, 192, 140, 19, 18, 42, 33, 135, 97, 173, 96, 29, 90, 84, 252, 34, 81, 214, 195, 209, 7, 104, 11, 166, 97, 90, 223, 154, 114, 246, 189, 122, 218, 109, 230, 86, 177, 169, 27, 18, 76, 200, 217, 43, 9, 204, 190, 209, 58, 96, 129, 38, 161, 127, 91, 182, 233, 175, 109, 210, 67, 97, 115, 101, 150, 217, 174, 119, 24, 210, 158, 214, 23, 101, 161, 173, 91, 83, 239, 77, 175, 72, 212, 238, 59, 183, 45, 0, 150, 169, 77, 232, 158, 86, 200, 128, 13, 181, 199, 201, 170, 231, 111, 89, 157, 104, 48, 160, 246, 248, 7, 0, 112, 108, 218, 47, 39, 91, 201, 210, 68, 70, 238, 236, 61, 51, 153, 67, 177, 42, 82, 5, 160, 191, 187, 98, 16, 151, 131, 96, 254, 107, 141, 45, 237, 127, 204, 143, 247, 170, 119, 78, 85, 223, 58, 253, 197, 219, 19, 155, 253, 184, 15, 235, 7, 78, 152, 79, 79, 171, 45, 226, 201, 212, 1, 84, 106, 177, 143, 250, 237, 62, 149, 254, 150, 106, 86, 55, 61, 113, 3, 13, 144, 10, 179, 146, 2, 154, 76, 150, 85, 115, 58, 143, 253, 41, 195, 184, 140, 186, 74, 130, 233, 138, 168, 105, 144, 199, 9, 53, 79, 122, 83, 27, 236, 130, 52, 10, 163, 116, 246, 64, 193, 104, 168, 12, 169, 13, 94, 68, 65, 3, 174, 118, 186, 3, 198, 89, 130, 233, 169, 100, 180, 24, 198, 238, 50, 37, 186, 82, 154, 134, 116, 123, 1, 64, 69, 13, 192, 194, 242, 48, 192, 3, 8, 36, 1, 96, 0, 96, 197, 2, 172, 170, 153, 119, 210, 38, 46, 190, 191, 234, 244, 178, 60, 122, 203, 55, 96, 39, 91, 12, 154, 101, 38, 107, 61, 118, 215, 15, 223, 61, 161, 90, 167, 50, 157, 245, 232, 128, 180, 123, 242, 86, 203, 50, 200, 214, 16, 148, 123, 215, 85, 154, 178, 59, 202, 22, 96, 11, 52, 223, 189, 69, 240, 111, 202, 71, 201, 248, 126, 128, 72, 129, 0, 252, 88, 0, 239, 190, 158, 106, 67, 117, 32, 83, 100, 239, 185, 238, 214, 128, 33, 84, 182, 20, 210, 198, 133, 212, 89, 15, 251, 253, 189, 40, 68, 225, 231, 112, 166, 91, 190, 71, 144, 191, 15, 0, 86, 129, 0, 92, 157, 1, 238, 125, 215, 119, 150, 50, 230, 228, 170, 186, 157, 249, 60, 182, 162, 223, 59, 133, 220, 21, 89, 57, 120, 49, 239, 11, 201, 6, 60, 221, 147, 145, 221, 178, 61, 41, 101, 176, 15, 16, 177, 2, 176, 47, 51, 224, 199, 98, 76, 41, 39, 138, 236, 86, 45, 231, 172, 77, 69, 134, 74, 67, 132, 25, 127, 238, 179, 107, 235, 88, 185, 139, 62, 44, 223, 7, 57, 249, 5, 83, 141, 255, 192, 3, 64, 164, 64, 0, 222, 187, 101, 129, 203, 75, 212, 111, 102, 22, 236, 65, 41, 103, 89, 43, 73, 39, 33, 146, 15, 57, 198, 220, 165, 211, 181, 115, 187, 3, 4, 225, 13, 68, 67, 155, 239, 201, 201, 191, 95, 2, 16, 41, 16, 128, 47, 19, 1, 30, 28, 251, 248, 27, 245, 64, 40, 58, 113, 22, 253, 177, 87, 58, 43, 244, 76, 154, 209, 93, 183, 60, 158, 35, 217, 185, 232, 6, 44, 227, 39, 68, 250, 46, 225, 35, 197, 138, 126, 0, 176, 42, 0, 191, 236, 190, 128, 90, 174, 201, 55, 73, 133, 200, 105, 246, 174, 222, 41, 73, 136, 187, 233, 134, 247, 207, 220, 189, 123, 191, 252, 149, 133, 6, 36, 227, 128, 255, 83, 47, 227, 68, 255, 7, 62, 64, 26, 214, 95, 70, 130, 167, 165, 46, 107, 87, 131, 26, 235, 190, 75, 183, 173, 52, 65, 142, 129, 76, 18, 139, 171, 222, 182, 170, 121, 95, 109, 82, 109, 111, 91, 210, 104, 236, 142, 253, 141, 158, 212, 14, 79, 14, 13, 20, 217, 49, 250, 91, 72, 170, 229, 51, 211, 219, 30, 106, 209, 100, 74, 93, 175, 236, 85, 101, 65, 154, 185, 158, 221, 212, 200, 149, 156, 44, 133, 149, 235, 234, 64, 21, 90, 122, 115, 158, 109, 153, 75, 29, 233, 203, 35, 212, 35, 145, 250, 17, 36, 231, 67, 100, 165, 255, 112, 61, 65, 84, 250, 125, 128, 166, 92, 252, 103, 135, 132, 128, 93, 95, 125, 62, 64, 165, 161, 175, 30, 151, 109, 94, 107, 169, 27, 15, 51, 250, 107, 234, 173, 83, 105, 245, 86, 140, 191, 213, 46, 59, 165, 120, 151, 109, 85, 228, 191, 180, 206, 222, 12, 26, 122, 213, 126, 155, 232, 65, 120, 160, 168, 80, 83, 90, 202, 45, 105, 155, 42, 38, 125, 98, 58, 251, 168, 61, 249, 120, 81, 252, 23, 109, 95, 22, 95, 92, 222, 154, 98, 29, 103, 98, 97, 103, 201, 146, 17, 54, 88, 85, 85, 17, 232, 215, 204, 185, 142, 155, 163, 251, 58, 123, 220, 110, 154, 78, 108, 174, 15, 58, 93, 89, 204, 161, 113, 175, 177, 169, 85, 94, 87, 148, 83, 153, 150, 34, 93, 3, 217, 186, 154, 245, 156, 251, 106, 181, 50, 115, 90, 153, 16, 68, 23, 37, 43, 3, 108, 149, 10, 175, 133, 34, 99, 10, 3, 173, 149, 48, 73, 189, 87, 42, 151, 141, 83, 235, 106, 6, 200, 202, 115, 221, 81, 156, 80, 76, 153, 200, 221, 57, 184, 104, 0, 2, 43, 94, 54, 204, 30, 123, 24, 103, 50, 42, 121, 222, 107, 167, 81, 169, 10, 0, 68, 1, 34, 161, 241, 50, 70, 201, 194, 98, 217, 211, 184, 141, 129, 107, 23, 59, 109, 5, 119, 252, 35, 50, 93, 52, 174, 112, 143, 111, 9, 129, 37, 27, 221, 66, 156, 151, 103, 104, 133, 98, 237, 80, 250, 202, 142, 216, 197, 114, 236, 195, 98, 245, 143, 102, 58, 165, 163, 203, 167, 115, 143, 173, 218, 172, 17, 39, 17, 24, 173, 161, 209, 31, 202, 95, 224, 104, 107, 232, 83, 109, 233, 181, 99, 75, 179, 131, 194, 135, 19, 206, 43, 0, 190, 106, 109, 211, 25, 63, 206, 160, 41, 198, 164, 53, 37, 171, 213, 121, 186, 70, 151, 48, 160, 65, 205, 120, 79, 149, 96, 244, 158, 153, 137, 67, 30, 98, 85, 85, 4, 172, 191, 127, 243, 217, 154, 203, 249, 149, 28, 205, 237, 73, 191, 71, 53, 244, 214, 177, 237, 234, 195, 175, 99, 238, 155, 135, 12, 36, 95, 123, 235, 189, 231, 57, 215, 218, 251, 101, 97, 183, 151, 44, 215, 151, 117, 158, 31, 181, 171, 170, 89, 173, 203, 186, 179, 56, 82, 217, 139, 20, 73, 103, 195, 42, 128, 101, 98, 250, 22, 122, 251, 202, 108, 22, 253, 90, 172, 75, 84, 14, 45, 214, 150, 135, 89, 57, 76, 172, 161, 186, 147, 18, 152, 238, 39, 129, 196, 9, 147, 211, 111, 55, 122, 33, 78, 76, 150, 90, 38, 162, 205, 178, 162, 108, 3, 144, 212, 110, 32, 135, 168, 71, 206, 202, 137, 225, 134, 244, 98, 103, 200, 160, 211, 107, 39, 84, 226, 44, 71, 53, 214, 201, 32, 150, 40, 29, 192, 4, 171, 48, 50, 226, 250, 48, 61, 250, 178, 40, 38, 220, 213, 3, 22, 32, 203, 200, 17, 86, 21, 102, 224, 102, 197, 191, 44, 24, 48, 211, 9, 52, 154, 13, 13, 76, 38, 217, 21, 195, 140, 123, 218, 161, 90, 222, 146, 217, 34, 38, 197, 246, 112, 185, 204, 41, 74, 118, 142, 129, 211, 142, 6, 20, 54, 57, 173, 237, 22, 38, 1, 14, 216, 77, 140, 132, 182, 122, 137, 78, 2, 108, 168, 61, 126, 126, 243, 233, 223, 202, 91, 21, 250, 85, 236, 247, 103, 87, 65, 141, 52, 50, 163, 236, 145, 202, 85, 85, 17, 0, 110, 12, 152, 152, 159, 61, 235, 220, 206, 116, 99, 214, 169, 100, 119, 227, 167, 225, 62, 29, 113, 24, 172, 237, 229, 241, 226, 121, 61, 247, 203, 234, 245, 101, 219, 238, 179, 48, 134, 19, 31, 163, 198, 114, 145, 229, 183, 15, 132, 170, 108, 134, 1, 21, 56, 75, 14, 40, 53, 202, 30, 200, 56, 42, 100, 136, 28, 224, 128, 203, 209, 184, 115, 198, 69, 146, 14, 23, 168, 200, 114, 35, 195, 198, 195, 43, 177, 162, 38, 107, 211, 147, 37, 71, 88, 181, 120, 106, 212, 155, 158, 123, 162, 81, 218, 68, 13, 245, 206, 177, 75, 70, 3, 66, 107, 15, 73, 163, 138, 66, 210, 211, 88, 83, 27, 146, 186, 182, 76, 122, 171, 24, 75, 179, 73, 25, 115, 153, 15, 211, 235, 66, 54, 247, 148, 231, 200, 232, 132, 78, 181, 180, 89, 230, 32, 98, 82, 20, 68, 187, 49, 86, 162, 9, 161, 152, 51, 79, 236, 124, 175, 96, 56, 182, 34, 180, 189, 215, 169, 225, 113, 64, 204, 237, 48, 87, 153, 182, 0, 64, 45, 27, 60, 143, 86, 1, 12, 215, 189, 56, 239, 13, 209, 108, 255, 11, 31, 0, 167, 2, 240, 246, 49, 192, 237, 215, 6, 176, 133, 214, 225, 242, 200, 184, 186, 220, 213, 139, 125, 220, 171, 40, 141, 151, 248, 207, 88, 120, 88, 31, 80, 0, 36, 219, 231, 136, 134, 38, 213, 215, 66, 195, 61, 0, 17, 20, 8, 192, 125, 14, 112, 203, 147, 135, 220, 146, 48, 81, 228, 146, 67, 9, 71, 24, 232, 10, 123, 117, 71, 123, 148, 204, 54, 229, 24, 7, 1, 12, 209, 189, 200, 184, 166, 251, 206, 195, 207, 7, 32, 82, 1, 184, 10, 192, 189, 183, 247, 184, 154, 19, 202, 238, 57, 123, 236, 121, 64, 68, 52, 5, 122, 185, 78, 241, 217, 31, 36, 15, 247, 45, 0, 20, 215, 163, 169, 226, 27, 170, 7, 179, 191, 61, 0, 68, 42, 0, 239, 122, 128, 175, 105, 150, 75, 175, 4, 241, 18, 187, 128, 208, 120, 209, 244, 84, 35, 130, 240, 227, 231, 132, 80, 205, 34, 0, 4, 211, 61, 249, 132, 26, 166, 15, 238, 47, 236, 254, 254, 111, 183, 70, 52, 68, 66, 32, 0, 43, 153, 241, 252, 183, 214, 242, 240, 211, 25, 164, 21, 183, 15, 115, 148, 189, 92, 58, 236, 68, 150, 4, 252, 2, 20, 219, 231, 94, 211, 118, 119, 238, 9, 207, 251, 0, 56, 5, 2, 240, 205, 108, 193, 152, 59, 142, 107, 224, 169, 185, 156, 57, 214, 215, 95, 181, 147, 132, 53, 49, 227, 133, 251, 161, 13, 58, 245, 179, 0, 20, 221, 211, 145, 219, 94, 122, 32, 132, 223, 3, 16, 169, 0, 252, 170, 56, 96, 218, 216, 59, 180, 119, 4, 85, 177, 98, 221, 95, 3, 93, 75, 61, 106, 193, 100, 70, 164, 245, 123, 163, 90, 157, 215, 84, 171, 0, 20, 219, 29, 225, 143, 252, 216, 192, 255, 83, 62, 64, 52, 84, 224, 63, 10, 224, 90, 215, 255, 235, 180, 90, 85, 90, 237, 116, 58, 203, 113, 214, 208, 168, 27, 105, 81, 63, 114, 245, 182, 221, 235, 87, 221, 42, 73, 154, 213, 70, 99, 223, 39, 228, 200, 87, 139, 5, 20, 215, 28, 119, 6, 228, 199, 117, 23, 40, 156, 255, 7, 72, 20, 10, 124, 184, 10, 48, 250, 9, 83, 147, 35, 220, 74, 175, 203, 88, 173, 217, 74, 107, 122, 13, 212, 214, 195, 165, 178, 238, 197, 202, 115, 166, 62, 201, 253, 148, 99, 237, 249, 165, 15, 36, 221, 53, 102, 166, 45, 174, 251, 136, 77, 254, 3, 164, 172, 192, 217, 76, 240, 114, 225, 121, 212, 40, 46, 68, 87, 150, 213, 188, 172, 42, 42, 235, 83, 15, 183, 125, 181, 223, 54, 181, 154, 165, 202, 214, 83, 171, 23, 253, 89, 37, 189, 41, 186, 57, 173, 203, 51, 76, 36, 124, 224, 86, 193, 110, 195, 77, 106, 93, 95, 209, 5, 105, 159, 248, 83, 77, 237, 240, 19, 207, 215, 95, 159, 244, 187, 179, 47, 47, 190, 91, 207, 99, 53, 74, 35, 109, 26, 116, 207, 44, 169, 88, 177, 98, 17, 0, 0, 140, 246, 90, 24, 191, 238, 112, 77, 173, 251, 14, 43, 230, 87, 119, 158, 236, 23, 198, 123, 47, 29, 82, 31, 63, 227, 113, 174, 175, 95, 251, 221, 99, 85, 155, 238, 184, 151, 244, 94, 246, 69, 42, 151, 126, 239, 90, 74, 8, 100, 96, 11, 8, 65, 228, 34, 38, 214, 62, 26, 140, 221, 23, 253, 103, 113, 198, 145, 3, 7, 107, 179, 72, 185, 184, 85, 42, 161, 124, 178, 174, 56, 138, 35, 51, 138, 1, 136, 245, 82, 105, 168, 82, 56, 83, 84, 34, 96, 58, 209, 162, 74, 25, 119, 87, 181, 59, 180, 97, 140, 195, 101, 245, 178, 62, 187, 246, 229, 81, 73, 201, 10, 114, 79, 55, 38, 205, 202, 52, 53, 54, 30, 86, 252, 239, 210, 177, 184, 120, 62, 255, 195, 188, 168, 60, 11, 198, 33, 253, 79, 182, 253, 140, 143, 36, 235, 136, 51, 4, 96, 62, 206, 68, 214, 72, 71, 4, 183, 41, 97, 23, 57, 83, 221, 62, 60, 57, 164, 121, 131, 86, 49, 45, 118, 222, 142, 84, 49, 199, 131, 1, 182, 232, 244, 211, 35, 34, 144, 70, 114, 16, 215, 30, 27, 159, 101, 121, 149, 20, 4, 188, 161, 246, 248, 11, 0, 112, 59, 144, 55, 118, 47, 81, 210, 84, 174, 114, 85, 17, 0, 0, 136, 171, 13, 195, 92, 34, 250, 60, 250, 88, 49, 206, 30, 167, 191, 68, 238, 245, 128, 255, 95, 249, 95, 95, 74, 187, 161, 48, 46, 192, 249, 177, 185, 81, 231, 31, 111, 149, 161, 219, 1, 157, 217, 219, 91, 132, 65, 187, 205, 170, 101, 183, 70, 173, 47, 154, 94, 253, 106, 186, 77, 169, 166, 82, 145, 138, 187, 114, 202, 100, 175, 0, 105, 89, 150, 166, 173, 105, 71, 142, 249, 212, 174, 143, 57, 214, 181, 1, 3, 57, 150, 229, 38, 88, 79, 244, 99, 49, 245, 82, 83, 163, 201, 98, 137, 151, 193, 178, 226, 200, 42, 13, 192, 180, 111, 28, 199, 2, 112, 206, 160, 74, 67, 198, 12, 27, 187, 216, 20, 78, 9, 29, 199, 130, 141, 47, 53, 16, 131, 219, 207, 156, 2, 230, 165, 46, 129, 243, 54, 213, 172, 139, 118, 37, 171, 43, 203, 244, 208, 10, 120, 16, 197, 57, 246, 243, 226, 10, 30, 118, 78, 16, 167, 179, 99, 123, 107, 166, 43, 46, 34, 166, 179, 205, 105, 52, 44, 245, 9, 252, 218, 3, 101, 52, 63, 245, 142, 114, 240, 61, 3, 112, 10, 4, 224, 31, 93, 111, 48, 235, 101, 69, 100, 85, 225, 152, 37, 88, 89, 216, 165, 131, 41, 212, 1, 83, 191, 77, 63, 234, 250, 163, 59, 81, 4, 221, 163, 116, 55, 51, 116, 9, 37, 191, 167, 1, 56, 5, 2, 240, 150, 207, 6, 143, 119, 248, 20, 116, 101, 189, 195, 219, 104, 226, 206, 21, 244, 77, 163, 114, 212, 156, 30, 14, 209, 98, 255, 129, 46, 12, 219, 151, 170, 153, 58, 119, 238, 69, 153, 222, 3, 64, 164, 64, 0, 190, 113, 185, 65, 75, 59, 245, 12, 139, 35, 91, 183, 166, 153, 245, 80, 93, 21, 85, 230, 83, 158, 124, 189, 50, 52, 187, 152, 16, 217, 252, 208, 59, 153, 73, 183, 59, 239, 200, 27, 252, 7, 0, 171, 64, 0, 94, 127, 2, 38, 239, 24, 160, 77, 109, 15, 149, 166, 88, 45, 133, 18, 229, 8, 193, 140, 183, 122, 129, 47, 66, 34, 252, 212, 123, 211, 131, 123, 118, 174, 85, 67, 253, 0, 224, 84, 0, 214, 78, 130, 139, 207, 234, 179, 187, 19, 229, 92, 51, 225, 4, 101, 230, 169, 102, 65, 42, 179, 201, 147, 57, 70, 139, 247, 169, 13, 178, 1, 12, 215, 231, 30, 13, 253, 157, 15, 82, 243, 61, 0, 73, 128, 64, 0, 62, 154, 7, 252, 40, 105, 198, 198, 32, 174, 4, 37, 142, 106, 201, 180, 129, 224, 233, 237, 188, 14, 45, 155, 51, 83, 228, 36, 3, 244, 214, 230, 46, 236, 130, 233, 164, 102, 196, 61, 0, 145, 10, 192, 239, 171, 3, 196, 46, 252, 105, 67, 33, 99, 145, 233, 112, 113, 237, 34, 189, 44, 110, 60, 244, 69, 145, 227, 199, 157, 191, 88, 163, 0, 228, 208, 220, 58, 49, 205, 218, 89, 123, 190, 253, 31, 0, 151, 33, 2, 126, 117, 3, 207, 90, 217, 238, 23, 148, 9, 109, 91, 163, 233, 52, 235, 157, 207, 50, 171, 32, 8, 164, 86, 18, 213, 94, 181, 155, 251, 218, 223, 239, 117, 188, 13, 38, 81, 207, 109, 245, 214, 0, 44, 215, 155, 192, 173, 225, 121, 40, 52, 252, 189, 95, 63, 230, 65, 54, 165, 192, 253, 175, 221, 73, 217, 122, 61, 154, 235, 71, 165, 134, 244, 174, 99, 209, 185, 46, 97, 164, 141, 244, 70, 22, 95, 234, 167, 47, 92, 199, 247, 167, 227, 1, 186, 232, 148, 243, 99, 18, 141, 136, 171, 209, 182, 108, 42, 181, 199, 201, 168, 59, 94, 241, 137, 184, 104, 183, 108, 62, 118, 19, 126, 1, 0, 122, 241, 93, 102, 178, 233, 66, 71, 73, 37, 171, 170, 170, 138, 128, 179, 175, 59, 43, 25, 229, 198, 238, 207, 167, 119, 9, 207, 111, 12, 14, 187, 107, 238, 101, 219, 197, 26, 174, 179, 204, 207, 139, 79, 24, 30, 142, 190, 195, 71, 79, 201, 198, 137, 123, 42, 142, 166, 6, 234, 187, 13, 169, 165, 162, 201, 86, 210, 58, 235, 162, 89, 216, 71, 218, 78, 207, 78, 225, 193, 75, 93, 114, 58, 178, 28, 173, 129, 101, 89, 200, 12, 64, 181, 23, 195, 30, 115, 44, 82, 239, 7, 246, 41, 101, 87, 63, 119, 50, 18, 228, 244, 98, 141, 214, 96, 21, 13, 180, 155, 118, 100, 112, 234, 87, 19, 243, 68, 228, 42, 18, 89, 77, 214, 238, 4, 240, 98, 77, 131, 166, 176, 114, 1, 85, 10, 47, 163, 210, 220, 133, 182, 197, 106, 42, 34, 148, 146, 231, 78, 173, 75, 33, 20, 233, 44, 236, 122, 236, 170, 176, 168, 237, 213, 139, 73, 189, 48, 38, 47, 3, 201, 149, 37, 232, 230, 5, 202, 214, 200, 139, 215, 229, 15, 246, 206, 83, 55, 224, 211, 246, 206, 187, 235, 200, 196, 204, 178, 150, 197, 162, 225, 121, 1, 44, 72, 165, 213, 178, 111, 71, 28, 189, 16, 0, 7, 118, 216, 236, 199, 53, 182, 32, 58, 201, 237, 84, 140, 221, 196, 192, 101, 91, 175, 161, 144, 216, 99, 132, 146, 205, 199, 110, 225, 231, 243, 202, 167, 127, 122, 231, 230, 177, 61, 113, 248, 176, 181, 202, 85, 122, 69, 79, 216, 81, 239, 52, 77, 229, 114, 85, 85, 4, 1, 240, 173, 249, 177, 206, 172, 251, 141, 209, 4, 29, 159, 207, 189, 74, 206, 139, 52, 15, 109, 248, 60, 55, 174, 219, 182, 211, 86, 166, 148, 90, 42, 15, 6, 234, 37, 201, 233, 168, 221, 191, 146, 179, 73, 219, 132, 208, 49, 180, 12, 35, 148, 219, 6, 110, 10, 56, 157, 117, 77, 178, 120, 216, 89, 140, 90, 86, 199, 92, 119, 229, 212, 116, 210, 148, 173, 156, 249, 126, 40, 2, 40, 70, 83, 128, 194, 39, 3, 137, 10, 143, 200, 233, 153, 136, 166, 47, 139, 134, 88, 205, 47, 225, 67, 18, 12, 71, 51, 217, 205, 12, 75, 101, 5, 77, 123, 179, 8, 174, 213, 114, 121, 27, 123, 254, 38, 136, 226, 120, 113, 113, 186, 88, 17, 183, 239, 116, 53, 110, 201, 185, 174, 221, 237, 34, 102, 94, 185, 163, 101, 209, 47, 134, 176, 198, 130, 227, 62, 62, 56, 197, 226, 238, 155, 249, 128, 119, 190, 124, 248, 114, 178, 115, 5, 130, 139, 97, 26, 19, 157, 192, 207, 139, 13, 112, 166, 9, 96, 3, 204, 206, 53, 145, 252, 106, 231, 214, 144, 253, 30, 128, 102, 160, 192, 47, 142, 0, 239, 190, 177, 175, 83, 39, 41, 99, 161, 130, 40, 195, 56, 59, 15, 106, 236, 113, 38, 43, 136, 42, 14, 173, 54, 77, 218, 170, 36, 213, 86, 222, 127, 199, 212, 22, 199, 118, 109, 252, 3, 212, 206, 110, 188, 51, 96, 218, 96, 90, 226, 223, 254, 15, 16, 21, 11, 252, 122, 36, 40, 126, 85, 126, 153, 140, 41, 106, 59, 170, 150, 77, 66, 56, 34, 180, 2, 249, 238, 101, 113, 155, 208, 102, 117, 74, 225, 187, 216, 169, 3, 58, 39, 143, 103, 0, 4, 219, 229, 103, 242, 13, 213, 125, 229, 226, 255, 0, 81, 46, 1, 248, 68, 7, 26, 79, 215, 199, 132, 86, 250, 182, 116, 154, 118, 213, 72, 225, 148, 118, 220, 94, 151, 247, 238, 74, 155, 171, 180, 41, 241, 115, 21, 82, 41, 111, 196, 186, 207, 0, 236, 216, 125, 102, 22, 23, 108, 247, 85, 255, 216, 147, 0, 92, 22, 8, 192, 167, 8, 176, 99, 209, 121, 140, 184, 146, 66, 6, 78, 48, 105, 83, 76, 205, 146, 169, 106, 41, 189, 169, 220, 17, 46, 28, 68, 60, 0, 204, 208, 195, 234, 196, 179, 193, 116, 83, 113, 98, 254, 241, 221, 100, 123, 5, 136, 20, 8, 192, 252, 78, 184, 213, 72, 216, 90, 22, 100, 118, 9, 214, 213, 207, 132, 144, 67, 30, 143, 157, 142, 80, 227, 0, 12, 211, 181, 252, 47, 90, 100, 119, 150, 217, 223, 7, 136, 50, 5, 2, 208, 120, 18, 28, 243, 71, 43, 166, 72, 161, 186, 154, 166, 63, 102, 247, 210, 198, 11, 34, 203, 139, 195, 234, 92, 180, 218, 164, 145, 170, 215, 145, 169, 245, 58, 82, 163, 152, 1, 252, 216, 67, 250, 68, 30, 84, 119, 237, 55, 253, 3, 68, 148, 129, 0, 252, 227, 71, 128, 123, 106, 223, 198, 235, 20, 85, 51, 43, 237, 138, 134, 88, 196, 170, 184, 131, 137, 197, 56, 104, 101, 95, 61, 21, 238, 163, 88, 124, 145, 6, 28, 215, 245, 78, 214, 22, 213, 117, 86, 39, 236, 214, 252, 181, 157, 73, 164, 156, 129, 0, 200, 208, 134, 215, 8, 23, 151, 38, 123, 249, 38, 8, 92, 29, 61, 238, 179, 190, 221, 159, 36, 165, 183, 94, 204, 60, 249, 180, 77, 249, 234, 57, 239, 109, 216, 122, 184, 244, 253, 30, 156, 73, 15, 188, 85, 83, 123, 108, 92, 166, 249, 17, 217, 136, 182, 224, 171, 154, 218, 195, 47, 0, 64, 107, 58, 177, 236, 44, 25, 37, 85, 204, 85, 85, 33, 4, 0, 46, 71, 228, 217, 215, 25, 109, 202, 81, 175, 71, 210, 159, 161, 132, 122, 191, 71, 99, 188, 103, 111, 71, 186, 223, 120, 234, 176, 186, 186, 109, 79, 255, 94, 241, 255, 42, 131, 166, 107, 6, 147, 145, 84, 52, 57, 231, 182, 91, 40, 153, 252, 62, 235, 204, 217, 208, 92, 111, 122, 115, 26, 87, 4, 2, 100, 81, 57, 6, 179, 198, 200, 52, 45, 19, 25, 117, 156, 181, 142, 152, 117, 249, 112, 246, 83, 141, 215, 113, 76, 247, 51, 179, 72, 198, 121, 103, 49, 203, 152, 190, 10, 0, 43, 77, 147, 14, 208, 64, 96, 173, 164, 155, 147, 106, 205, 212, 217, 68, 86, 84, 104, 165, 129, 30, 106, 153, 103, 72, 51, 109, 178, 40, 26, 3, 137, 132, 138, 13, 136, 56, 30, 35, 195, 211, 187, 222, 237, 152, 102, 114, 190, 212, 185, 128, 161, 5, 214, 235, 224, 202, 206, 64, 243, 75, 52, 105, 79, 169, 6, 71, 112, 57, 195, 188, 184, 96, 58, 24, 208, 13, 24, 128, 40, 91, 86, 21, 44, 0, 247, 84, 136, 41, 0, 28, 167, 19, 0, 62, 185, 236, 151, 207, 152, 9, 25, 189, 233, 169, 130, 221, 196, 198, 101, 93, 190, 200, 216, 127, 230, 42, 121, 174, 107, 177, 155, 120, 243, 166, 104, 235, 25, 245, 44, 153, 56, 36, 84, 36, 2, 0, 0, 176, 93, 209, 219, 185, 247, 207, 226, 239, 134, 121, 81, 59, 34, 119, 156, 79, 121, 121, 133, 221, 102, 123, 240, 101, 78, 141, 190, 174, 173, 190, 213, 238, 183, 61, 174, 175, 186, 168, 4, 7, 161, 104, 74, 50, 65, 40, 192, 61, 109, 89, 5, 76, 27, 194, 101, 149, 25, 42, 33, 114, 96, 152, 51, 65, 40, 124, 218, 253, 87, 122, 118, 49, 27, 91, 61, 60, 199, 153, 54, 70, 222, 211, 56, 112, 100, 40, 58, 141, 27, 192, 45, 46, 139, 186, 188, 171, 73, 192, 185, 234, 158, 194, 45, 131, 115, 122, 56, 235, 15, 26, 211, 174, 113, 31, 61, 238, 129, 197, 206, 42, 4, 157, 233, 2, 70, 171, 156, 109, 9, 34, 203, 128, 0, 16, 96, 17, 131, 48, 114, 218, 27, 105, 57, 176, 49, 97, 4, 0, 150, 13, 132, 160, 213, 53, 159, 128, 28, 56, 64, 175, 140, 240, 203, 126, 26, 38, 151, 198, 137, 153, 221, 50, 27, 231, 108, 225, 9, 205, 78, 135, 45, 14, 246, 58, 18, 7, 254, 183, 92, 198, 7, 182, 218, 207, 238, 11, 85, 83, 83, 28, 92, 30, 227, 3, 59, 235, 227, 185, 224, 159, 172, 52, 53, 197, 59, 158, 222, 135, 130, 157, 81, 102, 38, 102, 86, 49, 2, 0, 1, 0, 0, 88, 95, 237, 93, 110, 121, 139, 251, 249, 235, 125, 203, 196, 168, 43, 218, 54, 145, 87, 171, 24, 50, 176, 25, 160, 70, 16, 198, 85, 76, 170, 193, 214, 134, 212, 58, 45, 154, 107, 102, 155, 51, 246, 89, 91, 157, 99, 46, 134, 12, 50, 0, 93, 89, 209, 224, 36, 50, 65, 200, 128, 226, 172, 182, 16, 170, 96, 4, 166, 174, 224, 245, 122, 121, 150, 33, 33, 171, 91, 131, 65, 38, 54, 125, 185, 165, 233, 201, 108, 40, 152, 196, 233, 137, 27, 119, 69, 236, 46, 145, 65, 33, 88, 76, 181, 219, 122, 155, 190, 197, 20, 24, 55, 105, 81, 73, 169, 209, 42, 107, 158, 129, 78, 44, 38, 193, 82, 135, 85, 250, 206, 92, 8, 221, 241, 16, 45, 136, 1, 1, 242, 34, 11, 208, 251, 22, 55, 188, 198, 65, 166, 27, 148, 159, 206, 245, 158, 158, 21, 184, 60, 45, 0, 204, 142, 69, 0, 30, 183, 92, 215, 43, 80, 47, 187, 199, 159, 50, 245, 51, 214, 45, 207, 237, 5, 204, 124, 153, 240, 7, 245, 51, 222, 193, 183, 103, 177, 51, 163, 164, 169, 114, 21, 0, 128, 1, 0, 0, 128, 69, 100, 59, 110, 123, 135, 163, 220, 141, 55, 182, 179, 168, 132, 97, 163, 101, 184, 245, 222, 180, 123, 167, 84, 31, 89, 219, 114, 100, 251, 40, 15, 27, 106, 220, 234, 171, 83, 145, 109, 202, 174, 112, 91, 223, 22, 105, 119, 173, 88, 191, 21, 160, 88, 0, 114, 90, 110, 40, 85, 135, 67, 86, 151, 103, 10, 231, 56, 43, 247, 122, 147, 174, 222, 7, 194, 154, 30, 104, 220, 81, 109, 140, 72, 67, 172, 54, 221, 168, 12, 70, 57, 53, 162, 193, 202, 12, 97, 237, 100, 50, 137, 171, 210, 116, 42, 46, 156, 110, 201, 126, 61, 146, 214, 186, 160, 169, 164, 154, 50, 104, 165, 138, 108, 103, 247, 19, 3, 245, 48, 47, 8, 13, 182, 88, 33, 72, 230, 210, 28, 71, 214, 130, 247, 170, 117, 102, 180, 239, 9, 207, 251, 178, 48, 140, 87, 0, 190, 19, 0, 0, 190, 182, 220, 143, 27, 48, 31, 118, 197, 87, 53, 53, 99, 219, 114, 123, 185, 3, 253, 230, 84, 188, 85, 168, 25, 223, 245, 140, 128, 30, 185, 118, 6, 59, 202, 136, 81, 82, 194, 80, 4, 0, 135, 32, 0, 0, 248, 181, 22, 149, 255, 255, 50, 179, 219, 37, 89, 239, 9, 71, 6, 227, 32, 61, 173, 210, 186, 98, 173, 98, 80, 47, 75, 233, 21, 147, 197, 111, 120, 117, 9, 27, 103, 175, 208, 244, 214, 250, 204, 200, 185, 72, 71, 176, 54, 144, 49, 80, 66, 53, 207, 226, 43, 165, 80, 163, 56, 227, 102, 249, 87, 7, 51, 53, 144, 227, 96, 125, 18, 148, 80, 162, 204, 68, 165, 8, 102, 208, 53, 171, 74, 63, 187, 68, 114, 136, 86, 26, 106, 93, 170, 239, 126, 228, 107, 110, 115, 166, 1, 24, 238, 97, 216, 103, 230, 126, 12, 219, 152, 253, 171, 53, 176, 66, 157, 70, 175, 251, 76, 210, 25, 146, 73, 85, 231, 212, 92, 246, 147, 36, 41, 187, 135, 191, 68, 183, 44, 228, 5, 1, 150, 209, 10, 206, 111, 178, 1, 248, 46, 33, 0, 100, 3, 192, 231, 63, 1, 28, 192, 174, 249, 60, 128, 24, 8, 0, 158, 182, 188, 93, 111, 64, 223, 109, 176, 161, 54, 28, 91, 110, 207, 43, 65, 219, 13, 176, 161, 54, 124, 255, 206, 228, 106, 0, 86, 136, 65, 103, 20, 209, 73, 26, 170, 2, 85, 0, 192, 48, 195, 131, 220, 52, 47, 123, 220, 109, 171, 172, 62, 184, 125, 103, 21, 213, 135, 244, 54, 205, 206, 41, 117, 51, 27, 187, 236, 116, 231, 118, 246, 216, 99, 231, 223, 139, 253, 122, 169, 106, 87, 79, 122, 242, 77, 86, 89, 19, 131, 129, 33, 171, 106, 109, 100, 33, 88, 82, 87, 88, 211, 66, 107, 83, 228, 90, 2, 210, 64, 79, 93, 76, 66, 10, 148, 85, 207, 221, 150, 234, 132, 172, 200, 116, 15, 93, 212, 214, 24, 181, 218, 251, 136, 237, 115, 239, 253, 12, 203, 208, 221, 156, 222, 81, 207, 174, 126, 107, 217, 219, 76, 124, 26, 87, 38, 34, 55, 237, 159, 112, 14, 117, 253, 82, 73, 42, 32, 79, 169, 74, 189, 42, 127, 205, 119, 10, 9, 16, 182, 101, 153, 79, 116, 123, 21, 110, 40, 129, 102, 227, 102, 27, 12, 94, 85, 0, 98, 176, 61, 216, 47, 244, 149, 133, 167, 2, 108, 0, 154, 210, 224, 200, 118, 0, 62, 182, 124, 92, 15, 92, 214, 237, 180, 14, 79, 181, 212, 67, 241, 108, 249, 113, 59, 232, 248, 110, 131, 119, 88, 212, 140, 239, 191, 63, 206, 27, 52, 205, 32, 118, 172, 157, 73, 211, 153, 18, 2, 69, 42, 64, 0, 128, 103, 247, 135, 240, 254, 21, 218, 242, 231, 12, 78, 145, 104, 252, 233, 243, 15, 217, 217, 228, 146, 58, 88, 108, 173, 23, 201, 210, 218, 193, 192, 74, 221, 205, 213, 84, 46, 123, 239, 195, 215, 93, 179, 189, 220, 220, 201, 140, 148, 213, 157, 3, 204, 100, 10, 226, 80, 201, 122, 211, 160, 144, 28, 220, 57, 51, 93, 107, 241, 106, 156, 48, 152, 79, 159, 99, 85, 250, 152, 110, 213, 221, 217, 2, 178, 195, 122, 78, 147, 140, 246, 51, 115, 218, 184, 57, 172, 249, 201, 239, 179, 19, 17, 239, 47, 64, 153, 83, 211, 80, 39, 107, 153, 222, 53, 176, 123, 232, 233, 188, 19, 40, 200, 187, 7, 152, 103, 168, 98, 108, 134, 155, 118, 80, 246, 180, 129, 71, 0, 0, 40, 141, 213, 125, 5, 8, 59, 157, 33, 45, 13, 0, 30, 202, 83, 128, 97, 93, 0, 8, 54, 36, 84, 1, 110, 27, 2, 0, 30, 182, 124, 220, 86, 118, 217, 111, 68, 188, 213, 58, 212, 140, 97, 203, 231, 229, 224, 165, 237, 128, 47, 212, 20, 207, 232, 17, 53, 51, 10, 69, 144, 16, 16, 0, 132, 16, 0, 113, 127, 151, 33, 247, 243, 245, 224, 34, 183, 185, 171, 192, 141, 239, 213, 223, 191, 22, 176, 62, 127, 127, 123, 93, 120, 124, 252, 126, 175, 186, 156, 158, 227, 124, 222, 109, 213, 113, 110, 175, 139, 57, 198, 121, 120, 230, 124, 113, 127, 155, 247, 137, 145, 219, 30, 64, 211, 80, 75, 145, 142, 156, 117, 209, 89, 85, 116, 175, 196, 94, 155, 240, 121, 123, 71, 109, 162, 111, 104, 82, 131, 7, 47, 245, 113, 244, 64, 169, 214, 6, 216, 130, 163, 41, 106, 207, 110, 167, 255, 89, 125, 54, 189, 214, 216, 95, 3, 45, 247, 78, 253, 53, 35, 229, 185, 115, 78, 214, 13, 0, 0, 0, 0, 208, 108, 2, 138, 133, 1, 0, 32, 109, 95, 8, 63, 2, 143, 61, 224, 6, 104, 10, 108, 102, 72, 128, 102, 3, 208, 128, 0, 52, 0, 62, 182, 252, 188, 238, 28, 124, 7, 188, 161, 166, 24, 182, 124, 93, 14, 222, 234, 59, 40, 190, 80, 83, 188, 27, 192, 232, 25, 37, 29, 196, 32, 2, 0, 0, 0, 0, 0, 0, 0, 0, 123, 179, 61, 240, 125, 143, 15, 227, 80, 200, 253, 76, 235, 158, 170, 165, 223, 221, 143, 202, 120, 255, 249, 233, 103, 99, 142, 249, 199, 227, 111, 187, 125, 168, 215, 117, 251, 227, 231, 229, 253, 245, 237, 231, 175, 115, 156, 243, 243, 236, 3, 95, 191, 120, 116, 110, 103, 65, 238, 242, 191, 10, 114, 226, 23, 96, 183, 241, 166, 114, 166, 99, 166, 184, 156, 51, 47, 204, 120, 24, 154, 225, 118, 222, 80, 57, 119, 110, 187, 7, 106, 88, 127, 165, 104, 0, 62, 15, 23, 251, 48, 197, 185, 50, 33, 89, 129, 72, 184, 217, 36, 238, 255, 151, 109, 85, 58, 127, 143, 121, 160, 18, 200, 188, 18, 0, 0, 63, 178, 109, 144, 4, 0, 62, 182, 124, 94, 119, 246, 242, 27, 112, 67, 109, 24, 182, 124, 94, 86, 222, 210, 27, 240, 132, 154, 241, 245, 111, 108, 0, 208, 51, 11, 157, 169, 170, 72, 72, 72, 4, 64, 107, 62, 23, 129, 233, 209, 128, 248, 179, 223, 140, 51, 166, 254, 19, 46, 39, 198, 158, 197, 253, 126, 103, 124, 51, 238, 213, 94, 43, 232, 176, 216, 125, 42, 94, 37, 155, 154, 255, 64, 63, 164, 98, 249, 168, 144, 213, 96, 87, 13, 193, 132, 167, 10, 201, 198, 9, 170, 120, 44, 149, 145, 87, 3, 232, 41, 28, 166, 238, 26, 235, 135, 171, 199, 93, 217, 211, 237, 237, 209, 154, 226, 46, 132, 168, 78, 170, 189, 244, 64, 215, 68, 188, 48, 239, 212, 204, 164, 78, 110, 210, 213, 173, 172, 204, 130, 162, 135, 7, 126, 46, 168, 33, 119, 157, 4, 240, 80, 5, 56, 118, 210, 252, 52, 27, 139, 97, 75, 139, 209, 224, 207, 81, 77, 83, 72, 235, 0, 64, 62, 25, 190, 193, 214, 44, 129, 86, 131, 233, 145, 60, 148, 109, 187, 81, 7, 176, 21, 98, 176, 113, 128, 155, 129, 0, 2, 100, 0, 62, 182, 124, 95, 87, 110, 23, 55, 224, 11, 53, 197, 176, 229, 235, 114, 240, 150, 216, 113, 225, 13, 53, 197, 215, 159, 175, 4, 208, 32, 163, 82, 34, 154, 138, 13, 33, 36, 2, 16, 4, 9, 0, 24, 207, 83, 80, 79, 145, 127, 240, 94, 127, 249, 165, 190, 191, 222, 205, 125, 191, 207, 199, 207, 191, 223, 239, 57, 20, 95, 231, 48, 180, 186, 158, 197, 121, 169, 27, 45, 204, 10, 225, 233, 110, 39, 51, 211, 90, 212, 168, 210, 140, 105, 130, 33, 171, 78, 142, 51, 218, 192, 216, 211, 216, 179, 55, 214, 218, 204, 188, 16, 37, 178, 139, 165, 186, 94, 186, 12, 149, 241, 246, 58, 111, 229, 100, 119, 39, 124, 137, 210, 0, 61, 169, 180, 127, 23, 94, 167, 186, 157, 108, 146, 24, 135, 158, 164, 174, 169, 2, 138, 157, 77, 11, 98, 99, 237, 244, 171, 180, 84, 115, 39, 31, 255, 154, 160, 130, 132, 99, 60, 246, 58, 22, 66, 223, 128, 64, 248, 143, 0, 65, 0, 246, 6, 128, 86, 146, 127, 68, 193, 9, 128, 3, 112, 192, 172, 0, 62, 182, 252, 184, 238, 20, 186, 219, 96, 67, 109, 120, 182, 252, 188, 158, 104, 249, 238, 141, 100, 24, 162, 54, 188, 52, 101, 130, 221, 163, 204, 40, 9, 2, 9, 1, 72, 36, 4, 16, 0, 181, 196, 255, 239, 199, 185, 170, 244, 215, 207, 251, 4, 242, 240, 118, 165, 247, 253, 86, 247, 51, 199, 219, 189, 169, 183, 59, 251, 141, 252, 170, 246, 55, 107, 114, 110, 179, 246, 235, 255, 239, 91, 110, 251, 97, 14, 247, 243, 160, 154, 89, 77, 213, 57, 115, 62, 109, 244, 208, 189, 44, 75, 78, 51, 70, 206, 218, 112, 114, 183, 226, 25, 180, 163, 251, 162, 120, 190, 51, 179, 11, 58, 85, 68, 249, 149, 26, 79, 228, 58, 73, 179, 35, 30, 168, 194, 67, 82, 126, 223, 26, 24, 81, 197, 84, 125, 13, 255, 57, 179, 247, 167, 232, 225, 134, 164, 247, 149, 209, 239, 107, 14, 211, 0, 142, 122, 0, 0, 30, 26, 240, 206, 167, 9, 222, 75, 144, 45, 0, 236, 23, 0, 198, 0, 100, 27, 0, 191, 166, 85, 33, 84, 105, 2, 96, 128, 38, 0, 77, 0, 160, 1, 113, 16, 124, 44, 96, 3, 190, 182, 252, 186, 175, 208, 196, 237, 43, 225, 78, 93, 67, 109, 216, 182, 252, 126, 157, 72, 216, 125, 33, 140, 100, 206, 181, 195, 75, 187, 13, 100, 207, 40, 147, 138, 67, 36, 4, 5, 81, 4, 9, 0, 0, 107, 223, 47, 12, 57, 166, 4, 183, 225, 86, 199, 89, 205, 79, 19, 63, 22, 95, 31, 198, 204, 190, 59, 239, 59, 253, 60, 13, 201, 93, 87, 94, 55, 0, 212, 117, 103, 1, 53, 231, 156, 85, 231, 215, 222, 84, 48, 196, 94, 238, 105, 56, 57, 104, 165, 108, 202, 86, 177, 101, 1, 116, 247, 68, 9, 152, 181, 104, 223, 203, 204, 221, 1, 163, 198, 91, 217, 71, 173, 70, 86, 15, 249, 94, 211, 69, 95, 9, 21, 251, 113, 110, 114, 23, 20, 44, 69, 38, 221, 167, 133, 119, 118, 197, 211, 195, 0, 160, 146, 49, 236, 46, 253, 73, 69, 201, 123, 59, 46, 55, 29, 133, 217, 88, 205, 141, 0, 184, 108, 3, 80, 247, 161, 217, 91, 155, 77, 211, 225, 90, 183, 7, 64, 22, 0, 0, 192, 3, 64, 179, 129, 109, 113, 67, 216, 6, 224, 17, 0, 30, 182, 124, 221, 86, 110, 226, 6, 220, 102, 106, 195, 177, 229, 235, 182, 210, 136, 219, 6, 95, 168, 41, 158, 158, 89, 232, 164, 67, 197, 170, 42, 4, 1, 240, 31, 71, 158, 34, 153, 106, 173, 215, 55, 115, 107, 229, 171, 131, 76, 163, 148, 62, 245, 126, 247, 195, 33, 255, 245, 73, 251, 124, 254, 27, 251, 150, 245, 99, 123, 246, 235, 163, 145, 117, 179, 4, 201, 208, 34, 78, 242, 162, 206, 254, 157, 206, 25, 178, 1, 112, 79, 43, 4, 0, 31, 138, 122, 114, 158, 20, 113, 119, 14, 3, 57, 68, 102, 83, 233, 153, 92, 166, 65, 89, 227, 132, 125, 101, 216, 156, 153, 185, 17, 174, 127, 80, 249, 232, 255, 196, 228, 192, 215, 36, 44, 0, 204, 175, 120, 147, 97, 134, 38, 129, 86, 242, 54, 100, 113, 104, 221, 131, 144, 0, 160, 235, 78, 230, 97, 166, 193, 50, 178, 120, 12, 64, 18, 63, 106, 255, 192, 22, 133, 38, 187, 24, 210, 142, 165, 87, 143, 58, 112, 43, 14, 233, 156, 31, 160, 17, 156, 107, 24, 40, 96, 246, 169, 233, 159, 3, 74, 58, 159, 21, 246, 167, 254, 152, 20, 156, 128, 133, 192, 38, 10, 52, 99, 128, 13, 0, 222, 181, 252, 56, 87, 118, 141, 29, 3, 79, 168, 41, 134, 45, 63, 183, 11, 111, 99, 71, 102, 190, 85, 83, 83, 108, 151, 204, 204, 18, 196, 132, 32, 8, 2, 0, 8, 0, 0, 0, 60, 246, 231, 109, 206, 219, 229, 231, 184, 157, 255, 230, 57, 210, 126, 253, 162, 168, 92, 225, 188, 79, 61, 253, 235, 202, 227, 210, 198, 128, 214, 139, 155, 219, 125, 125, 57, 243, 208, 117, 206, 233, 95, 183, 174, 186, 18, 128, 222, 71, 207, 12, 205, 243, 212, 7, 168, 202, 172, 60, 53, 115, 67, 114, 92, 57, 149, 192, 190, 249, 3, 69, 210, 187, 122, 179, 39, 79, 102, 189, 144, 84, 37, 217, 207, 8, 10, 128, 95, 50, 20, 221, 233, 63, 116, 115, 230, 208, 43, 31, 160, 42, 11, 160, 251, 235, 115, 245, 127, 178, 254, 191, 119, 105, 142, 233, 97, 207, 109, 62, 206, 49, 242, 117, 55, 0, 0, 0, 0, 0, 0, 92, 151, 13, 0, 0, 192, 54, 83, 0, 155, 237, 1, 67, 165, 196, 222, 2, 0, 158, 182, 252, 190, 237, 236, 33, 118, 96, 127, 67, 69, 77, 177, 107, 121, 63, 15, 150, 251, 14, 156, 47, 212, 20, 111, 200, 160, 205, 164, 51, 97, 177, 16, 0, 0, 0, 0, 0, 0, 0, 0, 60, 5, 178, 125, 156, 28, 26, 171, 224, 213, 139, 129, 229, 210, 193, 93, 59, 250, 35, 155, 117, 190, 121, 112, 117, 251, 103, 61, 44, 142, 219, 195, 39, 126, 140, 115, 198, 197, 250, 224, 168, 243, 85, 238, 155, 245, 56, 207, 193, 243, 52, 51, 204, 188, 239, 60, 239, 86, 194, 64, 83, 186, 212, 213, 228, 134, 120, 118, 85, 159, 187, 160, 171, 187, 122, 157, 22, 116, 103, 57, 161, 245, 84, 41, 197, 149, 77, 171, 203, 234, 102, 134, 164, 25, 42, 47, 92, 80, 245, 73, 81, 229, 81, 67, 145, 64, 107, 123, 234, 107, 183, 90, 76, 63, 227, 103, 169, 89, 55, 5, 106, 93, 0, 0, 90, 5, 192, 115, 0, 222, 181, 252, 60, 102, 246, 106, 59, 72, 190, 134, 168, 41, 142, 45, 255, 108, 71, 62, 116, 7, 210, 23, 199, 53, 197, 179, 244, 92, 146, 134, 197, 0, 0, 0, 32, 0, 0, 0, 0, 0, 0, 208, 219, 193, 227, 213, 220, 67, 92, 149, 17, 251, 214, 141, 231, 82, 106, 127, 33, 242, 209, 141, 156, 125, 186, 186, 156, 47, 114, 46, 111, 249, 175, 45, 47, 230, 190, 190, 253, 53, 23, 197, 125, 65, 93, 119, 206, 215, 109, 59, 251, 237, 231, 175, 215, 99, 159, 61, 71, 37, 244, 244, 240, 62, 37, 203, 64, 198, 87, 209, 3, 57, 231, 204, 197, 217, 153, 220, 9, 89, 89, 83, 33, 151, 92, 120, 250, 96, 3, 185, 212, 5, 147, 205, 60, 3, 215, 49, 16, 46, 67, 241, 113, 83, 0, 0, 179, 127, 89, 64, 101, 41, 128, 215, 43, 236, 6, 152, 30, 224, 206, 221, 7, 0, 192, 0, 30, 182, 252, 239, 44, 188, 212, 14, 212, 47, 212, 20, 191, 150, 255, 29, 133, 155, 216, 129, 250, 133, 154, 34, 21, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 128, 126, 95, 1, 0, 77, 13, 0, 14 )

[sub_resource type="Shader" id=43]
code = "shader_type canvas_item;
//
//uniform vec2 screensize = vec2(1024,600);

uniform sampler2D mask_texture;
//uniform vec2 scale = vec2(512,512); SIZE OF LIGHT
uniform vec2 position = vec2(0,0);
uniform vec2 ratio = vec2(2,1.17); //vec2(screensize/scale);
uniform float strength = float(1);

uniform vec2 position2 = vec2(0,0);
uniform vec2 ratio2 = vec2(2,1.17); //vec2(screensize/scale);
uniform float strength2 = float(0);

uniform vec2 position3 = vec2(0,0);
uniform vec2 ratio3 = vec2(2,1.17); //vec2(screensize/scale);
uniform float strength3 = float(0);

uniform vec2 position4 = vec2(0,0);
uniform vec2 ratio4 = vec2(2,1.17); //vec2(screensize/scale);
uniform float strength4 = float(0);

uniform vec2 position5 = vec2(0,0);
uniform vec2 ratio5 = vec2(2,1.17); //vec2(screensize/scale);
uniform float strength5 = float(0);

uniform vec2 world_pos = vec2(0,0);
//void vertex() {
////	world_pos = (WORLD_MATRIX * vec4(VERTEX, 1.0, 1.0)).xy;
//	world_pos = VERTEX/screensize;
////	VERTEX = world_pos;
//}

void fragment() {
vec4 texture_color = texture(TEXTURE, UV);
//if (strength > 0.09){
texture_color.a -= strength*texture(mask_texture, (((UV+world_pos)*ratio)-position)).a;
//	texture_color.a -= strength*texture(mask_texture, (UV*ratio+vec2(0.5,0.5)-(position)*ratio)).a;
//}
//if (strength2 > 0.09){
texture_color.a -= strength2*texture(mask_texture, (((UV+world_pos)*ratio2)-position2)).a;
//}
//if (strength3 > 0.09){
texture_color.a -= strength3*texture(mask_texture, (((UV+world_pos)*ratio3)-position3)).a;
//}
//if (strength4 > 0.09){
texture_color.a -= strength4*texture(mask_texture, (((UV+world_pos)*ratio4)-position4)).a;
//}
//if (strength5 > 0.09){
texture_color.a -= strength5*texture(mask_texture, (((UV+world_pos)*ratio5)-position5)).a;
//}
COLOR = texture_color;
}"

[sub_resource type="ShaderMaterial" id=44]
shader = SubResource( 43 )
shader_param/position = Vector2( 0, 0 )
shader_param/ratio = Vector2( 2, 1.17 )
shader_param/strength = 1.0
shader_param/position2 = Vector2( 0.8, 0.5 )
shader_param/ratio2 = Vector2( 2, 1.17 )
shader_param/strength2 = 0.0
shader_param/position3 = Vector2( 0, 0 )
shader_param/ratio3 = Vector2( 2, 1.17 )
shader_param/strength3 = 0.0
shader_param/position4 = Vector2( 0, 0 )
shader_param/ratio4 = Vector2( 2, 1.17 )
shader_param/strength4 = 0.0
shader_param/position5 = Vector2( 0, 0 )
shader_param/ratio5 = Vector2( 2, 1.17 )
shader_param/strength5 = 0.0
shader_param/world_pos = Vector2( 0, 0 )
shader_param/mask_texture = ExtResource( 35 )

[sub_resource type="TileSet" id=18]
0/name = "darksquare.png 0"
0/texture = ExtResource( 10 )
0/tex_offset = Vector2( 0, 0 )
0/modulate = Color( 1, 1, 1, 1 )
0/region = Rect2( 0, 0, 128, 128 )
0/tile_mode = 0
0/occluder_offset = Vector2( 0, 0 )
0/navigation_offset = Vector2( 0, 0 )
0/shape_offset = Vector2( 0, 0 )
0/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
0/shape_one_way = false
0/shape_one_way_margin = 0.0
0/shapes = [  ]
0/z_index = 0
1/name = "blacksquare.png 1"
1/texture = ExtResource( 8 )
1/tex_offset = Vector2( 0, 0 )
1/modulate = Color( 1, 1, 1, 1 )
1/region = Rect2( 0, 0, 128, 128 )
1/tile_mode = 0
1/occluder_offset = Vector2( 0, 0 )
1/navigation_offset = Vector2( 0, 0 )
1/shape_offset = Vector2( 0, 0 )
1/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
1/shape_one_way = false
1/shape_one_way_margin = 0.0
1/shapes = [  ]
1/z_index = 0
2/name = "fog2x2tiles.png 2"
2/texture = ExtResource( 40 )
2/tex_offset = Vector2( 0, 0 )
2/modulate = Color( 1, 1, 1, 1 )
2/region = Rect2( 0, 0, 512, 512 )
2/tile_mode = 1
2/autotile/bitmask_mode = 0
2/autotile/bitmask_flags = [ Vector2( 0, 0 ), 64, Vector2( 0, 1 ), 257, Vector2( 0, 2 ), 4, Vector2( 1, 0 ), 260, Vector2( 1, 1 ), 324, Vector2( 1, 2 ), 5, Vector2( 1, 3 ), 256, Vector2( 2, 0 ), 321, Vector2( 2, 1 ), 325, Vector2( 2, 2 ), 261, Vector2( 2, 3 ), 68, Vector2( 3, 0 ), 320, Vector2( 3, 1 ), 69, Vector2( 3, 2 ), 65, Vector2( 3, 3 ), 1 ]
2/autotile/icon_coordinate = Vector2( 0, 3 )
2/autotile/tile_size = Vector2( 128, 128 )
2/autotile/spacing = 0
2/autotile/occluder_map = [  ]
2/autotile/navpoly_map = [  ]
2/autotile/priority_map = [  ]
2/autotile/z_index_map = [  ]
2/occluder_offset = Vector2( 0, 0 )
2/navigation_offset = Vector2( 0, 0 )
2/shape_offset = Vector2( 0, 0 )
2/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
2/shape_one_way = false
2/shape_one_way_margin = 0.0
2/shapes = [  ]
2/z_index = 0

[sub_resource type="AtlasTexture" id=31]
flags = 4
atlas = ExtResource( 102 )
region = Rect2( 0, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=32]
flags = 4
atlas = ExtResource( 102 )
region = Rect2( 128, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=33]
flags = 4
atlas = ExtResource( 102 )
region = Rect2( 256, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=34]
flags = 4
atlas = ExtResource( 102 )
region = Rect2( 0, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=35]
flags = 4
atlas = ExtResource( 102 )
region = Rect2( 128, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=36]
flags = 4
atlas = ExtResource( 102 )
region = Rect2( 256, 128, 128, 128 )

[sub_resource type="SpriteFrames" id=37]
animations = [ {
"frames": [ SubResource( 31 ), SubResource( 32 ), SubResource( 33 ), SubResource( 34 ), SubResource( 35 ), SubResource( 36 ) ],
"loop": true,
"name": "kcursor",
"speed": 7.0
} ]

[sub_resource type="Animation" id=65]
resource_name = "shake"
length = 0.6
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 13, 4 ), Vector2( -17, 6 ), Vector2( 9, -2 ), Vector2( -4, -3 ), Vector2( 2, 1 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=66]
resource_name = "shake2"
length = 0.6
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 6, -12 ), Vector2( 8, 15 ), Vector2( -4, -2 ), Vector2( -4, 8 ), Vector2( 2, -2 ), Vector2( 0, 0 ) ]
}

[sub_resource type="AtlasTexture" id=67]
atlas = ExtResource( 131 )
region = Rect2( 0, 0, 128, 128 )

[sub_resource type="SpriteFrames" id=68]
animations = [ {
"frames": [ ExtResource( 132 ), ExtResource( 95 ), ExtResource( 94 ) ],
"loop": false,
"name": "speech",
"speed": 5.0
}, {
"frames": [ SubResource( 67 ) ],
"loop": true,
"name": "spin",
"speed": 5.0
} ]

[sub_resource type="Animation" id=71]
resource_name = "hide"
length = 0.1
tracks/0/type = "value"
tracks/0/path = NodePath(".:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}

[sub_resource type="Animation" id=69]
resource_name = "speech"
length = 0.3
tracks/0/type = "value"
tracks/0/path = NodePath(".:rotation_degrees")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.3 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 1, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:self_modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.3 ),
"transitions": PoolRealArray( 1, 0.3 ),
"update": 0,
"values": [ Color( 0.278431, 0.486275, 0.886275, 0.627451 ), Color( 0.298039, 0.247059, 0.74902, 0.470588 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:offset")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.3 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0, -64 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath(".:animation")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ "speech" ]
}
tracks/5/type = "value"
tracks/5/path = NodePath(".:visible")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}

[sub_resource type="Animation" id=70]
resource_name = "spin"
length = 0.6
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:rotation_degrees")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.6 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ 0.0, -360.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.3, 0.6 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 1.2, 1.2 ), Vector2( 1, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:self_modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.3, 0.6 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 0.333333, 0.843137, 1, 0.627451 ), Color( 0.254902, 0.796078, 0.796078, 0.470588 ), Color( 0.333333, 0.843137, 1, 0.627451 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:offset")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath(".:animation")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ "spin" ]
}
tracks/5/type = "value"
tracks/5/path = NodePath(".:visible")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}

[sub_resource type="TileSet" id=21]
2/name = "fogcloud2.png 2"
2/texture = ExtResource( 113 )
2/tex_offset = Vector2( 0, 0 )
2/modulate = Color( 1, 1, 1, 1 )
2/region = Rect2( 0, 0, 384, 384 )
2/tile_mode = 0
2/occluder_offset = Vector2( 0, 0 )
2/navigation_offset = Vector2( 0, 0 )
2/shape_offset = Vector2( 0, 0 )
2/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
2/shape_one_way = false
2/shape_one_way_margin = 0.0
2/shapes = [  ]
2/z_index = 0
3/name = "fogcloud2grad.png 3"
3/texture = ExtResource( 114 )
3/tex_offset = Vector2( 0, 0 )
3/modulate = Color( 1, 1, 1, 1 )
3/region = Rect2( 0, 0, 384, 384 )
3/tile_mode = 0
3/occluder_offset = Vector2( 0, 0 )
3/navigation_offset = Vector2( 0, 0 )
3/shape_offset = Vector2( 0, 0 )
3/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
3/shape_one_way = false
3/shape_one_way_margin = 0.0
3/shapes = [  ]
3/z_index = 0
4/name = "sandcloud.png 4"
4/texture = ExtResource( 133 )
4/tex_offset = Vector2( 0, 0 )
4/modulate = Color( 1, 1, 1, 1 )
4/region = Rect2( 0, 0, 384, 384 )
4/tile_mode = 0
4/occluder_offset = Vector2( 0, 0 )
4/navigation_offset = Vector2( 0, 0 )
4/shape_offset = Vector2( 0, 0 )
4/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
4/shape_one_way = false
4/shape_one_way_margin = 0.0
4/shapes = [  ]
4/z_index = 0
5/name = "sandcloud.png 5"
5/texture = ExtResource( 133 )
5/tex_offset = Vector2( 0, 0 )
5/modulate = Color( 1, 1, 1, 1 )
5/region = Rect2( 0, 384, 384, 256 )
5/tile_mode = 0
5/occluder_offset = Vector2( 0, 0 )
5/navigation_offset = Vector2( 0, 0 )
5/shape_offset = Vector2( 0, 0 )
5/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
5/shape_one_way = false
5/shape_one_way_margin = 0.0
5/shapes = [  ]
5/z_index = 0

[sub_resource type="Animation" id=22]
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 150 ), Vector2( 384, 150 ) ]
}

[sub_resource type="TileSet" id=23]
0/name = "bedrockhalf.png 0"
0/texture = ExtResource( 34 )
0/tex_offset = Vector2( 0, 0 )
0/modulate = Color( 1, 1, 1, 1 )
0/region = Rect2( 0, 0, 384, 384 )
0/tile_mode = 0
0/occluder_offset = Vector2( 0, 0 )
0/navigation_offset = Vector2( 0, 0 )
0/shape_offset = Vector2( 0, 0 )
0/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
0/shape_one_way = false
0/shape_one_way_margin = 0.0
0/shapes = [  ]
0/z_index = 0
1/name = "bedrocktop.png 1"
1/texture = ExtResource( 98 )
1/tex_offset = Vector2( 0, 0 )
1/modulate = Color( 1, 1, 1, 1 )
1/region = Rect2( 0, 0, 384, 384 )
1/tile_mode = 0
1/occluder_offset = Vector2( 0, 0 )
1/navigation_offset = Vector2( 0, 0 )
1/shape_offset = Vector2( 0, 0 )
1/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
1/shape_one_way = false
1/shape_one_way_margin = 0.0
1/shapes = [  ]
1/z_index = 0
2/name = "bedrocktopsmall.png 2"
2/texture = ExtResource( 99 )
2/tex_offset = Vector2( 0, 0 )
2/modulate = Color( 1, 1, 1, 1 )
2/region = Rect2( 0, 0, 384, 384 )
2/tile_mode = 0
2/occluder_offset = Vector2( 0, 0 )
2/navigation_offset = Vector2( 0, 0 )
2/shape_offset = Vector2( 0, 0 )
2/shape_transform = Transform2D( 1, 0, 0, 1, 0, 0 )
2/shape_one_way = false
2/shape_one_way_margin = 0.0
2/shapes = [  ]
2/z_index = 0

[sub_resource type="Shader" id=88]
code = "shader_type canvas_item;


uniform float hue_shift : hint_range(0.0,1.0) = 0.0;
uniform float sat_mul : hint_range(-1.0,10.0) = 0.0;
uniform float val_mul : hint_range(-1.0,10.0) = 0.0;

vec3 rgb2hsv(vec3 c) {
vec4 K = vec4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);
vec4 p = mix(vec4(c.bg, K.wz), vec4(c.gb, K.xy), step(c.b, c.g));
vec4 q = mix(vec4(p.xyw, c.r), vec4(c.r, p.yzx), step(p.x, c.r));
float d = q.x - min(q.w, q.y);
float e = 1.0e-10;
return vec3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), d / (q.x + e), q.x);
}

vec3 hsv2rgb(vec3 c) {
vec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
vec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);
return c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);
}

void fragment() {
vec4 texture_color = texture(TEXTURE, UV);
//vec4 texture_color = texture(SCREEN_TEXTURE, SCREEN_UV);
vec3 color_hsv = rgb2hsv(texture_color.rgb);
color_hsv.x = mod((color_hsv.x +hue_shift), 1.0);
color_hsv.y = min((color_hsv.y * (1.0 +sat_mul)), 1.01);
color_hsv.z = min((color_hsv.z * (1.0 +val_mul)), 1.01);
vec3 color_rgb = hsv2rgb(color_hsv);

COLOR.rgba = vec4(color_rgb.rgb,texture_color.a);
}"

[sub_resource type="ShaderMaterial" id=89]
shader = SubResource( 88 )
shader_param/hue_shift = 0.94
shader_param/sat_mul = -0.25
shader_param/val_mul = 0.13

[sub_resource type="ShaderMaterial" id=27]
shader = ExtResource( 60 )
shader_param/sine_time_scale = 0.5
shader_param/sine_offset_scale = Vector2( 0.3, 0.8 )

[sub_resource type="Animation" id=29]
length = 9.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 3, 4.5, 6, 9 ),
"transitions": PoolRealArray( 2, 0.5, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0, -20 ), Vector2( 0, -30 ), Vector2( 0, -20 ), Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 3, 4.5, 6, 9 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 0.705882, 0.862745, 0.705882, 1 ), Color( 0.941176, 0.862745, 0.901961, 1 ), Color( 1, 1, 1, 1 ), Color( 0.941176, 0.862745, 0.901961, 1 ), Color( 0.705882, 0.862745, 0.705882, 1 ) ]
}

[sub_resource type="Shader" id=91]
code = "shader_type canvas_item;

uniform float hue_shift : hint_range(0.0,1.0) = 0.0;
uniform float sat_mul : hint_range(0.0,10.0) = 1.0;
uniform float val_mul : hint_range(0.0,10.0) = 1.0;

vec3 rgb2hsv(vec3 c) {
vec4 K = vec4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);
vec4 p = mix(vec4(c.bg, K.wz), vec4(c.gb, K.xy), step(c.b, c.g));
vec4 q = mix(vec4(p.xyw, c.r), vec4(c.r, p.yzx), step(p.x, c.r));
float d = q.x - min(q.w, q.y);
float e = 1.0e-10;
return vec3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), d / (q.x + e), q.x);
}

vec3 hsv2rgb(vec3 c) {
vec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
vec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);
return c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);
}

void fragment() {
vec4 texture_color = texture(TEXTURE, UV);
//vec4 texture_color = texture(SCREEN_TEXTURE, SCREEN_UV);
vec3 color_hsv = rgb2hsv(texture_color.rgb);
color_hsv.x = mod((color_hsv.x + hue_shift), 1.0);
color_hsv.y = min((color_hsv.y * sat_mul), 1.01);
color_hsv.z = min((color_hsv.z * val_mul), 1.01);
vec3 color_rgb = hsv2rgb(color_hsv);
COLOR.rgba = vec4(color_rgb.rgb,texture_color.a);

}

"

[sub_resource type="ShaderMaterial" id=92]
shader = SubResource( 91 )
shader_param/hue_shift = 0.0
shader_param/sat_mul = 1.0
shader_param/val_mul = 1.0

[sub_resource type="Animation" id=90]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath("Camera2D:offset")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 0.5 ),
"update": 0,
"values": [ Vector2( -2000, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 0, 0, 0, 1 ) ]
}

[sub_resource type="Animation" id=38]
length = 0.8
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 0.8, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0, 0, 0, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Camera2D:offset")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 2, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 2000, 0 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("Camera2D:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = false
tracks/2/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 2, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 2000, 0 ) ]
}

[sub_resource type="Animation" id=64]
resource_name = "scrollalldark"
length = 0.8
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = false
tracks/0/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 0, 0, 0, 0 ), Color( 0, 0, 0, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Camera2D:offset")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = false
tracks/1/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 2, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 2000, 0 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("Camera2D:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = false
tracks/2/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 2, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 2000, 0 ) ]
}

[sub_resource type="Animation" id=39]
length = 0.8
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 1.2, 1 ),
"update": 0,
"values": [ Color( 0, 0, 0, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Camera2D:offset")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.8 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ Vector2( -2000, 0 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=86]
resource_name = "scrollallwarp"
length = 1.4
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3, 0.8, 1.4 ),
"transitions": PoolRealArray( 0.5, 1, 2, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.313726, 0.313726, 0.313726, 1 ), Color( 0.196078, 0.196078, 0.196078, 1 ), Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=79]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath("../CanvasLayer2/Dialogue:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("../CanvasLayer2/Dialogue:rect_position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("../CanvasLayer2/Dialogue:margin_right")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ 0.0 ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("../CanvasLayer2/Dialogue:margin_top")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ 0.0 ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("../CanvasLayer2/Dialogue:margin_bottom")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ -0.396851 ]
}

[sub_resource type="Animation" id=77]
resource_name = "screenshakebig"
length = 1.2
tracks/0/type = "value"
tracks/0/path = NodePath("../Camera2D:offset")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.5, 0.8, 1.2 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -8, 4 ), Vector2( 2, -8 ), Vector2( -2, 4 ), Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("../CanvasLayer2/Dialogue:rect_position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.2, 0.5, 0.9, 1.2 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 4, -6 ), Vector2( -5, 8 ), Vector2( 3, -2 ), Vector2( 0, 0 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("../CanvasLayer2/Dialogue:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.2, 1.2 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 0.784314, 0.784314, 1 ), Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=78]
resource_name = "screenshakehuge"
length = 1.6
tracks/0/type = "value"
tracks/0/path = NodePath("../Camera2D:offset")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.5, 1, 1.6 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -8, -12 ), Vector2( 4, 16 ), Vector2( -4, -4 ), Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("../CanvasLayer2/Dialogue:rect_position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.4, 1, 1.6 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 12, 8 ), Vector2( -4, -8 ), Vector2( 4, 4 ), Vector2( 0, 0 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("../CanvasLayer2/Dialogue:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.1, 1.6 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 0.72549, 0.72549, 1 ), Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Animation" id=62]
resource_name = "screenshakemain"
length = 0.9
tracks/0/type = "value"
tracks/0/path = NodePath("../Camera2D:offset")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.4, 0.6, 0.9 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -4, -2 ), Vector2( 1, 4 ), Vector2( -1, -1 ), Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("../CanvasLayer2/Dialogue:rect_position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.4, 0.7, 0.9 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 2, 3 ), Vector2( -1, -2 ), Vector2( 1, 1 ), Vector2( 0, 0 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("../CanvasLayer2/Dialogue:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}

[sub_resource type="Gradient" id=40]
offsets = PoolRealArray( 0, 0.0075188, 0.0676692, 0.804511, 1 )
colors = PoolColorArray( 0, 0, 0, 0, 1, 1, 1, 0, 1, 1, 1, 0.784314, 1, 1, 1, 0.568627, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=41]
gradient = SubResource( 40 )

[sub_resource type="ParticlesMaterial" id=42]
emission_shape = 2
emission_box_extents = Vector3( 2000, 1, 0 )
flag_disable_z = true
direction = Vector3( 1, 2, 0 )
gravity = Vector3( 0, 0, 0 )
initial_velocity = 250.0
initial_velocity_random = 0.7
angular_velocity = 120.0
angular_velocity_random = 0.7
orbit_velocity = 0.0
orbit_velocity_random = 0.0
radial_accel = 50.0
radial_accel_random = 1.0
damping = 5.0
color_ramp = SubResource( 41 )

[sub_resource type="AtlasTexture" id=51]
atlas = ExtResource( 28 )
region = Rect2( 0, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=52]
atlas = ExtResource( 28 )
region = Rect2( 128, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=53]
atlas = ExtResource( 28 )
region = Rect2( 256, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=54]
atlas = ExtResource( 28 )
region = Rect2( 384, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=55]
atlas = ExtResource( 28 )
region = Rect2( 512, 0, 128, 128 )

[sub_resource type="SpriteFrames" id=56]
animations = [ {
"frames": [ SubResource( 51 ), SubResource( 52 ), SubResource( 53 ), SubResource( 54 ), SubResource( 55 ), null ],
"loop": false,
"name": "caught",
"speed": 8.0
} ]

[sub_resource type="AtlasTexture" id=57]
atlas = ExtResource( 28 )
region = Rect2( 0, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=58]
atlas = ExtResource( 28 )
region = Rect2( 128, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=59]
atlas = ExtResource( 28 )
region = Rect2( 256, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=60]
atlas = ExtResource( 28 )
region = Rect2( 384, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=61]
atlas = ExtResource( 28 )
region = Rect2( 512, 128, 128, 128 )

[sub_resource type="SpriteFrames" id=50]
animations = [ {
"frames": [ SubResource( 57 ), SubResource( 58 ), SubResource( 59 ), SubResource( 60 ), SubResource( 61 ), null ],
"loop": false,
"name": "hit",
"speed": 8.0
} ]

[sub_resource type="Animation" id=63]
resource_name = "block"
length = 4.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1, 2, 3, 4 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.470588 ), Color( 1, 1, 1, 0.784314 ), Color( 1, 1, 1, 0.470588 ), Color( 1, 1, 1, 0.784314 ), Color( 1, 1, 1, 0.470588 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:rect_scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 2, 4 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 1.05, 1.1 ), Vector2( 1, 1 ) ]
}

[sub_resource type="AtlasTexture" id=72]
atlas = ExtResource( 134 )
region = Rect2( 0, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=73]
atlas = ExtResource( 134 )
region = Rect2( 256, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=74]
atlas = ExtResource( 134 )
region = Rect2( 128, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=75]
atlas = ExtResource( 134 )
region = Rect2( 256, 0, 128, 128 )

[sub_resource type="SpriteFrames" id=76]
animations = [ {
"frames": [ SubResource( 72 ), SubResource( 73 ), SubResource( 74 ), SubResource( 75 ) ],
"loop": true,
"name": "default",
"speed": 3.0
} ]

[sub_resource type="AtlasTexture" id=81]
flags = 4
atlas = ExtResource( 111 )
region = Rect2( 0, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=82]
flags = 4
atlas = ExtResource( 111 )
region = Rect2( 128, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=83]
flags = 4
atlas = ExtResource( 111 )
region = Rect2( 0, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=84]
flags = 4
atlas = ExtResource( 111 )
region = Rect2( 128, 128, 128, 128 )

[sub_resource type="SpriteFrames" id=11]
animations = [ {
"frames": [ SubResource( 81 ) ],
"loop": true,
"name": "conversation",
"speed": 5.0
}, {
"frames": [ SubResource( 82 ) ],
"loop": true,
"name": "exclamation",
"speed": 5.0
}, {
"frames": [ null ],
"loop": true,
"name": "none",
"speed": 5.0
}, {
"frames": [ SubResource( 83 ) ],
"loop": true,
"name": "normal",
"speed": 5.0
}, {
"frames": [ SubResource( 84 ) ],
"loop": true,
"name": "question",
"speed": 5.0
} ]

[sub_resource type="Animation" id=85]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath("quip:offset")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("quip:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0.65, 0.65 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("quip:rotation_degrees")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}

[sub_resource type="Animation" id=12]
resource_name = "quipanim"
length = 1.2
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("quip:rotation_degrees")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3, 0.7, 1.2 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, -8.0, 3.0, 0.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("quip:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.3, 0.6, 0.8, 1.2 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0.65, 0.65 ), Vector2( 1, 0.7 ), Vector2( 0.8, 0.9 ), Vector2( 1, 0.7 ), Vector2( 0.65, 0.65 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("quip:offset")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.7, 1.2 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -6, 7 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=13]
resource_name = "quipanim2"
length = 3.5
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("quip:offset")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.3, 3.5 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0, -30 ), Vector2( 0, 0 ), Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("quip:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1, 2.5, 3.5 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, -14.0, 14.0, 0.0 ]
}

[node name="Node2D" type="Node2D"]
modulate = Color( 0, 0, 0, 1 )
script = ExtResource( 7 )

[node name="CanvasLayer" type="CanvasLayer" parent="."]
layer = -5

[node name="StaticBackground" type="TextureRect" parent="CanvasLayer"]
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
expand = true
stretch_mode = 1

[node name="ParallaxBackground" type="ParallaxBackground" parent="CanvasLayer"]
layer = -4
offset = Vector2( -2000, -100 )
transform = Transform2D( 1, 0, 0, 1, -2000, -100 )

[node name="treesback" type="ParallaxLayer" parent="CanvasLayer/ParallaxBackground"]
modulate = Color( 1, 0.705882, 1, 1 )
show_behind_parent = true
motion_scale = Vector2( 0.08, 0.08 )
motion_offset = Vector2( -500, -50 )

[node name="TileMap" type="TileMap" parent="CanvasLayer/ParallaxBackground/treesback"]
material = ExtResource( 144 )
tile_set = SubResource( 1 )
cell_size = Vector2( 1920, 1080 )
format = 1

[node name="treesfront" type="ParallaxLayer" parent="CanvasLayer/ParallaxBackground"]
modulate = Color( 0.705882, 0.705882, 1, 1 )
motion_scale = Vector2( 0.3, 0.3 )
motion_offset = Vector2( 0, 350 )

[node name="TileMap" type="TileMap" parent="CanvasLayer/ParallaxBackground/treesfront"]
material = ExtResource( 144 )
tile_set = SubResource( 2 )
cell_size = Vector2( 1920, 1080 )
format = 1

[node name="CanvasModulate" type="CanvasModulate" parent="CanvasLayer/ParallaxBackground"]
visible = false

[node name="AnimationPlayer" type="AnimationPlayer" parent="CanvasLayer/ParallaxBackground/CanvasModulate"]
method_call_mode = 1
anims/lighting = SubResource( 3 )

[node name="AudioStreamPlayer" type="AudioStreamPlayer" parent="CanvasLayer"]
stream = ExtResource( 48 )
volume_db = -18.0
pitch_scale = 0.95
bus = "Ambience"

[node name="constructmap" type="TileMap" parent="."]
z_index = 3
tile_set = ExtResource( 47 )
cell_size = Vector2( 128, 128 )
cell_quadrant_size = 32
format = 1

[node name="watermap" type="TileMap" parent="."]
material = SubResource( 5 )
z_index = 10
tile_set = SubResource( 80 )
cell_size = Vector2( 128, 128 )
cell_quadrant_size = 32
format = 1

[node name="watermaptops" type="TileMap" parent="."]
material = SubResource( 9 )
position = Vector2( 0, 70 )
z_index = 13
tile_set = SubResource( 10 )
cell_size = Vector2( 128, 128 )
cell_quadrant_size = 32
format = 1

[node name="constructmapedges" type="TileMap" parent="."]
z_index = -2
z_as_relative = false
tile_set = ExtResource( 50 )
cell_size = Vector2( 128, 128 )
format = 1

[node name="constructbackground" type="TileMap" parent="."]
z_index = -35
tile_set = ExtResource( 51 )
cell_size = Vector2( 128, 128 )
format = 1

[node name="backgroundfeatures" type="TileMap" parent="constructbackground"]
self_modulate = Color( 0.588235, 0.588235, 0.588235, 1 )
tile_set = ExtResource( 21 )
cell_size = Vector2( 128, 128 )
format = 1

[node name="Map" type="TileMap" parent="."]
material = ExtResource( 144 )
z_index = 12
tile_set = ExtResource( 32 )
cell_size = Vector2( 128, 128 )
cell_quadrant_size = 32
show_collision = true
format = 1

[node name="Mapedges" type="TileMap" parent="."]
material = ExtResource( 144 )
z_index = -3
z_as_relative = false
tile_set = ExtResource( 107 )
cell_size = Vector2( 128, 128 )
format = 1

[node name="Mapbackground" type="TileMap" parent="."]
modulate = Color( 0.32549, 0.32549, 0.32549, 1 )
material = ExtResource( 144 )
z_index = -40
tile_set = SubResource( 16 )
cell_size = Vector2( 128, 128 )
format = 1

[node name="rulessprite" parent="." instance=ExtResource( 2 )]
position = Vector2( 164.921, 428.72 )

[node name="Camera2D" type="Camera2D" parent="."]
offset = Vector2( -2000, 0 )
current = true
process_mode = 0
limit_left = -10000
limit_top = -10000
limit_right = 10000
limit_bottom = 10000

[node name="enemyturn" type="Timer" parent="."]
wait_time = 0.2

[node name="Previewgrid" type="TileMap" parent="."]
visible = false
self_modulate = Color( 1, 1, 1, 0.470588 )
z_index = 15
tile_set = SubResource( 17 )
cell_size = Vector2( 128, 128 )
format = 1

[node name="selectors" type="Node2D" parent="."]
z_index = 35

[node name="playermoving" type="Timer" parent="."]
one_shot = true

[node name="sfx" type="Node2D" parent="."]

[node name="environment" type="Node2D" parent="sfx"]

[node name="step0" type="AudioStreamPlayer" parent="sfx/environment"]
stream = ExtResource( 23 )
volume_db = -18.0
pitch_scale = 0.95
bus = "SFX"

[node name="wstep0" type="AudioStreamPlayer" parent="sfx/environment"]
stream = ExtResource( 46 )
volume_db = -8.0
bus = "SFX"

[node name="step1" type="AudioStreamPlayer" parent="sfx/environment"]
stream = ExtResource( 24 )
volume_db = -18.0
pitch_scale = 0.95
bus = "SFX"

[node name="wstep1" type="AudioStreamPlayer" parent="sfx/environment"]
stream = ExtResource( 29 )
volume_db = -14.0
bus = "SFX"

[node name="step2" type="AudioStreamPlayer" parent="sfx/environment"]
stream = ExtResource( 26 )
volume_db = -18.0
pitch_scale = 0.95
bus = "SFX"

[node name="wstep2" type="AudioStreamPlayer" parent="sfx/environment"]
stream = ExtResource( 58 )
volume_db = -11.0
bus = "SFX"

[node name="step3" type="AudioStreamPlayer" parent="sfx/environment"]
stream = ExtResource( 27 )
volume_db = -18.0
pitch_scale = 0.95
bus = "SFX"

[node name="wstep3" type="AudioStreamPlayer" parent="sfx/environment"]
stream = ExtResource( 45 )
volume_db = -16.0
bus = "SFX"

[node name="rstep0" type="AudioStreamPlayer" parent="sfx/environment"]
stream = ExtResource( 138 )
volume_db = -16.0
bus = "SFX"

[node name="rstep1" type="AudioStreamPlayer" parent="sfx/environment"]
stream = ExtResource( 143 )
volume_db = -16.0
bus = "SFX"

[node name="rstep2" type="AudioStreamPlayer" parent="sfx/environment"]
stream = ExtResource( 139 )
volume_db = -16.0
bus = "SFX"

[node name="rstep3" type="AudioStreamPlayer" parent="sfx/environment"]
stream = ExtResource( 141 )
volume_db = -16.0
bus = "SFX"

[node name="rstep4" type="AudioStreamPlayer" parent="sfx/environment"]
stream = ExtResource( 142 )
volume_db = -16.0
bus = "SFX"

[node name="stonemoved" type="AudioStreamPlayer2D" parent="sfx/environment"]
stream = ExtResource( 39 )
volume_db = -3.0
pitch_scale = 1.15
bus = "SFX"

[node name="woodbreak0" type="AudioStreamPlayer2D" parent="sfx/environment"]
stream = ExtResource( 123 )
volume_db = -4.0
bus = "SFX"

[node name="woodbreak1" type="AudioStreamPlayer2D" parent="sfx/environment"]
stream = ExtResource( 124 )
volume_db = -3.0
bus = "SFX"

[node name="plankbreak0" type="AudioStreamPlayer2D" parent="sfx/environment"]
stream = ExtResource( 120 )
volume_db = -3.0
bus = "SFX"

[node name="plankbreak1" type="AudioStreamPlayer2D" parent="sfx/environment"]
stream = ExtResource( 122 )
volume_db = -3.0
bus = "SFX"

[node name="plankbreak2" type="AudioStreamPlayer2D" parent="sfx/environment"]
stream = ExtResource( 119 )
volume_db = -3.0
bus = "SFX"

[node name="stonebreak0" type="AudioStreamPlayer2D" parent="sfx/environment"]
stream = ExtResource( 117 )
volume_db = -2.0
bus = "SFX"

[node name="stonebreak1" type="AudioStreamPlayer2D" parent="sfx/environment"]
stream = ExtResource( 118 )
volume_db = -5.0
bus = "SFX"

[node name="stoneresist0" type="AudioStreamPlayer2D" parent="sfx/environment"]
stream = ExtResource( 121 )
volume_db = -8.0
bus = "SFX"

[node name="stoneresist1" type="AudioStreamPlayer2D" parent="sfx/environment"]
stream = ExtResource( 116 )
volume_db = -8.0
bus = "SFX"

[node name="ceramicbreak0" type="AudioStreamPlayer2D" parent="sfx/environment"]
stream = ExtResource( 93 )
volume_db = -7.0
bus = "SFX"

[node name="ceramicbreak1" type="AudioStreamPlayer2D" parent="sfx/environment"]
stream = ExtResource( 96 )
volume_db = -7.0
bus = "SFX"

[node name="ceramicbreak2" type="AudioStreamPlayer2D" parent="sfx/environment"]
stream = ExtResource( 115 )
volume_db = -7.0
bus = "SFX"

[node name="door" type="AudioStreamPlayer2D" parent="sfx"]
stream = ExtResource( 110 )
volume_db = -7.0
pitch_scale = 1.35
bus = "SFX"

[node name="smallrip" type="AudioStreamPlayer2D" parent="sfx"]
stream = ExtResource( 62 )
volume_db = -7.0
pitch_scale = 0.8
bus = "SFX"

[node name="bigrip" type="AudioStreamPlayer2D" parent="sfx"]
stream = ExtResource( 83 )
volume_db = -6.0
pitch_scale = 1.2
bus = "SFX"

[node name="goorip" type="AudioStreamPlayer2D" parent="sfx"]
stream = ExtResource( 136 )
volume_db = -8.0
pitch_scale = 0.5
max_distance = 3000.0
bus = "SFX"

[node name="hit" type="AudioStreamPlayer" parent="sfx"]
stream = ExtResource( 80 )
volume_db = -6.0
pitch_scale = 1.1
bus = "SFX"

[node name="struggle" type="AudioStreamPlayer" parent="sfx"]
stream = ExtResource( 125 )
volume_db = -6.0
pitch_scale = 1.3
bus = "SFX"

[node name="activate_comms" type="AudioStreamPlayer" parent="sfx"]
stream = ExtResource( 127 )
volume_db = -8.0
bus = "SFX"

[node name="struggle2" type="AudioStreamPlayer" parent="sfx"]
stream = ExtResource( 126 )
volume_db = -6.0
bus = "SFX"

[node name="hitsweet" type="AudioStreamPlayer" parent="sfx"]
stream = ExtResource( 6 )
volume_db = -17.0
pitch_scale = 2.7
bus = "SFX"

[node name="money" type="AudioStreamPlayer" parent="sfx"]
stream = ExtResource( 42 )
volume_db = -10.0
bus = "SFX"

[node name="lockcorruption" type="AudioStreamPlayer" parent="sfx"]
stream = ExtResource( 128 )
volume_db = -7.0
pitch_scale = 2.0
bus = "SFX"

[node name="caught" type="AudioStreamPlayer" parent="sfx"]
stream = ExtResource( 59 )
volume_db = -6.0
pitch_scale = 1.3
bus = "SFX"

[node name="collect" type="AudioStreamPlayer" parent="sfx"]
stream = ExtResource( 44 )
volume_db = -5.0
bus = "SFX"

[node name="badcollect" type="AudioStreamPlayer" parent="sfx"]
stream = ExtResource( 130 )
volume_db = -14.0
pitch_scale = 0.5
bus = "SFX"

[node name="watermove" type="AudioStreamPlayer2D" parent="sfx"]
stream = ExtResource( 112 )
bus = "SFX"

[node name="damagetakenblock" type="AudioStreamPlayer" parent="sfx"]
stream = ExtResource( 76 )
volume_db = -5.0
pitch_scale = 1.1
bus = "SFX"

[node name="playerdamagetaken1" type="AudioStreamPlayer" parent="sfx"]
stream = ExtResource( 22 )
volume_db = -6.0
bus = "SFX"

[node name="playerdamagetaken2" type="AudioStreamPlayer" parent="sfx"]
stream = ExtResource( 31 )
volume_db = -24.0
pitch_scale = 1.6
bus = "SFX"

[node name="playerdamagetaken3" type="AudioStreamPlayer" parent="sfx"]
stream = ExtResource( 6 )
volume_db = -18.0
pitch_scale = 1.5
bus = "SFX"

[node name="bath" type="AudioStreamPlayer" parent="sfx"]
stream = ExtResource( 65 )
volume_db = -5.0
pitch_scale = 0.9
bus = "SFX"

[node name="enemysweetdamage" type="AudioStreamPlayer2D" parent="sfx"]
stream = ExtResource( 6 )
volume_db = -18.0
pitch_scale = 0.8
max_distance = 3000.0
bus = "SFX"

[node name="cursedbell" type="AudioStreamPlayer" parent="sfx"]
stream = ExtResource( 63 )
volume_db = -1.0
pitch_scale = 1.3
bus = "SFX"

[node name="enemydamagetaken" type="AudioStreamPlayer2D" parent="sfx"]
stream = ExtResource( 82 )
pitch_scale = 1.5
max_distance = 3000.0
bus = "SFX"

[node name="enemyblock" type="AudioStreamPlayer2D" parent="sfx"]
stream = ExtResource( 76 )
pitch_scale = 0.8
max_distance = 3000.0
bus = "SFX"

[node name="enemyblockfailed" type="AudioStreamPlayer2D" parent="sfx"]
stream = ExtResource( 86 )
pitch_scale = 1.3
max_distance = 3000.0
bus = "SFX"

[node name="open" type="AudioStreamPlayer2D" parent="sfx"]
stream = ExtResource( 97 )
max_distance = 3000.0
bus = "SFX"

[node name="healthpickup1" type="AudioStreamPlayer2D" parent="sfx"]
stream = ExtResource( 66 )
volume_db = -13.0
max_distance = 3000.0
bus = "SFX"

[node name="healthpickup2" type="AudioStreamPlayer2D" parent="sfx"]
stream = ExtResource( 69 )
volume_db = -13.0
pitch_scale = 1.5
max_distance = 3000.0
bus = "SFX"

[node name="foxobj" type="AudioStreamPlayer" parent="sfx"]
stream = ExtResource( 81 )
volume_db = -5.0
pitch_scale = 1.2
bus = "SFX"

[node name="enteredwater" type="AudioStreamPlayer" parent="sfx"]
stream = ExtResource( 84 )
volume_db = -8.0
pitch_scale = 0.85
bus = "SFX"

[node name="timedown" type="AudioStreamPlayer" parent="sfx"]
stream = ExtResource( 88 )
volume_db = -8.0
bus = "SFX"

[node name="abilitysfx" type="Node2D" parent="sfx"]

[node name="Shockwave" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 43 )
volume_db = -5.0
max_distance = 3000.0
bus = "SFX"

[node name="Overflow" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 61 )
volume_db = -6.0
max_distance = 3000.0
bus = "SFX"

[node name="Resonance" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 68 )
volume_db = -5.0
max_distance = 3000.0
bus = "SFX"

[node name="Empower" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 64 )
volume_db = -4.0
max_distance = 3000.0
bus = "SFX"

[node name="Hypnosis" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 73 )
volume_db = -5.0
pitch_scale = 0.8
max_distance = 3000.0
bus = "SFX"

[node name="Pole" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 87 )
volume_db = 1.0
pitch_scale = 1.3
max_distance = 3000.0
bus = "SFX"

[node name="Piton" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 74 )
volume_db = -5.0
max_distance = 3000.0
bus = "SFX"

[node name="Torch" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 75 )
volume_db = -5.0
max_distance = 3000.0
bus = "SFX"

[node name="Explosive" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 75 )
volume_db = -5.0
pitch_scale = 0.8
max_distance = 3000.0
bus = "SFX"

[node name="Axe" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 41 )
volume_db = 2.0
max_distance = 3000.0
bus = "SFX"

[node name="Pickaxe" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 41 )
volume_db = 3.0
pitch_scale = 0.6
max_distance = 3000.0
bus = "SFX"

[node name="Sweep" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 77 )
max_distance = 3000.0
bus = "SFX"

[node name="Plunge" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 77 )
pitch_scale = 0.6
max_distance = 3000.0
bus = "SFX"

[node name="Scimitar" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 77 )
pitch_scale = 1.3
max_distance = 3000.0
bus = "SFX"

[node name="Dagger" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 78 )
volume_db = -5.0
max_distance = 3000.0
bus = "SFX"

[node name="Heavenly Chord" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 72 )
volume_db = -7.0
pitch_scale = 0.8
max_distance = 3000.0
bus = "SFX"

[node name="Draining Kiss" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 129 )
volume_db = -13.0
max_distance = 3000.0
bus = "SFX"

[node name="Milky Drain" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 105 )
volume_db = -4.0
max_distance = 3000.0
bus = "SFX"

[node name="Shortbow" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 135 )
volume_db = -6.0
max_distance = 3000.0
bus = "SFX"

[node name="Sheep Song" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 72 )
volume_db = -7.0
pitch_scale = 1.2
max_distance = 3000.0
bus = "SFX"

[node name="Lance" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 80 )
pitch_scale = 1.1
max_distance = 3000.0
bus = "SFX"

[node name="Lance-II" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = SubResource( 87 )
pitch_scale = 0.5
max_distance = 3000.0
bus = "SFX"

[node name="Sense" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 85 )
volume_db = -6.0
pitch_scale = 1.2
max_distance = 6000.0
bus = "SFX"

[node name="Ghost Bell" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 71 )
volume_db = -7.0
pitch_scale = 0.7
max_distance = 6000.0
bus = "SFX"

[node name="Pounce" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 79 )
volume_db = -5.0
pitch_scale = 0.8
max_distance = 3000.0
bus = "SFX"

[node name="Ruin" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 86 )
volume_db = -4.0
pitch_scale = 1.4
max_distance = 3000.0
bus = "SFX"

[node name="Scratch" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 82 )
volume_db = -6.0
pitch_scale = 1.4
max_distance = 3000.0
bus = "SFX"

[node name="Buckler" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 89 )
volume_db = -3.0
pitch_scale = 1.2
max_distance = 3000.0
bus = "SFX"

[node name="Dash" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 90 )
volume_db = 3.0
pitch_scale = 2.0
max_distance = 3000.0
bus = "SFX"

[node name="Reposition" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 26 )
volume_db = -4.0
pitch_scale = 1.4
max_distance = 3000.0
bus = "SFX"

[node name="Warpath" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 91 )
volume_db = -3.0
pitch_scale = 1.3
max_distance = 3000.0
bus = "SFX"

[node name="Windstep" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 92 )
volume_db = -3.0
pitch_scale = 1.3
max_distance = 3000.0
bus = "SFX"

[node name="Remove Seal" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 83 )
volume_db = -5.0
pitch_scale = 1.7
max_distance = 3000.0
bus = "SFX"

[node name="Cum Seal" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 130 )
volume_db = -7.0
pitch_scale = 0.8
max_distance = 3000.0
bus = "SFX"

[node name="Fox Seal" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 130 )
volume_db = -7.0
pitch_scale = 0.8
max_distance = 3000.0
bus = "SFX"

[node name="Juggernaut" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 106 )
volume_db = 2.0
pitch_scale = 1.6
max_distance = 3000.0
bus = "SFX"

[node name="Pulse" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 140 )
max_distance = 3000.0
bus = "SFX"

[node name="Shine" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 137 )
volume_db = -6.0
max_distance = 3000.0
bus = "SFX"

[node name="Sweet Smoke" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 52 )
volume_db = -4.0
max_distance = 3000.0
bus = "SFX"

[node name="Smoke Plume" type="AudioStreamPlayer2D" parent="sfx/abilitysfx"]
stream = ExtResource( 53 )
volume_db = -1.0
pitch_scale = 1.2
max_distance = 3000.0
bus = "SFX"

[node name="levelcomplete" type="AudioStreamPlayer" parent="sfx"]
stream = ExtResource( 70 )
volume_db = -13.0
bus = "SFX"

[node name="Ambience" parent="sfx" instance=ExtResource( 9 )]

[node name="enemies" type="Node2D" parent="."]
z_index = 7

[node name="lighttest" type="Node2D" parent="."]
process_priority = 50
z_index = 60

[node name="playermovingtotal" type="Timer" parent="."]

[node name="newfoglayer" type="CanvasLayer" parent="."]
layer = 2

[node name="newfog" type="TextureRect" parent="newfoglayer"]
self_modulate = Color( 1, 1, 1, 0.54902 )
light_mask = 2
material = SubResource( 44 )
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 2
texture = ExtResource( 8 )
expand = true
stretch_mode = 1

[node name="CanvasLayer3" type="CanvasLayer" parent="."]
layer = 3
follow_viewport_enable = true

[node name="fogofwar" type="TileMap" parent="CanvasLayer3"]
z_index = 5
tile_set = SubResource( 18 )
cell_size = Vector2( 128, 128 )
format = 1

[node name="raisezlevel" type="Node2D" parent="CanvasLayer3"]
z_index = 10

[node name="attackgraphs" type="VBoxContainer" parent="CanvasLayer3/raisezlevel"]
visible = false
anchor_left = 0.5
anchor_right = 0.5
margin_top = -160.0
margin_bottom = -160.0
grow_horizontal = 2
grow_vertical = 0
mouse_filter = 2
custom_constants/separation = 64
alignment = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="notice" type="Node2D" parent="CanvasLayer3/raisezlevel"]

[node name="bouncers" type="Node2D" parent="CanvasLayer3"]
z_index = 10

[node name="ParallaxBackground" type="ParallaxBackground" parent="CanvasLayer3"]
layer = 4

[node name="cloudfront" type="ParallaxLayer" parent="CanvasLayer3/ParallaxBackground"]
z_index = 70
motion_scale = Vector2( 1.7, 1.7 )

[node name="randomevents" type="Timer" parent="CanvasLayer3/ParallaxBackground/cloudfront"]
wait_time = 2.0
autostart = true

[node name="kcursor" type="AnimatedSprite" parent="CanvasLayer3"]
visible = false
z_index = 100
frames = SubResource( 37 )
animation = "kcursor"
frame = 4

[node name="cursorscrolltimer" type="Timer" parent="CanvasLayer3/kcursor"]
one_shot = true

[node name="miniscene" type="Node2D" parent="CanvasLayer3"]

[node name="minishake" type="AnimationPlayer" parent="CanvasLayer3/miniscene"]
playback_speed = 1.5
anims/shake = SubResource( 65 )
anims/shake2 = SubResource( 66 )

[node name="touchsprite" type="AnimatedSprite" parent="CanvasLayer3"]
visible = false
self_modulate = Color( 0, 0.764706, 1, 0.627451 )
z_index = 2
frames = SubResource( 68 )
animation = "speech"
frame = 2
playing = true

[node name="toucheffect" type="AnimationPlayer" parent="CanvasLayer3/touchsprite"]
anims/hide = SubResource( 71 )
anims/speech = SubResource( 69 )
anims/spin = SubResource( 70 )

[node name="CanvasLayer2" type="CanvasLayer" parent="."]
layer = 5

[node name="Dialogue" parent="CanvasLayer2" instance=ExtResource( 30 )]
margin_top = 0.0

[node name="attackeffects" type="Node2D" parent="."]
z_index = 70

[node name="attackeffectstemp" type="Node2D" parent="."]
z_index = 68

[node name="paths" type="TileMap" parent="."]
z_index = 3
tile_set = ExtResource( 103 )
cell_size = Vector2( 128, 128 )
format = 1

[node name="features" type="TileMap" parent="."]
position = Vector2( 0, 20 )
z_index = 3
tile_set = ExtResource( 3 )
cell_size = Vector2( 128, 128 )
format = 1

[node name="destructiblefeatures" type="TileMap" parent="."]
position = Vector2( 0, 20 )
z_index = 3
tile_set = ExtResource( 38 )
cell_size = Vector2( 128, 128 )
format = 1

[node name="limitfog" type="TileMap" parent="."]
self_modulate = Color( 1, 1, 1, 0.627451 )
position = Vector2( 0, 150 )
z_index = 13
tile_set = SubResource( 21 )
cell_size = Vector2( 384, 384 )
format = 1

[node name="AnimationPlayer" type="AnimationPlayer" parent="limitfog"]
autoplay = "scrollfog"
playback_speed = 0.02
anims/scrollfog = SubResource( 22 )

[node name="limitrock" type="TileMap" parent="."]
position = Vector2( 0, -64 )
z_index = -4
tile_set = SubResource( 23 )
cell_size = Vector2( 384, 384 )
format = 1

[node name="roofmap" type="TileMap" parent="."]
material = SubResource( 89 )
use_parent_material = true
position = Vector2( 0, 44 )
z_index = 3
tile_set = ExtResource( 55 )
cell_size = Vector2( 128, 128 )
format = 1

[node name="foliage" type="TileMap" parent="."]
material = ExtResource( 144 )
position = Vector2( 0, 44 )
z_index = 2
tile_set = ExtResource( 37 )
cell_size = Vector2( 128, 128 )
format = 1

[node name="objects" type="Node2D" parent="."]
z_index = 4

[node name="justscrolled" type="Timer" parent="."]
one_shot = true

[node name="shards" type="TileMap" parent="."]
modulate = Color( 0.705882, 0.862745, 0.705882, 1 )
material = SubResource( 27 )
z_index = 4
tile_set = ExtResource( 25 )
cell_size = Vector2( 128, 128 )
format = 1

[node name="AnimationPlayer" type="AnimationPlayer" parent="shards"]
anims/shards = SubResource( 29 )

[node name="indicators" type="Node2D" parent="."]
z_index = 28

[node name="subindicators" type="Node2D" parent="."]
z_index = 28

[node name="trees" type="Node2D" parent="."]
material = SubResource( 92 )
z_index = -3

[node name="stun" type="Timer" parent="."]
wait_time = 0.8
one_shot = true

[node name="cummap" type="TileMap" parent="."]
position = Vector2( 0, 28 )
z_index = 4
tile_set = ExtResource( 67 )
cell_size = Vector2( 128, 128 )
format = 1

[node name="lines" type="Node2D" parent="."]
z_index = 30
script = ExtResource( 100 )

[node name="repeatmovetimer" type="Timer" parent="."]
one_shot = true

[node name="stageend" type="AnimationPlayer" parent="."]
anims/RESET = SubResource( 90 )
anims/scrollall = SubResource( 38 )
anims/scrollalldark = SubResource( 64 )
anims/scrollallin = SubResource( 39 )
anims/scrollallwarp = SubResource( 86 )

[node name="screenshake" type="AnimationPlayer" parent="."]
root_node = NodePath("../stageend")
playback_speed = 3.0
anims/RESET = SubResource( 79 )
anims/screenshakebig = SubResource( 77 )
anims/screenshakehuge = SubResource( 78 )
anims/screenshakemain = SubResource( 62 )

[node name="aimers" type="Node2D" parent="."]
z_index = 20

[node name="enemyreticule" parent="aimers" instance=ExtResource( 1 )]
visible = false

[node name="enemyreticule2" parent="aimers" instance=ExtResource( 1 )]
visible = false

[node name="backgroundleaves" type="Particles2D" parent="."]
visible = false
z_index = -13
emitting = false
amount = 30
lifetime = 10.0
speed_scale = 0.5
explosiveness = 0.15
local_coords = false
process_material = SubResource( 42 )
texture = ExtResource( 20 )

[node name="norotaterules" type="Node2D" parent="."]
position = Vector2( 164.921, 428.72 )
z_index = 7

[node name="shadow" type="Sprite" parent="norotaterules"]
position = Vector2( 0, 78 )
z_index = -3
texture = ExtResource( 36 )

[node name="hitanim" type="AnimatedSprite" parent="norotaterules"]
visible = false
self_modulate = Color( 0.870588, 0.690196, 0.909804, 1 )
z_index = 14
frames = SubResource( 56 )
animation = "caught"
offset = Vector2( -8, 0 )

[node name="hitanim2" type="AnimatedSprite" parent="norotaterules"]
visible = false
z_index = 13
frames = SubResource( 50 )
animation = "hit"
frame = 5
playing = true

[node name="blockanim" type="AnimationPlayer" parent="norotaterules"]
root_node = NodePath("../blocks")
anims/block = SubResource( 63 )

[node name="blocks" type="GridContainer" parent="norotaterules"]
modulate = Color( 1, 1, 1, 0.470588 )
margin_left = -72.0
margin_top = -92.0
margin_right = 72.0
margin_bottom = 92.0
grow_horizontal = 2
grow_vertical = 2
columns = 2

[node name="possessionarrow" type="AnimatedSprite" parent="norotaterules"]
visible = false
self_modulate = Color( 1, 1, 1, 0.705882 )
position = Vector2( -64, 64 )
rotation = 3.98808
frames = SubResource( 76 )
frame = 3

[node name="healthouter" type="Sprite" parent="norotaterules"]
visible = false
modulate = Color( 0.203922, 1, 0.196078, 0.705882 )
position = Vector2( 0, 90 )
z_index = 9
texture = ExtResource( 101 )

[node name="healthinner" type="Sprite" parent="norotaterules/healthouter"]
texture = ExtResource( 104 )

[node name="healthtimer" type="Timer" parent="norotaterules/healthouter"]
one_shot = true

[node name="corruptionnode" type="Node2D" parent="norotaterules"]
z_index = 5

[node name="corruptionbars" type="VBoxContainer" parent="norotaterules/corruptionnode"]
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
margin_left = -157.0
margin_top = 80.0
margin_right = 157.0
margin_bottom = 80.0
mouse_filter = 2
alignment = 1

[node name="quip" type="AnimatedSprite" parent="norotaterules"]
visible = false
self_modulate = Color( 1, 1, 1, 0.784314 )
position = Vector2( 40, -114 )
scale = Vector2( 0.65, 0.65 )
z_index = 14
frames = SubResource( 11 )
animation = "normal"

[node name="quipanim" type="AnimationPlayer" parent="norotaterules"]
playback_speed = 0.85
anims/RESET = SubResource( 85 )
anims/quipanim = SubResource( 12 )
anims/quipanim2 = SubResource( 13 )

[node name="debugpointers" type="CanvasLayer" parent="."]
layer = 4
follow_viewport_enable = true

[node name="touchtimer" type="Timer" parent="."]
one_shot = true

[node name="triggerlevelfinish" type="Timer" parent="."]
wait_time = 3.0
one_shot = true

[connection signal="timeout" from="CanvasLayer3/ParallaxBackground/cloudfront/randomevents" to="." method="_on_randomevents_timeout"]
[connection signal="timeout" from="CanvasLayer3/kcursor/cursorscrolltimer" to="." method="_on_cursorscrolltimer_timeout"]
[connection signal="animation_finished" from="stageend" to="." method="_on_stageend_animation_finished"]
[connection signal="timeout" from="norotaterules/healthouter/healthtimer" to="." method="_on_healthtimer_timeout"]
[connection signal="timeout" from="touchtimer" to="." method="_on_touchtimer_timeout"]
[connection signal="timeout" from="triggerlevelfinish" to="." method="_on_triggerlevelfinish_timeout"]
