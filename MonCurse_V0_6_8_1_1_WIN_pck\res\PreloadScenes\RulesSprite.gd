extends Node

var rules_node_dict = {}

func preload_new_part(partnode,folder):#currently not in use, just preload everything instead.
	if rules_node_dict.has(partnode) == true:
		if rules_node_dict[partnode] != folder:
			$ResourcePreloader.remove_resource(partnode)
		else:
			return
	rules_node_dict[partnode] = folder
	for file in list_files_in_directory(folder):
		$ResourcePreloader.add_resource(partnode,load(file))

#with the new MC system, I've revamped it.
##This one is different. It simply preloads everything in the MC folders.
##this might cause issues with how loading bars work?
#
func _ready():
	var filesmain = list_files_in_directory("res://MC/")
	var filesdebuff = list_files_in_directory("res://MC/debuff/")
#	var filesdebuffnew = []
#	for folder in list_files_in_directory("res://MC_processed/debuff"):
#	var filesdebuffnew = list_files_in_directory("res://MC_processed/debuff/",true)
#	var filesmainnew 
#	filesmainnew = list_files_in_directory("res://MC_processed/",true)#either do this or load debuffs and use above function for non-debuffs
#	print(filesdebuffnew)
#	var filesoutfit = list_files_in_directory("res://MC/outfit/")
	var files = filesmain+filesdebuff#+filesmainnew#+filesoutfit
	for file in files:
		$ResourcePreloader.add_resource("RP",load(file))


func list_files_in_directory(path,deep = false):
	var files = []
	var dir = Directory.new()
	dir.open(path)
	dir.list_dir_begin(true)

	while true:
		var file = dir.get_next()
		if file == "":
			break
#		elif not file.begins_with("."):
		elif deep == true and dir.current_is_dir():
			for newfile in list_files_in_directory(path+file+"/",true):
				files.append(newfile)
		elif file.ends_with(".png"):# or file.ends_with(".json")):
			files.append(path+file)

	dir.list_dir_end()

	return files
