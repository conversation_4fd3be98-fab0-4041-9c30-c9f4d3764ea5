[gd_scene load_steps=7 format=2]

[ext_resource path="res://Assets/lightgodod.png" type="Texture" id=1]
[ext_resource path="res://Assets/ui/deployabledoublehover.png" type="Texture" id=2]
[ext_resource path="res://Assets/abilityicons/Lance.png" type="Texture" id=3]
[ext_resource path="res://projectilepickup.gd" type="Script" id=4]

[sub_resource type="Animation" id=1]
resource_name = "pickup"
length = 1.5
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.4, 1.5 ),
"transitions": PoolRealArray( 0.5, 3, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0, -30 ), Vector2( 49, 200 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.4, 1.5 ),
"transitions": PoolRealArray( 0.5, 3, 1 ),
"update": 0,
"values": [ 0.0, -50.0, 360.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("Sprite2:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.7, 1.3, 1.5 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.117647 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0.117647 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("Light2D:modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.7, 1.1, 1.5 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.117647 ), Color( 1, 1, 1, 0.54902 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("Light2D:scale")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 0.7, 1.5 ),
"transitions": PoolRealArray( 1, 4, 1 ),
"update": 0,
"values": [ Vector2( 0.7, 0.7 ), Vector2( 0.4, 0.4 ), Vector2( 1.3, 1.3 ) ]
}
tracks/5/type = "value"
tracks/5/path = NodePath(".:scale")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0, 0.3, 1.5 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0.5, 0.5 ), Vector2( 1, 1 ), Vector2( 0.7, 0.7 ) ]
}

[sub_resource type="Animation" id=2]
resource_name = "pickuppushfront"
tracks/0/type = "value"
tracks/0/path = NodePath(".:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3, 1 ),
"transitions": PoolRealArray( 0.5, 3, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -20, -30 ), Vector2( 80, 180 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.3, 1 ),
"transitions": PoolRealArray( 0.5, 3, 1 ),
"update": 0,
"values": [ 0.0, -25.0, 180.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("Sprite2:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.4, 0.8, 1 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.117647 ), Color( 1, 1, 1, 0.784314 ), Color( 1, 1, 1, 0.784314 ), Color( 1, 1, 1, 0.117647 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("Light2D:modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.4, 0.7, 1 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.117647 ), Color( 1, 1, 1, 0.54902 ), Color( 1, 1, 1, 0.784314 ), Color( 1, 1, 1, 0 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("Light2D:scale")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 0.3, 1 ),
"transitions": PoolRealArray( 1, 4, 1 ),
"update": 0,
"values": [ Vector2( 0.7, 0.7 ), Vector2( 0.4, 0.4 ), Vector2( 1.1, 1.1 ) ]
}
tracks/5/type = "value"
tracks/5/path = NodePath(".:scale")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0, 0.3, 1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0.5, 0.5 ), Vector2( 0.8, 0.8 ), Vector2( 0.6, 0.6 ) ]
}

[node name="projectilepickup" type="Node2D"]
modulate = Color( 1, 1, 1, 0.784314 )
position = Vector2( 80, 180 )
rotation = 3.14159
scale = Vector2( 0.6, 0.6 )
script = ExtResource( 4 )

[node name="Light2D" type="Sprite" parent="."]
modulate = Color( 1, 1, 1, 0 )
self_modulate = Color( 0, 1, 1, 1 )
scale = Vector2( 1.1, 1.1 )
texture = ExtResource( 1 )

[node name="Sprite2" type="Sprite" parent="."]
modulate = Color( 1, 1, 1, 0.117647 )
self_modulate = Color( 1, 1, 1, 0.509804 )
texture = ExtResource( 2 )

[node name="Sprite" type="Sprite" parent="Sprite2"]
texture = ExtResource( 3 )

[node name="projectile" type="AnimationPlayer" parent="."]
anims/pickup = SubResource( 1 )
anims/pickuppushfront = SubResource( 2 )

[connection signal="animation_finished" from="projectile" to="." method="_on_projectile_animation_finished"]
