extends CanvasLayer

#edges: Image extends 138 up, 137 down
#height: 1355 (including extra space)
#extra space: 275, normal space 1080

const expressionclosed = preload("res://DialogueArt/CG/gameovers/ram/expression closed.png")
const expressionopen = preload("res://DialogueArt/CG/gameovers/ram/expression open.png")
const expressionboth = preload("res://DialogueArt/CG/gameovers/ram/expression both.png")

const upperstretched = preload("res://DialogueArt/CG/gameovers/ram/body upper stretch.png")
const upperhold = preload("res://DialogueArt/CG/gameovers/ram/body upper hold.png")

const silhouettehover = preload("res://DialogueArt/CG/gameovers/ram/rules silhouette hover.png")
const silhouetteheld = preload("res://DialogueArt/CG/gameovers/ram/rules silhouette held.png")
const silhouetteheldhorns = preload("res://DialogueArt/CG/gameovers/ram/rules silhouette held horns.png")
const silhouettedick = preload("res://DialogueArt/CG/gameovers/ram/rules silhouette hover dick.png")
const backgroundnoram = preload("res://Background/bedsheetsnoram.png")
const background = preload("res://Background/bedsheets.png")

const sheen = preload("res://DialogueArt/CG/gameovers/ram/sheen.png")
const sheenlipstick = preload("res://DialogueArt/CG/gameovers/ram/sheen lipstick.png")


#POSITIONS
#BACK: 203x, 244y
#LOWER: 745x, 675y
#UPPER: 310x, 190y
#HEAD: 0x, 0y
#DICK: 910x, 90y
#SHEEN: see above
#CUM: see above
#SILHOUETTE: 435x, 400y NOTE, DICK HOVER IS 30 Y DIFFERENT I gave it blank space
#UPPEROVER: 620x, 335y
#EXPRESSION: 330x, 265y
#STEAMBODY: 0x, 120y
#SPLATTER: 0x, 0y
#steamdick: 470x,360y (kind of a special case?) PLUS half size: (1392x, 791y)*0.5 = TOTAL of 1166x, 755.5y from adding onto th eoriginal


enum{STRETCHED=0,HOLD=1}
func holdswitch(pose):
	if pose == STRETCHED:
		if dickscene == false:
			$sprites/onbed/silhouette.texture = silhouettehover
		$sprites/onbed/upper.texture = upperstretched
		$sprites/onbed/upperover.visible = false
	elif pose == HOLD:
		if dickscene == false:
			if Playervariables.get_corruption("horns") > 0 or Playervariables.get_corruption("backhorns") > 0:#Playervariables.corruptiondict.has("horns") and Playervariables.corruptiondict["horns"] > 0:
				$sprites/onbed/silhouette.texture = silhouetteheldhorns
			else:
				$sprites/onbed/silhouette.texture = silhouetteheld
		$sprites/onbed/upper.texture = upperhold
		$sprites/onbed/upperover.visible = true

enum{CLOSED = 0, OPEN = 1, BOTH = 2}
func expressionswitch(pose):
	if pose == CLOSED:
		$sprites/onbed/expression.texture = expressionclosed
	elif pose == OPEN:
		$sprites/onbed/expression.texture = expressionopen
	elif pose == BOTH:
		$sprites/onbed/expression.texture = expressionboth
	

func _ready():
	var _c = get_tree().root.connect("size_changed", self, "_on_viewport_size_changed")
#	$hover.play("hover")
	holdswitch(STRETCHED)
	$sprites/onbed/silhouette.visible = false
	$sprites/onbed/dick.visible = false
	$sprites/onbed.set_modulate(Color(1,1,1,0))
	$sprites/background.set_modulate(Color(1,1,1,0))
	_on_viewport_size_changed()

const hoverstart = -138
const hoverdistance = 100
const basesize = Vector2(1920,1355)
var firstrun = true
var recentsizechange = false
func _on_viewport_size_changed():
	if recentsizechange == false:
		recentsizechange = true #this makes it so it only updates the viewport every 4 frames
		if firstrun == false:
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
		else:
			firstrun = false
		
		var viewportrect = get_parent().get_viewport_rect().size
		var proportion = viewportrect/basesize
		var maxproportion = max(proportion.x,proportion.y)
#		var proportion = get_parent().get_viewport_rect().size/basesize
#		$sprites.scale = proportion
		$sprites.scale = Vector2(maxproportion,maxproportion)
		var newhoveranim
		if $hover.has_animation("newhover"):
			newhoveranim = $hover.get_animation("newhover")
		else:
			newhoveranim = $hover.get_animation("hover").duplicate()
		
		if (proportion.x > proportion.y):
			newhoveranim.track_set_key_value(0,0,Vector2(0,0))
			newhoveranim.track_set_key_value(0,1,Vector2(0,viewportrect.y - (basesize.y * maxproportion)))
			newhoveranim.track_set_key_value(0,2,Vector2(0,0))
			newhoveranim.track_set_key_value(0,3,Vector2(0,viewportrect.y - (basesize.y * maxproportion)))
			newhoveranim.track_set_key_value(0,4,Vector2(0,0))
		else:
			newhoveranim.track_set_key_value(0,0,Vector2(0,0))
			newhoveranim.track_set_key_value(0,1,Vector2(viewportrect.x - (basesize.x * maxproportion),0))
			newhoveranim.track_set_key_value(0,2,Vector2(0,0))
			newhoveranim.track_set_key_value(0,3,Vector2(viewportrect.x - (basesize.x * maxproportion),0))
			newhoveranim.track_set_key_value(0,4,Vector2(0,0))
		
#		newhoveranim.track_set_key_value(0,0,Vector2(0,hoverstart)*proportion.y)
#		newhoveranim.track_set_key_value(0,1,Vector2(0,hoverstart-hoverdistance)*proportion.y)
#		newhoveranim.track_set_key_value(0,2,Vector2(0,hoverstart)*proportion.y)
#		newhoveranim.track_set_key_value(0,3,Vector2(0,hoverstart+hoverdistance)*proportion.y)
#		newhoveranim.track_set_key_value(0,4,Vector2(0,hoverstart)*proportion.y)
		if $hover.has_animation("newhover") == false:
			$hover.add_animation("newhover",newhoveranim)
		var x = ((maxproportion+0.4) / (min(proportion.x,proportion.y)+0.4))
		$hover.playback_speed = (0.17 / (x*x))
		$hover.play("newhover")
		recentsizechange = false

var dickscene = false
func action(num = 0):
	if Playervariables.consent == true:
		match num:
			0: #background apperas
				$sprites/background.texture = backgroundnoram
				if $AnimationPlayer.is_playing():
					$AnimationPlayer.advance(9)
				$AnimationPlayer.play("appearbg")
			1: #ram appears
				$sprites/background.texture = background
				if $AnimationPlayer.is_playing():
					$AnimationPlayer.advance(9)
				$AnimationPlayer.play("appear")
			2: #switch to altscene
				expressionswitch(BOTH)
				if dickscene == false:
					var rankshift = Playervariables.RANKhsv[3]
					$sprites/onbed.material.set_shader_param("hue_shift",rankshift.x)
					$sprites/onbed.material.set_shader_param("sat_mul",rankshift.y)
					$sprites/onbed.material.set_shader_param("val_mul",rankshift.z)
					if Playervariables.prefdict[Playervariables.pref.FUTAENEMY] == true:
						$sprites/onbed/dick.visible = true
					else:
						$sprites/onbed/dick.visible = false
					$sprites/onbed.use_parent_material = false
					$sprites/onbed/silhouette.texture = silhouettedick
					$sprites/onbed/silhouette.set_self_modulate(Color(1,1,1,0.5))
					dickscene = true
			3: #arms stretched out
				holdswitch(STRETCHED)
			4: #holding and closed at same time
				holdswitch(HOLD)
				expressionswitch(CLOSED)
				if $AnimationPlayer.is_playing():
					$AnimationPlayer.advance(9)
				$AnimationPlayer.play("shake")
			5: #enable silhouette
				$sprites/onbed/silhouette.visible = true
			6: #open expression
				expressionswitch(OPEN)
			7: #fade away again
				if $AnimationPlayer.is_playing():
					$AnimationPlayer.advance(9)
				$AnimationPlayer.play_backwards("appear")
				$sprites/onbed/steamdick.visible = false
				$sprites/onbed/steambody.visible = false
			8: #fade away but all of it now
				if $AnimationPlayer.is_playing():
					$AnimationPlayer.advance(9)
				$AnimationPlayer.play_backwards("appearbg")
			9: #steam, dick
				if Playervariables.prefdict[Playervariables.pref.FUTAENEMY] == true:
					$sprites/onbed/steamdick.emitting = true
					$sprites/onbed/steamdick.visible = true
				else:
					$sprites/onbed/steamdick.visible = false
			10: #steam, body
				$steam.play("steam")
				$sprites/onbed/steambody.visible = true
			11: #add sheen to dick
				$sprites/onbed/dick/sheen.visible = true
				if Playervariables.corruptiondict.has("eyes") and Playervariables.corruptiondict["eyes"] == Playervariables.raceRAM:
					$sprites/onbed/dick/sheen.texture = sheenlipstick
				else:
					$sprites/onbed/dick/sheen.texture = sheen
			12: #cumming
				$cum.stop()
				$cum.play("cumming")
				if $AnimationPlayer.is_playing():
					$AnimationPlayer.advance(9)
				$AnimationPlayer.play("shake")
				expressionswitch(CLOSED)
			13: #cumming without the cum
				if $AnimationPlayer.is_playing():
					$AnimationPlayer.advance(9)
				$AnimationPlayer.play("shake")
				expressionswitch(CLOSED)
			14: #no silhouette
				$sprites/onbed/silhouette.visible = false

func _process(_delta):
	$sprites.position = $sprites.position.round()
