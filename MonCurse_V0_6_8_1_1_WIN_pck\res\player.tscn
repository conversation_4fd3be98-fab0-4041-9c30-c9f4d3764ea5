[gd_scene load_steps=8 format=2]

[ext_resource path="res://MC/rulesplc5.png" type="Texture" id=1]
[ext_resource path="res://MC/rulesjump.png" type="Texture" id=2]
[ext_resource path="res://Assets/sparkstar.png" type="Texture" id=3]
[ext_resource path="res://effects/windstep.tres" type="Material" id=4]
[ext_resource path="res://MC/rulescat.png" type="Texture" id=5]

[sub_resource type="SpriteFrames" id=1]
animations = [ {
"frames": [ ExtResource( 1 ) ],
"loop": false,
"name": "idle",
"speed": 5.0
}, {
"frames": [ ExtResource( 2 ) ],
"loop": false,
"name": "jump",
"speed": 5.0
}, {
"frames": [ ExtResource( 5 ) ],
"loop": false,
"name": "cat",
"speed": 5.0
} ]

[sub_resource type="ParticlesMaterial" id=2]
emission_shape = 2
emission_box_extents = Vector3( 50, 50, 0 )
flag_disable_z = true
spread = 0.0
gravity = Vector3( 0, 50, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0

[node name="Player" type="AnimatedSprite"]
frames = SubResource( 1 )
animation = "idle"

[node name="Particles2D" type="Particles2D" parent="."]
position = Vector2( 0, -80 )
emitting = false
lifetime = 2.0
process_material = SubResource( 2 )
texture = ExtResource( 3 )

[node name="Particles2D3" type="Particles2D" parent="."]
self_modulate = Color( 0.32549, 1, 0.905882, 1 )
position = Vector2( 0, 120 )
emitting = false
amount = 12
lifetime = 2.0
process_material = ExtResource( 4 )
texture = ExtResource( 3 )
