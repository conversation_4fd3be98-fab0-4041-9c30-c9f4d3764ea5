[gd_scene load_steps=5 format=2]

[ext_resource path="res://Assets/ui/deployabledouble.png" type="Texture" id=1]
[ext_resource path="res://reseteffect.gd" type="Script" id=2]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_underwater_bubble_wet_collect_item_earn_point_bonus_002_78396.ogg" type="AudioStream" id=3]

[sub_resource type="Animation" id=1]
resource_name = "flower"
length = 2.8
tracks/0/type = "value"
tracks/0/path = NodePath("TextureRect1:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1.4 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("TextureRect2:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 1.6 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ false, true, false ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("TextureRect3:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.2, 1.8 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ false, true, false ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("TextureRect4:visible")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.3, 2 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ false, true, false ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("TextureRect5:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 0.4, 2.2 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ false, true, false ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("TextureRect6:visible")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0, 0.5, 2.4 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ false, true, false ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("TextureRect7:visible")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0, 0.6, 2.6 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ false, true, false ]
}
tracks/7/type = "value"
tracks/7/path = NodePath("TextureRect8:visible")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"times": PoolRealArray( 0, 0.7, 2.8 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ false, true, false ]
}
tracks/8/type = "value"
tracks/8/path = NodePath(".:rect_rotation")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/keys = {
"times": PoolRealArray( 0, 1.1, 2.8 ),
"transitions": PoolRealArray( 0.5, 2, 1 ),
"update": 0,
"values": [ 0.0, 360.0, -45.0 ]
}
tracks/9/type = "value"
tracks/9/path = NodePath(".:anchor_top")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/keys = {
"times": PoolRealArray( 0, 1.1, 2.8 ),
"transitions": PoolRealArray( 0.5, 2, 1 ),
"update": 0,
"values": [ 0.5, 0.3, 0.5 ]
}
tracks/10/type = "value"
tracks/10/path = NodePath(".:anchor_bottom")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/keys = {
"times": PoolRealArray( 0, 1.1, 2.8 ),
"transitions": PoolRealArray( 0.5, 2, 1 ),
"update": 0,
"values": [ 0.5, 0.3, 0.5 ]
}
tracks/11/type = "value"
tracks/11/path = NodePath("AudioStreamPlayer:playing")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/keys = {
"times": PoolRealArray( 0, 2.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}

[node name="reseteffect" type="Control"]
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 2
script = ExtResource( 2 )

[node name="centre" type="Control" parent="."]
anchor_left = 0.5
anchor_top = 0.300692
anchor_right = 0.5
anchor_bottom = 0.300692
rect_rotation = 358.599
mouse_filter = 2

[node name="TextureRect1" type="TextureRect" parent="centre"]
margin_right = 202.0
margin_bottom = 235.0
mouse_filter = 2
texture = ExtResource( 1 )
flip_v = true

[node name="TextureRect2" type="TextureRect" parent="centre"]
margin_right = 202.0
margin_bottom = 235.0
rect_rotation = 45.0
mouse_filter = 2
texture = ExtResource( 1 )
flip_v = true

[node name="TextureRect3" type="TextureRect" parent="centre"]
margin_right = 202.0
margin_bottom = 235.0
rect_rotation = 90.0
mouse_filter = 2
texture = ExtResource( 1 )
flip_v = true

[node name="TextureRect4" type="TextureRect" parent="centre"]
margin_right = 202.0
margin_bottom = 235.0
rect_rotation = 135.0
mouse_filter = 2
texture = ExtResource( 1 )
flip_v = true

[node name="TextureRect5" type="TextureRect" parent="centre"]
margin_right = 202.0
margin_bottom = 235.0
rect_rotation = 180.0
mouse_filter = 2
texture = ExtResource( 1 )
flip_v = true

[node name="TextureRect6" type="TextureRect" parent="centre"]
margin_right = 202.0
margin_bottom = 235.0
rect_rotation = 225.0
mouse_filter = 2
texture = ExtResource( 1 )
flip_v = true

[node name="TextureRect7" type="TextureRect" parent="centre"]
margin_right = 202.0
margin_bottom = 235.0
rect_rotation = 270.0
mouse_filter = 2
texture = ExtResource( 1 )
flip_v = true

[node name="TextureRect8" type="TextureRect" parent="centre"]
margin_right = 202.0
margin_bottom = 235.0
rect_rotation = 315.0
mouse_filter = 2
texture = ExtResource( 1 )
flip_v = true

[node name="AnimationPlayer" type="AnimationPlayer" parent="centre"]
autoplay = "flower"
anims/flower = SubResource( 1 )

[node name="AudioStreamPlayer" type="AudioStreamPlayer" parent="centre"]
stream = ExtResource( 3 )
volume_db = -16.0
pitch_scale = 0.7

[connection signal="animation_finished" from="centre/AnimationPlayer" to="." method="_on_AnimationPlayer_animation_finished"]
