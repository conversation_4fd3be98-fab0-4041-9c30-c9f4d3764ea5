[gd_scene load_steps=11 format=2]

[ext_resource path="res://Assets/pickups/barredframe.png" type="Texture" id=1]
[ext_resource path="res://Assets/pickups/unbarred.png" type="Texture" id=2]
[ext_resource path="res://Assets/abilityicons/Dagger.png" type="Texture" id=3]
[ext_resource path="res://pickupsprite.gd" type="Script" id=4]
[ext_resource path="res://Assets/pickups/wildchest2.png" type="Texture" id=5]
[ext_resource path="res://Assets/pickups/wildchest.png" type="Texture" id=6]
[ext_resource path="res://Assets/pickups/curseditems.png" type="Texture" id=7]

[sub_resource type="AtlasTexture" id=3]
atlas = ExtResource( 7 )
region = Rect2( 128, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=1]
atlas = ExtResource( 7 )
region = Rect2( 256, 128, 128, 128 )

[sub_resource type="SpriteFrames" id=2]
animations = [ {
"frames": [ ExtResource( 1 ) ],
"loop": false,
"name": "barred",
"speed": 5.0
}, {
"frames": [ ExtResource( 6 ) ],
"loop": false,
"name": "chest",
"speed": 5.0
}, {
"frames": [ ExtResource( 5 ) ],
"loop": false,
"name": "chest2",
"speed": 5.0
}, {
"frames": [ null ],
"loop": false,
"name": "empty",
"speed": 5.0
}, {
"frames": [ SubResource( 3 ) ],
"loop": false,
"name": "emptypedestal",
"speed": 5.0
}, {
"frames": [ SubResource( 1 ) ],
"loop": false,
"name": "pedestal",
"speed": 5.0
}, {
"frames": [ ExtResource( 2 ) ],
"loop": false,
"name": "unbarred",
"speed": 5.0
} ]

[node name="pickupsprite" type="Node2D"]
script = ExtResource( 4 )

[node name="pickup" type="TextureRect" parent="."]
margin_left = -48.0
margin_top = -48.0
margin_right = 48.0
margin_bottom = 48.0
grow_horizontal = 2
grow_vertical = 2
rect_min_size = Vector2( 96, 96 )
texture = ExtResource( 3 )
expand = true
stretch_mode = 7

[node name="barrier" type="AnimatedSprite" parent="."]
frames = SubResource( 2 )
animation = "empty"
