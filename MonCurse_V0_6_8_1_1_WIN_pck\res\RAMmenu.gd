extends CanvasLayer


# Declare member variables here. Examples:
# var a = 2
# var b = "text"


func _ready():
	if Playervariables.modarray[Playervariables.mods.TRANSLATION] == true:
		if Playervariables.default_font_dict["font_attack_record"] != null:
			$veil/veilbutton2/Label.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_record"].font_data
			$veil/Label.get("custom_fonts/font").font_data = Playervariables.default_font_dict["font_attack_record"].font_data
		if Playervariables.transdict.has("mainmenu") == true:
			if Playervariables.transdict["mainmenu"].has("yes"):
				$veil/veilbutton1/Label.set_text(Playervariables.transdict["mainmenu"]["yes"])
			if Playervariables.transdict["mainmenu"].has("cancel"):
				$veil/veilbutton2/Label.set_text(Playervariables.transdict["mainmenu"]["cancel"])
		$veil/Label.set_text(Playervariables.veiltextdict["crashprevention"])
	var _c = get_tree().root.connect("size_changed", self, "_on_viewport_size_changed")
	_on_viewport_size_changed()

var firstrun = true
var recentsizechange = false
func _on_viewport_size_changed():
	if recentsizechange == false:
		recentsizechange = true #this makes it so it only updates the viewport every 4 frames
		if firstrun == false:
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
		else:
			firstrun = false
		recentsizechange= false
		var newsize = $veil.get_viewport_rect().size
#		var proportiony = (newsize.length()/600)
		var proportionfull = (newsize.length()/1024)
		if Playervariables.modarray[Playervariables.mods.TRANSLATION] == true and Playervariables.default_font_dict["font_attack_record"] != null:
			proportionfull = proportionfull*Playervariables.default_font_dict["font_attack_record_size"]
		$veil/veilbutton2/Label.get("custom_fonts/font").size = int(20*proportionfull)
		$veil/Label.get("custom_fonts/font").size = int(((24.0*proportionfull) + 24.0) /2)



func _on_veilbutton1_pressed():
	get_node("/root/Master/SFX/ui").play()
	Playervariables.lastloadedvariables = -2
	Playervariables.savevalue("Crash","Loadedvariables",-2)
	self.queue_free()


func _on_veilbutton2_pressed():
	get_node("/root/Master/SFX/ui").play()
#	if Playervariables.lastloadedvariables == 0:
	Playervariables.lastloadedvariables = 999
	Playervariables.savevalue("Crash","Loadedvariables",999)
	get_node("/root/Master").preload_procedure()
	self.queue_free()
