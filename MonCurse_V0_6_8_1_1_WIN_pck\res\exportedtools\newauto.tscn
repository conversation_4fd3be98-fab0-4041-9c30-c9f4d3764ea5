[gd_scene load_steps=13 format=2]

[ext_resource path="res://Assets/uibutton.png" type="Texture" id=1]
[ext_resource path="res://font/OpenSans-ExtraBold.ttf" type="DynamicFontData" id=2]
[ext_resource path="res://Assets/movebarempty.png" type="Texture" id=3]
[ext_resource path="res://Assets/movebarfillbonus.png" type="Texture" id=4]
[ext_resource path="res://Assets/movebaremptybonus.png" type="Texture" id=5]
[ext_resource path="res://exportedtools/newauto.gd" type="Script" id=6]
[ext_resource path="res://Assets/movebarfill.png" type="Texture" id=7]
[ext_resource path="res://Assets/ui/key.png" type="Texture" id=8]
[ext_resource path="res://Assets/ui/keyhover.png" type="Texture" id=9]
[ext_resource path="res://Assets/ui/keypress.png" type="Texture" id=10]
[ext_resource path="res://Assets/ui/qadesclosebuttonalt.png" type="Texture" id=11]

[sub_resource type="DynamicFont" id=4]
size = 18
font_data = ExtResource( 2 )

[node name="Control" type="Control"]
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource( 6 )

[node name="Label3" type="Label" parent="."]
self_modulate = Color( 1, 0.886275, 0.894118, 1 )
anchor_left = 0.1
anchor_top = 0.04
anchor_right = 0.7
anchor_bottom = 0.22
grow_horizontal = 2
custom_fonts/font = SubResource( 4 )
text = "Official"
align = 1
valign = 1

[node name="oldenglish" type="TextureButton" parent="."]
self_modulate = Color( 0.580392, 0.584314, 0.65098, 1 )
anchor_left = 0.25
anchor_top = 0.3
anchor_right = 0.45
anchor_bottom = 0.7
texture_normal = ExtResource( 8 )
texture_pressed = ExtResource( 10 )
texture_hover = ExtResource( 9 )
expand = true

[node name="Label" type="Label" parent="oldenglish"]
anchor_top = 0.2
anchor_right = 1.0
anchor_bottom = 1.0
custom_fonts/font = SubResource( 4 )
text = "??????"
align = 1
valign = 1

[node name="Label2" type="Label" parent="oldenglish"]
self_modulate = Color( 1, 0.886275, 0.894118, 1 )
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
custom_fonts/font = SubResource( 4 )
text = "ORIGINAL UNALTERED DATA"
align = 1
valign = 1

[node name="old" type="TextureButton" parent="."]
self_modulate = Color( 0.580392, 0.584314, 0.65098, 1 )
anchor_left = 0.05
anchor_top = 0.3
anchor_right = 0.25
anchor_bottom = 0.7
texture_normal = ExtResource( 8 )
texture_pressed = ExtResource( 10 )
texture_hover = ExtResource( 9 )
expand = true

[node name="Label" type="Label" parent="old"]
anchor_right = 1.0
anchor_bottom = 0.8
custom_fonts/font = SubResource( 4 )
text = "??????"
align = 1
valign = 1

[node name="Label2" type="Label" parent="old"]
self_modulate = Color( 1, 0.886275, 0.894118, 1 )
anchor_right = 1.0
grow_horizontal = 2
grow_vertical = 0
custom_fonts/font = SubResource( 4 )
text = "OLD TRANSLATED DATA"
align = 1
valign = 1

[node name="new" type="TextureButton" parent="."]
visible = false
anchor_left = 0.6
anchor_top = 0.3
anchor_right = 0.8
anchor_bottom = 0.7
texture_normal = ExtResource( 1 )
expand = true

[node name="Label2" type="Label" parent="new"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_fonts/font = SubResource( 4 )
text = "??????"
align = 1
valign = 1

[node name="Label3" type="Label" parent="new"]
anchor_top = -0.1
anchor_right = 1.0
anchor_bottom = -0.1
custom_fonts/font = SubResource( 4 )
text = "NEW"
align = 1
valign = 1

[node name="quit" type="TextureButton" parent="."]
anchor_left = 0.85
anchor_top = 0.05
anchor_right = 0.95
anchor_bottom = 0.2
texture_normal = ExtResource( 11 )
expand = true

[node name="create" type="TextureButton" parent="."]
self_modulate = Color( 1, 0.686275, 0.741176, 1 )
anchor_left = 0.3
anchor_top = 0.8
anchor_right = 0.7
anchor_bottom = 1.0
texture_normal = ExtResource( 8 )
texture_pressed = ExtResource( 10 )
texture_hover = ExtResource( 9 )
expand = true

[node name="Label3" type="Label" parent="create"]
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 0
custom_fonts/font = SubResource( 4 )
text = "CREATE NEW VERSION + CHANGELOG"
align = 1
valign = 1

[node name="new2" type="TextureRect" parent="."]
anchor_left = 0.65
anchor_top = 0.27
anchor_right = 0.95
anchor_bottom = 0.73
texture = ExtResource( 1 )
expand = true

[node name="Label3" type="Label" parent="new2"]
anchor_top = -0.1
anchor_right = 1.0
anchor_bottom = -0.1
custom_fonts/font = SubResource( 4 )
text = "OPTIONS"
align = 1
valign = 1

[node name="VBoxContainer" type="VBoxContainer" parent="new2"]
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = 40.0
margin_top = 40.0
margin_right = -40.0
margin_bottom = -40.0

[node name="CheckBox" type="CheckBox" parent="new2/VBoxContainer"]
margin_right = 227.0
margin_bottom = 28.0
text = "ADD NEW DATA"
icon = ExtResource( 4 )

[node name="CheckBox2" type="CheckBox" parent="new2/VBoxContainer"]
margin_top = 32.0
margin_right = 227.0
margin_bottom = 60.0
text = "LIST CHANGED DATA"
icon = ExtResource( 5 )

[node name="Label" type="Label" parent="new2/VBoxContainer"]
margin_top = 64.0
margin_right = 227.0
margin_bottom = 95.0
text = "Please provide the untranslated, 
original english copy"

[node name="CheckBox4" type="CheckBox" parent="new2/VBoxContainer"]
margin_top = 99.0
margin_right = 227.0
margin_bottom = 127.0
text = "OVERWRITE CHANGED DATA"
icon = ExtResource( 5 )

[node name="CheckBox3" type="CheckBox" parent="new2/VBoxContainer"]
margin_top = 131.0
margin_right = 227.0
margin_bottom = 159.0
text = "DELETE REMOVED DATA"
icon = ExtResource( 3 )

[node name="CheckBox5" type="CheckBox" parent="new2/VBoxContainer"]
margin_top = 163.0
margin_right = 227.0
margin_bottom = 191.0
text = "LIST UNTRANSLATED DATA"
icon = ExtResource( 7 )

[connection signal="pressed" from="oldenglish" to="." method="_on_oldenglish_pressed"]
[connection signal="pressed" from="old" to="." method="_on_old_pressed"]
[connection signal="pressed" from="new" to="." method="_on_new_pressed"]
[connection signal="pressed" from="quit" to="." method="_on_quit_pressed"]
[connection signal="pressed" from="create" to="." method="_on_create_pressed"]
[connection signal="pressed" from="new2/VBoxContainer/CheckBox" to="." method="_on_CheckBox_pressed"]
[connection signal="pressed" from="new2/VBoxContainer/CheckBox2" to="." method="_on_CheckBox2_pressed"]
[connection signal="pressed" from="new2/VBoxContainer/CheckBox4" to="." method="_on_CheckBox4_pressed"]
[connection signal="pressed" from="new2/VBoxContainer/CheckBox3" to="." method="_on_CheckBox3_pressed"]
[connection signal="pressed" from="new2/VBoxContainer/CheckBox5" to="." method="_on_CheckBox5_pressed"]
