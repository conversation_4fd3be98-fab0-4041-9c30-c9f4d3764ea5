[gd_scene load_steps=3 format=2]

[ext_resource path="res://font/simonetta/Simonetta-Black.otf" type="DynamicFontData" id=1]

[sub_resource type="DynamicFont" id=1]
size = 32
outline_size = 2
outline_color = Color( 0.156863, 0.129412, 0.156863, 1 )
font_data = ExtResource( 1 )

[node name="corrupted" type="Label"]
margin_left = -79.0
margin_top = -42.0
margin_right = 79.0
margin_bottom = 42.0
grow_horizontal = 2
grow_vertical = 2
custom_colors/font_color = Color( 0.584314, 0.380392, 0.564706, 1 )
custom_fonts/font = SubResource( 1 )
text = "Corrupted!"
align = 1
valign = 1
