[gd_scene load_steps=7 format=2]

[ext_resource path="res://font/OpenSans-ExtraBold.ttf" type="DynamicFontData" id=1]
[ext_resource path="res://Assets/ui/debufficons/-1m.png" type="Texture" id=2]
[ext_resource path="res://Assets/ui/debuffnamemeter.png" type="Texture" id=3]
[ext_resource path="res://font/OpenSans-SemiBold.ttf" type="DynamicFontData" id=4]

[sub_resource type="DynamicFont" id=1]
size = 42
outline_size = 2
outline_color = Color( 0.129412, 0, 0.105882, 0.627451 )
font_data = ExtResource( 4 )

[sub_resource type="DynamicFont" id=2]
size = 32
outline_size = 3
outline_color = Color( 0, 0, 0, 1 )
font_data = ExtResource( 1 )

[node name="hbox" type="HBoxContainer"]
margin_right = 384.0
margin_bottom = 64.0
custom_constants/separation = 8

[node name="icon" type="TextureRect" parent="."]
margin_right = 64.0
margin_bottom = 64.0
texture = ExtResource( 2 )

[node name="meter" type="TextureRect" parent="."]
margin_left = 72.0
margin_right = 328.0
margin_bottom = 64.0
texture = ExtResource( 3 )

[node name="name" type="Label" parent="meter"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_colors/font_color = Color( 0.988235, 0.921569, 0.964706, 1 )
custom_fonts/font = SubResource( 1 )
text = "35as"
align = 1
valign = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="count" type="Label" parent="."]
margin_left = 336.0
margin_right = 382.0
margin_bottom = 64.0
grow_horizontal = 0
grow_vertical = 0
size_flags_horizontal = 0
size_flags_vertical = 1
custom_fonts/font = SubResource( 2 )
text = "XX"
align = 2
valign = 1
__meta__ = {
"_edit_use_anchors_": false
}

[node name="count2" type="Label" parent="."]
self_modulate = Color( 0.5625, 1, 0.569336, 0.392157 )
margin_left = 390.0
margin_right = 390.0
margin_bottom = 64.0
grow_horizontal = 0
grow_vertical = 0
size_flags_horizontal = 0
size_flags_vertical = 1
custom_fonts/font = SubResource( 2 )
align = 2
valign = 1
__meta__ = {
"_edit_use_anchors_": false
}
