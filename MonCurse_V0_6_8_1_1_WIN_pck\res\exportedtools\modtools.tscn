[gd_scene load_steps=8 format=2]

[ext_resource path="res://Assets/blacksquare.png" type="Texture" id=1]
[ext_resource path="res://exportedtools/modtools.gd" type="Script" id=2]
[ext_resource path="res://font/OpenSans-ExtraBold.ttf" type="DynamicFontData" id=3]
[ext_resource path="res://Assets/ui/key.png" type="Texture" id=4]
[ext_resource path="res://Assets/ui/keyhover.png" type="Texture" id=5]
[ext_resource path="res://Assets/ui/keypress.png" type="Texture" id=6]

[sub_resource type="DynamicFont" id=1]
size = 20
font_data = ExtResource( 3 )

[node name="modtools" type="CanvasLayer"]
layer = 121
script = ExtResource( 2 )

[node name="veil" type="TextureRect" parent="."]
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = -40.0
margin_top = -40.0
margin_right = 40.0
margin_bottom = 40.0
mouse_filter = 0
texture = ExtResource( 1 )
expand = true

[node name="TextureButton" type="TextureButton" parent="veil"]
self_modulate = Color( 0.580392, 0.584314, 0.65098, 1 )
anchor_left = 0.1
anchor_top = 0.2
anchor_right = 0.3
anchor_bottom = 0.4
texture_normal = ExtResource( 4 )
texture_pressed = ExtResource( 6 )
texture_hover = ExtResource( 5 )
expand = true

[node name="Label" type="Label" parent="veil/TextureButton"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_fonts/font = SubResource( 1 )
text = "Make a
translatable
text file"
align = 1
valign = 1

[node name="TextureButton2" type="TextureButton" parent="veil"]
self_modulate = Color( 0.580392, 0.584314, 0.65098, 1 )
anchor_left = 0.4
anchor_top = 0.2
anchor_right = 0.6
anchor_bottom = 0.4
texture_normal = ExtResource( 4 )
texture_pressed = ExtResource( 6 )
texture_hover = ExtResource( 5 )
expand = true

[node name="Label2" type="Label" parent="veil/TextureButton2"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_fonts/font = SubResource( 1 )
text = "Update an old
translatable
text file"
align = 1
valign = 1

[node name="TextureButton3" type="TextureButton" parent="veil"]
self_modulate = Color( 1, 0.639216, 0.639216, 1 )
anchor_left = 0.7
anchor_top = 0.2
anchor_right = 0.9
anchor_bottom = 0.4
texture_normal = ExtResource( 4 )
texture_pressed = ExtResource( 6 )
texture_hover = ExtResource( 5 )
expand = true

[node name="Label3" type="Label" parent="veil/TextureButton3"]
anchor_right = 1.0
anchor_bottom = 1.0
custom_fonts/font = SubResource( 1 )
text = "Close menu"
align = 1
valign = 1

[node name="veil2" type="TextureRect" parent="."]
visible = false
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 0
texture = ExtResource( 1 )
expand = true

[connection signal="pressed" from="veil/TextureButton" to="." method="_on_TextureButton_pressed"]
[connection signal="pressed" from="veil/TextureButton2" to="." method="_on_TextureButton2_pressed"]
[connection signal="pressed" from="veil/TextureButton3" to="." method="_on_TextureButton3_pressed"]
