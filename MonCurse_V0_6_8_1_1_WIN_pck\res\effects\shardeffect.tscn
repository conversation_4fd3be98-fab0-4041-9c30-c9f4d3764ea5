[gd_scene load_steps=6 format=2]

[ext_resource path="res://Assets/pickups/genericpickupitemv3.png" type="Texture" id=1]
[ext_resource path="res://effects/shardeffect.gd" type="Script" id=3]

[sub_resource type="ShaderMaterial" id=2]

[sub_resource type="AtlasTexture" id=3]
atlas = ExtResource( 1 )
region = Rect2( 0, 0, 128, 128 )

[sub_resource type="Animation" id=1]
resource_name = "depleteshard"
length = 1.2
tracks/0/type = "value"
tracks/0/path = NodePath(".:self_modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3, 0.6, 0.8, 1.2 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 0.588235, 0.796078, 0.431373, 1 ), Color( 1, 1, 1, 1 ), Color( 0.843137, 0.560784, 1, 1 ), Color( 0.341176, 0.341176, 0.341176, 1 ), Color( 0, 0, 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath(".:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1.2 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}
tracks/2/type = "value"
tracks/2/path = NodePath(".:offset")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1.2 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0, -215 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath(".:rotation_degrees")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 1.2 ),
"transitions": PoolRealArray( 2, 1 ),
"update": 0,
"values": [ 15.0, -15.0 ]
}

[node name="shardeffect" type="Sprite"]
visible = false
self_modulate = Color( 0, 0, 0, 0 )
material = SubResource( 2 )
rotation = -0.261799
scale = Vector2( 0.5, 0.5 )
z_index = 8
texture = SubResource( 3 )
offset = Vector2( 0, -215 )
script = ExtResource( 3 )

[node name="depleteshard" type="AnimationPlayer" parent="."]
autoplay = "depleteshard"
anims/depleteshard = SubResource( 1 )

[connection signal="animation_finished" from="depleteshard" to="." method="_on_depleteshard_animation_finished"]
