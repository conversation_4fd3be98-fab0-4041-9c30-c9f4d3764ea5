[gd_scene load_steps=8 format=2]

[ext_resource path="res://corruptionmeter.gd" type="Script" id=1]
[ext_resource path="res://Assets/ui/corruptionbarv3.png" type="Texture" id=2]
[ext_resource path="res://Assets/ui/corruptionbarv3fill.png" type="Texture" id=3]
[ext_resource path="res://zapsplat/zapsplat_multimedia_game_sound_underwater_bubble_wet_collect_item_earn_point_bonus_002_78396.ogg" type="AudioStream" id=4]

[sub_resource type="Animation" id=1]
resource_name = "drainbar"
length = 2.1
tracks/0/type = "value"
tracks/0/path = NodePath("meter:value")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.3, 1.4 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ 100.0, 100.0, 0.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("meter:rect_rotation")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.2, 0.4, 1, 1.4, 2.1 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 0.5, 1 ),
"update": 0,
"values": [ 0.0, -2.0, 2.0, -5.0, -11.0, -65.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("meter:rect_position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.1, 0.2, 1.3, 1.5, 1.6, 2.1 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 0.5, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0, -5 ), Vector2( 0, 15 ), Vector2( 0, 50 ), Vector2( 0, 30 ), Vector2( 0, 50 ), Vector2( 25, 250 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("meter:modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 1.4, 2.1 ),
"transitions": PoolRealArray( 1, 0.5, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.627451, 0.627451, 0.627451, 1 ), Color( 0, 0, 0, 0 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath(".:modulate")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("icon:self_modulate")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}

[sub_resource type="Animation" id=3]
resource_name = "fade"
length = 2.5
tracks/0/type = "value"
tracks/0/path = NodePath(".:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 2.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}

[sub_resource type="Animation" id=2]
resource_name = "fillbar"
tracks/0/type = "value"
tracks/0/path = NodePath("meter:value")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ 0.0, 0.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("meter:rect_rotation")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.1, 0.2, 0.4, 0.7, 1 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 2.0, -2.0, 1.0, -1.0, 0.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("meter:rect_position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.1, 0.6, 1 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 3 ), Vector2( 0, -7 ), Vector2( 0, 3 ), Vector2( 0, 0 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("meter:modulate")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.5, 1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 0.647059, 0.647059, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/4/type = "value"
tracks/4/path = NodePath(".:modulate")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("icon:self_modulate")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0, 0.5, 1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.705882 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0.705882 ) ]
}

[node name="hbox" type="HBoxContainer"]
margin_right = 264.0
margin_bottom = 64.0
grow_horizontal = 0
grow_vertical = 2
mouse_filter = 2
custom_constants/separation = 8
script = ExtResource( 1 )

[node name="meter" type="TextureProgress" parent="."]
modulate = Color( 0, 0, 0, 0 )
margin_right = 256.0
margin_bottom = 64.0
mouse_filter = 2
texture_under = ExtResource( 2 )
texture_progress = ExtResource( 3 )

[node name="icon" type="TextureRect" parent="."]
self_modulate = Color( 1, 1, 1, 0 )
margin_left = 264.0
margin_right = 264.0
margin_bottom = 64.0
mouse_filter = 2

[node name="drain" type="AnimationPlayer" parent="."]
anims/drainbar = SubResource( 1 )
anims/fade = SubResource( 3 )
anims/fillbar = SubResource( 2 )

[node name="fill" type="AudioStreamPlayer" parent="."]
stream = ExtResource( 4 )
volume_db = -22.0
bus = "SFX"

[node name="free_time" type="Timer" parent="."]
one_shot = true

[connection signal="animation_finished" from="drain" to="." method="_on_drain_animation_finished"]
[connection signal="timeout" from="free_time" to="." method="_on_free_time_timeout"]
