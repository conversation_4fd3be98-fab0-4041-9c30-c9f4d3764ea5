[gd_scene load_steps=40 format=2]

[ext_resource path="res://DialogueArt/CG/gameovers/specfoxscene.gd" type="Script" id=1]
[ext_resource path="res://DialogueArt/CG/gameovers/specfox/ram hand.png" type="Texture" id=2]
[ext_resource path="res://DialogueArt/CG/gameovers/specfox/fox2 B.png" type="Texture" id=3]
[ext_resource path="res://DialogueArt/CG/gameovers/specfox/ghostfox vase.png" type="Texture" id=4]
[ext_resource path="res://DialogueArt/CG/gameovers/specfox/fox1 B.png" type="Texture" id=5]
[ext_resource path="res://DialogueArt/CG/gameovers/specfox/fox1 C.png" type="Texture" id=6]
[ext_resource path="res://DialogueArt/CG/gameovers/specfox/fox1 main.png" type="Texture" id=7]
[ext_resource path="res://DialogueArt/CG/gameovers/specfox/fox2 main.png" type="Texture" id=8]
[ext_resource path="res://Background/specfox background.png" type="Texture" id=9]
[ext_resource path="res://Background/blacksquare.png" type="Texture" id=10]
[ext_resource path="res://DialogueArt/CG/gameovers/specfox/fox2 milkright.png" type="Texture" id=11]
[ext_resource path="res://DialogueArt/CG/gameovers/specfox/fox2 milkleft.png" type="Texture" id=12]
[ext_resource path="res://DialogueArt/CG/gameovers/specfox/fox2 milkhuff.png" type="Texture" id=13]
[ext_resource path="res://DialogueArt/CG/gameovers/specfox/vaseupright.png" type="Texture" id=14]
[ext_resource path="res://Assets/ui/translationflag.png" type="Texture" id=48]

[sub_resource type="Animation" id=67]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath("fox1:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("fox2:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("fox2:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=75]
resource_name = "fox2shake"
tracks/0/type = "value"
tracks/0/path = NodePath("fox2:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.8, 1 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( -3, 0 ), Vector2( -8, 0 ), Vector2( -10, 0 ), Vector2( -5, 0 ), Vector2( 4, 0 ), Vector2( 12, 0 ), Vector2( 8, 0 ), Vector2( -4, 0 ), Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("fox2:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 1, 1 ) ]
}

[sub_resource type="Animation" id=76]
resource_name = "fox2squish"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath("fox2:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.7, 1.4 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -80, 0 ), Vector2( -8, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("fox2:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.7, 1.4 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 1.05, 1 ), Vector2( 1, 1 ) ]
}

[sub_resource type="Animation" id=82]
resource_name = "grabshake"
length = 0.9
tracks/0/type = "value"
tracks/0/path = NodePath("fox1:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.3, 0.4, 0.6, 0.8, 0.9 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 4, 3 ), Vector2( 9, 5 ), Vector2( 6, -3 ), Vector2( -7, 1 ), Vector2( -4, 0 ), Vector2( 2, 0 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=79]
resource_name = "jump"
length = 0.6
tracks/0/type = "value"
tracks/0/path = NodePath("fox1:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 4 ), Vector2( 0, -11 ), Vector2( 0, -14 ), Vector2( 0, -3 ), Vector2( 0, 6 ), Vector2( 0, -2 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=68]
resource_name = "multishake"
length = 1.7
tracks/0/type = "value"
tracks/0/path = NodePath("fox1:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.8, 0.9, 1, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 7, 0 ), Vector2( 9, 0 ), Vector2( 6, 0 ), Vector2( -7, 0 ), Vector2( -4, 0 ), Vector2( 2, 0 ), Vector2( 0, 0 ), Vector2( 7, 0 ), Vector2( 9, 0 ), Vector2( 6, 0 ), Vector2( -7, 0 ), Vector2( -4, 0 ), Vector2( 2, 0 ), Vector2( 0, 0 ), Vector2( 7, 0 ), Vector2( -3, 0 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=80]
resource_name = "othershake"
length = 0.6
tracks/0/type = "value"
tracks/0/path = NodePath("fox1:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 3, -5 ), Vector2( -7, 3 ), Vector2( 2, 0 ), Vector2( 9, 0 ), Vector2( -5, 3 ), Vector2( 2, 0 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=69]
resource_name = "shake"
length = 0.6
tracks/0/type = "value"
tracks/0/path = NodePath("fox1:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 7, 0 ), Vector2( 9, 0 ), Vector2( 6, 0 ), Vector2( -7, 0 ), Vector2( -4, 0 ), Vector2( 2, 0 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=25]
resource_name = "hover"
length = 4.0
tracks/0/type = "value"
tracks/0/path = NodePath("sprites:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1, 2, 3, 4 ),
"transitions": PoolRealArray( 0.9, 1.3, 0.9, 1.3, 1 ),
"update": 0,
"values": [ Vector2( 0, -138 ), Vector2( 0, -238 ), Vector2( 0, -138 ), Vector2( 0, -38 ), Vector2( 0, -138 ) ]
}

[sub_resource type="Animation" id=26]
resource_name = "move_to_point"
tracks/0/type = "value"
tracks/0/path = NodePath("sprites:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.8, 1 ),
"transitions": PoolRealArray( 0.65, 1, 0.5 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -2, -3 ), Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.8, 1 ),
"transitions": PoolRealArray( 0.8, 1, 0.5 ),
"update": 0,
"values": [ Vector2( 0.25, 0.25 ), Vector2( 0.25, 0.25 ), Vector2( 0.25, 0.25 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("sprites:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.7, 1 ),
"transitions": PoolRealArray( 2, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 0.705882, 0.705882, 0.705882, 1 ), Color( 1, 1, 1, 1 ) ]
}
tracks/3/type = "method"
tracks/3/path = NodePath(".")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"values": [ {
"args": [ -1, false ],
"method": "action"
} ]
}

[sub_resource type="Animation" id=27]
resource_name = "shake"
tracks/0/type = "value"
tracks/0/path = NodePath("sprites:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.2, 0.3, 0.6, 1 ),
"transitions": PoolRealArray( 0.9, 1.3, 0.9, 1.3, 1 ),
"update": 0,
"values": [ Vector2( -2, -3 ), Vector2( 4, -8 ), Vector2( -5, 3 ), Vector2( 4, -2 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=83]
length = 0.001

[sub_resource type="Animation" id=81]
resource_name = "flashvase"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/vase:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.8, 2 ),
"transitions": PoolRealArray( 1, 2, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0.352941 ), Color( 1, 1, 1, 1 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites/vase:position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.8, 2 ),
"transitions": PoolRealArray( 0.5, 2, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 300, 0 ), Vector2( 0, 0 ) ]
}

[sub_resource type="Animation" id=66]
resource_name = "step 1"
length = 4.0
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/fox1:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites/fox2:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("sprites/vase:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("sprites/hand:visible")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("sprites/background:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ false, true ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("sprites/fox1:modulate")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("sprites/fox1/B:visible")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/7/type = "value"
tracks/7/path = NodePath("sprites/fox1/C:visible")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/8/type = "value"
tracks/8/path = NodePath("sprites/background:self_modulate")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/keys = {
"times": PoolRealArray( 0, 2, 4 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/9/type = "value"
tracks/9/path = NodePath("sprites/blackbg:visible")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/10/type = "value"
tracks/10/path = NodePath("sprites/vase2:visible")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}

[sub_resource type="Animation" id=70]
resource_name = "step 2"
length = 4.0
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/fox1:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites/fox2:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("sprites/vase:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1.2 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ false, true ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("sprites/hand:visible")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("sprites/background:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("sprites/fox1:modulate")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("sprites/fox1/B:visible")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = false
tracks/6/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/7/type = "value"
tracks/7/path = NodePath("sprites/fox1/C:visible")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = false
tracks/7/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/8/type = "value"
tracks/8/path = NodePath("sprites/background:self_modulate")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/9/type = "value"
tracks/9/path = NodePath("sprites/blackbg:visible")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/10/type = "value"
tracks/10/path = NodePath("sprites/hand:position")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/keys = {
"times": PoolRealArray( 0, 0.7, 1 ),
"transitions": PoolRealArray( 0.3, 1, 1 ),
"update": 0,
"values": [ Vector2( -300, 0 ), Vector2( 40, 0 ), Vector2( 0, 0 ) ]
}
tracks/11/type = "value"
tracks/11/path = NodePath("sprites/hand:modulate")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/12/type = "animation"
tracks/12/path = NodePath("sprites/foxeffect")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/keys = {
"clips": PoolStringArray( "grabshake" ),
"times": PoolRealArray( 0.4 )
}
tracks/13/type = "value"
tracks/13/path = NodePath("sprites/vase:position")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/keys = {
"times": PoolRealArray( 0, 0.8, 1.8, 2.1 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( -50, -50 ), Vector2( 50, 0 ), Vector2( 0, 0 ) ]
}
tracks/14/type = "value"
tracks/14/path = NodePath("sprites/vase:modulate")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/keys = {
"times": PoolRealArray( 0, 1.2, 2.1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/15/type = "value"
tracks/15/path = NodePath("sprites/vase2:visible")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}

[sub_resource type="Animation" id=71]
resource_name = "step 3"
length = 4.0
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/fox1:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites/fox2:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("sprites/vase:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("sprites/hand:visible")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("sprites/background:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("sprites/fox1:modulate")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("sprites/fox1/B:visible")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ false, true ]
}
tracks/7/type = "value"
tracks/7/path = NodePath("sprites/fox1/C:visible")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/8/type = "value"
tracks/8/path = NodePath("sprites/background:self_modulate")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/9/type = "value"
tracks/9/path = NodePath("sprites/blackbg:visible")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/10/type = "value"
tracks/10/path = NodePath("sprites/hand:position")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ) ]
}
tracks/11/type = "value"
tracks/11/path = NodePath("sprites/hand:modulate")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/12/type = "animation"
tracks/12/path = NodePath("sprites/foxeffect")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/keys = {
"clips": PoolStringArray( "shake", "multishake" ),
"times": PoolRealArray( 0, 0.5 )
}
tracks/13/type = "value"
tracks/13/path = NodePath("sprites/vase:position")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/keys = {
"times": PoolRealArray( 0.9 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ) ]
}
tracks/14/type = "value"
tracks/14/path = NodePath("sprites/vase:modulate")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/keys = {
"times": PoolRealArray( 0, 0.5, 1.1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0.666667 ), Color( 1, 1, 1, 1 ) ]
}
tracks/15/type = "value"
tracks/15/path = NodePath("sprites/vase2:visible")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}

[sub_resource type="Animation" id=72]
resource_name = "step 4"
length = 4.0
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/fox1:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites/fox2:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("sprites/vase:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("sprites/hand:visible")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("sprites/background:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("sprites/fox1:modulate")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("sprites/fox1/B:visible")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}
tracks/7/type = "value"
tracks/7/path = NodePath("sprites/fox1/C:visible")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"times": PoolRealArray( 0, 0.5 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ false, true ]
}
tracks/8/type = "value"
tracks/8/path = NodePath("sprites/background:self_modulate")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/9/type = "value"
tracks/9/path = NodePath("sprites/blackbg:visible")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/10/type = "value"
tracks/10/path = NodePath("sprites/hand:position")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ) ]
}
tracks/11/type = "value"
tracks/11/path = NodePath("sprites/hand:modulate")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/12/type = "animation"
tracks/12/path = NodePath("sprites/foxeffect")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/keys = {
"clips": PoolStringArray( "shake", "multishake" ),
"times": PoolRealArray( 0, 0.5 )
}
tracks/13/type = "value"
tracks/13/path = NodePath("sprites/vase:position")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/keys = {
"times": PoolRealArray( 0.9 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ) ]
}
tracks/14/type = "value"
tracks/14/path = NodePath("sprites/vase:modulate")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/keys = {
"times": PoolRealArray( 0, 0.5, 1.1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0.666667 ), Color( 1, 1, 1, 1 ) ]
}
tracks/15/type = "value"
tracks/15/path = NodePath("sprites/vase2:visible")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}

[sub_resource type="Animation" id=73]
resource_name = "step 5"
length = 4.0
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/fox1:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1.6 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites/fox2:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("sprites/vase:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1.4 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("sprites/hand:visible")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 1.4 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("sprites/background:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("sprites/fox1:modulate")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0, 1.6 ),
"transitions": PoolRealArray( 3, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("sprites/fox1/B:visible")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0, 1.6 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}
tracks/7/type = "value"
tracks/7/path = NodePath("sprites/fox1/C:visible")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"times": PoolRealArray( 0, 1.6 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}
tracks/8/type = "value"
tracks/8/path = NodePath("sprites/background:self_modulate")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/9/type = "value"
tracks/9/path = NodePath("sprites/blackbg:visible")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/10/type = "value"
tracks/10/path = NodePath("sprites/hand:position")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ) ]
}
tracks/11/type = "value"
tracks/11/path = NodePath("sprites/hand:modulate")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/keys = {
"times": PoolRealArray( 0, 1.4 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/12/type = "animation"
tracks/12/path = NodePath("sprites/foxeffect")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/keys = {
"clips": PoolStringArray( "shake", "multishake" ),
"times": PoolRealArray( 0, 0.5 )
}
tracks/13/type = "value"
tracks/13/path = NodePath("sprites/vase:position")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ) ]
}
tracks/14/type = "value"
tracks/14/path = NodePath("sprites/vase:modulate")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/keys = {
"times": PoolRealArray( 0, 1.4 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/15/type = "value"
tracks/15/path = NodePath("sprites/blackbg:modulate")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/keys = {
"times": PoolRealArray( 0, 1.6 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/16/type = "value"
tracks/16/path = NodePath("sprites/vase2:visible")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}

[sub_resource type="Animation" id=74]
resource_name = "step 6"
length = 4.0
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/fox1:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites/fox2:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("sprites/vase:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("sprites/hand:visible")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("sprites/background:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("sprites/background:self_modulate")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("sprites/blackbg:visible")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/7/type = "animation"
tracks/7/path = NodePath("sprites/foxeffect")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"clips": PoolStringArray( "fox2squish" ),
"times": PoolRealArray( 0 )
}
tracks/8/type = "value"
tracks/8/path = NodePath("sprites/blackbg:modulate")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/9/type = "value"
tracks/9/path = NodePath("sprites/fox2:modulate")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/keys = {
"times": PoolRealArray( 0, 1.5 ),
"transitions": PoolRealArray( 0.5, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ) ]
}
tracks/10/type = "value"
tracks/10/path = NodePath("sprites/fox2/B:visible")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/11/type = "value"
tracks/11/path = NodePath("sprites/fox2/milksprites:visible")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/12/type = "value"
tracks/12/path = NodePath("sprites/vase2:visible")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}

[sub_resource type="Animation" id=77]
resource_name = "step 7"
length = 4.0
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/fox1:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites/fox2:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("sprites/vase:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("sprites/hand:visible")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("sprites/background:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("sprites/background:self_modulate")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("sprites/blackbg:visible")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/7/type = "animation"
tracks/7/path = NodePath("sprites/foxeffect")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"clips": PoolStringArray(  ),
"times": PoolRealArray(  )
}
tracks/8/type = "value"
tracks/8/path = NodePath("sprites/blackbg:modulate")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/9/type = "value"
tracks/9/path = NodePath("sprites/fox2:modulate")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/10/type = "value"
tracks/10/path = NodePath("sprites/fox2/B:visible")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/11/type = "value"
tracks/11/path = NodePath("sprites/fox2/milksprites:visible")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/12/type = "value"
tracks/12/path = NodePath("sprites/vase2:visible")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}

[sub_resource type="Animation" id=78]
resource_name = "step 8"
length = 4.0
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/fox1:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites/fox2:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("sprites/vase:visible")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("sprites/hand:visible")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("sprites/background:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("sprites/blackbg:visible")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/6/type = "animation"
tracks/6/path = NodePath("sprites/foxeffect")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"clips": PoolStringArray(  ),
"times": PoolRealArray(  )
}
tracks/7/type = "value"
tracks/7/path = NodePath("sprites/blackbg:modulate")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ) ]
}
tracks/8/type = "value"
tracks/8/path = NodePath("sprites/fox2:modulate")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/9/type = "value"
tracks/9/path = NodePath("sprites/fox2/B:visible")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}
tracks/10/type = "value"
tracks/10/path = NodePath("sprites/fox2/milksprites:visible")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ true ]
}
tracks/11/type = "value"
tracks/11/path = NodePath("sprites/vase2:visible")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 1,
"values": [ false ]
}

[sub_resource type="Animation" id=84]
resource_name = "vasedrift"
length = 4.0
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/vase2:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 2, 3 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 2141, 1858 ), Vector2( 2541, 1858 ), Vector2( 2580, 1900 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites/vase2:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 3 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("sprites/vase2:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1, 2, 3 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("sprites/vase2:rotation_degrees")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 0.9, 2.1, 3 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, -20.0, 5.0, 13.9052 ]
}

[sub_resource type="Animation" id=85]
resource_name = "vasedrift2"
length = 5.0
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/vase2:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1.3, 1.5, 3 ),
"transitions": PoolRealArray( 1, 1, 2, 1 ),
"update": 0,
"values": [ Vector2( 2300, 2250 ), Vector2( 1640.16, 1859.68 ), Vector2( 1601, 1858 ), Vector2( 1400, 2250 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites/vase2:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 3 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("sprites/vase2:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1, 2, 3 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("sprites/vase2:rotation_degrees")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 2, 3 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 20.0, 13.9052 ]
}

[sub_resource type="Animation" id=86]
resource_name = "vasedrift3"
length = 5.0
tracks/0/type = "value"
tracks/0/path = NodePath("sprites/vase2:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1.3, 1.5, 2.5, 3 ),
"transitions": PoolRealArray( 1, 1, 0.5, 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 1000 ), Vector2( 800, 2000 ), Vector2( 850, 1980 ), Vector2( 1000, 1800 ), Vector2( 900, 1800 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("sprites/vase2:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 3 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 1,
"values": [ true, false ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("sprites/vase2:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1, 2.3, 3 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("sprites/vase2:rotation_degrees")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 2, 3 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ -40.0, 5.0, -5.0 ]
}

[node name="specfoxscene" type="CanvasLayer"]
layer = 4
script = ExtResource( 1 )

[node name="testdonotdelete" type="TextureRect" parent="."]
visible = false
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource( 48 )
expand = true

[node name="sprites" type="Node2D" parent="."]
position = Vector2( 0, -38 )
scale = Vector2( 0.25, 0.25 )

[node name="background" type="Sprite" parent="sprites"]
visible = false
texture = ExtResource( 9 )
centered = false

[node name="blackbg" type="Sprite" parent="sprites"]
scale = Vector2( 60, 33.75 )
texture = ExtResource( 10 )
centered = false

[node name="fox1" type="Sprite" parent="sprites"]
visible = false
texture = ExtResource( 7 )
centered = false
offset = Vector2( 925, 0 )

[node name="B" type="Sprite" parent="sprites/fox1"]
texture = ExtResource( 5 )
centered = false
offset = Vector2( 2363, 258 )

[node name="C" type="Sprite" parent="sprites/fox1"]
visible = false
texture = ExtResource( 6 )
centered = false
offset = Vector2( 2317, 378 )

[node name="fox2" type="Sprite" parent="sprites"]
modulate = Color( 1, 1, 1, 0 )
texture = ExtResource( 8 )
centered = false

[node name="B" type="Sprite" parent="sprites/fox2"]
texture = ExtResource( 3 )
centered = false
offset = Vector2( 1435, 495 )

[node name="milksprites" type="Node2D" parent="sprites/fox2"]

[node name="top" type="Sprite" parent="sprites/fox2/milksprites"]
texture = ExtResource( 13 )
centered = false
offset = Vector2( 1340, 675 )

[node name="bottomleft" type="Sprite" parent="sprites/fox2/milksprites"]
texture = ExtResource( 12 )
centered = false
offset = Vector2( 0, 1550 )

[node name="bottomright" type="Sprite" parent="sprites/fox2/milksprites"]
texture = ExtResource( 11 )
centered = false
offset = Vector2( 2630, 1530 )

[node name="vase" type="Sprite" parent="sprites"]
visible = false
modulate = Color( 1, 1, 1, 0.514706 )
position = Vector2( 281.25, 0 )
texture = ExtResource( 4 )
centered = false
offset = Vector2( 969, 0 )

[node name="vase2" type="Sprite" parent="sprites"]
visible = false
modulate = Color( 1, 1, 1, 0 )
self_modulate = Color( 0.784314, 0.784314, 0.784314, 1 )
position = Vector2( 2541, 1608 )
rotation = -0.0872665
z_index = 1
texture = ExtResource( 14 )

[node name="hand" type="Sprite" parent="sprites"]
visible = false
texture = ExtResource( 2 )
centered = false
offset = Vector2( 1165, 1070 )

[node name="foxeffect" type="AnimationPlayer" parent="sprites"]
playback_speed = 2.0
anims/RESET = SubResource( 67 )
anims/fox2shake = SubResource( 75 )
anims/fox2squish = SubResource( 76 )
anims/grabshake = SubResource( 82 )
anims/jump = SubResource( 79 )
anims/multishake = SubResource( 68 )
anims/othershake = SubResource( 80 )
anims/shake = SubResource( 69 )

[node name="hover" type="AnimationPlayer" parent="."]
anims/hover = SubResource( 25 )
anims/move_to_point = SubResource( 26 )
anims/shake = SubResource( 27 )

[node name="fadeshow" type="AnimationPlayer" parent="."]
anims/RESET = SubResource( 83 )
anims/flashvase = SubResource( 81 )
"anims/step 1" = SubResource( 66 )
"anims/step 2" = SubResource( 70 )
"anims/step 3" = SubResource( 71 )
"anims/step 4" = SubResource( 72 )
"anims/step 5" = SubResource( 73 )
"anims/step 6" = SubResource( 74 )
"anims/step 7" = SubResource( 77 )
"anims/step 8" = SubResource( 78 )
anims/vasedrift = SubResource( 84 )
anims/vasedrift2 = SubResource( 85 )
anims/vasedrift3 = SubResource( 86 )

[connection signal="animation_finished" from="hover" to="." method="_on_hover_animation_finished"]
