extends Control

func _ready():
	if Playervariables.current_translation_code != Playervariables.TL_DEFAULT:
		$Label3.set_text(Playervariables.current_translation_code)
	else:
		$oldenglish.visible = false
		$old.visible = false
		$new2.visible = false
		$Label3.set_text("Please select a translation\nthen return here.")
		$create.visible = false
		return
	$create.set_modulate(Color(0.5,0.5,0.5,0.5))
	$create.disabled = true
	$new2.visible = false
	find_mods()
	_on_CheckBox_pressed()
	_on_CheckBox2_pressed()
	_on_CheckBox3_pressed()
	_on_CheckBox4_pressed()

func process_speechdict(filestring):
	var speechdatafile = File.new()
	speechdatafile.open(filestring, File.READ) #special version takes full filepaths
	var speechdatajson = JSON.parse(speechdatafile.get_as_text())
	speechdatafile.close()
	return speechdatajson.result

func write_json(filepath,savewhat):
	var savedatafile = File.new()
	savedatafile.open(filepath, File.WRITE)
#	savedatafile.store_var(JSON.print(savewhat,"\t"))
	savedatafile.store_string(JSON.print(savewhat,"\t"))
	savedatafile.close()

func list_files_in_directory(path,searchgd=false) -> Array:
	var files = []
	var dir = Directory.new()
	dir.open(path)
	dir.list_dir_begin()

	while true:
		var file = dir.get_next()
		if file == "":
			break
		else:
			if not file.begins_with("."):
				if searchgd == false and file.ends_with(".json"):
					files.append(path+file)
				else:
					if file.ends_with(".gd"):# or file.ends_with(".gdc"):
						files.append(path+file)
					elif dir.current_is_dir() and file != "donotexport":
						var extrastrings = list_files_in_directory(path+file+"/",true)
						if typeof(extrastrings) == TYPE_ARRAY:
							files += extrastrings
	dir.list_dir_end()
	return files

var translationpath = ""
var orderedresults = []
#var activate_mods = false
enum mods{TRANSLATION}
var modarray = [false]
func find_mods():
	var path #
	if Playervariables.current_translation_code.find("internalmods") == -1:
		path = OS.get_executable_path().get_base_dir() + "/" + Playervariables.current_translation_code + "/"
	else:
		path = "res://" + Playervariables.current_translation_code + "/"
	var dir = Directory.new()
	dir.open(path)
	dir.list_dir_begin()
	var possibleresults = []
	var extraresults = []
	while true:
		var file = dir.get_next()
#		print(file)
		if file == "":
			break
		elif file != ".":
			if file.begins_with("Original"):
				possibleresults.append(file)
			elif file.begins_with("ef.") or file.begins_with("Translatable"):
				possibleresults.append(file)
			elif file.ends_with(".json") and file.find("font_parameters") == -1:
				extraresults.append(file)


		orderedresults = possibleresults.duplicate()
		Playervariables.order_by_date(orderedresults)
		orderedresults += extraresults

	dir.list_dir_end()
	valid = false
#	match orderedresults.size():
#		0:
	if orderedresults.size() < 1:
		$old/Label.set_text("Could not find any 'ef.' or 'Translatable.' or .json files.")
		$new/Label2.set_text("Please ensure Moncurse's .exe and .pck are\non the same level as the 'mods' folder.")
	else:
#		1,2,_:
#			$old/Label.set_text("Only found one file. Needs two to compare.")
#			$new/Label2.set_text(orderedresults[0])
#		2,_:
		$old/Label.set_text(orderedresults[0])
		$oldenglish/Label.set_text(orderedresults[0])
		var num = 0
		for result in orderedresults:
			if result.begins_with("ef.") or result.begins_with("Translatable."):
				$old/Label.set_text(result)
				oldnum = num
		for result in orderedresults:
			if result.begins_with("Original"):
				$oldenglish/Label.set_text(result)
				oldenglishnum = num
#			$new/Label2.set_text(orderedresults[-1])
#			$create.visible = true
#			newnum = orderedresults.size()-1
		valid = true
		$new2.visible = true
		can_make_changelog()
var valid = false

#func order_by_date(usearray)->Array:
#	var clear = false
#	while clear == false:
#		clear = true
#		for i3 in range(usearray.size()-1):
#			if compare_date(usearray[i3],usearray[i3+1]) == false:#orderedresults[i3] < orderedresults[i3+1]:
#				var value = usearray[i3]
#				usearray.remove(i3)
#				usearray.append(value)
#				clear = false
#	return usearray
#func compare_date(left,right)->bool:
#	var leftarray = []
#	var rightarray = []
#	while left.find(".") > -1:
#		leftarray.append(left.to_int())
#		left = left.substr(left.find(".")+1,-1)
#	while right.find(".") > -1:
#		rightarray.append(right.to_int())
#		right = right.substr(right.find(".")+1,-1)
#	for i in range(leftarray.size()):
#		if i > (rightarray.size()-1):
#			return true
#		if leftarray[i] == rightarray[i]:
#			pass
#		elif leftarray[i] > rightarray[i]:
#			return true
#		else:
#			return false
#	if rightarray.size() > leftarray.size():
#		return false
#	else:
#		return true


func _on_quit_pressed():
	if get_parent().get_name() != "root":
		get_parent().visible = false
	queue_free()

var dictadded = {}
var dictchanged = {}
var dictnontl = {}
var dictremoved = {}
var truedictold
var dictoldenglish
var dictupdated
var dictnew
func _on_create_pressed():
	if Playervariables.current_translation_code.find("internalmods") > -1:
		translationpath = "res://" +  Playervariables.current_translation_code + "/"
	else:
		translationpath = OS.get_executable_path().get_base_dir() + "/" + Playervariables.current_translation_code + "/"
	truedictold = process_speechdict(translationpath+$old/Label.get_text())
	if $new2/VBoxContainer/CheckBox2.pressed == true:
		dictoldenglish = process_speechdict(translationpath+$oldenglish/Label.get_text())
	else:
		dictoldenglish = truedictold.duplicate(true)
	dictupdated = truedictold.duplicate(true)
	var attempt_textify = load("res://exportedtools/Editor Tools Textify.tscn").instance()
	attempt_textify.visible = false
	add_child(attempt_textify)
	dictnew = attempt_textify.generate_file(false)
	attempt_textify.queue_free()
	var OriginalDict = dictnew.duplicate(true)
#	dictnew = pass#process_speechdict(translationpath+$new/Label2.get_text())
	if dictoldenglish == null or dictnew == null or truedictold == null:
		$create/Label3.set_text("There was an error in loading the dictionaries.\nEnsure no corrupted characters are in the dictionary so it can parse.")
	dictadded = {}#dictnew.duplicate()
	dictchanged = {}#dictnew.duplicate()
	dictremoved = {}#dictoldenglish.duplicate()
	dictnontl = {}
	var inconsistentdict = {}
	check_inside_dictionary(dictoldenglish,dictnew,dictadded,dictchanged,dictremoved,dictupdated,truedictold,inconsistentdict)
	var finaldict = {}
	var create_new = false
	if inconsistencies > 0:
		finaldict["ENTRIES NOT FOUND IN ORIGINALDONOTALTER .json"] = inconsistentdict
#	else:
#		finaldict["The OriginalDoNoAlter .json is 100% compatible with your Translatable .json."] = true
	if $new2/VBoxContainer/CheckBox.pressed == true:
		finaldict["ADDED_TO_NEW_DICTIONARY"] = dictadded
		create_new = true
	if $new2/VBoxContainer/CheckBox2.pressed == true:
		finaldict["VALUE_CHANGED_FROM_OLD_TO_NEW"] = dictchanged
		create_new = true
	if $new2/VBoxContainer/CheckBox3.pressed == true:
		finaldict["REMOVED_FROM_OLD_DICTIONARY"] = dictremoved
		create_new = true
	if $new2/VBoxContainer/CheckBox5.pressed == true:
		finaldict["VALUE_NOT_TRANSLATED"] = dictnontl
#	var finaldict = {
#		"ADDED_TO_NEW_DICTIONARY":dictadded,
#		"VALUE_CHANGED_FROM_OLD_TO_NEW":dictchanged,
#		"REMOVED_FROM_OLD_DICTIONARY":dictremoved
#	}

	var newtlname = $old/Label.get_text()
	if newtlname.ends_with(".json"):
		newtlname = newtlname.substr(0,newtlname.length()-5)
	var selfpath = OS.get_executable_path()
	var rawnum2 = "CurrentVer"
	if selfpath.find("V") != -1:
		selfpath = selfpath.substr(selfpath.rfind("/"),-1)
		selfpath = selfpath.substr(selfpath.find("V"),-1)
		selfpath = selfpath.substr(0,selfpath.rfind("_"))
		if selfpath.length() > 4:
			newtlname = "Translatable."+selfpath
		rawnum2 = selfpath
		
	var rawnum1 = $old/Label.get_text().replace(".","").to_int()
	var usename = "CHANGELOG_"+str(rawnum1) + "_to_" + str(rawnum2)

	var dir = Directory.new()
	var path = OS.get_executable_path().get_base_dir()
	dir.open(path+"/mods/translation")

	
	var teststring = usename
	var testtlstring = newtlname
	var i = 1
	while dir.file_exists(str(teststring)+".json") or dir.file_exists(str(testtlstring)+".json"):
		teststring = usename + "." + str(i)
		testtlstring = newtlname + "." + str(i)
		i += 1
	usename = teststring
	newtlname = testtlstring
	
	if dictnontl.size() > 0 or dictadded.size() > 0 or dictchanged.size() > 0 or dictremoved.size() > 0:
		write_json(path+"/mods/translation/"+usename+".json",finaldict)
		if create_new == true:
			write_json(path+"/mods/translation/"+newtlname+".json",dictupdated)
			if $new2/VBoxContainer/CheckBox2.pressed == true or $new2/VBoxContainer/CheckBox.pressed == true:
				write_json(path+"/mods/translation/OriginalDoNotAlter."+rawnum2+".json",OriginalDict)

		if dir.file_exists(usename+".json") == false:
			if dir.file_exists(newtlname+".json") == false:
				$create/Label3.set_text("ERROR: The mods/translation directory was found,\n but both the changelog and new version failed to write.")
			else:
				$create/Label3.set_text("ERROR: The mods/translation directory was found,\n but the Changelog failed to write.")
		elif dir.file_exists(newtlname+".json") == false and create_new == true:
			$create/Label3.set_text("ERROR: Although the mods/translation directory was found,\nthe Changelog was made but the updated translationfile failed to write.")
		else:
			$create/Label3.set_text("Your file has been created in the mods/translation folder, go look at it.\nTop-right button quits.\nFilenames are: "+usename+" and "+newtlname)
		if dir.file_exists(usename+".json") == true and inconsistencies > 0:
			$Label3.set_text(str(inconsistencies)+" inconsistencies were found in the OriginalDoNotAlter input,\n these are listed in the changelog produced.")
		$old.visible = false
		$oldenglish.visible = false
		$new2.visible = false
		$create.disabled = true
		valid = false
		$create.set_modulate(Color(0.5,0.5,0.5,0.5))
	else:
		$create/Label3.set_text("No changes were found. No files were produced. All OK!")
#	for key in dictnew:
#		if typeof(dictnew[key]) == TYPE_DICTIONARY:
#			if dictoldenglish.has(key) == false:
#				dictadded[key] = dictnew[key]
#			else:
#				for subkey in dictnew[key]:
#					if dictoldenglish.has(key) == false:
#						dictadded[key][subkey] = dictnew[key][subkey]
#					elif dictadded

var inconsistencies = 0
func check_inside_dictionary(currentoldenglish,currentnewdict,currentaddeddict,currentchangeddict,currentremoveddict,updatethisdict,currenttrueolddict,currentinconsistentdict):
#	var addnew = !$new2/VBoxContainer/CheckBox4.pressed
	for key in currentnewdict:
		
		if currenttrueolddict.has(key) == true and currentoldenglish.has(key) == false:
			if typeof(currenttrueolddict[key]) == TYPE_DICTIONARY:
				currentoldenglish[key] = {}
#			currentoldenglish[key] = currenttrueolddict[key]#.duplicate()
#			print("Pointer transfer of value from OldTranslation to DoNotAlter:"+str(currenttrueolddict[key]))
		elif currentoldenglish.has(key) == true and currenttrueolddict.has(key) == false:
			if typeof(currentoldenglish[key]) == TYPE_DICTIONARY:
				currenttrueolddict[key] = {}
#			else:
#				currenttrueolddict[key] = null
#			currenttrueolddict[key] = currentoldenglish[key]#.duplicate()
#			print("Pointer transfer of value from DoNotAlter to OldTranslation:"+str(currentoldenglish[key]))
		
		if typeof(currentnewdict[key]) == TYPE_DICTIONARY:
			if updatethisdict.has(key) == false:
				if $new2/VBoxContainer/CheckBox.pressed == true:
					updatethisdict[key] = currentnewdict[key].duplicate(true)
				currentaddeddict[key] = currentnewdict[key].duplicate(true)
#				updatethisdict = reorder_dict(updatethisdict,"","","",true)
#			elif (key == "event_messages" or key == "chapter_text") and OS.is_debug_build() == false:
#				currentaddeddict[key] = {"error":key+" is currently bugged, you'll need to ask the dev to check it"}
			elif key == "meta":
				pass
			else:
				currentaddeddict[key] = {}
				currentchangeddict[key] = {}
				currentremoveddict[key] = {}
				currentinconsistentdict[key] = {}
				check_inside_dictionary(currentoldenglish[key],currentnewdict[key],currentaddeddict[key],currentchangeddict[key],currentremoveddict[key],updatethisdict[key],currenttrueolddict[key],currentinconsistentdict[key])
				if currentaddeddict[key].size() == 0:
					currentaddeddict.erase(key)
				if currentchangeddict[key].size() == 0:
					currentchangeddict.erase(key)
				if currentremoveddict[key].size() == 0:
					currentremoveddict.erase(key)
				if currentinconsistentdict[key].size() == 0:
					currentinconsistentdict.erase(key)
		elif typeof(key) in [TYPE_REAL,TYPE_INT] and updatethisdict.has(str(key)):
			if currentoldenglish.has(str(key)) == false:
				inconsistencies += 1
				currentinconsistentdict[str(key)] = updatethisdict[str(key)]
#				currentremoveddict[key] = updatethisdict[key]
#				if $new2/VBoxContainer/CheckBox3.pressed == true:
#					updatethisdict.erase(key)
			else:
				var nontl = false
				if currenttrueolddict[str(key)] == currentoldenglish[str(key)]:
					if currenttrueolddict[str(key)] == currentnewdict[key]:
						dictnontl[str(key)] = currenttrueolddict[str(key)]
					nontl = true
				if currentoldenglish[str(key)] != currentnewdict[key]:
					currentchangeddict[str(key)] = currentnewdict[key]
					if nontl == false:
						currentchangeddict[str(key)+".OLD_ENGLISH_DEBUG"] = currentoldenglish[str(key)]
						currentchangeddict[str(key)+".OLD_TRANSLATION_DEBUG"] = currenttrueolddict[str(key)]
						if $new2/VBoxContainer/CheckBox2.pressed == true and $new2/VBoxContainer/CheckBox4.pressed == true:
							updatethisdict[str(key)] = currentnewdict[key]
					else:
						updatethisdict[str(key)] = currentnewdict[key]
		elif updatethisdict.has(key) == true:#test if key is changed?
			if currentoldenglish.has(str(key)) == false:
				inconsistencies += 1
				currentinconsistentdict[key] = updatethisdict[key]
#				currentremoveddict[key] = updatethisdict[key]
#				if $new2/VBoxContainer/CheckBox3.pressed == true:
#					updatethisdict.erase(key)
			else:
				var nontl = false
				if (typeof(currentoldenglish[key]) == typeof(currenttrueolddict[key])) and (typeof(currentoldenglish[key]) == TYPE_ARRAY and hash(currenttrueolddict[key]) == hash(currentoldenglish[key])) or (currenttrueolddict[key] == currentoldenglish[key]):
					if key.ends_with("DEBUG") or key.ends_with(".char"):
						pass
					else:
						if (typeof(currentnewdict[key]) == typeof(currenttrueolddict[key])) and ((typeof(currentnewdict[key]) == TYPE_ARRAY and hash(currenttrueolddict[key]) == hash(currentnewdict[key])) or (currenttrueolddict[key] == currentnewdict[key])):
							dictnontl[key] = currenttrueolddict[key]
						nontl = true
				if (typeof(currentnewdict[key]) != typeof(currentoldenglish[key])) or (typeof(currentnewdict[key]) == TYPE_ARRAY and hash(currentoldenglish[key]) != hash(currentnewdict[key])) or (currentoldenglish[key] != currentnewdict[key]):
					currentchangeddict[key] = currentnewdict[key]
					if nontl == false:
						currentchangeddict[key+".OLD_ENGLISH_DEBUG"] = currentoldenglish[key]
						currentchangeddict[key+".OLD_TRANSLATION_DEBUG"] = currenttrueolddict[key]
						if $new2/VBoxContainer/CheckBox2.pressed == true and $new2/VBoxContainer/CheckBox4.pressed == true:
							updatethisdict[key] = currentnewdict[key]
					else:
						updatethisdict[key] = currentnewdict[key]
		else: #key is ADDED
			currentaddeddict[key] = currentnewdict[key]
			if $new2/VBoxContainer/CheckBox.pressed == true:
				updatethisdict[key] = currentnewdict[key]
	for key in currentoldenglish:
		if currentnewdict.has(key) == false:#key is NOT removed
			if key.is_valid_integer() and currentnewdict.has(key.to_int()):
				pass
			else:
				currentremoveddict[key] = currentoldenglish[key]
				if $new2/VBoxContainer/CheckBox3.pressed == true:
					if updatethisdict.has(key):
						updatethisdict.erase(key)

func reorder_dict(usedict,insert_key,new_key_name,insert_data,alphabet) -> Dictionary:
	var newdict = {}
	if alphabet == false:
		for key in usedict:
			newdict[key] = usedict[key]
			if key == insert_key:
				newdict[new_key_name] = insert_data
#				print(newdict)
	else:
		var keyorderarray = []
		for key in usedict:
			keyorderarray.append(key)
		keyorderarray.sort()
		for keyvalue in keyorderarray:
			newdict[keyvalue] = usedict[keyvalue]
	return newdict.duplicate(true)

var newnum = 0
func _on_new_pressed():
	if orderedresults.size() > 2:
		newnum = newnum-1
		if newnum == oldnum:
			newnum = newnum-1
		if newnum <= 0:
			newnum = orderedresults.size() -1
		$new/Label2.set_text(orderedresults[newnum])

var oldnum = 0
func _on_old_pressed():
	if orderedresults.size() > 2:
		oldnum = (oldnum+1) % (orderedresults.size())
		$old/Label.set_text(orderedresults[oldnum])

var oldenglishnum = 0
func _on_oldenglish_pressed():
	if orderedresults.size() > 2:
		oldenglishnum = (oldenglishnum+1) % (orderedresults.size())
		$oldenglish/Label.set_text(orderedresults[oldenglishnum])



func _on_CheckBox_pressed():
	if $new2/VBoxContainer/CheckBox.pressed == true:
		$new2/VBoxContainer/CheckBox.set_self_modulate(Color(0.8,1,0.8,1))
	else:
		$new2/VBoxContainer/CheckBox.set_self_modulate(Color(0.7,0.7,0.7,0.5))
	can_make_changelog()


func _on_CheckBox2_pressed():
	if $new2/VBoxContainer/CheckBox2.pressed == true:
		$new2/VBoxContainer/CheckBox2.set_self_modulate(Color(1,1,0.6,1))
		$new2/VBoxContainer/Label.visible = true
		$new2/VBoxContainer/CheckBox4.visible = true
		$oldenglish.visible = true
		$new2/VBoxContainer/Label.set_self_modulate(Color(1,1,0.6,1))
	else:
		$new2/VBoxContainer/CheckBox2.set_self_modulate(Color(0.7,0.7,0.7,0.5))
		$new2/VBoxContainer/CheckBox4.visible = false
		$new2/VBoxContainer/Label.visible = false
		$oldenglish.visible = false
	can_make_changelog()


func _on_CheckBox3_pressed():
	if $new2/VBoxContainer/CheckBox3.pressed == true:
		$new2/VBoxContainer/CheckBox3.set_self_modulate(Color(1,0.8,0.8,1))
	else:
		$new2/VBoxContainer/CheckBox3.set_self_modulate(Color(0.7,0.7,0.7,0.5))
	can_make_changelog()


func _on_CheckBox4_pressed():
	if $new2/VBoxContainer/CheckBox4.pressed == true:
		$new2/VBoxContainer/CheckBox4.set_self_modulate(Color(1,1,0.6,1))
	else:
		$new2/VBoxContainer/CheckBox4.set_self_modulate(Color(0.8,0.8,0.5,0.5))

func can_make_changelog():
	if valid == true:
		if $new2/VBoxContainer/CheckBox.pressed == true or $new2/VBoxContainer/CheckBox2.pressed == true or $new2/VBoxContainer/CheckBox3.pressed == true or $new2/VBoxContainer/CheckBox5.pressed == true:
			$create.disabled = false
			$create.set_modulate(Color(1,1,1,1))
		else:
			$create.disabled = true
			$create.set_modulate(Color(0.5,0.5,0.5,0.5))
		if $new2/VBoxContainer/CheckBox.pressed == false and $new2/VBoxContainer/CheckBox2.pressed == false and $new2/VBoxContainer/CheckBox3.pressed == false and $new2/VBoxContainer/CheckBox5.pressed == true:
			$create/Label3.set_text("CREATE CHANGELOG")
		else:
			$create/Label3.set_text("CREATE NEW VERSION + CHANGELOG")

func _on_CheckBox5_pressed():
	if $new2/VBoxContainer/CheckBox5.pressed == true:
		$new2/VBoxContainer/CheckBox5.set_self_modulate(Color(1,1,0.6,1))
#		if $new2/VBoxContainer/CheckBox4.pressed == true:
#			suppress_four = true
#			$new2/VBoxContainer/CheckBox4.pressed = false
#			_on_CheckBox4_pressed()
	else:
		$new2/VBoxContainer/CheckBox5.set_self_modulate(Color(0.8,0.8,0.5,0.5))
#		if suppress_four == true:
#			suppress_four = false
#			$new2/VBoxContainer/CheckBox4.pressed = true
#			_on_CheckBox4_pressed()
	can_make_changelog()

