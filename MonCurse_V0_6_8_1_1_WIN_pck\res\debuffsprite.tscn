[gd_scene load_steps=14 format=2]

[ext_resource path="res://frame_handler_rs.gd" type="Script" id=1]
[ext_resource path="res://MC/debuff/hypno particle small.png" type="Texture" id=4]
[ext_resource path="res://MC/debuff/impact particle.png" type="Texture" id=8]
[ext_resource path="res://MC/debuff/drowsy particle.png" type="Texture" id=11]

[sub_resource type="ParticlesMaterial" id=98]
flag_disable_z = true
gravity = Vector3( 0, 0, 0 )
initial_velocity = 100.0
angular_velocity = 100.0
orbit_velocity = 0.8
orbit_velocity_random = 0.0

[sub_resource type="Gradient" id=99]
colors = PoolColorArray( 1, 1, 1, 1, 0, 0, 0, 0 )

[sub_resource type="GradientTexture" id=100]
gradient = SubResource( 99 )

[sub_resource type="ParticlesMaterial" id=101]
flag_disable_z = true
gravity = Vector3( 100, 0, 0 )
angular_velocity = 100.0
orbit_velocity = 0.6
orbit_velocity_random = 0.0
color_ramp = SubResource( 100 )

[sub_resource type="Gradient" id=102]
offsets = PoolRealArray( 0, 0.197802, 1 )
colors = PoolColorArray( 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=103]
gradient = SubResource( 102 )

[sub_resource type="Curve" id=104]
_data = [ Vector2( 0, 0.398864 ), 0.0, 0.0, 0, 0, Vector2( 1, 1 ), 0.0, 0.0, 0, 0 ]

[sub_resource type="CurveTexture" id=105]
curve = SubResource( 104 )

[sub_resource type="ParticlesMaterial" id=106]
flag_disable_z = true
direction = Vector3( -1, -1, 0 )
spread = 10.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 15.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale_curve = SubResource( 105 )
color_ramp = SubResource( 103 )

[node name="debuffsprite" type="Sprite"]
z_index = 1
centered = false
script = ExtResource( 1 )

[node name="extra" type="Sprite" parent="."]
visible = false
centered = false
script = ExtResource( 1 )

[node name="hypno" type="Particles2D" parent="."]
position = Vector2( 0, -40 )
emitting = false
amount = 2
lifetime = 1.2
process_material = SubResource( 98 )
texture = ExtResource( 4 )

[node name="impact" type="Particles2D" parent="."]
position = Vector2( -5, -50 )
emitting = false
amount = 1
lifetime = 3.0
process_material = SubResource( 101 )
texture = ExtResource( 8 )

[node name="drowsy" type="Particles2D" parent="."]
position = Vector2( -15, -40 )
emitting = false
amount = 3
lifetime = 10.0
process_material = SubResource( 106 )
texture = ExtResource( 11 )

[node name="anim_timer" type="Timer" parent="."]
wait_time = 0.5

[connection signal="timeout" from="anim_timer" to="." method="_on_anim_timer_timeout"]
