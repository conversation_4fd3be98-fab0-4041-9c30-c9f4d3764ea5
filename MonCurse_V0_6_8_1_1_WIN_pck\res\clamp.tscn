[gd_scene load_steps=6 format=2]

[ext_resource path="res://assets/tiles/activelight.png" type="Texture" id=1]
[ext_resource path="res://assets/lamplightgodod.png" type="Texture" id=2]
[ext_resource path="res://assets/tiles/lightinactive.png" type="Texture" id=3]
[ext_resource path="res://clamp.gd" type="Script" id=4]

[sub_resource type="SpriteFrames" id=2]
animations = [ {
"frames": [ ExtResource( 1 ) ],
"loop": true,
"name": "active",
"speed": 5.0
}, {
"frames": [ ExtResource( 3 ) ],
"loop": true,
"name": "inactive",
"speed": 5.0
} ]

[node name="StaticBody2D" type="Node2D"]
position = Vector2( 64, 64 )
script = ExtResource( 4 )

[node name="Light2D" type="Light2D" parent="."]
visible = false
texture = ExtResource( 2 )
color = Color( 0.447059, 0.807843, 0.843137, 1 )
shadow_buffer_size = 4096
shadow_gradient_length = 20.0
shadow_filter_smooth = 10.0

[node name="Sprite" type="AnimatedSprite" parent="."]
frames = SubResource( 2 )
animation = "inactive"
