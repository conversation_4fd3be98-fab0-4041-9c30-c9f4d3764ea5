{"customization.json": {"000.CHOICE": ["Finish", "Change Colors", "Change Name"], "000.char": "Rules", "000": ["I can access customization features from here. What should I do?"], "001.char": "Narration", "001": [""], "002.char": "Narration", "002": [""]}, "endcatmission.json": {"000.char": "Voice", "000": ["You're back! How did it go?"], "001.char": "Voice... alt under conditions: Player has cat-tail, Player has cat-ears, Player has cat-paws, ", "001": ["There was a mercenary cell out there! I was going to come get you, but-"], "001.1.char": "Voice", "001.1": ["Ah. I was too late to save you from those perverse brats. You've started turning..."], "001.2.char": "Voice", "001.2": [0, ", you were a cat-girl all along? Are you hiding your tail?"], "001.3.char": "Voice", "001.3": ["You look ridiculous with those on. How did you even fight with toy paws like that?"], "002.char": "Voice... alt under conditions: Player has cat-tail, Player has cat-ears, Player has cat-paws, ", "002": ["You're surprisingly capable? However, next time, you can run away if things look dangerous, I won't judge!"], "002.1.char": "Rules", "002.1": ["<PERSON><PERSON>? I just wanna take it easy~"], "002.2.char": "Rules", "002.2": ["Nyo?! Having these weird twitchy ears is awkward, go get me a cure already!"], "002.3.char": "Rules", "002.3": ["I didn't want to! They're stuck!"], "003.char": "Voice... alt under conditions: Player has cat-tail, Player has cat-ears, Player has cat-paws, ", "003": ["Don't hesitate to tell me if you start to feel weird, okay? There are all manner of curses you can encounter out there."], "003.1.char": "Voice... alt under conditions: Player has cat-paws, ", "003.1": ["'<PERSON><PERSON>'? I need to get this out of you before you start inflicting your weird catty verbal tics on us!"], "003.1.1.char": "Voice", "003.1.1": ["'<PERSON><PERSON>'? You went and picked up a cursed item and threw yourself at the cat-girls? Jeez, I need to get this out of you and quick!"], "003.2.char": "Voice... alt under conditions: Player has cat-paws, ", "003.2": ["Fortunately, the affliction hasn't burrowed into your silly noggin yet."], "003.2.1.char": "Voice", "003.2.1": ["It's because of those gloves, isn't it? You should know better than to pick up cursed items!"], "003.3.char": "Voice", "003.3": ["Then leave suspicious items alone! How come you don't have any corruption on you? No fluffy ears?"], "004.char": "Voice... alt under conditions: Player has cat-paws, ", "004": ["Mmm. I'm going to have to do 'that' again, aren't I? Sheesh."], "004.1.char": "Voice", "004.1": ["First, let's get those paws off. You're not very useful if you can't use your hands."], "005.char": "Voice... alt under conditions: Player has cat-tail, Player has cat-ears, ", "005": ["The bindings on these wrap pretty tight so make sure you leave them alone! Anyway, I'll be taking my sweet time monitoring the comms line."], "005.1.char": "Voice", "005.1": ["When I first found you, you had taint in you that made your tits about this big."], "005.2.char": "Voice", "005.2": ["When I first found you, you had taint in you that made your tits about this big."], "006.char": "Voice", "006": ["I can resist this level of Neko-cat corruption in you, it's the bimbo juice you were souped up that's on a whole different level..."], "007.char": "Voice", "007": ["Now sit still, this shouldn't hurt."], "008.char": "Rules... alt under conditions: Player has cat-tail, ", "008": ["Nnmh?!"], "008.1.char": "Rules", "008.1": ["Nyaa?!"], "009.char": "Narration", "009": ["..."], "010.char": "Voice", "010": ["Bweh. Cat-girls. Are you feeling better now? If you need me again, I won't be leaving this area, I can't risk missing any messages on the comms line."], "100.char": "Voice", "100": ["If you had gained any afflictions, I'd have done my best to remove them. Though, there's nothing I can do if it's REALLY bad."], "101.char": "Voice", "101": ["Anyway, I'll be monitoring the call line for a while, I need to be here to intercept any messages."], "102.char": "Rules... alt under conditions: Player has cat-tail, ", "102": ["No more errands?"], "103.char": "Voice", "103": ["It was just the one small task, I didn't know it'd be that dangerous! Okay, tell you whaaaaat-"], "104.char": "Voice", "104": ["Here, a reward! 'Hollow Cross'. With this, enemies will leave behind healing essence when you've beaten them."], "105.char": "Voice", "105": ["That doesn't mean you can go on a rampage, even if you're free now. Stay safe while I handle the communications, OK?"], "1060.char": "Narration", "1060": ["Endless Mode has now unlocked, you can find it through the signpost near the Gallery."]}, "galleryconfirm.json": {"000.CHOICE": ["Return"], "000.char": "Narration... alt under conditions: Player has seen <PERSON>ing 0, ", "000": ["Records of the Cat Knights' bad ends. Right now, there's one you haven't found in 0-2."], "000.1.CHOICE": ["Return", "Cat-Girl Ending"], "000.1.char": "Narration", "000.1": ["Records of the Cat Knights' bad ends. It looks like you have all of them!"], "100.CHOICE": ["Return"], "100.char": "Narration... alt under conditions: Player has seen <PERSON> Ending 0, Player has seen <PERSON> Ending 1, ", "100": ["Records of the Ram Girls' bad ends. Right now, there's two you haven't found from losing to them."], "100.1.CHOICE": ["Return", "Ram-<PERSON> Ending"], "100.1.char": "Narration... alt under conditions: Player has seen <PERSON> Ending 1, ", "100.1": ["Records of the Ram Girls' bad ends. You only have one from one type of Ram-Girl."], "100.1.1.CHOICE": ["Return", "Ram-<PERSON> Ending", "<PERSON><PERSON><PERSON>"], "100.1.1.char": "Narration", "100.1.1": ["Records of the Ram Girls' bad ends. It looks like you have all of them!"], "100.2.CHOICE": ["Return", "<PERSON><PERSON><PERSON>"], "100.2.char": "Narration", "100.2": ["Records of the Ram Girls' bad ends. You only have one from the variant Ram-Girl."]}, "levelselect1confirm.json": {"000.CHOICE": ["No, return", "Tutorial 1 - Movement", "Tutorial 2 - Skills"], "000.char": "Voice", "000": ["These are the replay files for the 'tutorial' levels. Would you like to revisit them?"], "100.CHOICE": ["No, return", "Mission 0-1 - <PERSON><PERSON>", "Mission 0-2 - <PERSON><PERSON><PERSON> Galo<PERSON>"], "100.char": "Voice... alt under conditions: Playervariables.StagesCleared[0] < 3, ", "100": ["Here you can play the missions where we're setting up the comms relay. The cat-girls are a handful; hang in there kitty!"], "100.1.CHOICE": ["No, return", "Mission 0-1 - <PERSON><PERSON>"], "100.1.char": "Voice", "100.1": ["The first mission! We're missing a pink friend."], "200.CHOICE": ["No, return", "Mission 0-3 - A Long Walk", "Mission 0-4 - <PERSON>"], "200.char": "Rules... alt under conditions: Playervariables.StagesCleared[0] < 6, Playervariables.StagesCleared[0] > 6, ", "200": ["Voice has some explaining to do."], "200.1.CHOICE": ["No, return", "Mission 0-3 - A Long Walk"], "200.1.char": "Rules", "200.1": ["There should be a shrine not far from here. Let's check up on it."], "200.2.CHOICE": ["No, return", "Mission 0-3 - A Long Walk", "Mission 0-4 - <PERSON>"], "200.2.char": "Rules", "200.2": ["I can revisit the Ram-Girl mission or Voice here."], "500.char": "Narration", "500": ["Turning around, there stands a door, lock busted, open slightly ajar. ", 0, " knocks upon it with no response."], "501.char": "Rules... alt under conditions: Player has cat-tail, ", "501": ["I'm coming in."], "501.1.char": "Rules", "501.1": ["I'm coming in, nya."], "502.char": "Voice... alt under conditions: Player has ram-horns, Player has ram clothes top, Player has cat-tail, ", "502": [0, "?! Say something before you enter! We're wanted fugitives, you could've been a kidnapper."], "502.1.char": "Voice", "502.1": ["A Satyr?! Ram-- ", 0, "? That's you, isn't it?  You can't just burst in looking like that..."], "502.2.char": "Voice", "502.2": ["Those clothes-- Ram-girl?! No, ", 0, "? What's with the dress-up? We're wanted fugitives, you can't play pranks like this..."], "502.3.char": "Voice", "502.3": [0, "?! Don't burst in here pretending to be a cat-girl, I might've attacked you!"], "503.char": "Rules... alt under conditions: Player has cat-tail, ", "503": ["I said I was coming in! Your clothes..."], "503.1.char": "Rules", "503.1": ["Cat-girls are nice! You're nyext."], "504.char": "Voice... alt under conditions: Player has cat-tail, ", "504": ["This is your fault, too. My nightdress doesn't fit after dealing with you. So? ... You went out, didn't you?"], "504.1.char": "Voice", "504.1": ["You became a real cat-girl, didn't you? You damn stripping-obsessed bimbo! I've no nightwear that fit because of you! Why'd you come in?"], "505.char": "Voice... alt under conditions: Player has cat-ears, Player has ram clothes top, ", "505": ["... The locals don't like me? Were you talking to monster-girls?"], "505.1.char": "Voice", "505.1": ["... The locals don't like me? That's because you're listening to those cat-girls, idiot! Of course a monster-girl would say that!"], "505.2.char": "Voice", "505.2": ["... The locals don't like me? You haven't been brainwashed by those Rams have you? Of course the monster-girls would say that. I'm worried about you, ", 0, "."], "506.char": "Voice... alt under conditions: Player's bust size is > 2, ", "506": ["Can you really not tell <PERSON><PERSON><PERSON> apart from us? You can't tell who's right and who's wrong?"], "506.1.char": "Voice", "506.1": ["The reason for your chest's sudden growth is the monster-girls, isn't it? You can't tell what's safe and what's dangerous. What's right and who's wrong."], "507.char": "Voice... alt under conditions: Player's bust size is > 2, ", "507": ["As you've seen, those monsters are preying on us. They only want to turn us into horny beasts like themselves."], "507.1.char": "Voice", "507.1": ["We'll talk about your milk problem later. ", 0, ", I'll explain this clearly this time. Those monsters only want to turn us all into horny beasts like themselves."], "508.char": "Voice... alt under conditions: Player's bust size is > 3, ", "508": ["The purest bodies get used as breeding livestock, I've heard."], "508.1.char": "Voice", "508.1": ["Your chest is already growing back to the size you had when I first found you... No doubt, they want to use an impressionable girl like you as breeding stock."], "509.char": "Voice", "509": ["It wasn't always like this. The reason we had you set up the crystal is so we can find out why."], "510.char": "Voice", "510": ["Given she came here to fight me, we kiiiinda knew. The authority that's supposed to protect us is giving them the go-ahead."], "511.char": "Narration", "511": ["It's hard to concentrate with those huge tits on display..."], "512.char": "Voice", "512": ["Her highness has been taking orders and bowing down to a higher authority this entire time. Some bitch that cares nothing about us."], "513.char": "Rules", "513": ["You're sending me on another mission?"], "514.char": "Voice... alt under conditions: Player has seen <PERSON>ing 0, ", "514": ["No?! I mean- sorry. I'm sorry to have put you in all this danger for something you don't care about. I wanted you to know that you've really helped!"], "514.1.char": "Voice", "514.1": ["No?! I mean- sorry. I'm sorry, because of me those tiger brats already ruined your innocent purity. I wanted you to know that you've really helped!"], "515.char": "Voice", "515": ["We don't stand a chance against that authority. At the very least, we can work to get rid of the substance we were both poisoned with."], "516.char": "Voice", "516": ["There are still many Naman around the land, holding on. If they've saved anyone, they'd know how to reverse the effects."], "517.char": "Voice", "517": ["You'll need to leave me and find a surviving fort. They'll shelter you, too."], "518.char": "Rules", "518": ["Another mission... I'm tired, let me sleep."], "519.char": "Voice", "519": ["Huh? But- Why're you getting closer? Hold on, I'm not ready!"], "520.char": "Narration", "520": ["An immeasurable warmth has suffused the bed, <PERSON>'s body is anything but cold."], "521.char": "Narration", "521": ["No matter how ", 0, " shifts under the covers, marshmallowy softness engulfs."], "522.char": "Voice... alt under conditions: Player has ram-horns, Player has cat-tail, ", "522": ["Hey- Hey! Stop climbing over me!"], "522.1.char": "Voice", "522.1": ["Ghh- Your dumb horns are poking me, where'd you even get those!"], "522.2.char": "Voice", "522.2": ["Stop brushing your tail on my face, it tickles -- Do I really have to drain you again?!"], "523.char": "Rules", "523": ["Zzz..."], "524.char": "Voice", "524": ["Jeez, really. Were you too tired to row upstream?"], "525.char": "Voice", "525": ["There's no helping it. Good night, ", 0, "."], "300.CHOICE": ["No, return", "Mission 0-5 - Route A11", "Update Info"], "300.char": "Voice... alt under conditions: Playervariables.StagesCleared[0] > 7, ", "300": ["Starting missions are over! A pathway has opened up near the canoe, or you can access A11 through here."], "300.1.CHOICE": ["No, return", "Mission 0-5 - Route A11", "V0.6 Update Info"], "300.1.char": "Voice", "300.1": ["You can replay Route A11's starting fox conversation here."], "1300.char": "Voice", "1300": ["Wanna know what's up? The first chapter is complete, we're finally letting you out into the open world! The game is translatable too!! As a bonus..."], "1301.char": "Voice", "1301": ["Here's a secret for you. It's a hidden lever in the second tutorial level! If you lose your saves and have to start all over again, this lets you warp to me here."], "1302.char": "Voice", "1302": ["So. It's going to get harder. If you've seen the elite red ram you'll know you need Skills to handle harder monster-girls. Watch the difficulty tier marker above map tiles."], "1303.char": "Voice", "1303": ["We're still working on a new village, new areas. There'll be balancing and downsides to cursed items / corruptions in time."], "1304.char": "Voice... alt under conditions: Player's bust size is > 2, ", "1304": ["In fact, if your bust was larger, you might be feeling it already."], "1304.1.char": "Voice", "1304.1": ["Since your bust is that big, you might've already noticed an extra 'downside'."], "1305.char": "Voice... alt under conditions: Playervariables.removesex == true, ", "1305": ["If you like the game and want to support its development, check out the Patreon!"], "1306.char": "Voice", "1306": ["There's new experimental content and polls for new content on there. This wouldn't be possible without patrons' support, thank you!"], "1307.char": "Voice", "1307": ["That's all, I'll let you get back to the game now. Take it easy!"], "1305.1.char": "Voice", "1305.1": ["... This is where I shill the Patreon, isn't it? You're already on the Patreon version! Hmm..."], "1400.char": "Voice", "1400": ["Did you see the cat-girl and ram-girl on the game's page? There's art like that and future content that we're investing in!"], "1401.char": "Voice", "1401": ["Thank you for your faith in the game's development. We'll make sure it's improved and completed no matter what!"]}, "shikaroutea11talk.json": {"000.char": "FoxGirl", "000": ["Hey! Don't think I don't see you there!"], "001.char": "FoxGirl... alt under conditions: Player has fox-ears, ", "001": ["Don't ignore me! Explain yourself!"], "001.1.char": "FoxGirl", "001.1": ["Let's pretend for a moment I haven't already foxed you up so we can repeat this conversation... Explain yourself! <PERSON>!"], "002.char": "Rules... alt under conditions: Player has ram-horns, Player has cat-tail, Player has ram clothes top, Player has cat-ears, ", "002": ["You said to go to the land's center."], "002.1.char": "Rules", "002.1": ["Is it the horns again?"], "002.2.char": "Rules", "002.2": ["<PERSON><PERSON>? I'm not trying to be a cat-girl!"], "002.3.char": "Rules", "002.3": ["I'm not with the rams, really..."], "002.4.char": "Rules", "002.4": ["The ears? I'm stuck with them."], "003.char": "FoxGirl... alt under conditions: Player has ram-horns, Player has cat-tail, Player has ram clothes top, Player has cat-ears, ", "003": ["Not that, asshole! You've been plotting with someone, haven't you?"], "003.1.char": "FoxGirl", "003.1": ["You helped me so I don't care about that! I'm talking about your other allegiance!"], "003.2.char": "FoxGirl", "003.2": ["Did the tiger juice make you dumb? I'm talking about your other allegiance!"], "003.3.char": "FoxGirl", "003.3": ["You helped me so I don't care about that! I'm talking about your other allegiance!"], "003.4.char": "FoxGirl", "003.4": ["Not that-- you have no cat-tail? No, asshole, you've been plotting with someone, haven't you?!"], "004.char": "Rules... alt under conditions: Player has ram-horns, Player has cat-tail, Player has ram clothes top, ", "004": ["Voice? I'm going to look for humans."], "004.1.char": "Rules", "004.1": ["I'm keeping <PERSON> safe."], "004.2.char": "Rules", "004.2": ["<PERSON><PERSON><PERSON><PERSON>, I don't get it."], "004.3.char": "Rules", "004.3": ["Voice? We're doing fine."], "005.char": "FoxGirl... alt under conditions: Player has ram-horns, Player has cat-tail, Player has ram clothes top, ", "005": ["Wh- you've got guts waltzing out after talking to her! What lies have you been fed?"], "005.1.char": "FoxGirl", "005.1": ["WHY?! I don't even know what you're supposed to be at this point!"], "005.2.char": "FoxGirl", "005.2": ["That vampire. You're colluding. Did she turn you into her dumb kitty-pet?"], "005.3.char": "FoxGirl", "005.3": ["Even without horns, you're a real fluff-head! Don't you get why that's bad?"], "006.char": "Rules... alt under conditions: Player has ram-horns, Player has cat-tail, Player has ram clothes top, ", "006": ["You're a monster too, you know."], "006.1.char": "FoxGirl", "006.1": ["Nothing good comes of this land if you kiss up to her, cloudy-brains."], "006.2.char": "Rules", "006.2": ["Actually, I-"], "006.3.char": "Rules", "006.3": ["Relax."], "007.char": "FoxGirl... alt under conditions: Player has ram-horns, Player has cat-tail, Player has ram clothes top, ", "007": ["Still, it's a waste to let you become another's breedstock. The descendants of the great nine-tails will free you from that vampire!"], "007.1.char": "FoxGirl", "007.1": ["You shouldn't be her property or the satyrs'. The descendants of the great nine-tails will free you!"], "007.2.char": "FoxGirl", "007.2": ["I'll have to train you myself! The descendants of the great nine-tails will free you from that vampire!"], "007.3.char": "FoxGirl", "007.3": ["It's a waste to let the rams have you. The descendants of the great nine-tails will free you! From the vampire, too!"], "008.char": "FoxGirl", "008": ["We'll make you defect and become a loyal kitsune follower. You'll do as I say..."], "009.char": "Rules... alt under conditions: Player has ram-horns, Player has cat-tail, Player has ram clothes top, ", "009": ["Not going to deal with <PERSON> yourself?"], "009.1.char": "Rules", "009.1": ["I came out here to look for <PERSON><PERSON>. Deal with <PERSON> yourself."], "009.2.char": "Rules", "009.2": ["Headache. Talk to <PERSON> nyaself."], "009.3.char": "Rules", "009.3": ["Let's talk to Voice together...?"], "010.char": "FoxGirl", "010": ["Idiot! Don't you know how stupid an idea that is?! We don't leave our sacred shrines unguarded either!"], "011.char": "Rules... alt under conditions: Player has ram-horns, Player has cat-tail, Player has ram clothes top, ", "011": ["Your loss. I'll just walk around."], "011.1.char": "Narration", "011.1": ["The <PERSON>'s corruption seems to be urging ", 0, " to charge."], "011.2.char": "Narration", "011.2": [0, " can't tell if the fox is just dumb and confusing or if it's the corruption's fault."], "011.3.char": "Narration", "011.3": ["The <PERSON>'s garments seem to be keeping ", 0, " calm."], "012.char": "FoxGirl... alt under conditions: Player has ram-horns, Player has cat-tail, Player has ram clothes top, ", "012": ["Wait- ghhh, just try, bitch, my ghost-foxes won't let you."], "012.1.char": "FoxGirl", "012.1": ["Aaa...? I'll let the ghost-foxes deal with you, you won't hurt them."], "012.2.char": "FoxGirl", "012.2": ["Just no claws and no climbing on the roof, got it?"], "012.3.char": "FoxGirl", "012.3": ["You'll need to do more than just hypnotize me once I've got the ghost-foxes out."]}, "shikashrinetalk.json": {"000.char": "FoxGirl... alt under conditions: Playervariables.queststart == false, hadconversation == true, ", "000": ["It's finally over, I couldn't get any sleep at all. ... <PERSON><PERSON>gh, fine, I'll deal with the guest. Call me <PERSON><PERSON>."], "000.1.char": "FoxGirl", "000.1": ["Since we've only just met, call me <PERSON><PERSON>. ... So, if you cleared them out. Are you dangerous?"], "000.2.CHOICE": ["Leave", "Repeat Conversation"], "000.2.char": "FoxGirl", "000.2": ["Better head back home if you're not gonna suck on my ego. You're easy to mistake for a <PERSON><PERSON>, too."], "001.char": "FoxGirl... alt under conditions: Player has fox-ears, Player has ram-horns, Player has cat-ears, ", "001": ["You a traveller? It's some immense luck you're not walking through high on catnip and purring, tiger-girls are everywhere."], "001.1.char": "FoxGirl... alt under conditions: Player has ram-horns, ", "001.1": ["What's a fox like you doing wandering, anyway? It's not safe."], "001.1.1.char": "FoxGirl", "001.1.1": ["You should know better than to wander outside your shrine. You're lucky you still have your ears after dealing with the Rams!"], "001.2.char": "FoxGirl... alt under conditions: Player has cat-ears, ", "001.2": ["You're still a Ram-girl yourself. Don't think I'll let my guard down."], "001.2.1.char": "FoxGirl... alt under conditions: Player has cat-ears, ", "001.2.1": ["You're still some sorta catty-ram-girl yourself. Don't think I'll let my guard down. You could do with some fox in there!"], "001.3.char": "FoxGirl... alt under conditions: Player has cat-tail, ", "001.3": ["Aren't you cat-girls... Hey. You don't have a tail? So you're a Naman after all."], "001.3.1.char": "FoxGirl", "001.3.1": ["Aren't you cat-girls always hanging out with the Rams? You're making me nervous."], "002.char": "Rules... alt under conditions: Player has cat-tail, ", "002": ["I made this shrine, don't want other monsters ruining it."], "002.1.char": "Rules", "002.1": ["Nyaaa. I made this place! I can't remember how, my head's all fuzzy..."], "003.char": "FoxGirl", "003": ["What."], "004.char": "FoxGirl", "004": ["Are you an idiot? Lemme educate you, cloudy-brain. These fox-shrines have stood since the dawn of time. You couldn't have made it."], "005.char": "FoxGirl", "005": ["Then, the maidens who guard the shrines were gifted the great nine-tails' power, as were their disciples and descendants!"], "006.char": "Rules... alt under conditions: Player has cat-tail, ", "006": ["...The human shrine maidens were tricked and corrupted into sweaty perverts?"], "006.1.char": "Rules", "006.1": ["The shrine maidens got corrupted by the fox-demon into sweaty perverts, nya..."], "007.char": "FoxGirl... alt under conditions: Player has cat-tail, ", "007": ["Why else would girls would hang around a shrine all day if not to get this power?"], "007.1.char": "FoxGirl", "007.1": ["Why're you acting so smug when you're just a dumb cat?!"], "008.char": "FoxGirl", "008": [0, ", was it? You're a confused spirit. You came here for answers, I'll tell you."], "009.char": "FoxGirl", "009": ["Of Werewolf Clan, of Vampire Clan. We live above but they live below, like cowards, hiding with their sovereignty. They deserve no business up here."], "010.char": "FoxGirl", "010": ["So then, their Queen fucked up and her pet got loose. Fat lot of good their academies and 'high society' is doing for them!"], "011.char": "FoxGirl", "011": ["That Queen is evacuating the locals but she hired those damn Tigers and Rams to do it."], "012.char": "FoxGirl", "012": ["How am I supposed to evacuate? Worse yet, those mercs are using it as an excuse to abduct new members!"], "013.char": "FoxGirl", "013": ["...You want to know how to get down below?"], "014.char": "FoxGirl", "014": ["If you can make it to the center of the island - to the tar swamp around the tower - the Werewolf's Matriarch can show you the way."], "015.char": "FoxGirl", "015": ["Here's an extra for helping out. It's a token that'll let you into the hot-springs, you won't see the path without it."], "016.char": "FoxGirl... alt under conditions: Player's bust size is > 2, ", "016": ["It's the best spot for clearing away exhaustion, corruption and sweat!"], "016.1.char": "FoxGirl", "016.1": ["It's the best spot for clearing away exhaustion and corruption. Perfect for a bimbo whose tits keep growing 'til it rips her clothes!"], "017.char": "FoxGirl... alt under conditions: Player has cat-ears, 5horn, ", "017": ["If you become completely corrupt, you'll still fall to the enemy's side. Don't start taking it too easy."], "017.1.char": "FoxGirl... alt under conditions: Player has cat-tail, ", "017.1": ["In fact, it might help remove that cattiness you've got all over you. Take a dip so we can fit some fox-ears on you instead!"], "017.1.1.char": "FoxGirl", "017.1.1": ["You might be too far into the tigers' clutches to get that cat off of you at the springs. You have to get rid of the corruption before it's too late."], "017.2.char": "FoxGirl", "017.2": ["Don't get me wrong, I still don't fully trust you yet. That better keep the sheep off you."], "018.char": "FoxGirl", "018": ["That's about it. Don't be a stranger, 'kay?"], "2009.char": "FoxGirl", "2009": ["It's the Curse of Gaia. When a youth comes of age, they start becoming unruly, picking fights and challenging the rules."], "2010.char": "FoxGirl", "2010": ["We don't like law but it's necessary. If there are too many born in the same generation, they'll band together and wage war due to the curse."], "2011.char": "FoxGirl", "2011": ["Breeding has to be limited but we can convert and corrupt as we please."], "2012.char": "FoxGirl", "2012": ["Then -she- came, a youth aged 30 at the peak of rebelliousness. They were supposed to keep her under control and instead they raised a brat too strong to be contained!"], "2013.char": "FoxGirl", "2013": ["Unless you wanna stay with me, ", 0, ", you really shouldn't hang around here. That bastard child might just get us all killed."], "2014.char": "FoxGirl", "2014": ["Aah. It's all that <PERSON>'s fault. Their stupidity has us in the firing line while they hide away safe below. It's not fair..."], "2015.char": "Rules... alt under conditions: Player has cat-tail, ", "2015": ["...Breeding is limited? They still let humans breed, don't they?"], "2015.1.char": "Rules", "2015.1": ["Nn? So, so, humans with or without a bit of cat in them get to breed? I don't care what this 'Queen' says."], "2016.char": "FoxGirl", "2016": ["Anyone would breed the heck out of a human if they found one, they wouldn't be human for long. That's why it's only <PERSON><PERSON> here."], "2017.char": "Rules... alt under conditions: Player has cat-tail, ", "2017": ["Got it. We save the land and then we breed you and get some proper shrine maidens again. Nice."], "2018.char": "Rules", "2018": ["<PERSON><PERSON>! We save the land then we get to breed? I'll do it!"], "2019.char": "FoxGirl", "2019": ["Aaaaaa?! Quit it with the weird jokes, that doesn't even make sense?! You're looking after some others in the mountains, aren't you? Go make sure they're safe!"], "2020.char": "Rules", "2020": ["Bye."], "1008.char": "Rules", "1008": ["I was hoping you'd tell me what's going on. A war? Evacuating?"], "1009.char": "FoxGirl", "1009": ["Aaaaa, that. It's not a war, it's a scuffle between royalty and leaders. The Cat-Ram-girls are mercenaries sent to clear people out of the crossfire..."], "1010.char": "FoxGirl", "1010": ["Those damn 'mercenaries' are using it as an excuse to draft people into their ranks! Just what you'd expect since they take payment from the underside."], "1011.char": "Rules... alt under conditions: Player has cat-tail, ", "1011": ["... Right? I have no idea who 'royalty' and 'underside' are. Did all the monsters under the island turn into girls, too?"], "1012.char": "Rules", "1012": ["That's a lot of big wooooooords. <PERSON><PERSON> doesn't get it."], "1013.char": "FoxGirl", "1013": ["Siiigh. Us here on the top come under the 'Werewolf' clan. Those down below come from the 'Vampire' clan. Got it? The 'Vampire' clan has a 'Queen'. Got it?"], "1014.char": "FoxGirl", "1014": ["Of all the beasts of myth, the Werewolves and Vampires both passed down the ability to 'corrupt' so that we can have more of our kind and family, without the need to make new life."], "1015.char": "Rules... alt under conditions: Player has cat-tail, ", "1015": ["No breeding? That's fucked up. I'd help breed you if we need some shrine maidens who aren't perverts."], "1015.1.char": "Rules", "1015.1": ["But I wanna breed?!"], "1016.char": "FoxGirl", "1016": ["Y- You know that's illegal!! Sure, it's that bitch underside Queen's law to limit breeding but even us on the right side here know why that's dangerous!"], "1017.char": "Rules", "1017": ["Don't care. I'm human, I'll breed what I like."], "1018.char": "FoxGirl", "1018": ["...? If you were human, they'd corrupt you and use you to mother a whole new family of monster-girls, I bet."], "1019.char": "FoxGirl", "1019": [""]}, "tutorialabilitysecret.json": {"000.CHOICE": ["No, return", "Skip to Cat-Girl Mission", "Skip to Ram-Girl Mission", "Skip to <PERSON>-Girl Mission"], "000.char": "Rules", "000": ["There's a lever behind these vines? I can skip a few levels with this."], "100.char": "Rules", "100": ["I'll ask <PERSON><PERSON> if I need to change content preferences."], "200.char": "Rules", "200": ["I'll ask <PERSON><PERSON> if I need to change content preferences."], "300.char": "Rules", "300": ["I'll ask <PERSON><PERSON> if I need to change content preferences."]}, "tutorialmission1s1voicetake2.json": {"000.CHOICE": ["Why are we here?"], "000.char": "Voice... alt under conditions: hadconversation == true, ", "000": ["You came, ", 0, "! Good. There's one last thing I need."], "000.1.CHOICE": ["Why are we here?", "There are enemies out here.", "Never mind."], "000.1.char": "Voice", "000.1": ["Need me to go over it again?"], "001.char": "Voice", "001": ["If we're going to get you back on your feet, it'll be good to get to know the few people who don't wanna fuck you to kingdom come."], "002.char": "Voice", "002": ["But! Most of the crew are waaaaay far away, so we're trying to set up a comms branch up this way. Or, we were until you showed up."], "003.char": "Voice", "003": ["So, uhhh, you'll have to finish it for me! Go meet up with my friend up that-a-way and make sure you grab a shard."], "004.char": "Voice... alt under conditions: hadconversation == true, ", "004": ["You know, these floating things. It shouldn't be hard to find, which is why I never stocked up on them. Then, get up high to look for my friend. She's pink and fluffy, you can't miss her!"], "004.1.char": "Voice", "004.1": ["You know, these floating things. There are only A FEW cat-girls, you can deal with it."], "100.char": "Voice", "100": ["Really? I didn't expect that - give it a go anyway? Most girls will run off and get out of your way the moment you even show them that you can hurt them. It'll be fine!"], "101.char": "Voice", "101": ["Eyes on the prize,", 0, "! Also, tell <PERSON><PERSON> that she's allowed to actually, you know, stop by? For real?"]}, "tutorialmission1s2qades.json": {"000.char": "Qades... alt under conditions: Playervariables.playershards < 1, hadconversation == true, ", "000": ["Heeeeyo. You made it here without fainting. ", 0, ", isn't it? I hear <PERSON> wanted to put you through even more tutorials but we don't have time for that."], "000.1.char": "Qades... alt under conditions: hadconversation == true, ", "000.1": ["Did you just... Jump right over the shard?? The easiest resource to pick up and you miss it?? They're super obvious, you pick it up just by moving through it. Try again."], "000.1.1.char": "<PERSON><PERSON>", "000.1.1": ["Come on, what did you think was gonna happen, I'd keep talking like you actually picked up the big glowing thing and we'd continue without it? Pick up the damn shard."], "000.2.char": "<PERSON><PERSON>", "000.2": ["Sorry about that. Get a lot of people treating me like I'm dumb."], "001.char": "Qades... alt under conditions: hadconversation == true, ", "001": ["I'm <PERSON><PERSON>. Titles aren't important, my relationship here is purely confidential. Got it? I'm on paid vacation."], "001.1.char": "<PERSON><PERSON>", "001.1": ["Just because 'small and pink!' or 'cupids force their gay ships to become real!' or dumbs like that. Oh, I'm Q<PERSON> by the way."], "100.CHOICE": ["What??", "Sex is good."], "100.char": "<PERSON><PERSON>", "100": ["Whatevs, you made it this far without quitting. Hey, how do you feel about sex?"], "110.char": "<PERSON><PERSON>", "110": ["Okayokayokay-- Just about everything you might fight out there is going to be a monster-girl, right? Monster-girls are damn horny, especially for someone plain-looking like you."], "111.CHOICE": ["Open Menu", "More Info"], "111.char": "<PERSON><PERSON>", "111": ["See, I'm a Cupid. I'm offering my help if you're, like, not really into the whole 'monster-girl fucks your brains out' thing. Cupids are good at that."], "120.char": "<PERSON><PERSON>", "120": ["Good for you! I just get the feeling that you're going to be fighting off sex-crazed monster-girls left and right out there, so you're going to have to get used to it. They're stupid horny."], "121.CHOICE": ["Open Menu", "More Info"], "121.char": "<PERSON><PERSON>", "121": ["That makes my job easy. I was going to say, if you don't want all those perverts out there using you like some sort of sex doll, I'm a Cupid, I can do something about that."], "150.char": "<PERSON><PERSON>", "150": ["Changing these options isn't gonna help you. The difficulty should be the same. Instead of futa enemies, you might get female enemies, that sort of thing."], "151.char": "<PERSON><PERSON>", "151": ["You can disable NSFW and nobody's gonna fuck you. That's it. No protection from death or the likes, right?"], "200.CHOICE": ["Open Menu"], "200.char": "<PERSON><PERSON>", "200": ["So, I'm gonna need a decision if you want sex-stuff on or not. Also, even with sex disabled, we're still in adult-only territory."], "210.char": "<PERSON><PERSON>", "210": [""], "211.char": "Qades... alt under conditions: Playervariables.consent == false, ", "211": ["O-kay. Sexual stuff is on. There was some weird old law that needed me to ask about it, that's all. You can change the preferences if you find me in the village later."], "211.1.char": "<PERSON><PERSON>", "211.1": ["Sexual stuff is off, then. Your virginity is safe with me. I'll be in the village if you change your mind."], "240.CHOICE": ["Yes, continue.", "No, take me back."], "240.char": "<PERSON><PERSON>", "240": ["You... Don't want it on? It won't give you any benefits or bonuses, you know? Let me just confirm, you'll be playing with sex and some lewd stuff disabled, correct?"], "241.char": "<PERSON><PERSON>", "241": ["I'm sure you have your reasons, but you can always go back if you change your mind, or find me in the village later. Okay! Anyway."], "250.CHOICE": ["What Monster-Girls?", "Where are my weapons?", "What if I lose?", "Get on with it."], "250.char": "<PERSON><PERSON>", "250": ["There's a whole lot more info that Voice has yet to cover. This'll be your last chance to ask questions before you head in to the fight."], "300.char": "<PERSON><PERSON>", "300": ["There's a couple hanging around here. Don't be fooled if they came off as friendly to you, the girls are still monsters."], "301.char": "<PERSON><PERSON>", "301": ["If they hit you with an attack, you'll lose HP equal to the damage. The kitties have simple 1 damage attacks whereas some girls have 0 damage debuff attacks."], "302.char": "<PERSON><PERSON>", "302": ["Got a cat on you? You can push enemies off of your own tile. Aside from their readied 'pounce', the cat-girls only hurt you if you end your turn close to them."], "303.char": "<PERSON><PERSON>", "303": ["As for their corruption, just don't pick up anything weird. Pay attention!"], "400.char": "<PERSON><PERSON>", "400": ["Those? All the weapons lying around are ancient, only really <PERSON><PERSON> use them if their monster-abilities aren't good enough. They probably broke."], "401.char": "<PERSON><PERSON>", "401": ["Weapons are permanent within one mission or run, but you can only use them once per map. They're drawn automatically, the inventory is just for display purposes."], "402.char": "<PERSON><PERSON>", "402": ["You'll start seeing some permanent items once you hit up the world map. Could be fun."], "500.char": "<PERSON><PERSON>", "500": ["If you hit 0, you don't win any prizes on the next stage, sometimes you need special conditions to get an ending. Future monster-girls might bad-end you anyway."], "501.char": "<PERSON><PERSON>", "501": ["The cat-girls flee after only a whack or two, but they're not fighting you to the death either. Almost every monster-girl out there is gonna want your body."], "502.char": "<PERSON><PERSON>", "502": ["You get it, don't you? They're going to make you into one of them. You'll be purring and rolling around, you may change allegiance. Get used as a breeder."], "503.char": "<PERSON><PERSON>", "503": ["Hmmm, I heard humans mature really fast too, an endless supply of corruption bait. No wonder monsters really loved having humans around, Namans are only second-best."], "600.CHOICE": ["Continue", "What Monster-Girls?", "Where are my weapons?", "What if I lose?"], "600.char": "<PERSON><PERSON>", "600": ["Anything else before you head out?"], "700.char": "Rules", "700": ["That's nice and all, but can you at least undo this girlifying curse?"], "701.char": "<PERSON><PERSON>", "701": ["What. I don't get it? You're supposed to be more of a tomboy?"], "702.char": "Rules", "702": ["Why is everyone forgetting I'm supposed to be a guy?! Isn't there magic to undo that?"], "703.char": "<PERSON><PERSON>", "703": ["Hey now. <PERSON><PERSON><PERSON> didn't tell me. Look, as long as you don't turn into a monster-girl it's probably reversible."], "704.char": "<PERSON><PERSON>", "704": ["Fall too far and you might lose your original objective, become indoctrinated. You'll have to piss like the rest of us for the rest of your life."], "705.CHOICE": ["Fuck you", "I'm going to fuck you", "Whatever"], "705.char": "<PERSON><PERSON>", "705": ["... You have at least looked at your body, right? That's, like, step number one of suddenly turning into a girl. Riiiiight?"], "800.char": "Rules", "800": ["Fuck you, I've been listening to you girls belittle me all day. Just show me to the fight."], "801.char": "<PERSON><PERSON>", "801": ["Fine, fiiiine! Voice will be worried we're fucking out the back if we're down here too long anyway. Remember, you don't need to route all the enemies, focus on the objective!"], "900.char": "Rules", "900": ["When I get my dick back, I'm going to pin you to the floor and fuck you, '<PERSON><PERSON>'. Double that if you're not the one to return it to me."], "901.char": "Qades... alt under conditions: Playervariables.consent == false, ", "901": ["Is that a promise? Come on, we know this is the kind of scenario where you just run off and lose to the first enemy y-"], "901.1.char": "<PERSON><PERSON>", "901.1": [0, "... You do remember you asked me to turn the sexual content off, right? ... Look. Anyway."], "902.char": "Rules", "902": ["Did I stutter?"], "903.char": "<PERSON><PERSON>", "903": ["No sir, yes sir, let's get you to the mission."], "1000.char": "<PERSON><PERSON>", "1000": ["Remember, you're not trying to beat up everyone you see, you need to get this crystal in to give vampy her peace of mind. It'll be at the top of the first building you see. Here we go."]}, "villageqades.json": {"000.CHOICE": ["Leave", "What's this?", "OPEN MENU"], "000.char": "Qades... alt under conditions: Playervariables.consent == false, ", "000": ["Hey-hey. Here to change content preferences?"], "000.1.CHOICE": ["Leave", "What's this?", "OPEN MENU"], "000.1.char": "<PERSON><PERSON>", "000.1": ["NSFW content is off, open the menu if you wanna change that. Be aware that you may still see naughty bits leaking through."], "001.char": "Narration", "001": [""], "002.char": "<PERSON><PERSON>", "002": ["Let's see here... If you toggle off all the sexual content, it'll blank out any lewd scenes. Can't guarantee it'll always work. Now, as for the individual options:"], "003.char": "<PERSON><PERSON>", "003": ["The 'Futa - Enemy' toggle only affects one or two enemies right now. It'll ensure they stay female. May not apply to you, hence the 'Enemy' part."], "004.char": "<PERSON><PERSON>", "004": ["Extreme Breast Expansion prevents your bust size from going beyond the largest size, if that makes any sense to you. May limit more in the future."], "005.char": "<PERSON><PERSON>", "005": ["That's all for now. If you don't like a particular girl you might be able to avoid them."]}, "voiceintroconversation.json": {"000.char": "Rules", "000": ["('Monsters'. 'Humans'. The distinction between the two is so obvious and yet, if you were to draw a line between the two, where would it be?)"], "001.char": "Rules", "001": ["(The truth is, that line doesn't exist. A monster can become a human can become a monster.)"], "002.char": "Rules", "002": ["(In driving the monsters of yore to extinction, they adapted to our world to survive. They crossed the same line that we did.)"], "003.char": "Rules", "003": ["(In that rainy season, as they invaded, I couldn't protect the thing most important to me. That was the day I died.)"], "004.char": "Rules", "004": ["...Now I'm being disturbed by this awful dissonance."], "005.char": "Narration", "005": ["A clang like a thrown bell, an earth-shattering explosion, the distant thunder of conflict. <PERSON><PERSON><PERSON> scatters like buckshot."], "006.char": "Narration", "006": ["But, as soon as it starts, the combat abruptly ends, a cool breeze surges past. A controlled force unlike any human or beast's."], "007.char": "Narration", "007": ["Nothing left but the shrill, ringing aftermath. Peace at last...?"], "008.char": "Voice", "008": ["-- Wake up ... you jerk! <PERSON><PERSON><PERSON>!"], "009.CHOICE": ["Try to rest.", "Who's a bimbo?!"], "009.char": "Narration", "009": ["Whatever happened to letting the dead rest? Annoying."], "110.char": "Narration", "110": ["Something soft - and HEAVY - rams me."], "111.char": "Voice", "111": ["Stop lying around, asshole, I already fixed you up! I'm not carrying you!"], "112.char": "Narration", "112": ["I recoil into the grass, my vision blurs. There she stands, under dappled sunlight."], "150.char": "Rules", "150": ["The heck? I'm not going to be called 'bimbo' by some girl."], "151.char": "Voice", "151": ["<PERSON>gu<PERSON>-?! You bitch, this is your fault in the first place!"], "300.char": "Narration", "300": ["A monster...?"], "301.char": "Narration", "301": ["'Monmusu' is what we called them - the daughters of monster-kind."], "302.char": "Narration", "302": ["This lady before me sparks my hunter's instincts like a stake through my core."], "303.char": "Voice", "303": ["Oh, yeah! Since you're a new face and all, I'm <PERSON>!"], "304.CHOICE": ["Who's 'girly'?!", "Saved from what?", "I'm not fooled, monster."], "304.char": "Voice", "304": ["What's with the hostile eyes, girly? I just saved your life, idiot, are you brainwashed?"], "420.char": "Rules", "420": ["Are you blindIam aa- n- hey-"], "440.char": "Rules", "440": ["There's nothing save m- mmme- Stop th- Aaah...!"], "460.char": "Rules", "460": ["You can't fool m mmonst- I- Can you -?!"], "700.char": "Narration", "700": ["There it is again, a girl's voice parroting my words. Did you know that it's incredibly hard to speak when your words are played back to you?"], "701.char": "Rules", "701": ["No, wait. Aaa. Aa. aaaaAAAaa?"], "702.char": "Narration", "702": ["It's just the two of us here. Me and this 'me', isn't me. As if-- and who put my hair up in hairbands?!"], "703.char": "Narration", "703": ["Don't tell me I've reincarnated into a girl?! Then why am I- ... But I'm still me, right?"], "704.char": "Narration", "704": ["A mirror... Even a puddle - give me anything!"], "705.char": "Narration", "705": [""], "706.char": "Voice", "706": ["Reincarnated? What's with the freak-out? You kinda just fell out in the middle of the fight earlier, really shook things up, we were both shocked!"], "707.char": "Voice", "707": ["As for your hair, it was a mess so I brushed it out and tied it up like that to keep it neat. After setting you to the side, of course. I'm just too nice."], "708.CHOICE": ["I've had enough!", "Fine, whatever."], "708.char": "Voice", "708": ["Since you got dropped like loot, that means you're my property now! You've got a pure and neat body, you're a nice catch."], "750.char": "Voice", "750": ["And what, leave you as meat for the monster-girls to get their hands all over?! It's not safe up here! Plus, don't you owe me?"], "751.char": "Rules", "751": ["Then what did you do to my body?! I wake up to this racket and then you start harping on about how I should trust a stranger!"], "752.char": "Voice", "752": ["I did no such thing! You just kind of fell out, it's my first time seeing it! I'm not with them, I saved you from them! Really!"], "753.char": "Rules", "753": ["I'm too tired for this. I'll get up; I won't roll over for you."], "754.char": "Voice", "754": ["Yes! We can't sit around here after all that, just about everybody heard our fight."], "800.CHOICE": ["Begin Training #1"], "800.char": "Voice... alt under conditions: Playervariables.tutorialscleared > 0, ", "800": ["So! Before we continue further, let me check how your body is doing. We can use what remains from the fight as an obstacle course."], "800.1.CHOICE": ["I don't need to do this", "Begin Training #1"], "800.1.char": "Voice", "800.1": ["Before we continue further, let me check how your body is doing. We can use what remains from the fight as an obstacle course. You already seem to have some experience...?"], "810.CHOICE": ["Begin Training #1"], "810.char": "Voice... alt under conditions: Playervariables.tutorialscleared > 0, ", "810": ["If you'd stop scowling at me, there's a dug out area here you can use to get your bearings."], "810.1.CHOICE": ["I don't need to do this", "Begin Training #1"], "810.1.char": "Voice", "810.1": ["If you'd stop scowling at me, we can skip the first area if you'd like, it seems you know how to move already."], "900.char": "Voice", "900": ["So, you can at least move. Next I want your name or else I'll just call you 'pet' or 'petty'. Then I'd like to know just what sort of monster you're supposed to look like."], "900.1.char": "Voice", "900.1": ["If you insist you can move. You'll have to remind me what your name was, then I'd like to know just what sort of monster you're supposed to look like."], "901.char": "Rules", "901": ["I'm not supposed to look like a monster? Look. Just take me to the nearest human settlement, I'll be fine."], "902.char": "Voice", "902": ["<PERSON><PERSON><PERSON><PERSON>. 'Human settlement'. What, a museum? You've been digging here for old relics? Let me guess, you're a ghost? Zombie- no, a genie? You did just pop up out of nowhere, after all."], "903.char": "Rules", "903": ["H u m a n. These clothes, this stick, h u m a n s made them. Or do monsters not understand what it means to be human?"], "904.char": "Voice", "904": ["Calling me a monster is just tasteless! I'm not even one of the more monstrous <PERSON><PERSON>. See? N a m a n. Very different."], "905.char": "Voice", "905": ["Remember, before I drained you, your dumb sow tits were just as big as these things are now! I fixed your messed up body. Be more thankful!"], "906.char": "Voice", "906": ["Now. Your name. Did you forget that too? It's what people call you. Five letters long and a lazy conversation starter."], "907.char": "Rules", "907": ["Five letters? What if it isn't?"], "908.char": "Voice", "908": ["Then it's not a name, duh. Unless you're an object - you didn't taste like one. So? Name?"], "909.char": "Narration", "909": [""], "910.char": "Voice... alt under conditions: Playervariables.debugmodeon, WNT0, WNT1, WNT2, WNT3, WNT4, WNT5, WNT6, WNT7, ", "910": [0, ", is it? A pleasure to meet you, and a good day for being possibly-alive. Ah, wait, I didn't give my own name!"], "910.1.char": "Voice", "910.1": [0, "? That name... Sounds familiar. Naming yourself that may have consequences. Are you sure? Really sure? You can still right click and rename yourself before the world screws itself up."], "910.2.char": "Voice", "910.2": ["That- Did a mirror just break? Hey, don't name yourself weird glitchy nonsense! How the hell did you even do that?! Ughhh. Let's continue before this breaks further!"], "910.3.char": "Voice... alt under conditions: WNT8, ", "910.3": [0, "? Is that so? Funny that, I know someone called the very same thing. If you wanna be called that too then there's room for everyone!"], "910.3.1.char": "Voice... alt under conditions: WNT8, ", "910.3.1": [0, ", is it? A pleasure to meet you, and a... good day? Ah. My name?"], "910.4.char": "Voice", "910.4": ["'", 0, "'? Pfff. Really? You call yourself that? Hey, I'm not laughing! You CAN go back and change it, but if you're REALLY sure."], "910.5.char": "Voice", "910.5": ["'", 0, "'... That's more of a weird expression than a name, isn't it? Just spelling it gives me pins and needles in my brain. Write it down for me later?"], "910.6.char": "Voice", "910.6": ["Hey, you don't have to yell. '", 0, "'. I'll try saying it like that, if you don't mind."], "910.7.char": "Voice", "910.7": [0, "? Am I pronouncing this right? <PERSON><PERSON>, people really like the easy to remember names nowadays. Fine, you'd better not get tired of hearing '", 1, "'"], "910.8.char": "Voice", "910.8": [0, "? ", 0, ". Must be a foreign name? Perhaps an old-timey name? Hmmh."], "910.9.char": "Voice", "910.9": ["Great name! We'll get along juuuust fine, I'll forgive you for everything up until now with a cute name like that!"], "911.char": "Voice... alt under conditions: WNT7, ", "911": ["Allow me to introduce myself properly. I'm Voice, vampire-half. Vampires, being the more noble and civilized faction. If you're up here and not <PERSON><PERSON>, you're with the werewolf faction?"], "911.1.char": "Voice", "911.1": ["Allow me to introduce myself properly to my new pet! I'm Voice, vampire-half. Vampires, being under the more noble and civilized faction than the werewolf sorts. And as for your faction..."], "912.char": "Rules", "912": ["Nothing to do with me. Werewolves and vampires do the same thing to humans, anyway."], "913.char": "Voice", "913": ["You mean conversion? Wouldn't know why you'd want to be human anyway, they only live for a hundred years! <PERSON><PERSON> live longer."], "914.char": "Rules", "914": ["I'm. <PERSON><PERSON>. If you're not a monster, put me back where I'm supposed to be."], "915.char": "Voice", "915": ["<PERSON><PERSON><PERSON>? You, are, sure? About that. Hu-man? Everyone you know was human? Can't phase through the floor? Aren't sucked into a cursed vase? Why, stuck in the past, aren't we?"], "916.char": "Voice", "916": ["Woah- okay, fine, I'll stop pressing! For whatever reason you want to find humans, it's useless. They're prehistoric."], "917.char": "Voice", "917": ["Long ago, a monster-led crusade purified the land of true humans; <PERSON><PERSON> are the corrupted survivors of that. All Naman remember that much."], "918.char": "Voice", "918": ["So, you wouldn't be allowed to be human. In fact, you already had a potent transformative put in you... sure you're all right? You don't recognize your own body?"], "919.CHOICE": ["I'll do it.", "I don't wanna."], "919.char": "Voice", "919": ["We're building a base atop the remains of an old fortified village near here. Survive one last lesson and I'll take you home?"], "1000.char": "Rules", "1000": ["You just resurrected me from the dead and I'm a girl now. Give me a break."], "1001.char": "Voice", "1001": ["I'd love to, ", 0, ", but we can't hang around the crime scene unless you wanna become some passing monster-girl's pet project!"], "1002.char": "Voice", "1002": ["We're passing through the cave anyway, I'll give you a safe place to rest when we're back! Please!"], "1003.char": "Rules", "1003": ["Don't expect me to play by your rules. I'll take the bed anyway."], "1100.char": "Voice", "1100": ["Good spirit. I found a long fancy stick with some of the other old gear I found at the village ruins, you might take to it. I'll get you up to speed."]}, "wishingring.json": {"000.CHOICE": ["Use It", "Throw It Away"], "000.char": "Rules", "000": ["A ring... Of wishes? It's asking for a wish."], "100.CHOICE": ["Something to help the quest.", "Someone with huge tits.", "Make monster-girls love me.", "Give me a big dick."], "100.char": "Narration... alt under conditions: Player has seen <PERSON> Ending 0, Player has seen <PERSON> Ending 1, ", "100": ["Whatever I do, I shouldn't wish for something stupid."], "100.1.CHOICE": ["Something to help the quest.", "A girl with huge tits.", "For a big dick."], "100.1.char": "Narration... alt under conditions: Player has seen <PERSON> Ending 1, ", "100.1": ["Whatever I do, I shouldn't wish for something stupid."], "100.1.1.CHOICE": ["Something to help the quest.", "A girl with huge tits.", "Make monster-girls love me.", "For a big dick."], "100.1.1.char": "Narration", "100.1.1": ["If I don't want to end up captured by Rams again, I'd better choose something sensible."], "100.2.CHOICE": ["Something to help the quest.", "A girl with huge tits.", "Make monster-girls love me."], "100.2.char": "Narration", "100.2": ["Whatever I do, I shouldn't wish for something stupid."], "200.char": "Rules", "200": ["A pair of coins for hypnosis? Do these even work on monsters?"], "300.char": "Narration", "300": ["An unmistakable pressure wells up in one's chest along with instant regret and an increasing bra size."], "400.char": "Narration", "400": ["A wave of hypnotic magic makes one feel as light as air, the ring reveals itself to be a dangerous wedding ring!"], "500.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "500": ["With a wave of hypnotic magic, one's mind and body feel as light as air. Can't stop thinking about that Satyr girl..."], "500.1.char": "Narration", "500.1": ["Mid-wish, one's train of thought is derailed. A big...? A big, juicy pussy? The ring was packed with hypnotic lesbian energy!"]}, "badend/badendcatgirl.json": {"000.char": "Narration... alt under conditions: Player has seen <PERSON>ing 0, ", "000": ["Overwhelmed by foreign sensations, a pink haze sets in."], "000.1.char": "Narration", "000.1": ["Overwhelmed by foreign sensations, a pink haze sets in. Once again, the cat-girls have taken their toll."], "001.char": "Narration... alt under conditions: Playervariables.consent == false, ", "001": ["The teasing and groping had been bearable at first, a cat-girl's rough tongue would only leave a lingering heat."], "001.1.char": "Narration", "001.1": [0, " settled in with the cat-girls and lived happily ever after (NSFW content off). You can change the NSFW toggle by finding <PERSON><PERSON> in the village."], "002.char": "Rules", "002": ["Hya?! Why won't my legs move!"], "003.char": "Narration", "003": ["With the last black orb pulled out by ", 0, "'s own transformed cat-tail, nerves bolt electrifying pleasure all through the body, intensifying at the catty girls' touch."], "004.char": "Rules", "004": ["Nhyaaa. You...! Hyau-?"], "005.char": "Narration", "005": ["The former human can only make pathetic, girlish cries as she's ravished by the slick, slurping muscle, swamping her mind in dopamine."], "006.char": "Narration", "006": ["More and more of those demi-human girls flood in, attracted by the helpless moans of their new comrade. They take turns, several at a time, claiming every inch of ", 0, "'s body."], "007.char": "Narration", "007": ["On her first day of her new life, she came a dozen times before she was taken away to their hide-out. So began the lazy days..."], "100.char": "Narration", "100": ["Time passes..."], "101.char": "CatKnight", "101": ["Nya! It's been a week of lazing and hot sex and the newbie still just lays about the hay!"], "102.char": "CatKnight", "102": ["That's why we brought her this thing, though! She said she wanted a dick, right?"], "103.char": "CatKnight", "103": ["Nnnn? Didn't she say she-- Wow! That stuff? Last time I took that, I woke up all sore and surrounded by cum-drooling bitches! Ya sure?"], "104.char": "CatKnight", "104": ["'Course! How else nya gonna get the newbie outta their shell? She'd go out n' corrupt even her friends after this!"], "105.char": "Rules", "105": ["A drink? Why's it all weird-colored, nya?"], "106.char": "CatKnight", "106": ["Got a treat for ya today! It's a tonic to get ya fired up!"], "107.char": "Narration", "107": ["With an eager feline forcing it on her, ", 0, " eventually swallows all of it. The lingering cat-girl heat starts to concentrate and well up in her loins..."], "108.char": "Narration", "108": ["The sudden, pleasant swelling is enough to distract her from the familiar voice heard down the hallway."], "109.char": "Voice", "109": ["Just where do you think you're taking me?! You damn cats could at least stop fucking each other in the corridor when you have a guest over! Hey, paws off my ass!"], "110.char": "CatKnight", "110": ["<PERSON><PERSON><PERSON><PERSON>. Here she is, that fat-titted villain. She's realllllly messed things up for us. Came right to our doors when we told her about you."], "111.char": "Narration", "111": ["An obscene, throbbing length spills from the groping hand of ", 0, "'s cat-girl attendant, a futanari dick."], "112.char": "Narration", "112": ["Hearing the voice of a fertile, uncorrupted female gets the organ twitching, sending all sorts of signals to the brain. A monster-girl's need to take and convert others."], "113.char": "CatKnight", "113": ["Here she is, princess! Howsat? She's got the most adorablest twitchy ears, really loves a good petting between the ears. She's one of ours now!"], "200.char": "Voice", "200": [0, "?!"], "201.char": "Voice", "201": ["I got captured on purpose to find you, so quick! Let's escape togeth- A-a tail? Why do you have a d..ck?!"], "202.char": "Narration", "202": ["It's suddenly so obvious. <PERSON>'s body has such ridiculous, lewd proportions, it's only natural to try and stuff it full."], "203.char": "Narration", "203": ["She doesn't even have inhuman ears or any tail at all. There's something alluring about how corruptibly plain she is."], "204.char": "Voice", "204": ["H-hey. ", 0, ", that's my- Aah! Stop- let's escape and we can get all cuddly and have sex once I've drained you, okaaaa- Hyaa!"], "205.char": "Narration", "205": ["With new allies taking <PERSON>'s arms behind her, pulling the plump, humanish body into a lap is effortless. After all these days of lounging as a cat-girl, ", 0, " has finally decided."], "206.char": "Narration", "206": ["Just hanging out the barracks getting off with the girls isn't going to be enough."], "207.char": "Narration", "207": ["No matter how Voice tries to bargain, only thoughts of sex fill the mind."], "208.char": "Narration", "208": ["Voice's heavy, doughy ass hoisted up into the air, the grappling pair realize another detail: it's not just a cat-girl's tongue that's rough."], "209.char": "Narration", "209": ["A monster-girl's body is anything but ordinary, sexual organs included. The conjured shaft raking its soft barbs against <PERSON>'s marshmallowy ass."], "210.char": "Voice", "210": [0, "! I- I fucked up but this isn't a good idea! Once we get all the corruption out of you we can fuck all you want but-"], "211.char": "Voice", "211": ["This is my first time, that thing's going to mess me up! Hhh- Nnnh- don't rub it against my... sex... feels weird-"], "212.char": "Narration", "212": ["The tip finally finds purchase. Despite protests, that tight pussy squeezes down with greed."], "213.char": "Narration", "213": ["Every time the shaft's crown slips in, <PERSON>'s snatch is treated to the spine-tingling pull back, frying her nerves whilst she clenches her teeth, grins and bears it."], "214.char": "Narration", "214": ["She becomes wet with arousal, yet the rough, dredging motion spills the juices from her oversized vulva."], "215.char": "Narration", "215": ["The lewd, squishy hole is trained one push at a time to accept the first stage of corruption. A cat-girl's sweet yet rough love works in towards the vampire's core, to make her ovulate like a breeding-bitch."], "216.char": "Narration", "216": ["The last of <PERSON>'s willpower yields as barbs rake and stir and her juicy cunt wells with breeding hormones, sending her into an unnatural rut."], "217.char": "Narration", "217": ["The room fills with lewd, wet sex. Inhibitions break, a primal need overcomes their bodies."], "218.char": "Narration", "218": ["The 'villain' has already started to become one of them, a slave to pleasure. Her legs pull in and her toes curl, her plump body trembles as it cums itself silly."], "219.char": "Narration", "219": ["Voice gasps breathlessly as she starts to lose control of her body, creaming herself, but the heftiest dose of corruption has yet to come."], "220.char": "Narration", "220": ["The nearby cats snicker with cheshire-like grins as the two plunge into the throes of corruptive sex."], "221.char": "Narration", "221": ["Her spine tingles down to the tail-bone, her tongue lolls as sense of reason is scrambled with the rocking of her body."], "222.char": "Narration", "222": [0, "'s dick pounds against the quivering uterus, an uncontrollable feeling rising up the rock-hard length."], "223.char": "Voice", "223": ["<PERSON><PERSON><PERSON>- I'm cumming!"], "224.char": "Narration", "224": ["They spasm together, jets of sticky-hot white crammed against <PERSON>'s womb."], "225.char": "Narration", "225": ["In a flash of white, they reach sweet release, drowning in a happy mellowness as the two pant out together."], "226.char": "Narration", "226": ["There's so much that it overflows, belly stuffed completely full with transformative jizz."], "227.char": "Narration", "227": [0, " feels a tug at the ankle, then again, demanding something. They don't need to look down to know what it is."], "228.char": "Narration", "228": ["Between their legs, they now each bear a slender yet fluffy cat-tail. They start to move together again, the newly adorned cat-girl letting out a soft, happy rumble from her chest."], "229.char": "Narration", "229": ["Too caught up in the moment to realize that every cum-slick thrust is stirring the corruption even deeper into her. Cute, fluffy ears spring up atop her head."], "230.char": "Narration", "230": ["With no time to clear their thoughts, the two succumb to their new instincts."], "231.char": "Narration", "231": ["The cat-knights fill up the sidelines to welcome the new 'recruits', playing amongst themselves, jerking off and descending into orgy."], "232.char": "Narration", "232": ["The journey ends before it even starts, but they don't care. They prowl the forest looking for victims and enjoying their new, care-free life."], "233.char": "Narration", "233": ["The region would soon be dominated by frisky feline girls, overwhelming travellers with their sheer numbers."], "234.CHOICE": ["Start Over"], "234.char": "Rules", "234": ["In the end, I became a catty monster-girl obsessed with sex..."]}, "badend/badenddefault.json": {"000.char": "Narration... alt under conditions: endGeneric0, ", "000": ["Endurance hits 0. The weight of exhaustion drags down like shackles."], "000.1.char": "Narration", "000.1": ["Endurance hits 0."], "001.char": "Narration", "001": ["With the last of their energy, ", 0, " fights through the brush and slumps to the ground."], "002.char": "Narration", "002": ["Rain sets in. Time passes. The world out from under the cover is a cold, hostile yet tranquil one."], "003.char": "Narration", "003": ["Even as the sun and the stars and the clouds pass by, there's only the pitter-patter of rain onto the canopy above."], "004.char": "Narration", "004": ["Drifting to sleep..."], "005.char": "Narration", "005": ["Rustle rustle..."], "006.CHOICE": ["Return to Base"], "006.char": "Narration", "006": ["In the dark, ", 0, " was taken away safely, this time."]}, "badend/badenddragonharpy.json": {"000.char": "Narration", "000": [0, " starts to lose consciousness..."], "001.char": "Narration... alt under conditions: Playervariables.consent == false, Player is trapped in a harpy's egg., Player has CUM debuff, ", "001": ["Even when avoiding the eggs, a harpy's sheer size is enough to swoop ", 0, " right up."], "001.1.char": "Narration", "001.1": [0, " was whisked away and they lived happily ever after (NSFW content off)."], "001.2.char": "Narration", "001.2": ["Stuck in the harpy's egg, at least no monster can get through it. Warm, comforted, out of sight."], "001.3.char": "Narration", "001.3": ["What even is all this gunk? It's sticky, it weighs down the body."], "002.char": "Narration... alt under conditions: Player is trapped in a harpy's egg., ", "002": ["Carried away in to the great wide sky, everything blacks out. Most likely stuck in one of those eggs now, anyway."], "002.1.char": "Narration", "002.1": ["-and a gradual up and down, as if struggling to make flight into the skies..."], "003.char": "Narration", "003": ["As time passes, finally there's a glimpse of the great blue beyond. The morning sky, the fresh air. Nothing but a nest below -- oh no."], "004.char": "Narration", "004": ["There's no ground below. Abseiling this tree isn't happening. That <PERSON><PERSON><PERSON> won't help after taking all this effort to bring a heavy egg here."], "005.CHOICE": ["Start Over"], "005.char": "Narration... alt under conditions: Player has harpy wings, ", "005": ["I tuck back into the insulated egg, left with only dreams of some day taking flight from here..."], "005.1.CHOICE": ["Start Over"], "005.1.char": "Narration", "005.1": ["I've no choice left. Since that damn <PERSON><PERSON><PERSON> gave me these wings I'll have to learn to fly."]}, "badend/badendfoxgirl.json": {"000.char": "Narration... alt under conditions: Player has seen <PERSON> Ending 0, ", "000": ["I'm drifting away as if pulled into the realm of dreams. What an odd sensation."], "000.1.char": "Narration", "000.1": ["I'm drifting away as if pulled into the realm of dreams. What an odd sensation."], "001.char": "Narration... alt under conditions: Playervariables.consent == false, ", "001": ["The floor can still be felt below, the surroundings barely visible-"], "001.1.char": "Narration", "001.1": [0, " was whisked away to work at the kitsune shrine and they lived happily ever after (NSFW content off). Change the NSFW toggle through Qades in the village."], "002.char": "Narration", "002": ["And (<PERSON><PERSON>)? Is this what they call an Out-of-body experience? It's surreal."], "003.char": "Narration... alt under conditions: Player has fox-tail, Player has fox-ears, Player has cat-tail, Player has ram-horns, Player's bust size is > 2, Player has ram clothes top, ", "003": ["I can see those ghosts now, fox-features projecting from my body. I could be mistaken for a monster-girl..."], "003.1.char": "Narration", "003.1": ["Along with the shame of seeing my foxed state. Big fluffy tail and ears twitching."], "003.2.char": "Narration", "003.2": ["I can see those fox-ears from here, perched atop my body's head, marking me like I'm one of their kin."], "003.3.char": "Narration", "003.3": ["Free of the burden of my cat-girl body, I can think straight again."], "003.4.char": "Narration", "003.4": ["Though I can still see the ram's horns, there's no fox-corruption on me. Yet."], "003.5.char": "Narration", "003.5": ["I can't believe I was walking around with a chest like that. Seeing it from an outside angle is making me embarrassed."], "003.6.char": "Narration", "003.6": ["Now I look at it from the outside, how could I be wearing such a shameful outfit! Why didn't I notice how shameful it is?"], "004.char": "Narration", "004": ["However, that shrine-fox is standing over my body with her ghosts. I can't let them do whatever they like!"], "005.char": "Narration", "005": ["... but a pair of ghostly hands reach out from behind ..."], "100.char": "Narration", "100": ["How's a ghost dragging me?!"], "101.char": "FoxGirl", "101": ["<PERSON><PERSON><PERSON><PERSON>. It's been a loooooooong time~"], "102.char": "Narration", "102": ["I try to respond - no words come. Pulled further, further away from my body."], "103.char": "Narration", "103": ["..."], "104.char": "Narration", "104": ["It's morning. It's hard to see, this is Kitsune territory?"], "105.char": "Narration... alt under conditions: Playervariables.possessionrank == true, ", "105": ["And I'm trapped?! It's this fox shrine again!"], "105.1.char": "Narration", "105.1": ["And I'm trapped?! It's this fox shrine again!"], "106.char": "Rules", "106": ["I love you, master! Lick-lick~"], "107.char": "Narration", "107": ["My heart sinks through my spirit. Wasn't that my voice?"], "108.char": "Rules", "108": ["I'm addicted! Cum on my tongue ♥"], "109.char": "Narration", "109": ["Even as a spirit, I can feel something salty on my tongue. My body's in danger."], "110.char": "Narration", "110": ["I peer through the walls. A sauna? Those sweaty bodies are-"], "111.char": "Rules", "111": ["Gllp... Gulp. Phaa, thank yooou. Let me clean you up ♥"], "112.char": "Narration", "112": ["No! This isn't right, why am I happy?!"], "113.char": "FoxGirl", "113": ["You needy cunt, that's enough. You're enjoying this way too much."], "114.char": "Rules", "114": ["Slurrrp. I haven't had a body like this in sooooo long ♥"], "115.char": "FoxGirl", "115": ["Yeah, yeah. Don't play it up you slut, you've done your job."], "116.char": "FoxGirl", "116": ["Look, it's <PERSON>'s former bitch. How's the mofu-fox life?"], "117.char": "Narration... alt under conditions: Player has fox-tail, Player has fox-ears, ", "117": ["Those are the ghost's ears and tail. I'm not anyone's bitch."], "117.1.char": "Narration", "117.1": ["What was the point of turning me into a fox-girl if my body's going to get taken?!"], "117.2.char": "Narration", "117.2": ["Those might be my ears but I never had tails like that. I'm not a fox-girl."], "118.char": "FoxGirl... alt under conditions: Player has fox-tail, ", "118": ["Weren't you paying attention? You're one of our shrine maidens now. Want your body back?"], "118.1.char": "FoxGirl", "118.1": ["Your dumb body was wagging its tail even before I possessed you. I'm helping you beat your shyness."], "119.char": "Narration", "119": ["I'm taking my body back whether she wants it or not. She'll pay."], "120.char": "Rules", "120": ["Nnh- No, it's still my turn, we need to service master more!"], "121.char": "Narration", "121": ["..."], "200.char": "Rules... alt under conditions: Player has fox-tail, ", "200": ["Finally, my body!... Why am I a fox-girl now?!"], "200.1.char": "Rules", "200.1": ["Finally, my body's back!"], "201.char": "FoxGirl", "201": ["Welcome back, ", 0, ". What do you say?"], "202.char": "Rules", "202": ["What do I...? Thank you for giving my body back, master! Your cum was delicious!"], "203.char": "Rules", "203": ["... The ghost said that! Master?!"], "204.char": "FoxGirl... alt under conditions: Player has seen <PERSON> Ending 0, <PERSON> has seen <PERSON> Ending 1, <PERSON> has seen <PERSON> Ending 0, <PERSON> has seen <PERSON> Ending 0, ", "204": ["I've seen how you are. You get beat down by the other monster-girls, you submit, all that."], "204.1.char": "FoxGirl", "204.1": ["You have a habit of running away, don't you? That's why I'm training you to come back to me. Seems it's working."], "204.2.char": "FoxGirl", "204.2": ["Don't think I don't know. You've taken a Satyr's fluids down that throat, you've lost and submitted to monster-girls before."], "204.3.char": "FoxGirl", "204.3": ["Don't think I don't know. You've hung around with those bimbo cat-girls, you've lost and submitted to monster-girls before."], "204.4.char": "FoxGirl", "204.4": ["Don't think I don't know. You've gone and gotten trapped in marriage with the sheep-girls, you've lost and submitted to monster-girls before."], "205.char": "FoxGirl", "205": ["Yet you keep thinking about the Tower."], "206.char": "FoxGirl", "206": ["You're cuter when you struggle but I can't risk you doing anything dangerous. For your sake and ours."], "207.char": "FoxGirl", "207": ["You'll be my lovely obedient fox-bitch, yes?"], "208.char": "Rules", "208": ["I will! Not! I will! Master-"], "209.char": "FoxGirl... alt under conditions: Player's bust size is > 3, Player's bust size is > 2, Player's bust size is > 1, ", "209": ["We need to work on you, girl. Less fighting, more oral."], "209.1.char": "FoxGirl", "209.1": ["Girl, we have a -lot- to work on. Not just your attitude but your crippling obsession with drinking cursed milk, too. Your chest will take weeks to fix."], "209.2.char": "FoxGirl", "209.2": ["We have a lot to work on, girl: your attitude and your chest. You'll be wearing chest binds until it's down. No more milk, got it?!"], "209.3.char": "FoxGirl", "209.3": ["You'll be wearing a shrine maiden's chest binds while we fix that attitude of yours."], "210.char": "FoxGirl", "210": ["I've scrapped your old gear for proper Spirit tools. Make sure to gather more Spirit Skills, they synergize."], "211.char": "Rules", "211": ["Yes..."], "212.char": "Narration", "212": ["Even when I break free, I inevitably come crawling back. My body can't forget her taste anymore."], "213.CHOICE": ["Start Over"], "213.char": "Narration", "213": ["This is my home now..."]}, "badend/badendramgirl.json": {"000.char": "Narration... alt under conditions: Playervariables.makeuprank == true, Player has seen <PERSON> Ending 0, ", "000": ["Trapped in the <PERSON><PERSON>girl's soft embrace and lulled by the gentle, melodic hum, ", 0, "'s tension starts to melt away."], "000.1.char": "Narration... alt under conditions: Player has seen <PERSON> Ending 1, ", "000.1": ["Unable to escape the powerful ram's sway, ", 0, " stops in their tracks, following the lady's every movement."], "000.1.1.char": "Narration", "000.1.1": ["Once again, unable to escape the powerful ram's sway, ", 0, " stops in their tracks, following the lady's every movement."], "000.2.char": "Narration", "000.2": ["Once again, trapped in the <PERSON><PERSON>girl's soft embrace and lulled by the gentle, melodic hum, ", 0, "'s tension starts to melt away."], "001.char": "Narration... alt under conditions: Player has ram-horns, ", "001": ["The Ram-girl's thighs have the perfect amount of squish, the two's lips remain glued together, their eyes locked on each other's."], "001.1.char": "Narration", "001.1": ["Sat upon those perfectly plush <PERSON> thighs, both their horns lock together. The two remain glued to each other, locked in a kiss for what feels like an eternity."], "002.char": "Narration", "002": ["No words are exchanged, only the gentle thrum of their hearts, the hypnotic lullaby and a lover's kiss."], "003.char": "Narration", "003": ["Nobody, monster or not, dare break the sanctity of a Ram-girl and her prey."], "004.char": "Narration... alt under conditions: Player has ram-horns, ", "004": [0, " slowly releases the grip on their weapon. There's no need for it anymore, it's safe here in this muse's arms."], "004.1.char": "Narration", "004.1": ["Or, rather, the two <PERSON>-girls. The once-valiant ", 0, " throws away their weapon. After all, the only thing that feels 'right' is to make out until the opponent gives in."], "005.char": "Narration... alt under conditions: Player has ram-horns, ", "005": ["It's an embrace that melts away at the fiery, war-like mentality and douses it in a sweet, vivid yellow."], "005.1.char": "Narration", "005.1": ["It sates a deep-set primal need, empowering oneself through their kiss, the 'fight' dragging on as the two Rams are evenly matched."], "006.char": "Narration", "006": ["It's unsure how long the two spent like that. 'Time' isn't a thing to worry about anymore, after all. Monsters don't fear time."], "007.char": "Ram<PERSON><PERSON><PERSON>", "007": ["Haaa. Let's go home, shall we? I won't be calling you 'lamb' anymore. We're a couple, right, ", 0, "?"], "008.char": "Narration", "008": ["Something isn't right, like a dull hum. As if fate itself were being remoulded. A couple..?"], "009.char": "Narration", "009": ["Seeing that hesitance, the <PERSON> thrusts herself upon ", 0, " and bequeaths one more kiss to leave the mind in a happy swirl."], "010.char": "Rules", "010": ["Sorry, I must've been shocked at how... quickly my life has become so great?"], "011.char": "RamGirl... alt under conditions: Player has ram-horns, ", "011": ["<PERSON><PERSON><PERSON>. Still catching up to reality? You've always dreamed of living like this, after all."], "011.1.char": "Ram<PERSON><PERSON><PERSON>", "011.1": ["<PERSON><PERSON><PERSON>. Still catching up to reality? You've always dreamed of living like this, after all, big horns and a partner for hypnosis practice~"], "012.char": "Narration", "012": ["Though it's a long walk back, the Ram supports ", 0, " all the way, giving that battle-worn body a chance to stop and rest on her lap at any time."], "013.char": "Narration", "013": ["The whole walk is a trip of cognitive dissonance; her instincts demands her to fight, yet she's acting like a bashful lover in her enemy's arms."], "014.char": "Narration", "014": ["The two of them pass a number of curious cat-girls, the <PERSON> responds with a protective squeeze of her darling."], "015.char": "Narration", "015": ["That's how her tired body and spirit give in, craving that comfort and security. Days in the meadows, singing a duet. Sleeping under one roof, in one big bed."], "016.char": "Narration", "016": ["..."], "017.CHOICE": ["Wake Up"], "017.char": "Ram<PERSON><PERSON><PERSON>", "017": ["It's morning, honey-buns. Breakfast is ready~"], "018.char": "Narration", "018": [0, " wakes up to a chilling feeling. She'd been practicing the harp together all week, they'd gone for a walk to the seaside, she'd wake up like this every morning..."], "019.char": "Narration", "019": ["Why? She's even thinking of herself as 'she' now, as if she'd already accepted getting cosy as a newlywed!"], "020.char": "Narration", "020": ["Footsteps fall upon the stairway. The door opens. Curtains part, the room is bathed in the morning's blinding brilliance."], "021.char": "Ram<PERSON><PERSON><PERSON>", "021": ["Wakey-wakey, dear. Come on, you know it's your favorite- oh?"], "022.char": "Narration", "022": ["It's as if she's looking in on a wounded animal she'd taken in. Only now, having recovered enough to move, it fears the captor."], "023.char": "Ram<PERSON><PERSON><PERSON>", "023": ["My, no wonder. If I didn't keep you under my hypnosis, you'd have gotten yourself hurt again! How do you feel, all better now?"], "024.char": "Ram<PERSON><PERSON><PERSON>", "024": ["It's the big bad <PERSON> who stole your lips and your heart and toyed around with you. What're you going to do to me now?"], "025.char": "Narration", "025": ["Standing in the light, her arms reach out to the sides, leaving her wide-open to any sort of attack."], "026.char": "Narration", "026": ["As the air in the room settles, the smell of fresh pancakes, bacon, eggs wafts up from the lower floor. ", 0, " hasn't eaten since the shepherd's pie last evening."], "027.char": "Narration", "027": ["That's not important. ", 0, " reaches out to grasp a stiff, metal object at the bedside. She only has the one monster-girl in the way of her freedom."], "028.char": "Narration", "028": ["The Ram-girl doesn't even flinch as her captured bride rises from the bed. She holds up the blunt object, pulling it back to strike, adjusting her grip..."], "029.char": "Narration", "029": ["And as the hand brushes over her 'weapon', each string plays in a rising glissando, breaking through the room's chill silence with the lyre's familiar sound."], "030.char": "Ram<PERSON><PERSON><PERSON>", "030": ["Aw, don't make such a big deal out of it. If you're shy about being mine, just know, I'll shut down anyone who tries to belittle you. Okay?"], "031.char": "Rules", "031": ["You filled my head with all these fake happy memories! Your dumb hypnosis, wasting days in the field playing songs- I hate it! That if I escape I won't be able to forget about it!"], "032.char": "Narration", "032": ["With a sigh, the fluffy, sheepish lady finally moves, setting herself down to the bed and laying down on her back, finally able to meet ", 0, "'s downcast expression."], "033.char": "Ram<PERSON><PERSON><PERSON>", "033": ["If you don't want it to end, why should it? Besides, why should you be sad that you had a nice time with me? I only stopped you from fighting."], "034.char": "Rules", "034": ["Because it was all fake!"], "035.char": "Ram<PERSON><PERSON><PERSON>", "035": ["Then come here and make it real."], "036.char": "Narration", "036": ["No longer under a spell, she could leave at any time. Even if she does what she wants now, she could just leave whenever she's ready. That's why..."], "037.char": "Narration", "037": [0, " falls back into the <PERSON><PERSON><PERSON>'s clutches and puts aside the lyre. Soft, springy softness welcomes her in."], "038.char": "Ram<PERSON><PERSON><PERSON>", "038": ["There, there. You've been through a lot, haven't you? We'll take it slow and fill you up with happy thoughts~"], "039.char": "Narration", "039": ["Fighting back the welling frustration, ", 0, " now reaches all around that large, curvy body. It's like she's finally back home."], "040.char": "Ram<PERSON><PERSON><PERSON>", "040": ["Besides, haven't you noticed? You've a full sheep-girl now. After all our time together, you can walk out and nobody would jump you. You're one of us."], "041.char": "Rules", "041": ["That's not true- <PERSON>ya<PERSON>?!"], "042.char": "Narration... alt under conditions: Player has ram-horns, ", "042": ["The ram-girl calmly, effortlessly placates the back-talk with a firm hold and unbridled headpats."], "042.1.char": "Narration", "042.1": ["The larger ram-girl curls her hand about a horn and gives a light tug, eliciting a soft reaction."], "043.char": "RamGirl... alt under conditions: Player has ram-horns, ", "043": ["Horns or not, you're more of a cuddle-monster than a fighter, aren't you? Come on, where's that fire?"], "043.1.char": "Rules", "043.1": ["No fair! Just having horns doesn't make me like you!"], "044.char": "Rules... alt under conditions: Player has ram-horns, ", "044": ["I'll show you after you give me headpats, praise and due worship!"], "044.1.char": "Narration", "044.1": ["To the side, a tall mirror reflects the two horned cuddle-happy ladies smothered on each other like the girlfriends they are."], "045.char": "RamGirl... alt under conditions: Player has ram-horns, ", "045": ["Mmm. You're the strongest sheep-girl for me, there there. Breakfast now?"], "045.1.char": "Narration", "045.1": ["They both see the same scene. Two monster-girls, lovers of fluff. Every second that goes by weighs on ", 0, "'s self-image, struggling to find the 'human'."], "046.char": "Narration... alt under conditions: Player has ram-horns, ", "046": ["It takes some time to settle the now-conscious ", 0, ", only then do they get up off the sheets, hand in hand down the stairs. Two inseparable lovers."], "046.1.char": "Narration", "046.1": ["Nothing more is said. The embrace ends and one after the other, they make their way downstairs."], "047.char": "Narration... alt under conditions: Player has ram-horns, ", "047": ["With nothing more than an apron on, pancakes are served to the wife's plate, Ram-girl flashing cheeky peeks of her boobs and body all the while."], "047.1.char": "Narration", "047.1": ["The habits of the past week haven't faded. The two sit hip-to-hip, forking over food to each other in turn, horns clacking together from their closeness. No words are needed in their little world."], "048.char": "Narration... alt under conditions: Player has ram-horns, ", "048": ["So begins the days like a never-ending honeymoon. They'll be kissing and humping under the stairs by noon, they'll have a hand-held tour of the community by evening. The pair were a hot topic."], "048.1.char": "Narration", "048.1": ["So begins their honeymoon. The answer was never 'I am a Ram-girl now' but 'I am yours now'. Singing by the lake, baking treats for the cat-girls together, the pair were popular amongst the locals."], "049.char": "Rules", "049": ["'Return back home'? I've been brainwashed? Not this nonsense again~"], "050.char": "Ram<PERSON><PERSON><PERSON>", "050": ["My darling's schedule is full today. <PERSON><PERSON><PERSON>. This whole week too. Unless you're offering to be a concubine~"], "051.char": "Narration", "051": ["Even before becoming a ram-girl, people would try and take ", 0, " for themselves. Now, not even hypnosis can steal her from her lover."], "052.CHOICE": ["Start Over"], "052.char": "Narration", "052": ["The journey comes to an end. A new, lazy, fluffy tale begins."], "300.char": "Ram<PERSON><PERSON><PERSON>", "300": ["Sssh. Follow the conductor's baton. Any way I swing this you'll play to my tune~"], "301.char": "Ram<PERSON><PERSON><PERSON>", "301": ["You're a lewd hypno-slut, drooling from her cunt. A pervert jerking her fingers deep into her own pussy."], "302.char": "RamGirl... alt under conditions: Futa enemies toggled: OFF, ", "302": ["Then, the moment you feel a load of my hot jizz flooding your parched throat, you'll stop thinking and start swallowing. You'll quake, tighten up and C-U-M."], "302.1.char": "Ram<PERSON><PERSON><PERSON>", "302.1": ["The moment you feel the juices of my cunt flowing over your face, you'll stop thinking and start swallowing. Your body will quake, you'll tighten up and C-U-M."], "303.char": "Rules... alt under conditions: Player has cat-ears, ", "303": ["As if I'd- Hnnh?!"], "303.1.char": "Rules", "303.1": ["As if I'd- <PERSON>ya<PERSON>?!"], "304.char": "Narration... alt under conditions: Player has cat-ears, ", "304": ["With a sudden gasp, ", 0, " bites her lip as she feels the spasming grip of her own wet snatch -- she'd just finger-banged herself to orgasm?!"], "304.1.char": "Narration", "304.1": ["Embarrassed of her cattish slip-up, ", 0, " bites her lip and holds back her purr, feeling the spasming grip of her own wet snatch -- she'd just finger-banged herself to orgasm?!"], "305.char": "RamGirl... alt under conditions: Player has ram-horns, ", "305": ["My, such a nasty look you're giving me... While you do nothing but jill off to fantasies about me."], "305.1.char": "Ram<PERSON><PERSON><PERSON>", "305.1": ["My, such a nasty look you're giving me... While you jill off to your fantasies, soaked in corruption and growing out horns."], "306.char": "Rules... alt under conditions: Futa enemies toggled: OFF, ", "306": ["I'm not-- You're cheating using that dumb fat meat rod, fight me fairly!"], "306.1.char": "Rules", "306.1": ["I'm not-- You're cheating and messing with my head, fight me fairly!"], "307.char": "Ram<PERSON><PERSON><PERSON>", "307": ["Stop lying to yourself, you're the spitting image of a sheepish bitch. Here, I'm going to go lie down, so excuse me while I leave~"], "308.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "308": ["Left standing there, bow-legged and shamed, watching that hypnotic pendulum head back down the trail -- it's a given. ", 0, " follows her tail."], "308.1.char": "Narration", "308.1": ["Left standing there, bow-legged and shamed, watching that hypnotic ass head back down the trail -- it's a given. ", 0, " follows her tail."], "309.char": "Ram<PERSON><PERSON><PERSON>", "309": ["Mmm? I'm retreating. Aren't you satisfied after creaming your own thighs?"], "310.char": "Rules", "310": ["I can't defeat you if you run away. You might have the cure for your damn corruption. You might have more victims who need saving!"], "311.char": "Ram<PERSON><PERSON><PERSON>", "311": ["Or your thirsty female body wants to know how deep into you I'll go?"], "312.char": "Rules... alt under conditions: Futa enemies toggled: OFF, ", "312": ["...! Right, you're just screwing with me because I'm a female! Except you're wrong, I don't even see you as a mate. You're just competition."], "312.1.char": "Rules", "312.1": ["...! Right, you're just screwing with me because I'm a female! You'll be sorry if you make assumptions like that."], "313.char": "RamGirl... alt under conditions: Futa enemies toggled: OFF, ", "313": ["A competitor? You've come to suck every last drop of corruptive ooze from my balls so I can't take any women?"], "313.1.char": "Ram<PERSON><PERSON><PERSON>", "313.1": ["I'll be sorry? You've going to eat me out until I can't even stand to corrupt any more women?"], "314.char": "Rules... alt under conditions: Futa enemies toggled: OFF, ", "314": ["?? I- Yes? I'm going to empty you and you might as well be a female!"], "314.1.char": "Rules", "314.1": ["?? I- Yes? I'm going to have you squirm, you're the real female here!"], "315.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "315": ["The endowed, haughty Ram-girl finds herself cornered. Her obscene erection stands exposed, primed and ready to be emptied."], "315.1.char": "Narration", "315.1": ["The endowed, haughty Ram-girl finds herself cornered. Her sex exposed, her body laid back."], "316.char": "Narration", "316": ["The greedy mouth of a lust-drunk girl descends upon it, only to hesitate. Steam rises from that heated body and bathes her confused, flushed face."], "317.char": "Narration", "317": ["Then, a counter-attack! A firm grip has ", 0, " by the bangs, forcing her to breathe a heavy intake of mind-fuddling mating pheromones."], "318.char": "Ram<PERSON><PERSON><PERSON>", "318": ["Come on. Wasting time will just get you hypnotized by my musk instead. It's super effective against one's own species, you know?"], "319.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "319": ["A tender, cautious lick falls upon the helm of it, then another, bathing the twitchy thing in a slurpy-sloppy make-out."], "319.1.char": "Narration", "319.1": ["Tender, cautious licks fall upon the <PERSON>'s vulva, over her clit, steadily getting slurpier and sloppier."], "320.char": "Ram<PERSON><PERSON><PERSON>", "320": ["Mmmh. Finally, some good corruption play from a girl putting the effort in- ...Hnn-?"], "321.char": "Narration... alt under conditions: Futa enemies toggled: OFF, <PERSON> has ram-makeup, ", "321": ["A sudden push takes the futa off-guard, inch upon inch of that throbbing tool sinking past ", 0, "'s lips, eagerly sucked upon."], "321.1.char": "Narration... alt under conditions: Player has ram-makeup, ", "321.1": ["A sudden push takes the alluring woman off-guard, the assault on her folds now tongue-deep."], "321.1.1.char": "Narration", "321.1.1": ["A sudden push takes the alluring woman off-guard, the assault on her folds now tongue-deep with lipstick staining the sides."], "321.2.char": "Narration", "321.2": ["A sudden push takes the futa off-guard, inch upon inch of that throbbing tool sinking past ", 0, "'s painted lips, bright lipstick ringed mid-way down that shaft."], "322.char": "RamGirl... alt under conditions: Futa enemies toggled: OFF, ", "322": ["Haaa. Yes, you're still 'fighting'. Keep it up and my cock might start 'losing' to you~"], "322.1.char": "Ram<PERSON><PERSON><PERSON>", "322.1": ["Haaa. Yes, you're still 'fighting'. Keep it up and my body might start 'losing' to you~"], "323.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "323": ["As that maw tries fruitlessly to swallow, the pressure at the throat sends flashes of white through the body."], "323.1.char": "Narration", "323.1": [0, " finds herself so embedded up between plush thighs that it's depriving her of air."], "324.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "324": ["Not content to lose, ", 0, " clasps her hands around the un-swallowed length at the base with twisting-jerking motions, anything to coax out a thick load."], "324.1.char": "Narration", "324.1": ["Not content to lose, ", 0, " locks her arms around the thighs and stuffs her lapping tongue in again, again, again."], "325.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "325": ["Every shudder, every sing-song moan driven out from the dick-girl only reinforces that she's doing something right. She's winning, she's in control."], "325.1.char": "Narration", "325.1": ["Every shudder, every sing-song moan coaxed from the sheep-lady only reinforces that she's doing something right. She's winning, she's in control."], "326.char": "Ram<PERSON><PERSON><PERSON>", "326": ["Haaau, you're this eager to make me cum, you slutty brat? Take it slow."], "327.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "327": ["Pre-cum wells up against bulging cheek, the mellow saltiness of it mixes in as tongue swirls about it. Around and around. Shlp, shlick, shlup."], "327.1.char": "Narration", "327.1": ["Feminine juices spill upon the lapping tongue as it swirls about, around and around. Shlp, shlick, shlup."], "328.char": "Ram<PERSON><PERSON><PERSON>", "328": ["That's- hhhf- enough, you musk-drunk bitchnnh?!"], "329.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "329": ["In an instant, jets of hot, steamy cream coat ", 0, "'s palate in cloying stickiness, pulses and throbs cramming shot after shot more inside."], "329.1.char": "Narration", "329.1": ["In an instant, stockings-clad legs clamp down on either side of ", 0, ", a gush of fem-cum splattering the bridge of her nose."], "330.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "330": ["A sudden hair-tug forces the cum-spewing tip at the back of the throat, gooey heat cramming that gullet full as the Ram-bull's nuts tense up to squeeze out the payload."], "330.1.char": "Narration", "330.1": ["A sudden hair-tug forces ", 0, " deep into that muff, bathing her face in the heat of the afterglow."], "331.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "331": ["It keeps coming and coming, drowning that smug-sucking face in a mind-hazing, corruption-laced sea of creamy white."], "331.1.char": "Narration", "331.1": ["The slick, corruptive fluids keep coming, drowning that cunt-slurping girl in a mind-hazing sheen of clear fluid."], "332.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "332": ["Instinctual, shaky thrusts drive in the tip. Copious nut spills down ", 0, "'s chin as her throat struggles to wring each fresh batch down."], "332.1.char": "Narration... alt under conditions: Player has ram-makeup, ", "332.1": ["If any trace of 'human' remained, the transformation-inducing monster-girl juices spilling from those girly features would make short work of it."], "332.1.1.char": "Narration", "332.1.1": ["Yellowy-orange lipstick smeared all over, ", 0, " remains trapped there, transformation-inducing monster-girl juices pouring down those girly features."], "333.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "333": ["Every drop makes its way in or is dutifully licked up, even as steam rises from her nose, desperate huffs to get a lungful of fresh air."], "333.1.char": "Narration", "333.1": ["Every drop seeps its way in or is dutifully licked up. Steam bathes that pretty face that desperately huffs to get a lungful of fresh air."], "334.char": "RamGirl... alt under conditions: Player has ram-horns, ", "334": ["Look, your horns are finally coming in. Softer skin, too? Just think, every ounce of monster taint I add to your frame is another girl you've saved."], "334.1.char": "Ram<PERSON><PERSON><PERSON>", "334.1": ["Looks like you've gotten a bit thicker around the hips. Bigger horns? Softer skin? Just think, every ounce of monster taint I add to your frame is another girl you've saved."], "335.char": "Rules", "335": ["Saved? Whuh... I was saving... people?"], "336.char": "RamGirl... alt under conditions: Futa enemies toggled: OFF, ", "336": ["<PERSON><PERSON><PERSON>. You're an addict already, aren't you? You're kneading at your belly. Did it taste that good?"], "336.1.char": "Ram<PERSON><PERSON><PERSON>", "336.1": ["<PERSON><PERSON><PERSON>. You're an addict already, aren't you? A natural carpet muncher, I bet you revel in the taste."], "337.char": "Rules", "337": ["You tricked me into thinking I was trying to help people?! It's not addiction, you-"], "338.char": "Rules... alt under conditions: Futa enemies toggled: OFF, ", "338": ["This is just how I fight studs like you! Soon I'll be able to take you hilt-deep and you'll be the one addicted!"], "338.1.char": "Rules", "338.1": ["This is just how I fight girls! I'll turn you into an addict for steamy lesbian sex!"], "339.char": "RamGirl... alt under conditions: Futa enemies toggled: OFF, ", "339": ["Hilted in your throat? Your snatch? Oh, I can't wait, I'll sleep every night balls-deep in your ewe cunt."], "339.1.char": "Ram<PERSON><PERSON><PERSON>", "339.1": ["Sure. I'll trap you under the bed covers against my cunt every night until you succeed."], "340.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "340": [0, " doesn't even question it, swallowing down a monster-girl's dangerous load. From today onwards she'll sleep in the same bed, she'll spend her days and nights honing her technique."], "340.1.char": "Narration", "340.1": [0, " doesn't even question it, licking her lips. From today onwards she'll sleep in the same bed, honing her technique."], "341.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "341": ["Those two spend every night with one's stake lodged firmly in the other's sex. If not sated with dick, the former human is quick to try and take control."], "341.1.char": "Narration", "341.1": ["Those two spend every night with bodies pressed together in lusty grinding. If not sated, the former human is quick to try and get on top."], "342.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "342": ["Discarding her weapons, she lewdly bullies any opponent she meets, paralyzing them with her perverse techniques whilst her partner's girthy spire pounds out any resistance."], "342.1.char": "Narration", "342.1": ["Discarding her weapons, she lewdly bullies any opponent she meets, paralyzing them with her perverse techniques in a tag-team with her partner."], "343.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "343": ["Even other monster-girls may wake up some days to the taste of raw jizz, to an oozing creampie or to fluffier hair and softer, more maidenly skin..."], "343.1.char": "Narration", "343.1": ["Even other monster-girls may wake up some days to the taste of pussy, to fluffier hair and softer, more maidenly skin from the sheepish taint..."], "344.char": "Rules... alt under conditions: Futa enemies toggled: OFF, ", "344": ["Aah. I really hated my old outfit, getting it all off just so I can cram a dick in was so awkward."], "344.1.char": "Rules", "344.1": ["Aah. I really hated my old outfit. Having to get it all off just so I can mount and scissor a girl with my bare sex..."], "345.char": "RamGirl... alt under conditions: Futa enemies toggled: OFF, ", "345": ["Isn't it? I can just lift up the flap and jam my shaft right back in you anytime I want now."], "345.1.char": "Ram<PERSON><PERSON><PERSON>", "345.1": ["Isn't it? I can just lift up the flap and have you gasping any time I like."], "346.char": "Rules... alt under conditions: Futa enemies toggled: OFF, ", "346": ["Whaaa<PERSON>, are your balls overfull again? Better hypnotize me quick or it's all going in my womb."], "346.1.char": "Rules", "346.1": ["Whaaa<PERSON>, are you getting horny again? Better hypnotize me quick or you'll be the one between my legs."], "347.CHOICE": ["Start Over"], "347.char": "Narration", "347": ["The journey comes to an end. Rumors spread of a 'Satyr Forest', a place where nobody's maidenhood is safe."]}, "badend/badendwerewolf.json": {"000.char": "Narration", "000": [0, " falls to the ground, only for a large paw to reach out and grab..."], "001.char": "WereWolf... alt under conditions: Playervariables.consent == false, ", "001": ["Tired, aren't we? Forget everything else and drink."], "001.1.char": "Narration", "001.1": [0, " was whisked away and they lived happily ever after (NSFW content off)."], "002.char": "Narration", "002": ["A sweet, revitalizing liquid flows forth. It's impossible to resist the motherly warmth, the chance to replenish lost energy."], "003.char": "<PERSON><PERSON><PERSON><PERSON>", "003": ["Good girl. Don't mind the blue tar, it keeps the milk in during the day."], "004.char": "Narration... alt under conditions: Player's bust size is == 4, Player's bust size is == 3, Player's bust size is == 2, ", "004": [0, "'s eyes haze over, unable to stop drinking. All her effort to resist the milk until now proves futile, warmth filling up her chest at an alarming rate, racking up cup sizes."], "004.1.char": "Narration", "004.1": [0, " is already used to the milk, clinging to the <PERSON><PERSON> for protection and safety. Milk fills ", 0, "'s already huge bosom."], "004.2.char": "Narration", "004.2": [0, "'s already bountiful chest starts to swell further, sating a deep-set addiction to drinking that milk, growing her chest bigger and bigger."], "004.3.char": "Narration", "004.3": [0, " feels a familiar tightness in her chest, those modest breasts starting to fill with tainted milk, building up to a weighty heft."], "005.CHOICE": ["Start Over"], "005.char": "<PERSON><PERSON><PERSON><PERSON>", "005": ["Let's head back. Close your eyes, drink and rest, I'll carry you..."]}, "load/loadconversationscat.json": {"000.char": "CatKnight", "000": ["Heeey, let's do it!"], "001.char": "Rules", "001": ["We just had sex."], "002.char": "CatKnight", "002": ["Sex? <PERSON>ya meant hunting!"], "003.char": "Rules", "003": ["Hunting for girls to pin and have sex with?"], "004.char": "CatKnight", "004": ["Exactly!! Now nya get it!"], "100.char": "Narration", "100": ["The cat-girl launches at ", 0, " from the other side of the room."], "101.char": "Rules", "101": ["Wh- how come you girls sometimes jump big?"], "102.char": "CatKnight", "102": ["My<PERSON>? You ever want to get to that realllllly neat spot at the top? But you keep climbing and falling!"], "103.char": "CatKnight", "103": ["You get frustrated! <PERSON><PERSON> get mad! Then you jump really high!"], "104.char": "Rules... alt under conditions: Player has cat-tail, ", "104": ["Then just jump higher in the first place..."], "104.1.char": "Rules", "104.1": ["<PERSON><PERSON><PERSON>, just jump higher to begin with!"], "200.char": "CatKnight", "200": ["Hmmmmmu."], "201.char": "Rules", "201": ["Mmmmmmmu?"], "202.char": "CatKnight... alt under conditions: Player has ram-horns, Player has fox-ears, ", "202": ["Cat inspection day! You pass!"], "202.1.char": "CatKnight", "202.1": ["I've never seen a kitty-girl with horns! It's a blessing of friendship."], "202.2.char": "CatKnight", "202.2": ["<PERSON><PERSON> don't have the 'cat-girl' energy. Smells like fox. We need to fuck it out of you!"], "203.char": "Rules... alt under conditions: Player has ram-horns, Player has fox-ears, ", "203": ["I'm nyo cat-girl at heart..."], "203.1.char": "Rules", "203.1": ["I'm nyot saying how I got them!"], "203.2.char": "Rules", "203.2": ["I... Have somewhere I need to get to!"], "300.char": "CatKnight", "300": ["<PERSON><PERSON><PERSON>, time out!"], "301.char": "Rules", "301": ["I only hit nya once! You girls have no endurance at all."], "302.char": "CatKnight", "302": ["We're trained purrfessionals, nyaknow. No hazard pay."], "303.char": "CatKnight", "303": ["We get in, if the target's a pain we get out! Easy."], "304.char": "CatKnight", "304": ["Take care of yaself. <PERSON><PERSON> never take it easy!"], "400.char": "CatKnight", "400": ["<PERSON><PERSON><PERSON>. Having fun as a cat-girl?"], "401.char": "Rules", "401": ["Claws aren't a good weapon..."], "402.char": "CatKnight", "402": ["Just close the distance, dummy!"], "403.char": "Rules", "403": ["If I get close the monsters are going to... Sex."], "404.char": "CatKnight", "404": ["Sounds good! Close the distance, nya!"]}, "load/loadconversationsfox.json": {"000.char": "FoxGirl", "000": ["You'll just sleep whenever you want, huh?"], "001.char": "FoxGirl", "001": ["Between you and me, we'd love to give you more freedom, you know?"], "002.char": "FoxGirl", "002": ["Yet you have this weird obsession like the vampire girl does! That behavior that endangers yourself and everyone..."], "003.char": "FoxGirl", "003": ["That's why we've called your condition the 'Curse'. Not to mention the other strange things with your body."], "004.char": "FoxGirl", "004": ["So, rest all you like! You're already one of us."], "100.char": "Narration", "100": [0, " sits staring at a broken shrine."], "101.char": "Rules", "101": ["That masonry..."], "102.char": "FoxGirl", "102": ["You said you made these, once?"], "103.char": "Rules", "103": ["Only helped. Not my style."], "104.char": "FoxGirl", "104": ["You're a menace. Are you going to fix it or not?"], "200.char": "FoxGirl", "200": ["Haaa. Nothing beats a sauna."], "201.char": "FoxGirl", "201": ["It's good for your skin! You sweat out corruption, too. It reduces stress..."], "202.char": "FoxGirl", "202": ["Hey. Stop staring. If you weren't my pet I'd be covering up."], "203.char": "FoxGirl", "203": ["I've tried to make my chest flatter but nothing works."], "204.char": "FoxGirl... alt under conditions: Player's bust size is > 2, ", "204": ["Before you ask, no, I did NOT drink that strange milk. You stay away from it, y'hear?"], "204.1.char": "FoxGirl", "204.1": ["You'd know, I thought I bound your chest up good but here you are flaunting your dumb sow tits! Stop. Drinking. That. MILK."], "300.char": "FoxGirl", "300": [0, ". Y'know, ", 0, "?"], "301.char": "FoxGirl", "301": ["You're a freaky kind of strong. Just like that vampiress, nobody would approach you."], "302.char": "FoxGirl", "302": ["If you weren't such a slut for taking it from monsters, that is."], "303.char": "FoxGirl", "303": ["Seriously, where'd you find that body? Nothing about you makes sense."], "304.char": "Rules", "304": ["It's my body."], "305.char": "FoxGirl", "305": ["Why so sure, didn't you say you were a guy? Are you lying?"], "306.char": "FoxGirl", "306": ["If you don't like being the damsel, is there something you want to protect? Hmm~"], "400.char": "FoxGirl", "400": ["Overcast again. Those mercs won't come out if there's even a threat of rain."], "401.char": "FoxGirl", "401": ["You know what that means. Back to my room, pet."], "402.char": "Rules", "402": ["Again? I can still taste the last night..."], "403.char": "FoxGirl", "403": ["What's that? Not going to obey?"], "404.char": "Rules", "404": ["... Be gentle?"]}, "load/loadconversationsfoxelite.json": {"000.char": "FoxGirl", "000": ["<PERSON><PERSON><PERSON>, did you forget I'd come back today? Prepare yourself."], "001.char": "FoxGirl... alt under conditions: Futa enemies toggled: OFF, ", "001": ["Here, it's your favorite, roll over~"], "001.1.char": "FoxGirl", "001.1": ["Here, it's your favorite, roll over~"], "002.char": "FoxGirl", "002": ["If I don't give you attention you start getting fighty. Needy thing."], "003.char": "FoxGirl", "003": ["Let's keep working hard on your training, pet."], "100.char": "Rules", "100": ["..."], "101.char": "Rules", "101": ["<PERSON> isn't home."], "102.char": "Rules", "102": ["Idiot. What am I supposed to do by myself?"], "103.char": "Rules", "103": ["..."], "104.char": "Rules", "104": ["I'm going for a walk."], "200.char": "FoxGirl", "200": ["<PERSON>nn, that was a good fuck. You've been getting better~"], "201.char": "Rules", "201": ["Next time, do it before we leave..."], "202.char": "FoxGirl", "202": ["I want them to see, show them to stay away from my girl. All dressed, let's go."], "203.char": "Rules", "203": ["... Your clothes barely cover anything. Shrine maidens have long sleeves."], "204.char": "FoxGirl", "204": ["<PERSON><PERSON>? You want to dress up for me in the old outfit? We can pretend you're one of the shrine maidens."], "205.char": "FoxGirl", "205": ["The fox has broken free, corrupting the pure and innocent maiden! Good, we'll do it when we get back."], "300.char": "FoxGirl", "300": [0, " has been out all morning! She'd better not have gotten captured again."], "301.char": "FoxGirl", "301": ["... So be it. She'll be back anyway."], "302.char": "FoxGirl", "302": ["There you are! Do I have to punish you for running off again?"], "303.char": "Rules", "303": ["I was thinking."], "304.char": "FoxGirl", "304": ["Nothing good comes when you're thinking. If you're gonna be like that..."], "305.char": "FoxGirl", "305": ["Here. Let's forget about the sex for today, rest on my lap."], "306.char": "FoxGirl", "306": ["Don't give me that look! Go for my shoulder then, we'll wait for the rain."], "400.char": "FoxGirl", "400": ["Puhaa! Nothing better than gin! Nobody out here stops to drink, they think I'll just fuck'em!"], "401.char": "Rules", "401": ["But you would fuck them."], "402.char": "FoxGirl... alt under conditions: Futa enemies toggled: OFF, ", "402": ["I'm hurt! Just because my cock's out doesn't mean it's not sealed!"], "402.1.char": "FoxGirl", "402.1": ["I'm hurt! Just because my cunt's wet doesn't mean it's not sealed!"], "403.char": "Rules", "403": ["Since when have those stopped you from fucking?"], "404.char": "FoxGirl", "404": ["You're all slutted up and hazy whenever I take it off, you probably don't see. It's a real hassle un-sticking them!"], "405.char": "FoxGirl", "405": ["Yea anyway. Heat of the drink's getting to me, gonna give me some sloppy?"]}, "load/loadconversationsram.json": {"000.char": "Ram<PERSON><PERSON><PERSON>", "000": ["My, my, ", 0, "."], "001.char": "Ram<PERSON><PERSON><PERSON>", "001": ["You're a heavy sleeper, as they say. Here, cheek pinch~"], "002.char": "Rules", "002": ["Mnnnh. Nnh..."], "003.char": "Ram<PERSON><PERSON><PERSON>", "003": ["<PERSON><PERSON>. You were in a savage state before I found you, how about sleeping in?"], "004.char": "Rules", "004": ["Still have... Things to do..."], "100.char": "Narration", "100": [0, "'s hand slips."], "101.char": "Ram<PERSON><PERSON><PERSON>", "101": ["If you keep rushing, you'll keep bumping into the wrong chords."], "102.char": "Ram<PERSON><PERSON><PERSON>", "102": ["You don't have to be so high-strung! Relax a little, harps weren't made to be played fast."], "103.char": "Rules", "103": ["I can't take forever on it."], "104.char": "RamGirl... alt under conditions: Player has ram-horns, ", "104": ["Mm? You can. You might not have horns but you're a proper sheep-girl, through and through. You'll live for a loooong time."], "104.1.char": "Ram<PERSON><PERSON><PERSON>", "104.1": ["Mm? You can. Do I need to tug your horns? A ram-girl like you will live a loooooong time, monster-girls don't have to worry."], "200.char": "Ram<PERSON><PERSON><PERSON>", "200": ["The stars are as pretty as always. Do you ever stop to look?"], "201.char": "Rules", "201": ["Obviously. How else would I know where I'm going?"], "202.char": "Ram<PERSON><PERSON><PERSON>", "202": ["Is that some sort of magic? My, I didn't expect that."], "203.char": "Rules", "203": ["It's not magic. Just look at the formations, they fly by every season."], "204.char": "Ram<PERSON><PERSON><PERSON>", "204": ["That sounds like magic to me, dear. Astral magic is a long-forgotten art, you know more than you let on!"], "300.char": "Ram<PERSON><PERSON><PERSON>", "300": [1, " you sure like to run out a lot, is something up?"], "301.char": "Rules", "301": ["It'd help if you didn't attack me when I'm out."], "302.char": "Ram<PERSON><PERSON><PERSON>", "302": ["Attack? That's not true at all."], "303.char": "Ram<PERSON><PERSON><PERSON>", "303": ["Sometimes you look like you'll hurt yourself, or you're putting yourself in danger. I'm just trying to protect you."], "304.char": "Rules", "304": ["If it erodes my will to fight, it's no good."], "305.char": "Ram<PERSON><PERSON><PERSON>", "305": ["Plus, I'd get lonely otherwise! Come relax with me more often!"], "400.char": "Ram<PERSON><PERSON><PERSON>", "400": ["You're not still mad about the weapons, are you? It's no use defending yourself with those."], "401.char": "Ram<PERSON><PERSON><PERSON>", "401": ["Just steady your hands and play your music properly and none of the other girls will hurt you."], "402.char": "Rules", "402": ["But then we end up fucking!"], "403.char": "Ram<PERSON><PERSON><PERSON>", "403": ["Then convey your emotions properly through the music. I bet you're just hypnotizing them into having sex with you, really..."], "404.char": "Rules", "404": ["... Only sometimes?"]}, "load/loadconversationsramelite.json": {"000.char": "Ram<PERSON><PERSON><PERSON>", "000": ["<PERSON>u? With how you sleep, I'd know something's troubling you."], "001.char": "Rules", "001": ["...?"], "002.char": "RamGirl... alt under conditions: Futa enemies toggled: OFF, ", "002": ["Here. I'll put my dick on your face."], "002.1.char": "Ram<PERSON><PERSON><PERSON>", "002.1": ["Here. I'll sit on your face."], "003.char": "Rules", "003": ["! ...? ..."], "004.char": "Ram<PERSON><PERSON><PERSON>", "004": ["There you go. Huff the warmth and the nightmares will go away."], "100.char": "Narration", "100": ["Around the corner, Voice is calling out ", 0, "'s name..."], "101.char": "RamGirl... alt under conditions: Futa enemies toggled: OFF, ", "101": ["Sssh. I know you love it but don't suck it so vigorously, she'll hear."], "101.1.char": "Ram<PERSON><PERSON><PERSON>", "101.1": ["Sssh. I know you love it but don't tongue me so vigorously, she'll hear how sloppy you are."], "102.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "102": ["For a moment, ", 0, " hesitates, in the shameful position of deepthroating a monster-girl's cock."], "102.1.char": "Narration", "102.1": ["For a moment, ", 0, " hesitates, in the shameful position of worshipping monster-girl cunt."], "103.char": "Ram<PERSON><PERSON><PERSON>", "103": ["Mmm? Feeling regret now? You're already one of us. You're already mine."], "104.char": "Narration... alt under conditions: Player has ram-horns, ", "104": ["With a sigh, Ram-girl hands take a hold of that long, girlish hair and tug the slurper back to her crotch."], "104.1.char": "Narration", "104.1": ["Soft yet firm hands grip around ", 0, "'s strong ram-horns and jam the slurper back in until horns dig into Satyr thighs."], "200.char": "Narration", "200": ["In the middle of the night, ", 0, " feels a discomfort. She tries to shuffle over to give herself some space..."], "201.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "201": ["But she finds a fat pole wedged deep into her, holding her in place no matter how she moves."], "201.1.char": "Narration", "201.1": ["But she finds a soft and heavy weight pinning her, holding her in place no matter how she moves."], "202.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "202": ["As she tries, it stirs up her depths, lacing a magical feeling of calm and drowsiness in her..."], "202.1.char": "Narration", "202.1": ["The fluffiness induces a calming, sleep-inducing magic."], "203.char": "Ram<PERSON><PERSON><PERSON>", "203": ["Got you. No neglecting your night duties, hun."], "204.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "204": ["...Driven home as the Satyr girl hugs her dicksleeve and crams in the wicked curve of her shaft."], "204.1.char": "Narration", "204.1": ["Arms wrap around the ram-tainted ", 0, " like she's a life-sized body pillow, keeping her secure."], "205.char": "Narration... alt under conditions: Futa enemies toggled: OFF, ", "205": ["Come morning, ", 0, " will be filled to the brim once again."], "205.1.char": "Narration", "205.1": ["She won't have any escape until morning..."], "300.char": "Ram<PERSON><PERSON><PERSON>", "300": ["You love to run, don't you? Not very suitable for our new lady."], "301.char": "Rules... alt under conditions: Player has ram-horns, ", "301": ["Aren't your horns for charging?"], "301.1.char": "Rules", "301.1": ["These horns are for charging."], "302.char": "RamGirl... alt under conditions: Player has ram-horns, ", "302": ["My, how brutish. Even I'm not that mean, am I? These horns show my dominance over you, who has no horns at all. Yet."], "302.1.char": "RamGirl... alt under conditions: Futa enemies toggled: OFF, ", "302.1": [0, ". -Your- horns are so I can get a firm grip to make you deepthroat my dick and force you to swallow my cum."], "302.1.1.char": "Ram<PERSON><PERSON><PERSON>", "302.1.1": [0, ". -Your- horns are so I can get a firm grip to have you eat me out and pleasure me like the good toy you are."], "303.char": "Ram<PERSON><PERSON><PERSON>", "303": ["When we lock horns with each other, it's a pleasantly intimate sort of competition that ends in a make-out contest."], "304.char": "Ram<PERSON><PERSON><PERSON>", "304": ["If you ever want to top, brush up on your kissing game~"], "400.char": "Ram<PERSON><PERSON><PERSON>", "400": ["Hey. Didn't you say you were with that drill-haired criminal? I heard she's super sexy now, let's catch and fuck her."], "401.char": "Rules", "401": ["Wha-?!"], "402.char": "Ram<PERSON><PERSON><PERSON>", "402": ["Mmm? You're trying to protect her? I'll have to make you huff my crotch some more."], "403.char": "RamGirl... alt under conditions: Futa enemies toggled: OFF, ", "403": ["I don't dislike when you put up a fight. Your ass is still so tight after every late night snugglefuck."], "403.1.char": "Ram<PERSON><PERSON><PERSON>", "403.1": ["I don't dislike when you put up a fight. You put a lot of power into your worship once you're under my spell."]}, "load/loadconversationsvoice.json": {"000.char": "Voice", "000": [1, ", wake uuuuuuup."], "001.char": "Voice", "001": ["Come on, come on~"], "002.char": "Rules", "002": ["I'll get up once you drain the 'girl' out of me."], "003.char": "Voice", "003": ["Not possible."], "004.char": "Rules", "004": ["Then I'm going back to sleep."], "100.char": "Rules", "100": ["You're a vampire. So, you don't like garlic? Stakes?"], "101.char": "Voice", "101": ["I told you this before, I'm not a vampire! I'm <PERSON><PERSON>! With vampire in it! I don't even know what this 'garlic' thing is! What do you mean steaks?!"], "102.char": "Rules", "102": ["..."], "103.char": "Rules", "103": ["<PERSON>h."], "104.char": "Voice", "104": ["Stop acting smug that we don't know what your dumb prehistoric things are!"], "200.char": "Voice", "200": ["You know, staring at the tower for so long is reeeeally bad luck."], "201.char": "Rules", "201": ["Weird superstition."], "202.char": "Voice", "202": ["It makes sense! Keeps young'uns from climbing up it."], "203.char": "Rules", "203": ["Wanna see me climb it?"], "204.char": "Voice", "204": ["Sometimes I can't tell if you're being serious or not, ", 0, ". Quit it with the dumb jokes!"], "300.char": "Voice", "300": ["Where have you been! I'll have to hire someone just to watch you, you stray cat!"], "301.char": "Rules", "301": ["Doesn't the <PERSON><PERSON> do that?"], "302.char": "Voice", "302": ["She does? I can never keep tabs on her either."], "303.char": "Voice", "303": ["WAIT! You're not having an affair with her, are you?!"], "304.char": "Rules", "304": ["How would it be an affair?!"], "305.char": "Voice", "305": ["Because you're my property, remember?!"], "306.char": "Rules", "306": ["..."], "400.char": "Voice", "400": ["<PERSON><PERSON><PERSON>, ", 1, "! Since you're ancient and all that,"], "401.char": "Voice", "401": ["There were big scaly lizards in your time, right?!"], "402.char": "Rules", "402": ["Hm? Sure."], "403.char": "Voice", "403": ["Really?! I bet they put up one hell of a fight, how can you live like that!"], "404.char": "Rules", "404": ["They died out in the end, though."]}, "quip/quipcomplaints.json": {"000.char": "None- tl_note :???", "000": [""]}, "quip/quiplevelselect1.json": {"000.char": "Voice", "000": ["Hey! You can start missions by stepping under the tables on the glowy lights."], "001.char": "Rules", "001": ["Why am I doing this for you, anyway? What if I don't wanna."], "002.char": "Voice", "002": ["Wha-?! Uh, um- Okay, see. You fell out of a big bad's pocket during our fight."], "003.char": "Voice", "003": ["That 'big bad' likely wants to steal you back into whatever void you came from."], "004.char": "Voice", "004": ["But I can't really fight much in this state now."], "005.char": "Voice", "005": ["I'd like to know more about you, too. I'd really like to work together. Please?"], "006.char": "Rules", "006": ["Hmmmm."], "100.char": "Voice", "100": ["I don't have time to clean up! Messages could come down the line at any time."]}, "quip/quipshrinefox.json": {"000.char": "FoxGirl... alt under conditions: Player has ram clothes top, ", "000": ["Seriously! What's with these doe-heads? They deserve some hypnosis-ing themselves!"], "000.1.char": "FoxGirl", "000.1": ["Great, more of you sheep. Just what we needed, ugh-"], "001.char": "FoxGirl", "001": ["You're not with them? Why not have them chase after you instead?"], "002.char": "FoxGirl", "002": ["No, starting a fight will make things worse."], "100.char": "FoxGirl... alt under conditions: Player has ram clothes top, Player has ram-horns, ", "100": ["Get these cloud-brains off the porch and we can talk!"], "100.1.char": "FoxGirl", "100.1": ["You're one of them, aren't you? Get your friends out of here!"], "100.2.char": "FoxGirl", "100.2": ["Are you their leader? Do you idiots even have a leader?"], "200.char": "FoxGirl", "200": ["I tried to possess one of them but they came right back afterwards..."], "201.char": "FoxGirl... alt under conditions: Player has ram clothes top, ", "201": ["I can't use hypnosis. I'm a fox. Duh."], "201.1.char": "FoxGirl", "201.1": ["I can't use hypnosis, duh. You've got that swirly purple thing, can't you do it?"], "300.char": "FoxGirl... alt under conditions: Playervariables.queststart == false, ", "300": ["C'mere, let's talk!"], "300.1.char": "FoxGirl", "300.1": ["You cleared out the Rams?"], "400.char": "FoxGirl... alt under conditions: Playervariables.queststart == false, ", "400": ["Wha- the hell?! Get out! Don't come back until you're done!"], "400.1.char": "FoxGirl", "400.1": ["The hell?! You just waltz in here and start stripping people?! Get out!"]}, "quip/quipshrineram.json": {"000.char": "RamGirl... alt under conditions: Player has ram-horns, Player has fox-ears, ", "000": ["The shrine isn't protecting her from 'brainwashing', she's just being hyperbolic."], "000.1.char": "RamGirl... alt under conditions: Player has fox-ears, Player has cat-ears, ", "000.1": ["Weren't you that new convert? My, aren't you elegant."], "000.1.1.char": "Ram<PERSON><PERSON><PERSON>", "000.1.1": ["Fox-ears...? Did a fox get onto you, sister? Are you from a shrine? How unusual."], "000.1.2.char": "Ram<PERSON><PERSON><PERSON>", "000.1.2": ["Kyaa- Cat-girl-ram! It's like a labour of our love, so adorable!"], "000.2.char": "Ram<PERSON><PERSON><PERSON>", "000.2": ["Two Fox-girls in one place? <PERSON><PERSON><PERSON>, would you like to join us?"], "100.char": "RamGirl... alt under conditions: Player has ram clothes top, Player has fox-ears, ", "100": ["I tried to charm her. She recovered right away..."], "100.1.char": "Ram<PERSON><PERSON><PERSON>", "100.1": ["Hypnosis works better on <PERSON><PERSON>. It pulls them right to us."], "100.2.char": "Ram<PERSON><PERSON><PERSON>", "100.2": ["You're better than her, aren't you? You'll be a good girl for us, right?"], "200.char": "RamGirl... alt under conditions: Player has ram clothes top, Player has fox-ears, ", "200": ["It's dangerous here! There's a dangerous group fighting out here."], "200.1.char": "Ram<PERSON><PERSON><PERSON>", "200.1": ["You'll help her see reason, won't you, sister?"], "200.2.char": "Ram<PERSON><PERSON><PERSON>", "200.2": ["Haaaa. Stroking a cute fluffy-eared girl all over... Come here, you!"], "300.char": "RamGirl... alt under conditions: Player has ram-horns, Player has fox-ears, ", "300": ["It's safer to stay with the herd. Fox-girls are notorious loners."], "300.1.char": "Ram<PERSON><PERSON><PERSON>", "300.1": ["My, I haven't seen you around. Is this your first time?"], "300.2.char": "Ram<PERSON><PERSON><PERSON>", "300.2": ["Are you her friend? Let's get some sheep in you, too~"], "400.char": "RamGirl... alt under conditions: Player has ram-horns, Player has fox-ears, Player has cat-ears, ", "400": ["We need to be somewhere else? What do the others think?"], "400.1.char": "Ram<PERSON><PERSON><PERSON>", "400.1": ["We should fall back? Mmm. If you  insist, we'll have to."], "400.2.char": "Ram<PERSON><PERSON><PERSON>", "400.2": ["Girls, do you think this fox will be more compliant?"], "400.3.char": "Ram<PERSON><PERSON><PERSON>", "400.3": ["You want to leave, kitty? The others still want to stay."], "500.char": "RamGirl... alt under conditions: Player has ram-horns, Player has fox-ears, Player has cat-ears, ", "500": ["Our precious kitties were attacked? We'll all have to move out!"], "500.1.char": "Ram<PERSON><PERSON><PERSON>", "500.1": ["Such big horns... If we have to get going, that's fine."], "500.2.char": "Ram<PERSON><PERSON><PERSON>", "500.2": ["Such adorable fox-ears... Yes, we'll hunt you instead!"], "500.3.char": "Ram<PERSON><PERSON><PERSON>", "500.3": ["I couldn't refuse you, kitty. If the Cat-girls need us, we'll be there~"]}, "quip/quiptutorial1.json": {"000.char": "Voice", "000": ["Hey! We can talk while you're on the move like this. If you see a speech bubble, that person has something to say..."], "001.char": "Voice... alt under conditions: Player is using a touchscreen, ", "001": ["You can close the conversation by right-clicking within 1 tile of me or with the big 'X' buttons."], "001.1.char": "Voice", "001.1": ["You can close the conversation by holding down, tapping with camera mode or with the big 'X' buttons."], "100.char": "Voice... alt under conditions: Player is using a touchscreen, ", "100": ["With this weapon, you can also 'ATTACK' for 1 damage! Hold down left click to repeatedly move."], "100.1.char": "Voice", "100.1": ["With this weapon, you can also 'ATTACK' for 1 damage! Get next to the bush then tap it."], "101.char": "Voice", "101": ["Also! That slab behind the conversation menu records messages, hover over it and drag or scroll wheel it to see them!"], "1020.char": "Voice", "1020": ["... I'm not in the way of the upper dialogue menu, am I?"], "200.char": "Voice", "200": ["You also have a SWIM speed of 2. Water will reduce your fall speed, so it's kinda like floating?"], "201.char": "Voice", "201": ["You'll also have noticed, you can 'RUN' 4 tiles or jump 2 tiles in a turn, after which enemies take their turns."], "300.char": "Voice", "300": ["Trees, rooves, pitons and roots are just some of the things you can climb to avoid falling."]}, "quip/quiptutorial2.json": {"000.char": "Voice", "000": ["Every time you move, everyone else gets to move too! These crates will heal if they get a turn, use these 'Skills' to beat them before they heal!"], "001.char": "Voice... alt under conditions: Player is using a touchscreen, ", "001": ["That dagger Skill is 'FAST' meaning it doesn't take a turn! Left click the dagger and drag it with your mouse like a playing card."], "001.1.char": "Voice", "001.1": ["That dagger Skill is 'FAST' meaning it doesn't take a turn! Touch that dagger and drag it into the middle like it's a playing card."], "002.char": "Voice", "002": ["The red square shows where it hits when you drop it, use it together with your basic attack to do 2 damage in one turn."], "100.char": "Voice", "100": ["The red weight icon on the axe shows it's a HEAVY attack, so enemies will move before the axe hits."], "101.char": "Voice", "101": ["Use the axe, dagger and your basic attack together to do 4 damage before the crate can heal!"], "200.char": "Voice", "200": ["The attacks you've used so far have been FORCE or PRECISION, but torches do ENERGY damage."], "201.char": "Voice", "201": ["Torches have 0 damage, but they damage obstacles. They're also CONSUMABLE, it has a special slot!"], "300.char": "Voice", "300": ["Sometimes a target or enemy can block damage from one, two, three or four sides! If you're reading this, this is an error."], "301.char": "Voice", "301": ["So you'll need the Scimitar's odd attack range to get through, you need to be more 'above' it than to its left to hit it."], "400.char": "Voice", "400": ["Some skills have movement like <PERSON>, you can see where you'll move outlined in green."], "401.char": "Voice", "401": ["The nice thing about movement skills is they ignore a lot of debuffs and will move through bushes."], "500.char": "Voice... alt under conditions: Player is using a touchscreen, ", "500": ["Right click the torch to scroll through your consumables! <PERSON><PERSON> should be there."], "500.1.char": "Voice", "500.1": ["Use camera mode and then tap the torch to scroll through your consumables! <PERSON><PERSON> will be there."], "501.char": "Voice", "501": ["A Piton can attach to the background or a cliff to let you climb up to almost anywhere."], "600.char": "Voice", "600": ["Lastly, because I haven't gotten to bully you yet, we've got these ones that will actually attack you!"], "601.char": "Voice", "601": ["The attacks deal 0 damage but they inflict KNOCKBACK. You need BLOCK to negate it."], "602.char": "Voice", "602": ["The Buckler provides PURE block that blocks all damage types, other block types only protect against certain damages."], "700.char": "Voice", "700": ["'Windstep' here is pretty simple, it just sets your fall speed to 0 for a turn. You can try it instead of pitons."], "701.char": "Voice", "701": ["By the way, you can see your BLOCK around you while it's active. All sources of BLOCK lose 1 strength after the enemy's turn."], "702.char": "Voice", "702": ["If you ever run out of BLOCK, you'll take all of the debuffs from the attack, though it might reduce the damage you take!"], "800.char": "Voice", "800": ["Well done! One last thing: normally you won't find items lying around to pick up and re-use."], "801.char": "Voice", "801": ["But you get your non-consumable items back at the end of each screen, so build up a good supply of them!"], "802.char": "Voice", "802": ["Until you get permanent items, they'll only stay until the end of that particular run, however."]}, "quip/quipvoicevillage.json": {"200.char": "Voice", "200": ["Hey! Head inside and we'll conduct the war room meeting."], "300.char": "Voice", "300": ["The Gallery house I left you is in pretty good condition, isn't it?"], "301.char": "Voice", "301": ["I'll be here for a while so make yourself at home there."], "000.char": "Voice", "000": ["Hot springs? Weren't they up the foggy path up-left from here?"], "001.char": "Voice... alt under conditions: Player's bust size is > 2, ", "001": ["I tried once before but it was all horrid, blinding steam. No thanks!"], "001.1.char": "Voice", "001.1": ["You look like you've got corruption to vent!"], "100.char": "Voice", "100": ["I've opened up the path out to the right."], "101.char": "Voice", "101": ["You're really leaving, aren't you? ... I'll be OK, promise!"]}, "quip/tutorialmission1catknightquips.json": {"000.char": "CatKnight", "000": ["<PERSON><PERSON><PERSON>? You're a new face. Cute."], "100.char": "CatKnight", "100": ["You. Wanna join us? Being a cat beats being lost!"], "101.char": "Rules", "101": ["That's a weird thing to say to a stranger? Also, you're in the way."], "200.char": "CatKnight", "200": ["<PERSON><PERSON><PERSON>! You're ruining our day off, girls like you need spankin'!"]}, "quip/tutorialmission3catknightquips.json": {"000.char": "CatKnight", "000": ["<PERSON><PERSON> would be much cuter if ya stopped acting tough, swinging that around!"], "100.char": "CatKnight", "100": ["What're nya gonna do with those paws? Massage us? Give my ass a gropin' too~"], "200.char": "CatKnight", "200": ["Playing hard to get? I'm nyot as soft as the others!"], "300.char": "CatKnight", "300": ["Looking good there, newbie! You'll be one of us in no time."], "400.char": "CatKnight", "400": ["Hypno-beads? Wow, that puss's goin' all in on you!"], "401.char": "CatKnight", "401": ["You didn't know? Every bead that's pulled out will make you way more suggestible!"], "402.char": "CatKnight", "402": ["If your tail were to suddenly twitch and pull them free, you'd be all ours~"], "500.char": "CatKnight", "500": ["Cute tail! Makes you wanna grab it and pull ya in for dry-humping."], "600.char": "CatKnight", "600": ["Looking real hot and bothered there, newbie. About to give in?"], "700.char": "CatKnight", "700": ["Now that ya put the dumb stick down we can get real friendly."]}, "quip/worldfoxgirlquips.json": {"000.char": "FoxGirl- tl_note :First Meeting... alt under conditions: Player has seen <PERSON> Ending 0, ", "000": ["Once I've caught you I won't let you escape!"], "000.1.char": "FoxGirl... alt under conditions: Player has fox-tail, ", "000.1": ["Wandering around without your fox-tail, you need some re-training!"], "000.1.1.char": "FoxGirl", "000.1.1": ["Get down on all-fours, fox, it's training time~"], "100.char": "FoxGirl- tl_note :First meeting ram-girl ELITE... alt under conditions: Futa enemies toggled: OFF, ", "100": ["I spy a new cocksleeve. Smells good..."], "100.1.char": "FoxGirl", "100.1": ["I spy a new pet. Smells good..."], "101.char": "FoxGirl", "101": ["Your weapon can't get through my barrier."], "200.char": "FoxGirl- tl_note :Hypnosis used on fox-girl", "200": ["Keep that evil magic away from me!"], "600.char": "FoxGirl- tl_note :Special ready case for armpit scene", "600": ["With this possession, you can't escape. I'll just lift my arm, and..."], "700.char": "FoxGirl- tl_note :After armpit scene", "700": ["The salty fog burns your sinuses, yea?"], "800.char": "FoxGirl- tl_note :After armpit scene variant1", "800": ["Are you even trying? Here, I'll guide your nose."], "900.char": "FoxGirl- tl_note :After armpit scene variant2... alt under conditions: Player has POSSESSION debuff, ", "900": ["Are you fumbling on purpose? Here, lick~"], "900.1.char": "FoxGirl", "900.1": ["Good girl, staying put for me."], "1000.char": "FoxGirl- tl_note :After knotting player, elite... alt under conditions: Futa enemies toggled: OFF, Player has fox-tail, ", "1000": ["Nnnh, it keeps tugging my knot in, taking all my cum. Human pussy is the best~"], "1000.1.char": "FoxGirl", "1000.1": ["Haaa, spilling my sticky juices all over. You're mine now..."], "1000.2.char": "FoxGirl", "1000.2": ["Your fox-pussy is the best, it squeezes tight while I knock it up~"], "1100.char": "FoxGirl- tl_note :After pussy press scene, not elite... alt under conditions: Player has fox-tail, ", "1100": ["<PERSON>a. I'll take the seals off and we can stop fighting now, yeah?"], "1100.1.char": "FoxGirl", "1100.1": ["I'll let you go for now, fox-pet."], "1101.char": "FoxGirl... alt under conditions: Player has fox-tail, ", "1101": ["You're a real eager sub once those seals break your inhibitions."], "1101.1.char": "FoxGirl", "1101.1": ["Drop that weapon and come home any time."], "1400.char": "FoxGirl- tl_note :After TF-ing player - foxears? applied to nearby fox?", "1400": ["It's starting to take hold. You'll become a shrine maiden."], "1500.char": "FoxGirl- tl_note :After TF-ing player - foxtail? applied to nearby fox?", "1500": ["How does it feel to be a fox? Good, right?"], "1600.char": "FoxGirl- tl_note :After TF-ing player - fox hair? applied to nearby fox?", "1600": ["That hairstyle befits a real shrine maiden~"], "1800.char": "FoxGirl- tl_note :player is POSSESSED... alt under conditions: Player has POSSESSION debuff, ", "1800": ["The ghost left your body already? Bo<PERSON>."], "1800.1.char": "FoxGirl", "1800.1": ["How's it like to be bound to my shrine?"], "1900.char": "FoxGirl- tl_note :player is POSSESSED (elite)... alt under conditions: Player has POSSESSION debuff, ", "1900": ["Trying to resist possession? Let's try again!"], "1900.1.char": "FoxGirl", "1900.1": ["Good, let the ghosts take control. They'll make you behave."], "2000.char": "FoxGirl- tl_note :player has SLUT seal applied... alt under conditions: Player has SLUT fox-seal > 0, ", "2000": ["Put that seal back on you slut!"], "2000.1.char": "FoxGirl", "2000.1": ["That seal's to get you eager."], "2100.char": "FoxGirl- tl_note :player has SEAL seal applied... alt under conditions: Player has SEAL fox-seal > 0, ", "2100": ["You can't just rip those seals off!"], "2100.1.char": "FoxGirl", "2100.1": ["This seal will put your body into heat!"], "2200.char": "FoxGirl- tl_note :player has OBEY seal applied... alt under conditions: Player has OBEY fox-seal > 0, ", "2200": ["Obey me, damnit!"], "2200.1.char": "FoxGirl", "2200.1": ["This seal'll make you spread your legs for me."], "2300.char": "FoxGirl- tl_note :player is vulnerable to fox", "2300": ["Now you have all the seals, obey me and get on the floor! We're having sex!"], "5000.char": "FoxGirl- tl_note :RANDOM... alt under conditions: Player has POSSESSION debuff, ", "5000": ["You don't know how to use that body, give up and let the spirits in."], "5000.1.char": "FoxGirl", "5000.1": ["You look adorable with the ghost-fox in control."], "5100.char": "FoxGirl- tl_note :RANDOMplayer has catbeads", "5100": ["All Cat-Girls should have their tails tied up like that!"], "5200.char": "FoxGirl- tl_note :RANDOM, player has rampants", "5200": ["I'm not into the 'fluffy cloud' stockings, they'll get damp from the sweat."], "5300.char": "FoxGirl- tl_note :RANDOM, player has ramtop... alt under conditions: Player's bust size is > 2, ", "5300": ["That top's too big! Why're you letting the Rams toy with you?!"], "5300.1.char": "FoxGirl", "5300.1": ["Your cow-sized tits are disgraceful, wear something less revealing!"], "5400.char": "FoxGirl- tl_note :RAND<PERSON>, player has horns, again", "5400": ["I might keep your dumb horns to use as handlebars."], "5500.char": "FoxGirl- tl_note :RANDOM Player has size 3 or 4 tits", "5500": ["You look like a prostitute with tits like that!"], "5600.char": "FoxGirl- tl_note :RANDOM Player has size 4 tits", "5600": ["Do something about your dumb fat sow tits already!"], "5700.char": "FoxGirl- tl_note :RANDOM Player is fox class DIFFERS FROM WOLDRAMGI<PERSON><PERSON>UIPS", "5700": ["I don't remember giving you permission to wander out here, servant!"], "5800.char": "FoxGirl- tl_note :RANDOM Player has paws", "5800": ["Those mitts are fitting for a pet like you!"], "5900.char": "FoxGirl- tl_note :RANDOM cat-tail", "5900": ["I've got some seals to cure that cattiness of yours!"], "6000.char": "FoxGirl- tl_note :RANDOM fox-tail", "6000": ["You're already ours, why're you making a fuss?"], "6100.char": "FoxGirl- tl_note :RANDOM Player's resistance is below 10", "6100": ["If you're so tired, why not let these ghosts take over for you?"], "6200.char": "FoxGirl- tl_note :RANDOM Player's resistance is below 5", "6200": ["Any moment now, I'll seal you to my shrine."], "6300.char": "FoxGirl- tl_note :RANDOM", "6300": ["Come on, give up! That body isn't even yours."], "6400.char": "FoxGirl- tl_note :RANDOM (ranked)... alt under conditions: Futa enemies toggled: OFF, ", "6400": ["Just put these seals on an' you'll be sucking my dick in no time, bitch~"], "6400.1.char": "FoxGirl- tl_note :RANDOM (ranked)", "6400.1": ["Just put these seals on an' you'll be slurping my cunt in no time, bitch~"], "6500.char": "FoxGirl- tl_note :RAND<PERSON> fox-hair", "6500": ["You've got our symbol, shouldn't you be more obedient?"], "6600.char": "FoxGirl- tl_note :RANDOM harpy wings", "6600": ["How am I supposed to compete with those dumb sow harpies?!"], "6700.char": "FoxGirl- tl_note :RANDOM2 harpy wings", "6700": ["You don't even have hands anymore! Just let me fix you!"], "999999.char": "FoxGirl", "999999": ["aaaa"]}, "quip/worldramgirlquips.json": {"000.char": "RamGirl- tl_note :First Meeting", "000": ["What a cute lost lamb. Why not slow down for a bit? We don't bite."], "100.char": "RamGirl- tl_note :First meeting ram-girl ELITE... alt under conditions: Futa enemies toggled: OFF, ", "100": ["Pay close attention to the sway of my hips. There's more than one 'pendulum' to follow~"], "100.1.char": "Ram<PERSON><PERSON><PERSON>", "100.1": ["Pay close attention to the sway of this pendulum, my hips..."], "200.char": "RamGirl- tl_note :Hypnosis used on ram-girl", "200": ["<PERSON><PERSON><PERSON><PERSON>. You're so adorable, playing with my heart. I'm gonna squeeeeeeze you~"], "300.char": "RamGirl- tl_note :Hypnosis used on ram-girl BUT PLAYER IS KEMONOMIMI.", "300": ["Kyaaa! I want to snuggle with you and your adorable fluffiness so bad!"], "400.char": "RamGirl- tl_note :Hypnosis used on ram-girl (elite)", "400": ["Cute brats like you need humping until they're all sticky..."], "500.char": "RamGirl- tl_note :First meeting of non-elite with kemonomimi player <PERSON><PERSON>", "500": ["Wow! A big fluffy mofu-fox, free-roaming? Come heeeere~"], "600.char": "RamGirl- tl_note :First meeting of non-elite with kemonomimi player CAT", "600": ["Mmm? Don't you belong to my friend? I don't recognize you..."], "601.char": "Ram<PERSON><PERSON><PERSON>", "601": ["No way?! A real, unclaimed wild-cat? I'll feed you lots and tame you!"], "700.char": "RamGirl- tl_note :First meeting with player with horns", "700": ["My, someone's looking dashing. Let's lock horns and gaze deep~"], "800.char": "<PERSON>Girl- tl_note :After kissing player", "800": ["<PERSON><PERSON>ah. Your lips are really soft, lamb-chan."], "900.char": "RamGirl- tl_note :After kissing player variant", "900": ["Thanks for the meal~ You're quite a treat."], "1000.char": "RamGirl- tl_note :After kissing player, elite... alt under conditions: Player has ram-horns, ", "1000": ["Fwuah. I made you gulp down so much of my spit, you'll become just like me in no time."], "1000.1.char": "Ram<PERSON><PERSON><PERSON>", "1000.1": ["Fwuah. You like it when I drool down your throat, don't you?."], "1100.char": "RamGirl- tl_note :After kissing player, elite VARIANT... alt under conditions: Player has ram-horns, ", "1100": ["You writhe around all cutely when I'm stuffing your mouth~"], "1100.1.char": "Ram<PERSON><PERSON><PERSON>", "1100.1": ["You slutty kiss-addict. You'll make out with me all day, won't you?"], "1101.char": "RamGirl... alt under conditions: Player has ram-horns, ", "1101": ["Every time we kiss, your body is re-wiring itself to be a better bitch~"], "1101.1.char": "Ram<PERSON><PERSON><PERSON>", "1101.1": ["You'll make me big and strong and cling to me for protection. You'll become my woman."], "1200.char": "<PERSON>Girl- tl_note :After hugging player", "1200": ["<PERSON><PERSON><PERSON>. I got carried away, I forgot to kiss you!"], "1300.char": "RamGirl- tl_note :After petting player", "1300": ["Haaah. I just want to fluff you all day! Let's kiss until you're mine."], "1400.char": "RamGirl- tl_note :After futa-ing player... alt under conditions: Futa enemies toggled: OFF, ", "1400": ["Hnnh. If I could stuff your body, I'd have done it, but..."], "1400.1.char": "Ram<PERSON><PERSON><PERSON>", "1400.1": ["All this close contact will soften up your skin and help fill you with sheepishness~"], "1401.char": "RamGirl... alt under conditions: Futa enemies toggled: OFF, ", "1401": ["You're now completely marked in my corruption, kuf<PERSON><PERSON>."], "1401.1.char": "Ram<PERSON><PERSON><PERSON>", "1401.1": ["Give in and we can go back to my place, okay?"], "3000.char": "RamGirl- tl_note :Inflicted: Player clothes removed.... alt under conditions: Player has ram-horns, ", "3000": ["Now there's no awkward cloth to get between our love."], "3000.1.char": "Ram<PERSON><PERSON><PERSON>", "3000.1": ["Your old clothes don't suit your new self at all."], "3100.char": "RamGirl- tl_note :Inflicted: Player clothes removed (ranked).", "3100": ["That tight, snug body of yours, all exposed~"], "3200.char": "RamGirl- tl_note :Inflicted: Player clothes swapped to RAM.", "3200": ["You look softer already! You don't need a fighter's body."], "3300.char": "RamGirl- tl_note :Inflicted: Player clothes swapped to RAM (ranked)", "3300": ["These clothes suit you sooo much better. I can just tug this thong down~"], "3400.char": "RamGirl- tl_note :Inflicted: Player make-up", "3400": ["Even your lips are stained in my color. If you kissed a girl now..."], "3500.char": "RamGirl- tl_note :Inflicted: Player make-up (from ranked ram)", "3500": ["<PERSON><PERSON>. All this kissing has left my mark on you."], "3600.char": "RamGirl- tl_note :Inflicted: Player horns", "3600": ["Your horns grew in so big and so quick. You're a natural Ram-girl~"], "3700.char": "RamGirl- tl_note :Inflicted: Ram health 6 or higher", "3700": ["You're such an obedient girl. Give me all your strength~"], "3800.char": "RamGirl- tl_note :Inflicted: Ranked Ram health 7 or higher... alt under conditions: Futa enemies toggled: OFF, ", "3800": ["You're just giving away all your vitality, aren't you? It makes my dick throb~"], "3800.1.char": "Ram<PERSON><PERSON><PERSON>", "3800.1": ["Keep submitting to my kiss. I'll take you into wedlock, we can have a whole harem~"], "5000.char": "RamGirl- tl_note :RANDOM", "5000": ["Take it easy, lost lamb. If you keep fighting you'll only get hurt."], "5100.char": "RamGirl- tl_note :RANDOMplayer has catbeads", "5100": ["<PERSON><PERSON>. That's one of the cursed items the kitties stuffed in you, is it not?"], "5200.char": "RamGirl- tl_note :RANDOM, player has rampants", "5200": ["Mmm. These stockings suit you much better than those canvas pants. Now you look as soft as a cloud!"], "5300.char": "RamGirl- tl_note :RANDOM, player has ramtop... alt under conditions: Player's bust size is > 3, Player's bust size is > 2, Player's bust size is > 1, ", "5300": ["Our tops are rather breezy, aren't they? We have to help those budding tits grow!"], "5300.1.char": "Ram<PERSON><PERSON><PERSON>", "5300.1": ["How's the dress, lamb-chan? Your old clothes would've just hurt your chest!"], "5300.2.char": "RamGirl... alt under conditions: Player has ram-horns, ", "5300.2": ["You're looking almost motherly now! The dress shows just the right amount of nip-slip."], "5300.2.1.char": "Ram<PERSON><PERSON><PERSON>", "5300.2.1": ["I can barely tell you apart from the rest of us! You'll be joining us soon."], "5300.3.char": "Ram<PERSON><PERSON><PERSON>", "5300.3": ["You're going to ruin that lovely dress with those cow-like tits!"], "5400.char": "RamGirl- tl_note :RAND<PERSON>, player has horns, again", "5400": ["I wonder if we can angle our horns juuuuust right so that they'll be stuck. We'd be in liplock all night~"], "5500.char": "RamGirl- tl_note :RANDOM Player has big tits", "5500": ["Stuffing you against my chest is going to be hard with your big tits. We'll have to press chest-to-chest as we kiss!"], "5600.char": "RamGirl- tl_note :RANDOM Player has really big tits", "5600": ["Wow, you really let yourself go on the milk, I feel jealous."], "5700.char": "RamGirl- tl_note :RANDOM Player has ram horns", "5700": ["What a delightful curve your horns have~"], "5800.char": "RamGirl- tl_note :RANDOM Player has paws", "5800": ["Don't just pet down all the kitties, come give your big sis some chest-groping too~"], "5900.char": "RamGirl- tl_note :RANDOM cat-tail", "5900": ["Here, kitty kitty. You know us Ram-girl's take charge of the tigers, right? Come here and be mine~"], "6000.char": "RamGirl- tl_note :RANDOM fox-tail", "6000": ["The fox-girls are all sealed to shrines. To think that such a fluffy fox escaped!"], "6100.char": "RamGirl- tl_note :RANDOM Player's resistance is below 10", "6100": ["<PERSON><PERSON>chan, you're looking all worn out. Why not spend the night with me?"], "6200.char": "RamGirl- tl_note :RANDOM Player's resistance is below 5", "6200": ["You're all tuckered out. Let's get you into the warmth of my bust before your legs give out."], "6300.char": "RamGirl- tl_note :RANDOM", "6300": ["Come look deep into my eyes. We're not going to harm you~"], "6400.char": "RamGirl- tl_note :RANDOM (ranked)", "6400": ["Just follow the sway of my pendulum, become transfixed, insatiable..."], "6500.char": "RamGirl- tl_note :RANDOM fox-hair", "6500": ["Let's untie that, let your hair free. Nothing good will come from being a fox's pet."], "6600.char": "RamGirl- tl_note :RANDOM harpy wings", "6600": ["You need to stay out of the hills! The harpies are ruthless."], "6700.char": "RamGirl- tl_note :RANDOM2 harpy wings", "6700": ["Are you okay like that? We can't hold hands..."], "9999.char": "Ram<PERSON><PERSON><PERSON>", "9999": ["aaaa"]}, "meta": {"Icon_Path": null, "Translator_Comment": "No comment", "Game_Version": "V0.6.???"}, "misc_ui": {"DISCARD": "DISCARD", "REMOVE": "REMOVE", "petalmenutext": ["Skip All", "Skip <PERSON>", "Settings", "Go Back Once", "Go Back All"], "spec_enemy_text_array": ["Readied", "Flinch(!)Immune"]}, "mainmenu": {"progress": "Class:VAR1\n\nProgress:\n\nStages Cleared: VAR2 / VAR3\nTransformations: VAR4 / VAR5\nCGs: VAR6 / VAR7\nEndings: VAR8 / VAR9", "patreonbutton": "Support \nthe Game", "discordbutton": "Join the\n Community", "veiltextdict": {"noreset": "You cannot reset the save slot (VAR1) as it has no data.", "reset": "You are about to reset the save slot (VAR1) to the start. Continue?", "touchreset": "You are about to reset the save slot (VAR1) to the start. Continue?\nPress 'Yes' 3 times to confirm.", "start": "This save file (save slot VAR1) has no data on it yet. Would you like to start from the beginning?", "exit": "Close the application?", "loading": "Now loading...", "resetdone": "Save has been reset.", "patreon": "This will open a link to the game's Patreon page:\nhttps://www.patreon.com/moncurse\n\nHelp keep the game going!\n", "discord": "This will open a link to the official MonCurse discord server.\nYou can report bugs or just talk about games there.\n", "translationtools": "Translation\nTools", "crashprevention": "The game closed prematurely.\nIf your device has limited RAM click 'Yes' to enter low-performance mode.\n\nYou can also change this in the Settings."}, "yes": "Yes", "cancel": "Cancel"}, "specialmenu": {"fullscreenconfirm": "Please press this to confirm,\nelse it will revert back in:", "fullscreencancel": "Revert to <PERSON><PERSON>", "consentmessage": "The following game contains sexually explicit themes and content. Please ensure you are of legal age to access such content.\n\nThis game is in active development and may change over time. Characters shown are of legal age and legal consent.\n\nThis message will only show once."}, "maptext": {"areastringdict": {"101": "Mountains", "102": "Hills", "103": "Forest", "104": "Grassland", "105": "River", "0": "Fugitive's <PERSON><PERSON><PERSON>", "2": "Hotsprings", "1": "Fox's Shrine"}, "shortareastringdict": {"0": "Village", "2": "Hotsprings", "1": "Shrine", "T1": "T1", "T2": "T2", "T3": "T3", "T4": "T4", "T5": "T5", "T?": "T?", "Blocked": "Blocked", "Confirm?": "Confirm?", "End Expedition": "End Expedition", "Retry This Tile": "Retry This Tile", "Go Back": "<< Go Back"}, "queststringarray": ["Explore Shrine", "Defeat Rams", "Go To Shrine", "Return Home", "Route A11\n(WIP)", "OR\n??? Rams"], "namemissiondict": {"4": "A Long Walk", "11": "WIP"}, "chooselabelsarray": ["Ignore & Discard", "Accept & Continue", "Choose One Skill:", "Lasts until end of mission", "Class Skills:", "Fox-Girl - Choose Two Skills:"]}, "settingstextdict": {"Scrollcontainer/settings/cen0/VBoxContainer/CheckBox6": "Fullscreen (F11)", "Scrollcontainer/settings/cen0/VBoxContainer/CheckBox10": "Low RAM Compatibility Mode", "Scrollcontainer/settings/cen0/VBoxContainer/CheckBox8": "Faster Turns", "Scrollcontainer/settings/cen0/VBoxContainer/CheckBox2": "Camera Automatically Follows Player", "Scrollcontainer/settings/cen1/Mastervolume/volumelabel": "Master Volume: ", "Scrollcontainer/settings/cen2/Musicvolume/volumelabel": "Music Volume: ", "Scrollcontainer/settings/cen3/SFXvolume/volumelabel": "SFX Volume: ", "Scrollcontainer/settings/cen4/Ambiencevolume/volumelabel": "Ambience Volume: ", "Scrollcontainer/settings/qcontrol/quittomainmenu/mainmenu": "Quit to\n Main Menu", "Scrollcontainer/settings/fcontrol/forfeit/mainmenu": "Forfeit &\nReturn"}, "attacknames": {"Warp": "Warp", "Dash": "Dash", "Lance": "<PERSON>", "Sweep": "Sweep", "Pole": "Pole", "Axe": "Axe", "Dagger": "<PERSON>gger", "Torch": "<PERSON>ch", "Piton": "<PERSON><PERSON>", "Windstep": "Windstep", "Buckler": "<PERSON><PERSON>", "Reposition": "Reposition", "Scimitar": "Scimitar", "Pickaxe": "Pickaxe", "Struggle": "<PERSON><PERSON><PERSON>", "Shortbow": "Shortbow", "Pounce": "<PERSON><PERSON><PERSON>", "Scratch": "<PERSON><PERSON><PERSON>", "Heavenly Chord": "Heavenly Chord", "Sheep Song": "<PERSON><PERSON>", "Remove Seal": "Remove Seal", "Resonance": "Resonance", "Warpath": "Warpath", "Sense": "Sense", "Hypnosis": "Hypnosis", "Empower": "Empower", "Ruin": "<PERSON><PERSON>", "Juggernaut": "Juggernaut", "Shockwave": "Shockwave", "Ghost Bell": "<PERSON> Bell", "Plunge": "Plunge", "Milky Drain": "<PERSON><PERSON>", "Overflow": "Overflow", "Productivity": "Productivity", "Draining Kiss": "Draining Kiss", "Cum Seal": "Cum Seal", "Fox Seal": "Fox Seal", "Egg Bomb": "Egg Bomb", "Lance-II": "Lance-II", "Cursed Beads": "Cursed Beads", "Push": "<PERSON><PERSON>", "Bush Burning": "Bush Burning", "Torch Heat": "Torch Heat", "Suspicious Urn": "Suspicious Urn", "SturdyBoots": "SturdyBoots", "Cum Hazard": "Cum Hazard", "Milk Production": "Milk Production", "Milk": "Milk", "Milking": "Milking", "Facesit": "Facesit", "Lick": "Lick", "Kiss": "Kiss", "Petting": "Petting", "Hug": "<PERSON>g", "Grinding": "Grinding", "Hold": "Hold", "Bullying": "Bullying", "Knotting": "Knotting", "Smothering": "Smothering", "Knot Tug": "<PERSON>not <PERSON>g", "Cumming Inside": "Cumming Inside", "Pussy Press": "Pussy Press", "Grab": "<PERSON>rab", "Shredder": "Shredder", "Disrobe": "Disrobe", "Grope": "Grope", "Close Quarters": "Close Quarters", "Kitsune Possession": "Kitsune Possession", "Capturing": "Capturing", "Egg Capture": "Egg Capture", "Egg Shell": "Egg Shell", "Eggsplosion": "Eggsplosion", "Turbine Blast": "<PERSON>rb<PERSON>"}, "describecorruptiondict": {"horns5": "Commands authority. Move-only skills deal damage.", "eyes5": "<PERSON>'s makeup. <PERSON><PERSON> Draining Kiss.", "top5": "<PERSON>'s clothes. Leaves the wearer exposed.", "pants5": "<PERSON>'s stockings and thong.", "ears3": "<PERSON>'s hallmark. +1 Movement.", "tail3": "Agile, +1 Jump. Vulnerable to certain attacks.", "armright3": "Swipe attack. First hit always misses cat-girls.", "ears4": "Crafty fox's hallmark. +1 Skill Hotbar Capacity.", "tail4": "Large, brush-like fluffy tail. Scavenge +1 skill.", "hair4": "The fox's symbol. Forces a certain hairstyle.", "armright7": "Clumsy but fluffy wings. Fly on skill use.", "bust2": "A larger bust retains milk.", "bust3": "An even larger bust retains more milk.", "bust4": "An absurdly huge bust retains milk.", "leg7": "Egg-bearing hips."}, "icondescribearray": ["Windstep: Fall speed becomes 0.", "Sense: Reveals enemy locations.", "ExtraKnockback: Next attack gains knockback.", "Drain: Grants effect on hit.", "Drainheal: <PERSON>s health on hit.", "Fast: Doesn't end the turn.", "Heavy: Effect delayed until after enemy turn.", "Recovery: Counts down your debuffs.", "Echo: Repeats for each same-type hotbar skill.", "Block: Temporary shield vs. damage and debuffs."], "curseditemdescribedict": {"-16": "Weakens the wearer when pulled, tied to user's tail.", "-9": "You gain 'Running' when ending your turn climbing.", "-10": "Masks your scent to confuse Werewolves.", "-11": "Generates milk when hit by FORCE.", "-12": "Basic attack deals SWEET damage.", "-14": "+1 choice of Skill on the map.", "-15": "Basic attack hits three tiles."}, "curseditemname": {"Status": "Status: ", "Cursed Item": "Cursed Item: ", "bust2": "Bust: Medium", "bust3": "Bust: Large", "bust4": "Bust: <PERSON><PERSON>", "-16": "Hypno-Beads", "-9": "Climb Claws", "-10": "<PERSON>", "-11": "Milkmaid's Bell", "-12": "Soft Paws", "-13": "Wishing Ring", "-14": "Fox's Symbol", "-15": "Paws"}, "bodypartdict": {"bust": "bust", "armright": "arms", "hair": "hair", "pants": "pants", "top": "top", "horns": "horns", "ears": "ears", "tail": "tail", "eyes": "eyes", "leg": "legs"}, "debuffdescriptionarray": ["Sleepy, movement is halved.", "At risk of becoming obedient.", "Can't recover from other conditions.", "Gummed up. Cannot use consumables.", "Knowledge and movement are that of the Kitsune's.", "Damages clothes.", "May restrict the usage of Innate skills.", "At risk of becoming stunned.", "Adds weight to one's chest."], "debuffidentifyarray": ["Drowsy", "Hypnosis", "Sweaty", "Cum", "Possession", "Shredded", "Heat", "Impact", "Milk"], "debuffidentifynegativearray": ["Flinch", "K<PERSON><PERSON>"], "preview_words": ["Jump", "Sink", "Fall", "Run", "Climb", "Resist", "Ability", "Neutral", "Enemy", "Object", "Blocked", "Wait\nNo Moves", "Wait", "Wait+\nPush", "Talk", "Attack", "Fly", "Swim", "Swim+\nClimb", "Swim+\nJump", "Swim+\nJump+\nClimb", "Drop"], "customizationtextarray": ["Click here to finish - 5 Character names only.", "ENTER_NAME", "5 Character names only.", "Finish", "Customization can also be changed later.\nClick above when done.", "On", "Off", "Return", "Content preferences are shared between all save files.", "Warning! This disables ALL game content.", "R18 Consent\n/NSFW", "<PERSON><PERSON> (Enemy)", "Extreme Breast\n Expansion"], "tabinfodict": {"status": "Status", "statusinfo": "Explains debuffs and conditions.", "skills": "Skills", "skillsinfo": "Inventory is fully automatic. Right-click to flag items for deletion.", "settings": "Settings", "settingsinfo": "The game saves unlocks automatically. Leaving returns you to the village."}, "racearray": ["Human", "<PERSON><PERSON>", "Cupid", "Neko", "Kitsune", "Ram", "Werewolf", "<PERSON><PERSON><PERSON>"], "raceclassarray": ["Lancer", "Cow", "Cupid", "Tiger", "Priestess", "Bard", "Skirmisher", "<PERSON><PERSON><PERSON>"], "monster_display_names": {"HP": "HP", "0": ["Cat<PERSON><PERSON>", "Tiger-<PERSON>"], "1": ["Fox-<PERSON>", "Kutsu"], "2": ["<PERSON><PERSON><PERSON><PERSON>", "Pitch"], "3": ["Ram-Girl", "Saty<PERSON>"], "4": ["Spirit", "Spirit"], "5": ["<PERSON><PERSON><PERSON>", "<PERSON>"]}, "chapter_text": {"Cave of Shadows": "Cave of Shadows", "Fugitive's Hideaway": "Fugitive's <PERSON><PERSON><PERSON>", "Kitsune Shrine": "Kitsune Shrine", "Springs": "Springs", "Old Lands": "Old Lands", "Blasted Forest": "Blasted Forest", "Tiger Territory": "Tiger Territory"}, "event_messages": {"REMOVE: Skill permanently removed after level.": "REMOVE: Skill permanently removed after level.", "Not enough VAR1 to use it again, need VAR2.": "Not enough VAR1 to use it again, need VAR2.", "Ran out of VAR1": "Ran out of VAR1", "Your item hotbar is full! Right-click to discard.": "Your item hotbar is full! Right-click to discard.", "Cannot skip level, endurance too low.": "Cannot skip level, endurance too low.", "ATTACK now deals 1 damage instead of 0.": "ATTACK now deals 1 damage instead of 0.", "Fox-power. May find more skills to choose?": "Fox-power. May find more skills to choose?", "My legs match my wings...": "My legs match my wings...", "Everything hips-down feels bulky. Are those talons?": "Everything hips-down feels bulky. Are those talons?", "These are so awkward that they can't be undone?!": "These are so awkward that they can't be undone?!", "These don't fit but it's worth a try.": "These don't fit but it's worth a try.", "I'll hang them up in the gallery too.": "I'll hang them up in the gallery too.", "My arms are messed up again.": "My arms are messed up again.", "My hands...": "My hands...", "They're gone...?": "They're gone...?", "Nya? Became a full Cat-Girl.": "<PERSON><PERSON>? Became a full Cat-Girl.", "Jump speed increased.": "Jump speed increased.", "There's something soft behind me!": "There's something soft behind me!", "Crafty foxes may gather more skills.": "Crafty foxes may gather more skills.", "Cat-girl influence is taking over the existing corruption!": "Cat-girl influence is taking over the existing corruption!", "Move speed increased.": "Move speed increased.", "Turning into a Cat-girl?!": "Turning into a Cat-girl?!", "Kitsune energy is taking over the existing corruption!": "Kitsune energy is taking over the existing corruption!", "My ears are twitchy?": "My ears are twitchy?", "Head feels heavy... Movement skills now deal damage.": "Head feels heavy... Movement skills now deal damage.", "Forced into new clothes...": "Forced into new clothes...", "Stuffed into fluffy stockings?": "Stuffed into fluffy stockings?", "Class status has been cured.": "Class status has been cured.", "She stuffed some black beads in me?!": "She stuffed some black beads in me?!", "It's tied to my new tail..?": "It's tied to my new tail..?", "My tail slipped! Head feels hazy.": "My tail slipped! Head feels hazy.", "My tail slipped again, it's over...": "My tail slipped again, it's over...", "Too warm for clothes...": "Too warm for clothes...", "Bust size increased.": "Bust size increased.", "Bust size increased even further.": "Bust size increased even further.", "Bust size has become incredulous.": "Bust size has become incredulous.", "There's too much extra weight on my chest...": "There's too much extra weight on my chest...", "My chest feels so heavy now.": "My chest feels so heavy now.", "I can barely see my feet?!": "I can barely see my feet?!", "Inventory reset to class defaults.": "Inventory reset to class defaults.", "Enemy encampment has cleared from the overworld.": "Enemy encampment has cleared from the overworld.", "New temp skill ready.": "New temp skill ready.", "Entered area: 1-VAR1: Narrow Path": "Entered area: 1-VAR1: Narrow Path", "Entered area: 1-VAR1: Blasted Forest": "Entered area: 1-VAR1: Blasted Forest", "Entered area: 1-VAR1: Sunset Forest": "Entered area: 1-VAR1: Sunset Forest", "Entered area: 1-VAR1: Night Forest": "Entered area: 1-VAR1: Night Forest", "Entered area: VAR1": "Entered area: VAR1", "Entered the forest. WARNING:": "Entered the forest. WARNING:", "This area is outdated, look for Route A11 for current content.": "This area is outdated, look for Route A11 for current content.", "The sun is setting.": "The sun is setting.", "Night falls.": "Night falls.", "Tap and hold on the girl below to get a weapon.": "Tap and hold on the girl below to get a weapon.", "Right click the girl below to get a weapon.": "Right click the girl below to get a weapon.", "Block tests.": "Block tests.", "Cannot use Curse skills in this state.": "Cannot use Curse skills in this state.", "Ineffective.": "Ineffective.", "Couldn't move.": "Couldn't move.", "The wolves are howling. The night sky shimmers.": "The wolves are howling. The night sky shimmers.", "The wolves are howling.": "The wolves are howling.", "Chugged down the milk.": "Chugged down the milk.", "Resisted the temptation of milk.": "Resisted the temptation of milk.", "Drank all the milk.": "Drank all the milk.", "Wait here to temporarily remove conditions.": "Wait here to temporarily remove conditions.", "Status Reset.": "Status Reset.", "Wait here 3 times to remove corruption.": "Wait here 3 times to remove corruption.", "Progress will be saved.": "Progress will be saved.", "Class will also be reset.": "Class will also be reset.", "Wait here to change clothes.": "Wait here to change clothes.", "Cycle outfit 2/2: Ramgirl.": "Cycle outfit 2/2: <PERSON><PERSON>.", "Cycle outfit 1/2: Fuschia.": "Cycle outfit 1/2: Fuschia.", "Cycle outfit 0/2: None.": "Cycle outfit 0/2: None.", "Outfit #2 is unlocked with ram's hug while naked. Cannot unlock as current class RAM.": "Outfit #2 is unlocked with ram's hug while naked. Cannot unlock as current class RAM.", "Outfit #2 is unlocked with ram's hug while naked.": "Outfit #2 is unlocked with ram's hug while naked.", "Scenes switched to elite variants.": "Scenes switched to elite variants.", "Scenes switched to regular.": "Scenes switched to regular.", "Wait here to start scene (Tiger end)": "Wait here to start scene (<PERSON> end)", "Wait here to start scene (Sheep end)": "Wait here to start scene (She<PERSON> end)", "Wait here to start scene (Satyr end)": "Wait here to start scene (<PERSON><PERSON><PERSON> end)", "Wait here to start scene (Fox end)": "Wait here to start scene (<PERSON> end)", "Can't get away while under hypnosis...": "Can't get away while under hypnosis...", "The fox's spirit refuses to leave!": "The fox's spirit refuses to leave!", "SFW toggle - hid a mini-scene.": "SFW toggle - hid a mini-scene.", "Hypnosis made the Ram-girl act up!": "Hypnosis made the Ram-girl act up!", "After that, she took a pelt away...": "After that, she took a pelt away...", "It's stuck!!": "It's stuck!!", "Finally pulled free...": "Finally pulled free...", "Captured...": "Captured...", "Refreshed use of VAR1": "Refreshed use of VAR1", "Acquired consumables: VAR1": "Acquired consumables: VAR1", "Remember to right-click or scroll Torch to access Piton.": "Remember to right-click or scroll Torch to access Piton.", "Enter Camera Mode and tap Torch to switch to Piton.": "Enter Camera Mode and tap Tor<PERSON> to switch to Piton.", "Acquired: VAR1": "Acquired: VAR1", "I can't use any more of these.": "I can't use any more of these.", "Paws upgraded.": "Paws upgraded.", "Even better skill-finding power.": "Even better skill-finding power.", "Somehow, these paws altered my wings and got stuck?!": "Somehow, these paws altered my wings and got stuck?!", "The gloves are stuck, can't use items!": "The gloves are stuck, can't use items!", "A bell and a useless strip of cloth. I'll put the bell on.": "A bell and a useless strip of cloth. I'll put the bell on.", "The bell is a perfect fit!": "The bell is a perfect fit!", "It's a dupe, I'll take what I can.": "It's a dupe, I'll take what I can.", "This pelt has a wolf's scent. It should help.": "This pelt has a wolf's scent. It should help.", "These claws will help me climb better.": "These claws will help me climb better.", "Acquired a passive item: Lunar Dial Half. Gained insight.": "Acquired a passive item: Lunar Dial Half. Gained insight.", "I already have this item. Two halves don't make a whole.": "I already have this item. Two halves don't make a whole.", "Acquired a passive item: Sturdy Boots. Falling 4+ spaces deals (extra) damage.": "Acquired a passive item: Sturdy Boots. Falling 4+ spaces deals (extra) damage.", "Acquired a passive item: Basic Attack Max Range +1": "Acquired a passive item: Basic Attack Max Range +1", "Acquired a passive item: Torch lifetime increased. No further buff.": "Acquired a passive item: Torch lifetime increased. No further buff.", "Acquired a passive item: Torch strength increased but consumes x2 torches.": "Acquired a passive item: Torch strength increased but consumes x2 torches.", "Clang! Milk produced.": "Clang! Milk produced.", "You can open some conversations by moving to an NPC's space.": "You can open some conversations by moving to an NPC's space.", "Area will now refresh. Move anywhere to continue.": "Area will now refresh. Move anywhere to continue.", "Locked until 0-4. Use the canoe to get there.": "Locked until 0-4. Use the canoe to get there.", "Occurs from large bust, milk and Cat-girls.": "Occurs from large bust, milk and Cat-girls.", "Secret paws item + cat-girl in 0-2 Pussy Galore": "Secret paws item + cat-girl in 0-2 Pussy Galore", "Occurs from falling on Cat-girls.": "Occurs from falling on Cat-<PERSON>.", "Occurs from Ramgirl's debuff.": "Occurs from <PERSON><PERSON>'s debuff.", "Occurs from Ramgirl having the Hypnosis debuff.": "Occurs from <PERSON><PERSON> having the Hypnosis debuff.", "After too many paper seals from Fox-Girl": "After too many paper seals from Fox-Girl", "Occurs from Foxgirl reactionary attack.": "Occurs from Foxgirl reactionary attack.", "Occurs from Wolf Pelt item + Wolfgirl. Route A11 chests.": "Occurs from <PERSON> item + <PERSON><PERSON>. Route A11 chests.", "Get from chests in route A11.": "Get from chests in route A11.", "This vase is only half-full. Cannot unlock as current class NEKO.": "This vase is only half-full. Cannot unlock as current class NEKO.", "This vase is only half-full.": "This vase is only half-full.", "Occurs in mission 0-2 Pussy Galore": "Occurs in mission 0-2 Pussy Galore", "Occurs after milk consumption.": "Occurs after milk consumption.", "This vase is only half-full. Cannot finish it as current class KITSUNE.": "This vase is only half-full. <PERSON><PERSON> finish it as current class KITSUNE.", "Occurs from fox's combo scene.": "Occurs from fox's combo scene.", "This vase is only half-full. Cannot unlock as current class HARPY.": "This vase is only half-full. Cannot unlock as current class HARPY.", "From harpy's egg bomb.": "From harpy's egg bomb.", "Occurs from Ram's attacks.": "Occurs from <PERSON>'s attacks.", "From using Draining Kiss. Must hit an enemy on your own square.": "From using Draining Kiss. Must hit an enemy on your own square.", "Secret ending in 0-2 (Pussy Galore)": "Secret ending in 0-2 (<PERSON><PERSON><PERSON>)", "Wishing Ring in 0-3 / Losing to Satyr (Elite Ram)": "Wishing Ring in 0-3 / Losing to <PERSON><PERSON><PERSON> (Elite Ram)", "Wishing Ring in 0-3 / Losing to regular Sheep-girl.": "Wishing Ring in 0-3 / Losing to regular Sheep-girl.", "Loss to fox-girl in Route A11.": "Loss to fox-girl in Route A11.", "VAR1 resists the flinch effect.": "VAR1 resists the flinch effect.", "VAR1 was distracted by VAR2's VAR3.": "VAR1 was distracted by VAR2's VAR3.", "A different debuff might work.": "A different debuff might work.", "Cat-girl dodges the clumsy paws!": "Cat-girl dodges the clumsy paws!", "Attack fumbled, the cat-girl's messing with me!": "Attack fumbled, the cat-girl's messing with me!", "My attack fumbled. She's smothering me?!": "My attack fumbled. She's smothering me?!", "My attack fumbled. She's teasing me!": "My attack fumbled. She's teasing me!", "My attack fumbled...": "My attack fumbled...", "Hit Crate Alpha with Dagger, then a regular attack.": "Hit Crate Alpha with <PERSON><PERSON>, then a regular attack.", "Hit Crate Beta with Axe, then Dagger, then a regular attack.": "Hit Crate Beta with <PERSON><PERSON>, then <PERSON><PERSON>, then a regular attack.", "Hit crate Delta with a Torch.": "Hit crate Delta with a Torch.", "Stand on the platform, then use the scimitar down-right.": "Stand on the platform, then use the scimitar down-right.", "Crate Delta resisted damage taken against its sides.": "Crate Delta resisted damage taken against its sides.", "Stand on the platform, then hit the bottom-right crate Delta.": "Stand on the platform, then hit the bottom-right crate Delta.", "Crate Delta resisted ENERGY damage.": "Crate Delta resisted ENERGY damage.", "Corruption absorbed by shards.": "Corruption absorbed by shards."}}