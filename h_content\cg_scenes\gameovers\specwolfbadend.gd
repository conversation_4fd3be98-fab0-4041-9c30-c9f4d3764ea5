extends CanvasLayer



#OFFSETS:
#unb (but not unb children): 0x,392y
#tar: 0x,1315y
#arms: 1170x,0y
#juices: 505x,535y
#rules: 0x,275y
#faces: 2690x,335y
#belly: 1190x,0y


func _ready():
	var _c = get_tree().root.connect("size_changed", self, "_on_viewport_size_changed")
	_on_viewport_size_changed()
	set_hsv()
#
#func _input(event):
#	if event.is_action_pressed("debugequals"):
#		$AnimationPlayer.play("appear")
#	if event.is_action_pressed("debug-"):
##		move_to_location(Vector2(randf(),randf()))
#		current_zoom = 1+0.6*(randi()%2)
##		move_to_location(Vector2(randi()%2,randi()%2))
#		if current_zoom > 1:
#			move_to_location(Vector2(0.1,0.6))
#		else:
#			move_to_location(Vector2(0,0))

func set_hsv():
	
	var skin_material = Playervariables.get_materials(Playervariables.pcol.SKIN)
	var hair_material = Playervariables.get_materials(Playervariables.pcol.HAIR)
	
	$sprites/unb.material.set_shader_param("skin_hue_shift",skin_material.x)
	$sprites/unb.material.set_shader_param("skin_sat_mul",skin_material.y)
	$sprites/unb.material.set_shader_param("skin_val_mul",skin_material.z)
	
	$sprites/unb.material.set_shader_param("hair_hue_shift",hair_material.x)
	$sprites/unb.material.set_shader_param("hair_sat_mul",hair_material.y-1)
	$sprites/unb.material.set_shader_param("hair_val_mul",hair_material.z-1)
	
	
#	var colourarray = Playervariables.baseplayercolourarray
#	var skincolHSV = Playervariables.newcolorarrayskin[colourarray[2]]
##	for node in skinnodes: #I really don't need to do this for every node, it's redundant...
#	$sprites/unb.material.set_shader_param("skin_hue_shift",skincolHSV.x)
#	$sprites/unb.material.set_shader_param("skin_sat_mul",skincolHSV.y)
#	$sprites/unb.material.set_shader_param("skin_val_mul",skincolHSV.z)
#	var haircolHSV = Playervariables.newcolorarrayhair[Playervariables.truecolourarray[1]]
#	$sprites/unb.material.set_shader_param("hair_hue_shift",haircolHSV.x)
#	$sprites/unb.material.set_shader_param("hair_sat_mul",haircolHSV.y-1)
#	$sprites/unb.material.set_shader_param("hair_val_mul",haircolHSV.z-1)


const hover_offset = Vector2(-30,-30)
#const basesize = Vector2(3860,2160)
const basesize = Vector2(3800,2100)
var firstrun = true
var recentsizechange = false
func _on_viewport_size_changed():
	if recentsizechange == false:
		recentsizechange = true #this makes it so it only updates the viewport every 4 frames
		if firstrun == false:
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
			yield(get_tree(),"idle_frame")
#		var viewportrect = get_parent().get_viewport_rect().size
		engage_hover(true)
		recentsizechange = false
		firstrun = false

var saved_animation_point = 0.0
func engage_hover(from_viewport=false):
	var viewportrect = $testdonotdelete.get_viewport_rect().size
	var proportion = viewportrect/basesize
	var maxproportion = max(proportion.x,proportion.y)
	$sprites.scale = Vector2(maxproportion,maxproportion)*current_zoom
	var newhoveranim
	var actual_hover_offset = hover_offset*proportion*current_zoom
	if $hover.is_playing() == true:
		saved_animation_point = $hover.current_animation_position
	if ($hover.is_playing() == true and $hover.get_current_animation() == "newhover") or from_viewport == false:
		
		if $hover.has_animation("newhover"):
			newhoveranim = $hover.get_animation("newhover")
		else:
			newhoveranim = $hover.get_animation("hover").duplicate()
			newhoveranim = $hover.get_animation("hover").duplicate()
#			newhoveranim.track_set_key_value(0,0,Vector2(0,0))
#			newhoveranim.track_set_key_value(0,1,Vector2(0,0))
#			newhoveranim.track_set_key_value(0,2,Vector2(0,0))
#			newhoveranim.track_set_key_value(0,3,Vector2(0,0))
#			newhoveranim.track_set_key_value(0,4,Vector2(0,0))
		if (proportion.x > proportion.y):
			newhoveranim.track_set_key_value(0,0,Vector2(0,0)+actual_hover_offset)
			newhoveranim.track_set_key_value(0,1,Vector2(proportion.x*30,viewportrect.y - (basesize.y * maxproportion))+actual_hover_offset)
			newhoveranim.track_set_key_value(0,2,Vector2(0,0)+actual_hover_offset)
			newhoveranim.track_set_key_value(0,3,Vector2(-proportion.x*30,viewportrect.y - (basesize.y * maxproportion))+actual_hover_offset)
			newhoveranim.track_set_key_value(0,4,Vector2(0,0)+actual_hover_offset)
		else:
			newhoveranim.track_set_key_value(0,0,Vector2(0,0)+actual_hover_offset)
			newhoveranim.track_set_key_value(0,1,Vector2(viewportrect.x - (basesize.x * maxproportion),proportion.y*30)+actual_hover_offset)
			newhoveranim.track_set_key_value(0,2,Vector2(0,0)+actual_hover_offset)
			newhoveranim.track_set_key_value(0,3,Vector2(viewportrect.x - (basesize.x * maxproportion),-proportion.y*30)+actual_hover_offset)
			newhoveranim.track_set_key_value(0,4,Vector2(0,0)+actual_hover_offset)
#		$hover.playback_speed = 2 - ((maxproportion + 1) / (min(proportion.x,proportion.y)+1))
		var x = ((maxproportion+0.4) / (min(proportion.x,proportion.y)+0.4))
		$hover.playback_speed = (0.25 / (x*x))
		if $hover.has_animation("newhover") == false:
			$hover.add_animation("newhover",newhoveranim)
		$hover.stop()
		$hover.play("newhover")
		$hover.advance(saved_animation_point/$hover.playback_speed)
	elif $hover.is_playing() and $hover.get_current_animation() == "move_to_point":
		move_to_location(last_move_location,$hover.current_animation_position)

func pause_hover():
	saved_animation_point = $hover.current_animation_position
	$hover.stop(false)

func engage_shake_rules(bigiftrue = true):
	var viewportrect = $testdonotdelete.get_viewport_rect().size
	var proportion = viewportrect/basesize
	
	var oldshakeanim
	if bigiftrue == true:
		oldshakeanim = $sprites/rules/AnimationPlayer.get_animation("shakerules")
	else:
		oldshakeanim = $sprites/rules/AnimationPlayer.get_animation("smallshakerules")
	var newshakeanim
	if $sprites/rules/AnimationPlayer.has_animation("newshake"):
		newshakeanim = $sprites/rules/AnimationPlayer.get_animation("newshake")
	else:
		newshakeanim = $sprites/rules/AnimationPlayer.get_animation("shakerules").duplicate()
	
	for i in range(5):
		newshakeanim.track_set_key_value(0,i+1,oldshakeanim.track_get_key_value(0,i+1)*3*proportion*current_zoom)
		newshakeanim.track_set_key_value(1,i+1,oldshakeanim.track_get_key_value(1,i+1)*3*proportion*current_zoom)

	if $sprites/rules/AnimationPlayer.has_animation("newshake") == false:
		$sprites/rules/AnimationPlayer.add_animation("newshake",newshakeanim)
	$sprites/rules/AnimationPlayer.play("newshake")

func engage_shake():
	var viewportrect = $testdonotdelete.get_viewport_rect().size
	var proportion = viewportrect/basesize
	var maxproportion = max(proportion.x,proportion.y)
#	var actual_hover_offset = hover_offset*proportion*current_zoom
	
	$sprites.scale = Vector2(maxproportion,maxproportion)
	var newshakeanim
#	if $hover.is_playing() == true:
#		saved_animation_point = $hover.current_animation_position
	var oldshakeanim = $hover.get_animation("shake")
	if $hover.has_animation("newshake"):
		newshakeanim = $hover.get_animation("newshake")
	else:
		newshakeanim = $hover.get_animation("shake").duplicate()
	var before_pos = $sprites.position
	for i in range(4):
		newshakeanim.track_set_key_value(0,i,oldshakeanim.track_get_key_value(0,i)*3*proportion*current_zoom + before_pos)#does not include actual_hover_effect because before_pos has it
	newshakeanim.track_set_key_value(0,4,hover_offset*proportion*current_zoom)#does not include actual_hover_effect because before_pos has it

	if $hover.has_animation("newshake") == false:
		$hover.add_animation("newshake",newshakeanim)
	$hover.stop()
	$hover.play("newshake")
	$hover.playback_speed = 2
#	$hover.advance(saved_animation_point/$hover.playback_speed)


var last_move_location = Vector2(0,0)
var current_zoom = 1.0
func move_to_location(percentage_vector = Vector2(0.5,0.5),advance_value = 0,straight = false):
	var viewportrect = $testdonotdelete.get_viewport_rect().size
	var proportion = viewportrect/basesize
	var use_anim = $hover.get_animation("move_to_point")
	var actual_hover_offset = hover_offset*proportion*current_zoom
	var maxproportion = max(proportion.x,proportion.y)

	var intended_vector = Vector2(0,0)
	var overshoot_vector = Vector2(0,0)
	var prevscale = $sprites.scale.x
	if (proportion.x > proportion.y):
		intended_vector = Vector2(0,-(maxproportion - proportion.y) * percentage_vector.y * basesize.y) * current_zoom
		var direction = (intended_vector - $sprites.position).normalized()
		overshoot_vector = Vector2(30*proportion.x,-(maxproportion - proportion.y) * clamp(percentage_vector.y+direction.y*0.05,0,1) * basesize.y) * current_zoom
		if current_zoom > 1.0:
			intended_vector.x = -(current_zoom-1.0) * percentage_vector.x * basesize.x * maxproportion
			direction = (intended_vector - $sprites.position).normalized()
			overshoot_vector.x = -(current_zoom-1.0) * clamp(percentage_vector.x+direction.x*0.05,0,1) * basesize.x * maxproportion
		elif randf() > 0.5:
			overshoot_vector.x = -overshoot_vector.x
	else:
		intended_vector = Vector2(-(maxproportion - proportion.x) * percentage_vector.x * basesize.x,0) * current_zoom
		var direction = (intended_vector - $sprites.position).normalized()
		overshoot_vector = Vector2(-(maxproportion - proportion.x) * clamp(percentage_vector.x+direction.x*0.05,0,1) * basesize.x,30*proportion.y) * current_zoom
		if current_zoom > 1.0:
			intended_vector.y = -(current_zoom-1.0) * percentage_vector.y * basesize.y * maxproportion
			direction = (intended_vector - $sprites.position).normalized()
			overshoot_vector.y = -(current_zoom-1.0) * clamp(percentage_vector.y+direction.y*0.05,0,1) * basesize.y * maxproportion
		elif randf() > 0.5:
			overshoot_vector.y = -overshoot_vector.y
	use_anim.track_set_key_value(0,0,$sprites.position)
	if straight == false:
		use_anim.track_set_key_value(0,1,overshoot_vector+actual_hover_offset)
	else:
		use_anim.track_set_key_value(0,1,intended_vector+actual_hover_offset)
	use_anim.track_set_key_value(0,2,intended_vector+actual_hover_offset)#+actual_hover_offset)
	use_anim.track_set_key_value(1,0,Vector2(prevscale,prevscale))
	if straight == false:
		use_anim.track_set_key_value(1,1,Vector2(maxproportion,maxproportion)*current_zoom*1.3)
	else:
		use_anim.track_set_key_value(1,1,Vector2(maxproportion,maxproportion)*current_zoom)
	use_anim.track_set_key_value(1,2,Vector2(maxproportion,maxproportion)*current_zoom)
	if $AnimationPlayer.is_playing() == true:
		$AnimationPlayer.advance(4)
	use_anim.track_set_key_value(2,0,$sprites.get_modulate())
	if current_zoom > 1.0:
		use_anim.track_set_key_value(2,1,Color(0.7,0.7,0.7,1.0))
		use_anim.track_set_key_value(2,2,Color(0.85,0.85,0.85,1.0))
	else:
		use_anim.track_set_key_value(2,1,Color(0.85,0.85,0.85,1.0))
		use_anim.track_set_key_value(2,2,Color(1.0,1.0,1.0,1.0))
	$hover.playback_speed = 0.4
	$hover.stop()
	$hover.play("move_to_point")
	$hover.advance(advance_value/$hover.playback_speed)
	last_move_location = percentage_vector

func fix_zoom(alt = false):
	if current_zoom > 1.0:
		current_zoom = 1.0
		if alt == false:
			move_to_location(Vector2(0,0))
		else:
			move_to_location(Vector2(1,1))


var queued_action = -1
func action(num = 0,queued = false):
	if queued_action > -1:
		var save_action = queued_action
		queued_action = -1
		action(save_action,true)
	match num:
		-1: pass
		0: #first appearance
			fix_zoom()
			$AnimationPlayer.play("appear")
			engage_hover()
			$sprites/background.frame = 1
			$sprites/belly.frame = 0
			$sprites/face.frame = 1
			$sprites/arms.frame = 0
			$sprites/rules.frame = 0
			$sprites/tar.visible = true
			$sprites/juices.visible = false
			$sprites/unb.visible = false
			$sprites/fog.visible = false
			$sprites/kiss.visible = false
		1: #werewolf gets ready
			fix_zoom()
			$sprites/face.frame = 0
			$sprites/arms.frame = 1
			$sprites/juices.visible = false
		2: #pleasured wolf
			if Playervariables.get_corruption("ears") > 0 or Playervariables.get_corruption("backhorns") > 0 or Playervariables.get_corruption("horns") > 0:
#			if (Playervariables.corruptiondict.has("ears") and Playervariables.corruptiondict["ears"] > 0) or (Playervariables.corruptiondict.has("horns") and Playervariables.corruptiondict["horns"] > 0):
				$sprites/rules.frame = 2
			else:
				$sprites/rules.frame = 1
			fix_zoom()
			$sprites/face.frame = 0
			$sprites/arms.frame = 0
			$sprites/juices.visible = false
		3: #arms stuffed in
			fix_zoom()#thrust inside
			$sprites/face.frame = 2
			$sprites/arms.frame = 2
			engage_shake()
			$sprites/juices.visible = true
		4: #wolf stressed in trying to go further
			fix_zoom()
			$sprites/face.frame = 1
			$sprites/arms.frame = 2
			engage_hover()
		5: #kiss
			$sprites/face.frame = 0
			$sprites/fog/fogplayer.stop()
			$sprites/fog.visible = false
			$sprites/juices.visible = false
			if Playervariables.corruptiondict.has("eyes") and Playervariables.corruptiondict["eyes"] == Playervariables.raceRAM:
				$sprites/kiss.visible = true
		6: #rules taken further
			if queued == false:
				queued_action = num
				current_zoom = 1.2
				move_to_location(Vector2(0.1,0.9))
				return
			$sprites/background.frame = 1
			$sprites/belly.frame = 1
			$sprites/face.frame = 2
			$sprites/arms.frame = 2
			match Playervariables.get_corruption("leg"):
				Playervariables.raceIMP:
					$sprites/rules.frame = 6
				Playervariables.raceWOLF:
					$sprites/rules.frame = 5
				Playervariables.raceHARPY:
					$sprites/rules.frame = 4
				_:
					$sprites/rules.frame = 3
#			if Playervariables.corruptiondict.has("leg") and Playervariables.corruptiondict["leg"] == Playervariables.raceHARPY:
#				$sprites/rules.frame = 4
#			else:
#				$sprites/rules.frame = 3
			$sprites/tar.visible = true
			$sprites/unb.visible = false
			$sprites/juices.visible = true
			$sprites/kiss.visible = false
		7: #relaxing after rules is pretty much doomed
			current_zoom = 1.2
#			if queued == false:
#				queued_action = num
#				current_zoom = 1.2
#				move_to_location(Vector2(0.8,0.2))
#				return
			$sprites/face.frame = 0
			$sprites/arms.frame = 2
			$sprites/juices.visible = false
			$sprites/kiss.visible = false
#			$sprites/fog.visible = false
		8:#calming down
#			if queued == false:
#				queued_action = num
			current_zoom = 1.0
			move_to_location(Vector2(0.8,0.2),0,true)
#				return
			$sprites/face.frame = 0
			$sprites/arms.frame = 1
			$sprites/fog/fogplayer.stop()
			$sprites/fog.visible = false
			$sprites/juices.visible = false
		9: #fully taken in
			current_zoom = 1.2
			if queued == false:
				queued_action = num
				move_to_location(Vector2(0.7,0.4))
				return
			$sprites/background.frame = 1
			$sprites/belly.frame = 2
			$sprites/face.frame = 1
			$sprites/arms.frame = 0
			$sprites/rules.frame = 0
			$sprites/tar.visible = true
			$sprites/unb.visible = false
			$sprites/juices.visible = true
		10: #once again relaxing
			$sprites/background.frame = 1
			fix_zoom(true)
			$sprites/face.frame = 0
			$sprites/arms.frame = 2
		11: #oxygen
			$sprites/background.frame = 1
			engage_hover()
			$sprites/face.frame = 1
			$sprites/belly.frame = 2
			$sprites/tar.visible = false
			$sprites/unb.visible = false
		12: #second appearance
			fix_zoom()
			$AnimationPlayer.play("appear")
			engage_hover()
			$sprites/background.frame = 0
			$sprites/belly.frame = 2
			$sprites/face.frame = 1
			$sprites/arms.frame = 2
			$sprites/rules.frame = 0
			$sprites/tar.visible = false
			$sprites/juices.visible = true
			$sprites/unb.visible = false
		13: #second appearance
			$sprites/face.frame = 0
			engage_shake()
		14: #unb
			fix_zoom()
			$AnimationPlayer.play("appear")
			engage_shake()
			$sprites/background.frame = 0
			$sprites/belly.frame = 1
			$sprites/face.frame = 2
			$sprites/arms.frame = 2
			$sprites/rules.frame = 0
			$sprites/tar.visible = false
			$sprites/juices.visible = true
			$sprites/unb.visible = true
			$sprites/fog/fogplayer.play("engage_fog")
		15: #unb2
			fix_zoom()
			$AnimationPlayer.play("appear")
			engage_shake()
			$sprites/background.frame = 0
			$sprites/belly.frame = 0
			$sprites/face.frame = 0
			$sprites/arms.frame = 2
			$sprites/rules.frame = 0
			$sprites/tar.visible = false
			$sprites/juices.visible = true
			$sprites/unb.visible = false
		16:
			$sprites/face.frame = 1
			$sprites/juices.visible = false
		99: #unload screen
			fix_zoom(true)
			$hover.advance(4)
			$hover.stop(false)
			$AnimationPlayer.play_backwards("appear")
		
		30:$sprites.modulate = Color(1,1,1,0)
		
		32:engage_hover()
		33:engage_shake()
		34:
			engage_shake()
			$sprites/arms.frame = 1
		40:
			$sprites/fog/fogplayer.play("engage_fog")
		41:
			$sprites/fog/fogplayer.stop()
			$sprites/fog.visible = false
		50:$sprites/juices.visible = true
		51:$sprites/juices.visible = false
		60:engage_shake_rules(true)
		61:engage_shake_rules(false)


func _on_hover_animation_finished(anim_name):
	if anim_name == "move_to_point" and last_move_location == Vector2(0,0):
		saved_animation_point = 0
		engage_hover()

func _process(_delta):
	$sprites.position = $sprites.position.round()
#	for node in $sprites.get_children():
#		node.position = node.position.round()#Vector2(floor(node.position.x),floor(node.position.y))
