[gd_resource type="ParticlesMaterial" load_steps=3 format=2]

[sub_resource type="Gradient" id=1]
offsets = PoolRealArray( 0, 0.21164, 0.465608, 0.814815, 1 )
colors = PoolColorArray( 1, 1, 1, 0, 1, 1, 1, 0.446429, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=2]
gradient = SubResource( 1 )

[resource]
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
spread = 0.0
gravity = Vector3( 600, -500, 0 )
orbit_velocity = 1.0
orbit_velocity_random = 1.0
color_ramp = SubResource( 2 )
