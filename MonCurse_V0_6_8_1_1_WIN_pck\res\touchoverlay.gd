extends CanvasLayer


# Declare member variables here. Examples:
# var a = 2
# var b = "text"


# Called when the node enters the scene tree for the first time.
func _ready():
	Playervariables.touchscreenmode = true
	mode_switch(MENU)
	Playervariables.touchscreenalt = false

enum{CONV=0,PLAY=1,MENU=2}
var currentmode = -1
func mode_switch(num):
	if currentmode != num:
		if num != MENU:
			$Control/tempshow.play("hide")
		match num:
			MENU:
				if currentmode == PLAY and Playervariables.touchscreenalt == true:
					_on_toggle_pressed()
				$Control.visible = false
#				Playervariables.touchscreenalt = false
			CONV:
				if currentmode == PLAY and Playervariables.touchscreenalt == true:
					_on_toggle_pressed()
#				$Control/backer.visible = false
				$Control.visible = true
#				$Control/zoomout.visible = false
#				$Control/zoomin.visible = false
				$Control/toggle.visible = true
				$Control/toggle.texture_normal = load("res://Assets/ui/touchgear.png")
			PLAY:
#				$Control/backer.visible = true
				$Control.visible = true
				$Control/toggle.visible = true
				if Playervariables.touchscreenalt == true:
#					$Control/zoomout.visible = true
#					$Control/zoomin.visible = true
					$Control/toggle.texture_normal = load("res://Assets/ui/touchplay.png")
				else:
#					$Control/zoomout.visible = false
#					$Control/zoomin.visible = false
					$Control/toggle.texture_normal = load("res://Assets/ui/touchcamera.png")
		currentmode = num

func create_input(string):
	var a = InputEventAction.new()
	a.action = string
	a.pressed = true
	Input.parse_input_event(a)



func _on_zoomout_pressed():
	create_input("ui_scrolldown")

func _on_zoomin_pressed():
	create_input("ui_scrollup")

func _on_toggle_pressed():
	if currentmode == CONV:
		create_input("ui_rightclick")
	else:
		Playervariables.touchscreenalt = !Playervariables.touchscreenalt
		if Playervariables.touchscreenalt == true:
#			$Control/zoomout.visible = true
#			$Control/zoomin.visible = true
			$Control/toggle.texture_normal = load("res://Assets/ui/touchplay.png")
			$Control/showtimer.stop()
			show_up()
		else:
#			$Control/zoomout.visible = false
#			$Control/zoomin.visible = false
			$Control/toggle.texture_normal = load("res://Assets/ui/touchcamera.png")
			$Control/showtimer.start(0.01)
#			$Control/tempshow.play("hide")
#			$Control/tempshow.playback_speed = 4
		if get_node("/root/Master").call("map_visible_check") == true:
			$cameramode.visible = Playervariables.touchscreenalt
			if Playervariables.touchscreenalt == true:
				$cameramode/camera.play("flash")
			else:
				$cameramode/camera.stop()
		var currentnode = get_parent().currentscene
		if currentnode != null:
			if currentnode.has_method("camera_mode"):
				currentnode.camera_mode(Playervariables.touchscreenalt)
		get_tree().call_group("displayslot","update_touch")
		get_tree().call_group("itemslot","update_touch")
		get_tree().call_group("consumableslot","update_touch")

func disable_overlay_camera():
	$cameramode.visible = false
	$cameramode/camera.stop()

var firstshow = true
func show_up():
	if $Control/tempshow.is_playing():
		var currentanimation = $Control/tempshow.get_current_animation()
		if currentanimation == "show":
			$Control/tempshow.playback_speed = 4
		elif currentanimation == "hide":
			$Control/tempshow.playback_speed = -4
		elif currentanimation == "flashicon":
			camera_alert(false)
	elif $Control.get_modulate().a < 0.90:#yes, if the modulate isn't right it fucks everything up#$Control.anchor_bottom <= 0:
		$Control/tempshow.playback_speed = 4
		$Control/showtimer.stop()
		$Control/tempshow.play_backwards("show") #show is the same as hide, it's just differently named...
		if firstshow == true:
			$Control/tempshow.playback_speed = 1
			firstshow = false
func _on_showtimer_timeout():
	$Control/tempshow.playback_speed = 4
	$Control/tempshow.play("hide")
var lastshowfinished = "show"
func _on_tempshow_animation_finished(anim_name):
	lastshowfinished = anim_name
	if Playervariables.touchscreenalt == false or currentmode != PLAY:
		if $Control/tempshow.playback_speed < 0:
			$Control/showtimer.start(3)
			$Control/tempshow.playback_speed = 4
		elif anim_name == "show":
			$Control/showtimer.start(3)

func camera_alert(onoff):
	if onoff == true:
		if Playervariables.touchscreenalt == false:
			$Control/tempshow.play("flashicon")
			$Control/tempshow.playback_speed = 4
			$Control/showtimer.stop()
	else:
		if $Control/tempshow.is_playing():
			var currentanimation = $Control/tempshow.get_current_animation()
			if currentanimation == "flashicon":
				$Control/tempshow.playback_speed = 4
				$Control/tempshow.play("hide")
				$Control/tempshow.advance(9)
				$Control/showtimer.stop()
