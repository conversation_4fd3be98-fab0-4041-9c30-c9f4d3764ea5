extends Node2D

#var debuffshift = Vector2(500,200)
#func _ready():
#	print("debug, testing skillpreview, auto move , THIS WILL CRASH THE MAIN GAME")
#	var debugmove = 10
#	print("debugmovename:"+str(Playervariables.get("Move"+str(debugmove)).get("name")))
#	preview_skill(debugmove)

const spectilesize = 64
var movechoices = 0
var halfmovechoices = 0
var flipmove = false
var flipvector = Vector2(1,1)
var minusminimum = Vector2(0,0)
func preview_skill(movenum):
	var attackdict = Playervariables.get("Move"+str(movenum))
	var minimum = Vector2(0,0)
	var maximum = Vector2(0,0)
	var windup = attackdict.get("special")[3] == Playervariables.WINDUP
	var attackwindup = attackdict.get("special")[3] == Playervariables.ATTACKWINDUP
	movechoices = 0
	var movearrays = []
	var attackarrays = []
	for i in range(8):
		var constructm = str(i+1)+"m"
		if attackdict.has(constructm):
			movechoices += 1
			movearrays.append(attackdict[constructm])
			attackarrays.append(attackdict[str(i+1)+"a"])
		else:
			break
	var finalmovearrays = []
	var finalattackarrays = []
	if windup == true:
		for i in range(movechoices):
			var size = attackdict[str(i+1)+"m"].size()
			movechoices += clamp(size-1,0,999)
			for i2 in range(size):
				var nextarray = movearrays[i].duplicate()
				for _a in range(i2):
					nextarray.remove(nextarray.size()-1)
				finalmovearrays.append(nextarray)
				finalattackarrays.append(attackarrays[i])
	elif attackwindup == true:
		for i in range(movechoices):
			var size = attackdict[str(i+1)+"a"].size()
			movechoices += clamp(size-1,0,999)
			for i2 in range(size):
				var nextarray = attackarrays[i].duplicate()
				for _a in range(i2):
					nextarray.remove(nextarray.size()-1)
				finalmovearrays.append(movearrays[i])
				finalattackarrays.append(nextarray)
	else:
		finalmovearrays = movearrays
		finalattackarrays = attackarrays
	for i in range(movechoices):
#		if attackdict.has(str(i+1)+"m") == true:
#		movechoices = i+1
		vectorarray.append([])
		var testarray = finalmovearrays[i]
		var testarray2 = finalattackarrays[i]
		for vector in testarray:
			if vector.x > maximum.x:
				maximum.x = vector.x
			elif vector.x < minimum.x:
				minimum.x = vector.x
			if vector.y > maximum.y:
				maximum.y = vector.y
			elif vector.y < minimum.y:
				minimum.y = vector.y
			if testarray2.find(vector) == -1:
				vectorarray[i].append(Vector3(vector.x,vector.y,MOVE))
			else:
				vectorarray[i].append(Vector3(vector.x,vector.y,MOVEATTACK))
		for vector in testarray2:
			if vector.x > maximum.x:
				maximum.x = vector.x
			elif vector.x < minimum.x:
				minimum.x = vector.x
			if vector.y > maximum.y:
				maximum.y = vector.y
			elif vector.y < minimum.y:
				minimum.y = vector.y
			if testarray.find(vector) == -1:
					vectorarray[i].append(Vector3(vector.x,vector.y,ATTACK))
#		else:
#			break
	if attackdict.get("types")[3] in [Playervariables.TWODIR,Playervariables.FOURDIAGONALDIR]:
		if attackdict.get("types")[3] == Playervariables.TWODIR:
			minimum.x = min(minimum.x,-maximum.x)
			maximum.x = max(-maximum.x,maximum.x)
			flipvector = Vector2(-1,1)
		else:
			flipvector = Vector2(-1,-1)
			minimum.y = min(minimum.y,-maximum.y)
			maximum.y = max(-minimum.y,maximum.y)
			minimum.x = min(minimum.x,-maximum.x)
			maximum.x = max(-maximum.x,maximum.x)
		flipmove = true
#		minimum.x = min(minimum.x,-maximum.x)
#		minimum.y = min(minimum.y,-maximum.y)
#		maximum.x = max(-maximum.x,maximum.x)
#		maximum.y = max(-minimum.y,maximum.y)
		movechoices = movechoices*2
		halfmovechoices = ceil((movechoices)*0.5)
	minusminimum = -minimum
	$home.texture = load(Mainpreload.get("Particle"+str(attackdict.get("types")[4])))
	$home.position = spectilesize*Vector2(0,0)# + debuffshift
	if movechoices == 0:
		print("Skillpreview failed. Move does not have at least 1 M and A.")
		return
	for x in range ((1+maximum.x)-minimum.x):
		selector2darray.append([])
		for y in range ((1+maximum.y)-minimum.y):
			var newselector = load(Mainpreload.Selector).instance()
			$selectors.add_child(newselector)
			newselector.position = spectilesize*(Vector2(minimum.x+x,y+minimum.y))# + debuffshift
			newselector.set_self_modulate(Color(0.7,0.7,0.7,0.7))
			selector2darray[x].append(newselector)
	currentnum = movechoices-1
	$switch.start(0.5)
	_on_switch_timeout()
	var ascale = 4.0/clamp(max(1+(maximum.y-minimum.y),1+(maximum.x-minimum.x)),3,99)
	self.scale = Vector2(ascale,ascale)

enum{NONE,MOVE,ATTACK,MOVEATTACK}
const colorarray = [Color(0.7,0.7,0.7,0.7),Color(0.4,0.7,0.4,1.0),Color(1,0.45,0.35,1),Color(0.7,0.65,0.4,1)]
var vectorarray = []
var selector2darray = []
var currentnum = 0
func _on_switch_timeout():
	if movechoices == 1:
		$switch.stop()
	var lastnum = currentnum
	currentnum = (currentnum+1) % movechoices
	if flipmove == true:
		if currentnum >= halfmovechoices:
			if currentnum >= halfmovechoices+1:
				for vector in vectorarray[lastnum-halfmovechoices]:
					selector2darray[minusminimum.x+vector.x*flipvector.x][minusminimum.y+vector.y*flipvector.y].set_self_modulate(colorarray[NONE])
			else:
				for vector in vectorarray[lastnum]:
					selector2darray[minusminimum.x+vector.x][minusminimum.y+vector.y].set_self_modulate(colorarray[NONE])
			for vector in vectorarray[currentnum-halfmovechoices]:
				selector2darray[minusminimum.x+vector.x*flipvector.x][minusminimum.y+vector.y*flipvector.y].set_self_modulate(colorarray[vector.z])
		else:
			if lastnum == movechoices-1:
				for vector in vectorarray[lastnum-halfmovechoices]:
					selector2darray[minusminimum.x+vector.x*flipvector.x][minusminimum.y+vector.y*flipvector.y].set_self_modulate(colorarray[NONE])
			else:
				for vector in vectorarray[lastnum]:
					selector2darray[minusminimum.x+vector.x][minusminimum.y+vector.y].set_self_modulate(colorarray[NONE])
			for vector in vectorarray[currentnum]:
				selector2darray[minusminimum.x+vector.x][minusminimum.y+vector.y].set_self_modulate(colorarray[vector.z])
	else:
		for vector in vectorarray[lastnum]:
			selector2darray[minusminimum.x+vector.x][minusminimum.y+vector.y].set_self_modulate(colorarray[NONE])
		for vector in vectorarray[currentnum]:
			selector2darray[minusminimum.x+vector.x][minusminimum.y+vector.y].set_self_modulate(colorarray[vector.z])
