[gd_scene load_steps=20 format=2]

[ext_resource path="res://Master.gd" type="Script" id=1]
[ext_resource path="res://LevelMusicLoader.tscn" type="PackedScene" id=2]
[ext_resource path="res://Assets/ui/corruptionbarv3fill.png" type="Texture" id=3]
[ext_resource path="res://Assets/blacksquare.png" type="Texture" id=4]
[ext_resource path="res://resource_queue.gd" type="Script" id=5]
[ext_resource path="res://Enemy/qades/qadesshopsprite.png" type="Texture" id=6]
[ext_resource path="res://guiSFX.tscn" type="PackedScene" id=7]
[ext_resource path="res://Assets/ui/corruptionbarv3.png" type="Texture" id=8]
[ext_resource path="res://ConvoV1Holder.tscn" type="PackedScene" id=9]
[ext_resource path="res://Assets/materials/global_rankshift.tres" type="Material" id=10]
[ext_resource path="res://Assets/materials/global_hairshift.tres" type="Material" id=11]
[ext_resource path="res://font/Audiowide-Regular.ttf" type="DynamicFontData" id=12]
[ext_resource path="res://Assets/materials/global_skinshift.tres" type="Material" id=13]
[ext_resource path="res://Assets/materials/global_skinshift_with_mask.tres" type="Material" id=14]
[ext_resource path="res://Assets/materials/global_clothesshift.tres" type="Material" id=15]
[ext_resource path="res://Assets/materials/global_pantsshift.tres" type="Material" id=16]
[ext_resource path="res://Assets/materials/global_hornshift.tres" type="Material" id=17]
[ext_resource path="res://Assets/materials/global_eyeshift.tres" type="Material" id=18]

[sub_resource type="DynamicFont" id=1]
size = 18
font_data = ExtResource( 12 )

[node name="Master" type="Node"]
script = ExtResource( 1 )

[node name="LevelMusicLoader" parent="." instance=ExtResource( 2 )]

[node name="PreloadScenes" type="Node" parent="."]
script = ExtResource( 5 )

[node name="loadinglayer" type="CanvasLayer" parent="."]
layer = 10

[node name="LoadingVeil" type="TextureRect" parent="loadinglayer"]
visible = false
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 0
texture = ExtResource( 4 )
expand = true

[node name="TextureRect" type="TextureRect" parent="loadinglayer/LoadingVeil"]
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = -448.575
margin_top = -171.327
margin_right = -298.575
margin_bottom = -21.3268
grow_horizontal = 0
grow_vertical = 0
texture = ExtResource( 6 )
__meta__ = {
"_edit_use_anchors_": false
}

[node name="hbox" type="TextureProgress" parent="loadinglayer"]
visible = false
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = -294.0
margin_top = -90.0
margin_right = -35.0
margin_bottom = -26.0
texture_under = ExtResource( 8 )
texture_progress = ExtResource( 3 )

[node name="loadingtext" type="Label" parent="loadinglayer"]
visible = false
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = -272.0
margin_top = -185.0
margin_right = -58.0
margin_bottom = -88.0
grow_horizontal = 0
grow_vertical = 0
custom_fonts/font = SubResource( 1 )
text = "Now loading:"
align = 1
valign = 2
autowrap = true

[node name="SFX" parent="." instance=ExtResource( 7 )]

[node name="ramble" parent="SFX" index="5"]
volume_db = -23.0
bus = "SFX"

[node name="hoverclick" parent="SFX" index="6"]
bus = "SFX"

[node name="canoe" parent="SFX" index="7"]
bus = "SFX"

[node name="ConvoV1Holder" parent="." instance=ExtResource( 9 )]

[node name="map" type="CanvasLayer" parent="."]
layer = 7

[node name="translated" type="Node" parent="."]

[node name="global_materials" type="Node" parent="."]

[node name="global_hairshift" type="Node2D" parent="global_materials"]
visible = false
material = ExtResource( 11 )

[node name="global_eyeshift" type="Node2D" parent="global_materials"]
visible = false
material = ExtResource( 18 )

[node name="global_rankshift" type="Node2D" parent="global_materials"]
visible = false
material = ExtResource( 10 )

[node name="global_skinshift" type="Node2D" parent="global_materials"]
visible = false
material = ExtResource( 13 )

[node name="global_skinshift_rules_face" type="Node2D" parent="global_materials"]
visible = false
material = ExtResource( 14 )

[node name="global_clothesshift" type="Node2D" parent="global_materials"]
visible = false
material = ExtResource( 15 )

[node name="global_pantsshift" type="Node2D" parent="global_materials"]
visible = false
material = ExtResource( 16 )

[node name="global_hornshift" type="Node2D" parent="global_materials"]
visible = false
material = ExtResource( 17 )

[editable path="SFX"]
